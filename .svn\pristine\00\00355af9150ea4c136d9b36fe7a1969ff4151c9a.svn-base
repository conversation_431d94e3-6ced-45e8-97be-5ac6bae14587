                        -HC:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=29
-D<PERSON><PERSON>OID_PLATFORM=android-29
-D<PERSON>DROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++11
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\cxx\Debug\54t2sa2v\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\cxx\Debug\54t2sa2v\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\hhx\rk3588\AndroidStudio\XCamView\app\.cxx\Debug\54t2sa2v\armeabi-v7a
-GNinja
                        Build command args: []
                        Version: 2