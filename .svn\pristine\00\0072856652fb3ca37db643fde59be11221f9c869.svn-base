<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <FrameLayout
        android:id="@+id/right_panel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F5F5F5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.2"> <!-- 占25%宽度 -->

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="操作面板"
            android:textSize="18sp"/>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="#F0F0F0"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent">
        <ImageView
            android:id="@+id/browse_preview"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/ic_preview"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_after"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/next_n"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_picture"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/ic_picture"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <!-- 重复9个类似的ImageView -->
        <ImageView
            android:id="@+id/browse_video"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/ic_video"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_cell_count"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/ic_settings"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_allselect"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/stepframe_n"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_folder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/delete_n"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

        <ImageView
            android:id="@+id/browse_return"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/ic_return"
            android:scaleType="centerInside"
            android:background="#FFFFFF"
            android:padding="4dp"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/recycler_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="40dp" />

    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>