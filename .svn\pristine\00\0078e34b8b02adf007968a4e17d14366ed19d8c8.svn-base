package com.android.rockchip.camera2.activity

import android.content.Intent
import android.graphics.SurfaceTexture
import android.hardware.camera2.CameraDevice
import android.os.Bundle
import android.util.Log
import android.util.Size
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.Surface
import android.view.TextureView
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GestureDetectorCompat
import com.android.rockchip.camera2.activity.videomanagement.TpVideoEncoderActivity
import com.android.rockchip.camera2.databinding.ActivityMainBinding
import com.android.rockchip.camera2.util.setupEdgeToEdgeFullScreen
import com.android.rockchip.camera2.video.CameraManagerHelper
import com.android.rockchip.camera2.activity.browse.TpVideoBrowse
import com.android.rockchip.camera2.video.CaptureImageHelper
import com.android.rockchip.camera2.video.TvPreviewHelper
import com.android.rockchip.camera2.video.VideoEncoder
import android.os.Handler
import android.os.Looper
import com.android.rockchip.camera2.activity.videomanagement.TpVideoEncoderActivity.Companion
import com.android.rockchip.camera2.util.FileStorageUtils

class MainActivity : AppCompatActivity(), View.OnAttachStateChangeListener {
    private lateinit var binding: ActivityMainBinding
    private var tvpreview: TvPreviewHelper? = null
    private val TAG = "MainActivity"
    private lateinit var gestureDetector: GestureDetectorCompat
    private var videoEncoder: VideoEncoder? = null
    private var cameraManagerHelper: CameraManagerHelper? = null
    private var captureImageHelper: CaptureImageHelper? = null

    private var startTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var updateTimeRunnable: Runnable

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupEdgeToEdgeFullScreen() //全屏
        setupGestureControls()   //状态

        initCameraManager() //用于录像
        initializeTvComponents() //预览
    }

    private fun initializeTvComponents() {
        tvpreview = TvPreviewHelper(this, binding.rootView)
        tvpreview?.startPreview()
    }

    override fun onViewAttachedToWindow(v: View) = Unit //空实现
    override fun onViewDetachedFromWindow(v: View) = Unit

    private fun showMenu() {
        MainMenu().show(supportFragmentManager, "MainMenu")
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            if (it.action == MotionEvent.ACTION_UP) {
                showMenu()
                return true
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    fun showVideoEncoderActivity() {
        releaseTvView()
        startActivity(Intent(this, TpVideoEncoderActivity::class.java))
        finish()
    }

    fun showBrowseActivity() {
        releaseResources()
        releaseTvView()
        startActivity(Intent(this, TpVideoBrowse::class.java))
        finish()
    }

    fun releaseTvView() {
        tvpreview?.stopPreview()
        tvpreview = null
    }

    fun startRecord() {
        releaseTvView() //释放tv
        releaseResources()

        binding.blueTextureView.visibility = View.VISIBLE
        binding.blueTextureView.bringToFront()

        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化

        Handler(Looper.getMainLooper()).postDelayed({
            val newOutputPath = FileStorageUtils.createVideoPath(this)
            println("Video Path: $newOutputPath")
            videoEncoder?.startRecording(newOutputPath)
        }, 500)


        startTime = System.currentTimeMillis()
        binding.tvTimer.visibility = View.VISIBLE
        binding.tvTimer.bringToFront()
        startTimer()

//        val newOutputPath = FileStorageUtils.createVideoPath(this)
//        println("Video Path: $newOutputPath")
//        videoEncoder?.startRecording(newOutputPath)
    }

    fun stopRecord() {

        val duration = System.currentTimeMillis() - startTime
        showDuration(duration)

        videoEncoder?.stopRecording()
        releaseResources()  //此步骤处理较慢
        initializeTvComponents()
    }


    private fun setupGestureControls() {
        gestureDetector = GestureDetectorCompat(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                showMenu()
                return true
            }
        })

        binding.rootView.setOnTouchListener { view, event ->
            gestureDetector.onTouchEvent(event)
            if (event.action == MotionEvent.ACTION_UP) {
                view.performClick()
            }
            false
        }
    }

    private fun initCameraManager() {
        cameraManagerHelper = CameraManagerHelper(this)
        captureImageHelper = CaptureImageHelper(Size(3840, 2160)).apply {
            setCaptureCallback(object : CaptureImageHelper.CaptureCallback {
                override fun onImageSaved(filePath: String) {
                }

                override fun onError(errorMessage: String) {
                }
            })
        }
    }

    private fun setupViewListener() {
        binding.blueTextureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                initVideoEncoder(Surface(surface))
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean = true
            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }
    }

    private fun initVideoEncoder(textureSurface: Surface) {
        videoEncoder = VideoEncoder(object : VideoEncoder.Callback {
            override fun onSurfaceAvailable(encoderSurface: Surface, decoderSurface: Surface) {
                cameraManagerHelper?.openCamera(object : CameraDevice.StateCallback() {
                    override fun onOpened(camera: CameraDevice) {
                        cameraManagerHelper?.configCameraOutputs(camera, encoderSurface, captureImageHelper?.imageReader?.surface)
                    }

                    override fun onDisconnected(camera: CameraDevice) {
                        camera.close()
                    }

                    override fun onError(camera: CameraDevice, error: Int) {
                        camera.close()
                    }
                })
            }

            override fun onStorageFull() {
            }

            override fun onError(errorType: String, e: Exception) {
            }

            override fun onFileSizeLimitReached() {
            }
        }).apply {
            initialize(Size(3840, 2160), textureSurface)
        }
    }

    private fun releaseResources() {
        cameraManagerHelper?.releaseCamera()
        videoEncoder?.release()
    }



    //Timer
    private fun startTimer() {
        updateTimeRunnable = object : Runnable {
            override fun run() {
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - startTime
                updateTimerText(duration)
                handler.postDelayed(this, 1000)
            }
        }
        handler.post(updateTimeRunnable)
    }

    private fun updateTimerText(duration: Long) {
        val seconds = (duration / 1000) % 60
        val minutes = (duration / (1000 * 60)) % 60
        val hours = (duration / (1000 * 60 * 60)) % 24
        binding.tvTimer.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    private fun showDuration(duration: Long) {
        val seconds = (duration / 1000) % 60
        val minutes = (duration / (1000 * 60)) % 60
        val hours = (duration / (1000 * 60 * 60)) % 24
        val durationText = String.format("%02d:%02d:%02d", hours, minutes, seconds)

        // 使用自定义TextView显示
        binding.tvDuration.text = "录制时长：$durationText"
        binding.tvDuration.visibility = View.VISIBLE
        binding.tvDuration.postDelayed({
            binding.tvDuration.visibility = View.GONE
        }, 3000)
    }


    override fun onDestroy() {
        super.onDestroy()
//        handler.removeCallbacks(updateTimeRunnable)
//        releaseResources()
    }

    override fun onPause() {
        super.onPause()
        Log.d("Lifecycle", "onPause")
    }

    override fun onStop() {
        super.onStop()
        Log.d("Lifecycle", "onStop")
    }

    override fun onRestart() {
        super.onRestart()

        initializeTvComponents()
        Log.d("Lifecycle", "onRestart")
    }

    override fun onStart() {
        super.onStart()
        Log.d("Lifecycle", "onStart")
    }

    override fun onResume() {
        super.onResume()
        Log.d("Lifecycle", "onResume")
    }
}