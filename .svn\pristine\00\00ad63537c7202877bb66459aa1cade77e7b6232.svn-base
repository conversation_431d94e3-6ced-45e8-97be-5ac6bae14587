package com.android.rockchip.camera2.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.util.Map;

/**
 * 枚举类 TouptekIspParam 定义了摄像头 ISP 参数的键值对。
 * <p>
 * 每个枚举项对应一个 ISP 参数及其唯一的整数值。
 */
public enum TouptekIspParam {

    /** 版本号 */
    TOUPTEK_PARAM_VERSION(0x0),

    /** 曝光模式选择 */
    TOUPTEK_PARAM_EXPOSURECHOICE(0x1),

    /** 曝光补偿 */
    TOUPTEK_PARAM_EXPOSURECOMPENSATION(0x2),

    /** 曝光时间 */
    TOUPTEK_PARAM_EXPOSURETIME(0x3),

    /** 曝光增益 */
    TOUPTEK_PARAM_EXPOSUREGAIN(0x4),

    /** 白平衡模式选择 */
    TOUPTEK_PARAM_WBCHOICE(0x5),

    /** 红色通道的白平衡增益 */
    TOUPTEK_PARAM_WBREDGAIN(0x6),

    /** 绿色通道的白平衡增益 */
    TOUPTEK_PARAM_WBGREENGAIN(0x7),

    /** 蓝色通道的白平衡增益 */
    TOUPTEK_PARAM_WBBLUEGAIN(0x8),

    /** 锐化参数 */
    TOUPTEK_PARAM_SHARPNESS(0x9),

    /** 降噪参数 */
    TOUPTEK_PARAM_DENOISE(0xA),

    /** 镜像效果（水平/垂直镜像） */
    TOUPTEK_PARAM_MIRROR(0xB),

    /** 翻转效果（水平/垂直翻转） */
    TOUPTEK_PARAM_FLIP(0xC),

    /** 饱和度 */
    TOUPTEK_PARAM_SATURATION(0xD),

    /** Gamma 校正 */
    TOUPTEK_PARAM_GAMMA(0xE),

    /** 对比度 */
    TOUPTEK_PARAM_CONTRAST(0xF),

    /** 水平分辨率/频率（具体含义取决于硬件） */
    TOUPTEK_PARAM_HZ(0x10),

    /** 亮度 */
    TOUPTEK_PARAM_BRIGHTNESS(0x11),

    /** 色调 */
    TOUPTEK_PARAM_HUE(0x12),

    /** 彩色/灰度模式选择 */
    TOUPTEK_PARAM_COLORORGRAY(0x13),

    /** 带宽控制（影响图像处理的速度和质量） */
    TOUPTEK_PARAM_BANDWIDTH(0x14),

    /** 色彩色调 */
    TOUPTEK_PARAM_COLORTONE(0x15),

    /** 彩色温度红色通道增益 */
    TOUPTEK_PARAM_CTREDGAIN(0x16),

    /** 彩色温度绿色通道增益 */
    TOUPTEK_PARAM_CTGREENGAIN(0x17),

    /** 彩色温度蓝色通道增益 */
    TOUPTEK_PARAM_CTBLUEGAIN(0x18),

    /** 暗部增强 */
    TOUPTEK_PARAM_DARKENHANCE(0x19),

    /** 宽动态范围曝光比率 */
    TOUPTEK_PARAM_WDREXPRATIO(0x1A),

    /** 低动态范围对比度比率 */
    TOUPTEK_PARAM_LDCRATIO(0x1B);

    private final int value;
    private static SharedPreferences sharedPreferences;
    private static boolean flag;
    private static OnDataChangedListener dataChangedListener; // 监听器

    // 构造函数
    TouptekIspParam(int value) {
        this.value = value;
    }

    /**
     * 获取枚举值的整数值。
     *
     * @return 当前枚举项对应的整数值。
     */
    public int getValue() {
        return value;
    }

    // 根据整数值获取枚举项
    public static TouptekIspParam fromInt(int i) {
        for (TouptekIspParam param : TouptekIspParam.values()) {
            if (param.getValue() == i) {
                return param;
            }
        }
        return null; // 没有匹配项
    }

    /**
     * 初始化 SharedPreferences。
     *
     * @param context 应用上下文，用于获取 SharedPreferences 实例。
     */
    public static void init(Context context) {
        if (sharedPreferences == null) {
            sharedPreferences = context.getSharedPreferences("TouptekIspParams", Context.MODE_PRIVATE);
        }
    }

    /**
     * 设置数据变化监听器。
     *
     * @param listener 数据变化监听器实例。
     */
    public static void setOnDataChangedListener(OnDataChangedListener listener) {
        dataChangedListener = listener;
    }

    /**
     * 保存 int 类型数据。
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数。
     * @param data  要保存的 int 类型数据。
     */
    public static void saveData(TouptekIspParam param, int data) {
        System.out.println("传入的 data 值为: " + data);

        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(param.name(), data);
        editor.apply();

        // 触发回调
        if (dataChangedListener != null) {
            dataChangedListener.onDataChanged(param, data);
        }
    }

    /**
     * 保存 long 类型数据。
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数。
     * @param data  要保存的 long 类型数据。
     */
    public static void saveLongData(TouptekIspParam param, long data) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(param.name(), data);
        editor.apply();

        // 触发回调
        if (dataChangedListener != null) {
            dataChangedListener.onLongDataChanged(param, data);
        }
    }

    /**
     * 获取存储的数据。
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 int 类型数据，如果未找到则返回 -1。
     */
    public static int getData(TouptekIspParam param) {
        return sharedPreferences.getInt(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取存储的 long 类型数据。
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 long 类型数据，如果未找到则返回 -1。
     */
    public static long getLongData(TouptekIspParam param) {
        return sharedPreferences.getLong(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取所有存储的数据。
     * 
     * @return 包含所有存储键值对的 {@link Map}，键为参数名称，值为对应的存储数据。
     */
    public static Map<String, ?> getAllData() {
        return sharedPreferences.getAll();
    }

    /**
     * 根据索引获取对应的枚举项。
     *
     * @param index 索引值，从 0 开始。
     * @return 对应的 {@link TouptekIspParam} 枚举项，如果索引无效则返回 null。
     */
    public static TouptekIspParam getParamByIndex(int index) {
        TouptekIspParam[] params = TouptekIspParam.values();
        if (index >= 0 && index < params.length) {
            return params[index];
        }
        return null;  // 索引无效
    }

    /**
     * 设置状态标志。
     *
     * @param NewFlag 新的状态标志值，true 表示状态有效，false 表示状态无效。
     */
    public static void SetState(boolean NewFlag) {
        flag = NewFlag;
    }

    /**
     * 获取当前状态标志。
     *
     * @return 当前状态标志值，true 表示状态有效，false 表示状态无效。
     */
    public static boolean GetState() {
        return flag;
    }

    /**
     * 定义数据变化监听器接口。
     * <p>
     * 用于监听数据变化事件。
     */
    public interface OnDataChangedListener {

        /**
         * 当 int 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 int 类型值
         */
        void onDataChanged(TouptekIspParam param, int newValue);

        /**
         * 当 long 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 long 类型值
         */
        void onLongDataChanged(TouptekIspParam param, long newValue);
    }
}
