-- Merging decision tree log ---
manifest
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:2:1-98:12
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:2:1-98:12
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:2:1-98:12
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:2:1-98:12
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\83d45bd0b206b6badf684c7f00565dd2\transformed\viewbinding-8.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c8878ec0daf49981a7d44f20900a0e7f\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\fb1cea47df5c92fd43c6df734a7b8050\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6005461926148d117d3aae4150f29a25\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\129198fdb0b7b610c0d762c6a8d073c6\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\67871590078c7967de06e2f971f2c94b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fc275ad6289e5d79ac716b1909b974c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\2e45e76af36dae5b8215b61f980e31a4\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa13a1c42fa3ccc9f51856c0c1f13627\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8753f91da904df096e2c4492691fbe3b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ff67d99a879a9647120ef2619950e17\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8798f651b86c40ba5934defd04911933\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fe146d97dc3fa98515498913b830271\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\230b4c57fe4b8e06a1ded20484204bde\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dd70ed2fa88464651bef181034f58bad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be8eb2291e7e436b9c2b84ef72d2e46c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\409eaa5aea6a2d0bd11639f55fa89191\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\134d46147535f16abc3675887ea8eeff\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3c4d66a8cad1bb37e4f70f2eb21eb1f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff7072df11603eeb77db51bb693485bb\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2ae33a4628c1d1b81b2b49c436c577c3\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9d6dcf13f050a64ab04d7ed3d74270fc\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ebd6ebd3bfc425ab613396752f9c14a2\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc62215a7c6be4e14ff198b027d99cb3\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dfb5258c81a2cb6b23fb81f1475f943\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\780655dadb6c1a5994d98e5d1293d488\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0871c493276107a708ce7fd452b6a6ed\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af1442982367aa8fb10775c1dff2cbf4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88670dac7e2488b1c2278f408164377\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2dfdeaba7908e1101783256f9c852577\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc17c405380cd13b44260725cbba542b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\62a72fa6be160483dd7596c48e5c5d1f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e411fafb342b2224a86a5d429c54a304\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\97f7d8ccaa4c2db21a24d938b8a984c1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbb4afa035a5744f5abf93655407bb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a572946b3ae8523312289d3f0e9d966\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0150607d3be4698cae3d420bb2be34d7\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:3:5-63
uses-sdk
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:6:5-7:40
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:6:5-7:40
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:6:5-7:40
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\83d45bd0b206b6badf684c7f00565dd2\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\83d45bd0b206b6badf684c7f00565dd2\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c8878ec0daf49981a7d44f20900a0e7f\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c8878ec0daf49981a7d44f20900a0e7f\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\fb1cea47df5c92fd43c6df734a7b8050\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\fb1cea47df5c92fd43c6df734a7b8050\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6005461926148d117d3aae4150f29a25\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6005461926148d117d3aae4150f29a25\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\129198fdb0b7b610c0d762c6a8d073c6\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\129198fdb0b7b610c0d762c6a8d073c6\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\67871590078c7967de06e2f971f2c94b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\67871590078c7967de06e2f971f2c94b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fc275ad6289e5d79ac716b1909b974c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fc275ad6289e5d79ac716b1909b974c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\2e45e76af36dae5b8215b61f980e31a4\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\2e45e76af36dae5b8215b61f980e31a4\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa13a1c42fa3ccc9f51856c0c1f13627\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa13a1c42fa3ccc9f51856c0c1f13627\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8753f91da904df096e2c4492691fbe3b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8753f91da904df096e2c4492691fbe3b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ff67d99a879a9647120ef2619950e17\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ff67d99a879a9647120ef2619950e17\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8798f651b86c40ba5934defd04911933\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8798f651b86c40ba5934defd04911933\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fe146d97dc3fa98515498913b830271\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fe146d97dc3fa98515498913b830271\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\230b4c57fe4b8e06a1ded20484204bde\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\230b4c57fe4b8e06a1ded20484204bde\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dd70ed2fa88464651bef181034f58bad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dd70ed2fa88464651bef181034f58bad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be8eb2291e7e436b9c2b84ef72d2e46c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be8eb2291e7e436b9c2b84ef72d2e46c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\409eaa5aea6a2d0bd11639f55fa89191\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\409eaa5aea6a2d0bd11639f55fa89191\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\134d46147535f16abc3675887ea8eeff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\134d46147535f16abc3675887ea8eeff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3c4d66a8cad1bb37e4f70f2eb21eb1f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3c4d66a8cad1bb37e4f70f2eb21eb1f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff7072df11603eeb77db51bb693485bb\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff7072df11603eeb77db51bb693485bb\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2ae33a4628c1d1b81b2b49c436c577c3\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2ae33a4628c1d1b81b2b49c436c577c3\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9d6dcf13f050a64ab04d7ed3d74270fc\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9d6dcf13f050a64ab04d7ed3d74270fc\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ebd6ebd3bfc425ab613396752f9c14a2\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ebd6ebd3bfc425ab613396752f9c14a2\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc62215a7c6be4e14ff198b027d99cb3\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc62215a7c6be4e14ff198b027d99cb3\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dfb5258c81a2cb6b23fb81f1475f943\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dfb5258c81a2cb6b23fb81f1475f943\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\780655dadb6c1a5994d98e5d1293d488\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\780655dadb6c1a5994d98e5d1293d488\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0871c493276107a708ce7fd452b6a6ed\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0871c493276107a708ce7fd452b6a6ed\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af1442982367aa8fb10775c1dff2cbf4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af1442982367aa8fb10775c1dff2cbf4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88670dac7e2488b1c2278f408164377\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88670dac7e2488b1c2278f408164377\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2dfdeaba7908e1101783256f9c852577\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2dfdeaba7908e1101783256f9c852577\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc17c405380cd13b44260725cbba542b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc17c405380cd13b44260725cbba542b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\62a72fa6be160483dd7596c48e5c5d1f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\62a72fa6be160483dd7596c48e5c5d1f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e411fafb342b2224a86a5d429c54a304\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e411fafb342b2224a86a5d429c54a304\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\97f7d8ccaa4c2db21a24d938b8a984c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\97f7d8ccaa4c2db21a24d938b8a984c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbb4afa035a5744f5abf93655407bb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbb4afa035a5744f5abf93655407bb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a572946b3ae8523312289d3f0e9d966\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a572946b3ae8523312289d3f0e9d966\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0150607d3be4698cae3d420bb2be34d7\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0150607d3be4698cae3d420bb2be34d7\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:7:9-38
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:6:15-41
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
uses-permission#android.permission.CAMERA
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:9:9-69
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:9:26-66
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:10:9-85
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:10:26-82
uses-permission#android.permission.MANAGE_MEDIA_PROJECTION
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:13:9-86
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:13:26-83
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:16:9-81
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:16:26-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:17:9-75
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:17:26-72
uses-permission#android.permission.CAPTURE_AUDIO_OUTPUT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:18:9-82
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:18:26-80
uses-permission#android.permission.CAPTURE_VIDEO_OUTPUT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:19:9-82
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:19:26-80
uses-permission#android.permission.ACCESS_USB
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:21:9-72
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:21:26-70
uses-permission#android.permission.SERIAL_PORT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:22:9-73
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:22:26-71
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:23:9-84
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:23:26-81
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:24:9-86
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:24:26-83
application
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:29:5-96:19
INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:29:5-96:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c8878ec0daf49981a7d44f20900a0e7f\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c8878ec0daf49981a7d44f20900a0e7f\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\fb1cea47df5c92fd43c6df734a7b8050\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\fb1cea47df5c92fd43c6df734a7b8050\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2dfdeaba7908e1101783256f9c852577\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2dfdeaba7908e1101783256f9c852577\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:34:9-35
	android:label
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:32:9-41
	android:roundIcon
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:33:9-54
	android:icon
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:31:9-43
	android:allowBackup
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:30:9-35
	android:theme
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:35:9-41
activity#com.android.rockchip.camera2.RockchipCamera2
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:36:9-44:20
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:37:10-33
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:38:13-44
activity#com.android.rockchip.camera2.activity.WelcomeActivity
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:46:9-59:20
	android:screenOrientation
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:51:13-52
	android:launchMode
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:50:13-44
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:49:13-36
	android:supportsPictureInPicture
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:52:13-53
	android:configChanges
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:48:13-115
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:47:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.LAUNCHER
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:53:13-58:29
action#android.intent.action.MAIN
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:54:17-69
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:54:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:56:27-74
category#android.intent.category.DEFAULT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:57:17-76
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:57:27-73
activity#com.android.rockchip.camera2.activity.MainActivity
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:61:9-68:54
	android:screenOrientation
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:67:13-52
	android:launchMode
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:65:13-44
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:64:13-36
	android:supportsPictureInPicture
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:68:13-52
	android:resizeableActivity
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:66:13-46
	android:configChanges
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:63:13-115
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:62:13-50
service#com.android.rockchip.camera2.ScreenRecordService
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:70:9-73:62
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:71:13-36
	android:foregroundServiceType
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:73:13-60
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:72:13-48
service#com.android.rockchip.camera2.HdmiService
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:75:9-83:19
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:76:10-33
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:77:13-40
intent-filter#action:name:com.android.rockchip.camera2.HdmiService+category:name:android.intent.category.DEFAULT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:78:13-82:29
action#com.android.rockchip.camera2.HdmiService
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:79:17-83
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:79:25-80
receiver#com.android.rockchip.camera2.BootBroadcastReceiver
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:86:9-94:20
	android:enabled
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:87:17-39
	android:exported
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:88:17-41
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:86:19-56
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:89:13-93:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:90:17-79
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:90:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:91:17-82
	android:name
		ADDED from C:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\AndroidManifest.xml:91:25-79
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\69945dceeda6265559d7a9324f685c09\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\70600eeaad55e7913ed14710f02ec494\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f2c69c2bec547a523b829ecb17be5c7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\b78e7d33379a9561c285f9ed2178c208\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d0c6b7a405ce929367a808d4e21dce72\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
