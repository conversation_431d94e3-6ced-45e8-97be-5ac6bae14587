<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jul 16 10:30:46 CST 2025 -->
<title>F - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-16">
<meta name="description" content="index: F">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:F">F</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">FileStorageUtils 类提供文件存储相关的工具方法。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#%3Cinit%3E()" class="member-name-link">FileStorageUtils()</a> - 类的构造器 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.StorageListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">FileStorageUtils.StorageListener</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">存储设备变化监听接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpCaptureImage.html#FORMAT_BMP" class="member-name-link">FORMAT_BMP</a> - 类中的静态变量 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpCaptureImage.html" title="com.android.rockchip.camera2.video中的类">TpCaptureImage</a></dt>
<dd>
<div class="block">BMP格式常量</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpCaptureImage.html#FORMAT_JPEG" class="member-name-link">FORMAT_JPEG</a> - 类中的静态变量 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpCaptureImage.html" title="com.android.rockchip.camera2.video中的类">TpCaptureImage</a></dt>
<dd>
<div class="block">JPEG格式常量</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpCaptureImage.html#FORMAT_PNG" class="member-name-link">FORMAT_PNG</a> - 类中的静态变量 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpCaptureImage.html" title="com.android.rockchip.camera2.video中的类">TpCaptureImage</a></dt>
<dd>
<div class="block">PNG格式常量</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpCaptureImage.html#FORMAT_TIFF" class="member-name-link">FORMAT_TIFF</a> - 类中的静态变量 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpCaptureImage.html" title="com.android.rockchip.camera2.video中的类">TpCaptureImage</a></dt>
<dd>
<div class="block">TIFF格式常量</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#fromInt(int)" class="member-name-link">fromInt(int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">根据整数值获取枚举项。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
