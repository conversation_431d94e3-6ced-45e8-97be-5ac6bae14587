                        -HD:\RK3588\APP\APP_cangshiyizhirkcame2\rkCamer2\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=34
-DANDROID_PLATFORM=android-34
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++11
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\RK3588\APP\APP_cangshiyizhirkcame2\rkCamer2\app\build\intermediates\cxx\Debug\k726jz3a\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\RK3588\APP\APP_cangshiyizhirkcame2\rkCamer2\app\build\intermediates\cxx\Debug\k726jz3a\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\RK3588\APP\APP_cangshiyizhirkcame2\rkCamer2\app\.cxx\Debug\k726jz3a\x86
-GNinja
                        Build command args: []
                        Version: 2