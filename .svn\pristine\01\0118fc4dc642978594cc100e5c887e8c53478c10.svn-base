<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 30 14:13:27 CST 2025 -->
<title>NetworkManager</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-30">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, class: NetworkManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="类 NetworkManager" class="title">类 NetworkManager</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.util.NetworkManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">NetworkManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">NetworkManager 类用于管理WiFi、以太网和热点相关操作。
 <p>
 此类提供了网络连接的监控、状态查询和控制功能，支持WiFi、以太网和热点的状态管理。
 通过回调机制通知应用网络状态变化，并提供网络接口信息查询功能。
 </p>
 
 <p><b>主要功能：</b></p>
 <ul>
   <li>监控WiFi和以太网连接状态</li>
   <li>管理和监控热点状态</li>
   <li>提供网络接口信息查询</li>
   <li>支持WiFi开关控制</li>
   <li>打开系统网络设置界面</li>
 </ul>
 
 <p><b>使用示例：</b></p>
 <pre><code>
 // 创建网络管理器
 NetworkManager networkManager = new NetworkManager(context, 
     new NetworkManager.NetworkStateListener() {
         @Override
         public void onWifiStateChanged(boolean isConnected, String ssid) {
             if (isConnected) {
                 Log.d(TAG, "WiFi已连接: " + ssid);
             } else {
                 Log.d(TAG, "WiFi已断开");
             }
         }
         
         @Override
         public void onEthernetStateChanged(boolean isConnected) {
             Log.d(TAG, "以太网状态: " + (isConnected ? "已连接" : "已断开"));
         }
         
         @Override
         public void onHotspotStateChanged(boolean isEnabled, String hotspotInfo) {
             Log.d(TAG, "热点状态: " + (isEnabled ? "已开启" : "已关闭") + 
                   (isEnabled ? ", " + hotspotInfo : ""));
         }
     }, 
     wifiPanelLauncher
 );
 
 // 开始监控网络状态
 networkManager.startMonitoring();
 
 // 获取网络接口信息
 List&lt;NetworkManager.NetworkInterfaceInfo&gt; interfaces = 
     networkManager.getAvailableNetworkInterfaces();
 
 // 控制WiFi
 networkManager.toggleWifi(isCurrentlyConnected);
 
 // 停止监控
 networkManager.stopMonitoring();
 </code></pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="NetworkManager.HotspotInfo.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">NetworkManager.HotspotInfo</a></code></div>
<div class="col-last even-row-color">
<div class="block">热点信息类</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="NetworkManager.NetworkInterfaceInfo.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">NetworkManager.NetworkInterfaceInfo</a></code></div>
<div class="col-last odd-row-color">
<div class="block">网络接口信息类</div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="NetworkManager.NetworkStateListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">NetworkManager.NetworkStateListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">网络状态监听器接口</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="NetworkManager.WifiConnectionInfo.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">NetworkManager.WifiConnectionInfo</a></code></div>
<div class="col-last odd-row-color">
<div class="block">WiFi连接信息类</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(android.content.Context,com.android.rockchip.camera2.util.NetworkManager.NetworkStateListener,androidx.activity.result.ActivityResultLauncher)" class="member-name-link">NetworkManager</a><wbr>(android.content.Context&nbsp;context,
 <a href="NetworkManager.NetworkStateListener.html" title="com.android.rockchip.camera2.util中的接口">NetworkManager.NetworkStateListener</a>&nbsp;listener,
 androidx.activity.result.ActivityResultLauncher&lt;android.content.Intent&gt;&nbsp;wifiPanelLauncher)</code></div>
<div class="col-last even-row-color">
<div class="block">创建一个新的网络管理器实例</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="NetworkManager.NetworkInterfaceInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.NetworkInterfaceInfo</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAvailableNetworkInterfaces()" class="member-name-link">getAvailableNetworkInterfaces</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取所有可用的网络接口信息</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="NetworkManager.HotspotInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.HotspotInfo</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentHotspotState()" class="member-name-link">getCurrentHotspotState</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前热点状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="NetworkManager.WifiConnectionInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.WifiConnectionInfo</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentWifiState()" class="member-name-link">getCurrentWifiState</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前WiFi连接状态</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEthernetConnected()" class="member-name-link">isEthernetConnected</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前以太网连接状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#openHotspotSettings()" class="member-name-link">openHotspotSettings</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">打开热点设置页面</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pauseHotspotMonitoring()" class="member-name-link">pauseHotspotMonitoring</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">暂停热点状态检查（在Activity onPause时调用）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resumeHotspotMonitoring()" class="member-name-link">resumeHotspotMonitoring</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">恢复热点状态检查（在Activity onResume时调用）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startMonitoring()" class="member-name-link">startMonitoring</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始监听网络状态变化</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopMonitoring()" class="member-name-link">stopMonitoring</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止监听网络状态变化</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toggleWifi(boolean)" class="member-name-link">toggleWifi</a><wbr>(boolean&nbsp;isCurrentlyConnected)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换WiFi开关状态</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(android.content.Context,com.android.rockchip.camera2.util.NetworkManager.NetworkStateListener,androidx.activity.result.ActivityResultLauncher)">
<h3>NetworkManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NetworkManager</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 <a href="NetworkManager.NetworkStateListener.html" title="com.android.rockchip.camera2.util中的接口">NetworkManager.NetworkStateListener</a>&nbsp;listener,
 androidx.activity.result.ActivityResultLauncher&lt;android.content.Intent&gt;&nbsp;wifiPanelLauncher)</span></div>
<div class="block">创建一个新的网络管理器实例</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于访问系统服务</dd>
<dd><code>listener</code> - 网络状态监听器，用于接收网络状态变化通知</dd>
<dd><code>wifiPanelLauncher</code> - 用于启动系统WiFi设置面板的ActivityResultLauncher</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="startMonitoring()">
<h3>startMonitoring</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startMonitoring</span>()</div>
<div class="block">开始监听网络状态变化
 <p>
 此方法启动WiFi、以太网和热点状态的监控。
 注册网络回调以接收连接状态变化通知，并启动热点状态的周期性检查。
 调用此方法后，所有网络状态变化将通过NetworkStateListener回调通知。
 方法会立即更新当前网络状态并触发初始回调。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="stopMonitoring()">
<h3>stopMonitoring</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopMonitoring</span>()</div>
<div class="block">停止监听网络状态变化</div>
</section>
</li>
<li>
<section class="detail" id="pauseHotspotMonitoring()">
<h3>pauseHotspotMonitoring</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pauseHotspotMonitoring</span>()</div>
<div class="block">暂停热点状态检查（在Activity onPause时调用）</div>
</section>
</li>
<li>
<section class="detail" id="resumeHotspotMonitoring()">
<h3>resumeHotspotMonitoring</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resumeHotspotMonitoring</span>()</div>
<div class="block">恢复热点状态检查（在Activity onResume时调用）</div>
</section>
</li>
<li>
<section class="detail" id="getCurrentWifiState()">
<h3>getCurrentWifiState</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="NetworkManager.WifiConnectionInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.WifiConnectionInfo</a></span>&nbsp;<span class="element-name">getCurrentWifiState</span>()</div>
<div class="block">获取当前WiFi连接状态
 <p>
 此方法检查当前WiFi的连接状态，包括是否连接和SSID信息。
 通过ConnectivityManager和NetworkCapabilities API获取准确的连接状态，
 确保只有当WiFi传输通道实际可用时才返回已连接状态。
 如果WiFi已连接，还会获取当前连接的SSID。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>WiFi连接信息对象，包含连接状态(isConnected)和网络名称(ssid)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEthernetConnected()">
<h3>isEthernetConnected</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEthernetConnected</span>()</div>
<div class="block">获取当前以太网连接状态</div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否连接到以太网</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentHotspotState()">
<h3>getCurrentHotspotState</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="NetworkManager.HotspotInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.HotspotInfo</a></span>&nbsp;<span class="element-name">getCurrentHotspotState</span>()</div>
<div class="block">获取当前热点状态</div>
<dl class="notes">
<dt>返回:</dt>
<dd>热点状态信息，包含是否开启和配置信息</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toggleWifi(boolean)">
<h3>toggleWifi</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">toggleWifi</span><wbr><span class="parameters">(boolean&nbsp;isCurrentlyConnected)</span></div>
<div class="block">切换WiFi开关状态
 <p>
 如果WiFi当前已开启，则关闭WiFi；如果当前已关闭，则开启WiFi。
 此方法会立即执行操作，无需用户确认。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>isCurrentlyConnected</code> - 当前WiFi是否已连接，true表示已连接，false表示未连接</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="openHotspotSettings()">
<h3>openHotspotSettings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">openHotspotSettings</span>()</div>
<div class="block">打开热点设置页面</div>
</section>
</li>
<li>
<section class="detail" id="getAvailableNetworkInterfaces()">
<h3>getAvailableNetworkInterfaces</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="NetworkManager.NetworkInterfaceInfo.html" title="com.android.rockchip.camera2.util中的类">NetworkManager.NetworkInterfaceInfo</a>&gt;</span>&nbsp;<span class="element-name">getAvailableNetworkInterfaces</span>()</div>
<div class="block">获取所有可用的网络接口信息</div>
<dl class="notes">
<dt>返回:</dt>
<dd>网络接口信息列表</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
