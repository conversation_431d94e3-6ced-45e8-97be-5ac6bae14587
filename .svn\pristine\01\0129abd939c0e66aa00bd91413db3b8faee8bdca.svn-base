#ifndef __soap_session_h__
#define __soap_session_h__

#include <utiny/utiny_config.h>
#include <utiny/http_header.h>
#include <utiny/http_session_context.h>
#include <utiny/usys_thread.h>
#include <utiny/http_transceiver.h>
#include <utiny/usys_task.h>
#include <utiny/usys_reactor.h>
#include <utiny/http_packet_context.h>
#include <utiny/http_response_context.h>
#include <utiny/http_request_context.h>
#include <utiny/usys_data_block.h>
#include <utiny/stdsoap2.h>
#include <deque>

extern std::string soap_msgtotag_response(const char* msg);
extern const char* soap_msgtotag_request(const char* msg);

class soap_transceiver : public http_transceiver
{
public:
    soap_transceiver(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size = 0, unsigned recv_buffer_size = 0);
	virtual ~soap_transceiver();

public:
	void send_request();
};

class soap_packet_context
{
protected:
	int					reqtype_;
	int					restype_;
	const char*			reqmsg_;
	const char*			resmsg_;
	usys_data_block_ptr	mb_;
	soap				soap_;
public:
	soap_packet_context(bool httpauth, const char* userid, const char* passwd);
	virtual ~soap_packet_context();

	soap* get_soap() { return &soap_; }
	usys_data_block_ptr get_mb() { return mb_; };
	void set_mb(const usys_data_block_ptr& mb);
	void set_error(const char *faultstring, const char *faultdetailXML, int soaperror, bool sender);

	int reqtype()const { return reqtype_; }
	int restype()const { return restype_; }
	void reqtype(int req, int res) { reqtype_ = req; restype_ = res; }

	const char* reqmsg()const { return reqmsg_; }
	const char* resmsg()const { return resmsg_; }
	void reqmsg(const char* req, const char* res) { reqmsg_ = req; resmsg_ = res; }

	int send(const char* s, size_t n);
	size_t recv(char* s, size_t n);
};

class soap_request_context : public http_request_context, public soap_packet_context
{
public:
	soap_request_context(const usys_smartptr<usys_transceiver>& transceiverptr);
};
typedef usys_smartptr<soap_request_context>	soap_request_context_ptr;

class soap_response_context : public http_response_context, public soap_packet_context
{
protected:
	const int	timeout_;
	const bool	oneway_;
	std::string	username_;
	std::string password_;
	std::string nonce_;
	std::string created_;
public:
	soap_response_context(const usys_smartptr<usys_transceiver>& transceiverptr, bool httpauth = false, const char* username = NULL, const char* password = NULL, int timeout = 0, bool oneway = false);

	int timeout()const { return timeout_; }
	bool oneway()const { return oneway_; }
	const std::string& username()const { return username_; }
	const std::string& password()const { return password_; }
	const std::string& nonce()const { return nonce_; };
	const std::string& created()const {	return created_; };
	
	void request_cancel(const char *faultstring, const char *faultdetailXML, int soaperror, bool sender);
};
typedef usys_smartptr<soap_response_context>	soap_response_context_ptr;

#define DECL_SOAP_REQUEST(MSGTYPE)			int request_msg(_##MSGTYPE*, const usys_smartptr<soap_response_context>& response_ptr)
#define DECL_SOAP_RESPONSE(MSGTYPE)			int response_msg(_##MSGTYPE*, const usys_smartptr<soap_request_context>& request_ptr)
#define DECL_SOAP_PRERESPONSE(MSGTYPE)		void pre_response_##MSGTYPE(const usys_smartptr<soap_response_context>& response_ptr); void proc_response(const usys_smartptr<soap_response_context>& response_ptr, const _##MSGTYPE* msg, int soaperror)
#define DECL_SOAP_PREREQUEST(MSGTYPE)		void pre_request_##MSGTYPE(const usys_smartptr<soap_request_context>& request_ptr); int proc_request(const usys_smartptr<soap_request_context>& request_ptr, const _##MSGTYPE* msg)

#define BEGIN_SOAP_RESPONSE()	\
	public:						\
	void proc_response(const usys_smartptr<soap_response_context>& rptr)	\
	{								\
		switch (rptr->restype())	\
		{

#define END_SOAP_RESPONSE()	\
		default:			\
			break;			\
		}					\
	}

#define BEGIN_SOAP_REQUEST()	\
	public:						\
	void proc_request(const usys_smartptr<soap_request_context>& rptr)	\
	{							\
		soap* psoap = rptr->get_soap();

#define END_SOAP_REQUEST()				\
		psoap->error = SOAP_NO_METHOD;	\
	}

#define SOAP_RESPONSE_HANDLER(MSGTYPE)	\
	case SOAP_TYPE__##MSGTYPE:			\
		pre_response_##MSGTYPE(rptr);	\
		break;

#define SOAP_REQUEST_HANDLER(MSGTYPE)	\
	{												\
		const char* tag = soap_msgtotag_request(#MSGTYPE);	\
		if (!soap_match_tag(psoap, psoap->tag, tag))	\
		{											\
			pre_request_##MSGTYPE(rptr);			\
			return;									\
		}											\
	}

#define IMPL_SOAP_REQUEST(CLSTYPE, MSGTYPE)	\
int CLSTYPE::request_msg(_##MSGTYPE* msg, const usys_smartptr<soap_response_context>& response_ptr)	\
{	\
	response_ptr->reqtype(SOAP_TYPE__##MSGTYPE, SOAP_TYPE__##MSGTYPE##Response);	\
	response_ptr->reqmsg(#MSGTYPE, #MSGTYPE "Response");	\
	soap* psoap = response_ptr->get_soap();		\
	if (SOAP_OK != psoap->error)				\
		return psoap->error;					\
	struct __##MSGTYPE soap_tmp___##MSGTYPE;	\
	psoap->encodingStyle = NULL;				\
	soap_tmp___##MSGTYPE.MSGTYPE = msg;			\
	soap_begin(psoap);							\
	soap_serializeheader(psoap);				\
	if (soap_begin_count(psoap))				\
		return psoap->error;					\
	if (psoap->mode & SOAP_IO_LENGTH)			\
	{											\
		if (soap_envelope_begin_out(psoap)		\
		 || soap_putheader(psoap)				\
		 || soap_body_begin_out(psoap)			\
		 || soap_put___##MSGTYPE(psoap, &soap_tmp___##MSGTYPE, NULL, NULL)	\
		 || soap_body_end_out(psoap)			\
		 || soap_envelope_end_out(psoap))		\
			 return psoap->error;				\
	}											\
	if (soap_end_count(psoap))					\
		return psoap->error;					\
	if (soap_connect(psoap, _endpoint.c_str(), #MSGTYPE)	\
		|| soap_envelope_begin_out(psoap)		\
		|| soap_putheader(psoap)				\
		|| soap_body_begin_out(psoap)			\
		|| soap_put___##MSGTYPE(psoap, &soap_tmp___##MSGTYPE, NULL, NULL)	\
		|| soap_body_end_out(psoap)				\
		|| soap_envelope_end_out(psoap)			\
		|| soap_end_send(psoap))				\
		return soap_closesock(psoap);			\
	_pending_response_ptr = response_ptr;		\
	return SOAP_OK;								\
}

#define IMPL_SOAP_PRERESPONSE(CLSTYPE, MSGTYPE)	\
void CLSTYPE::pre_response_##MSGTYPE(const usys_smartptr<soap_response_context>& response_ptr)	\
{	\
	soap* psoap = response_ptr->get_soap();	\
	_##MSGTYPE* pmsg = new _##MSGTYPE;		\
	pmsg->soap_default(psoap);				\
	if (SOAP_OK != psoap->error)			\
	{										\
		soap_closesock(psoap);				\
		proc_response(response_ptr, pmsg, psoap->error);		\
		return;								\
	}										\
	if (soap_begin_recv(psoap)				\
		|| soap_envelope_begin_in(psoap)	\
		|| soap_recv_header(psoap)			\
		|| soap_body_begin_in(psoap))		\
	{										\
		soap_closesock(psoap);				\
		proc_response(response_ptr, pmsg, psoap->error);		\
		return;								\
	}										\
	std::string restag = soap_msgtotag_response(response_ptr->resmsg());	\
	pmsg->soap_get(psoap, restag.c_str(), restag.c_str());		\
	if (psoap->error)						\
	{										\
		soap_recv_fault(psoap, 0);			\
		proc_response(response_ptr, pmsg, psoap->error);	\
		return;								\
	}										\
	if (soap_body_end_in(psoap)				\
		|| soap_envelope_end_in(psoap)		\
		|| soap_end_recv(psoap))			\
	{										\
		soap_closesock(psoap);				\
		proc_response(response_ptr, pmsg, psoap->error);	\
	}										\
	soap_closesock(psoap);					\
	proc_response(response_ptr, pmsg, psoap->error);		\
}

#define IMPL_SOAP_PREREQUEST(CLSTYPE, MSGTYPE)	\
void CLSTYPE::pre_request_##MSGTYPE(const usys_smartptr<soap_request_context>& request_ptr)	\
{	\
	request_ptr->reqtype(SOAP_TYPE__##MSGTYPE, SOAP_TYPE__##MSGTYPE##Response);	\
	request_ptr->reqmsg(#MSGTYPE, #MSGTYPE "Response");		\
	soap* psoap = request_ptr->get_soap();		\
	if (SOAP_OK != psoap->error)					\
		return;									\
	struct __##MSGTYPE soap_tmp___##MSGTYPE;	\
	soap_default___##MSGTYPE(psoap, &soap_tmp___##MSGTYPE);	\
	psoap->encodingStyle = NULL;				\
	if (!soap_get___##MSGTYPE(psoap, &soap_tmp___##MSGTYPE, soap_msgtotag_request(request_ptr->reqmsg()), NULL))	\
		return;									\
	if (soap_body_end_in(psoap)					\
		|| soap_envelope_end_in(psoap)			\
		|| soap_end_recv(psoap))				\
		return;									\
	psoap->error = proc_request(request_ptr, soap_tmp___##MSGTYPE.MSGTYPE);	\
}

#define IMPL_SOAP_RESPONSE(CLSTYPE, MSGTYPE)	\
int CLSTYPE::response_msg(_##MSGTYPE* msg, const usys_smartptr<soap_request_context>& request_ptr)	\
{												\
	soap* psoap = request_ptr->get_soap();		\
	if (SOAP_OK != psoap->error)				\
		return psoap->error;					\
	if (psoap->header)							\
	{											\
		psoap->header->wsse__username = NULL;	\
		psoap->header->wsse__password = NULL;	\
		psoap->header->wsse__nonce = NULL;		\
		psoap->header->wsse__created = NULL;	\
	}											\
	soap_serializeheader(psoap);				\
	msg->soap_serialize(psoap);					\
	if (soap_begin_count(psoap))				\
		return psoap->error;					\
	if (psoap->mode & SOAP_IO_LENGTH)			\
	{											\
		if (soap_envelope_begin_out(psoap)		\
			|| soap_putheader(psoap)			\
			|| soap_body_begin_out(psoap)		\
			|| msg->soap_put(psoap, NULL, "")	\
			|| soap_body_end_out(psoap)			\
			|| soap_envelope_end_out(psoap))	\
			return psoap->error;				\
	};											\
	if (soap_end_count(psoap)					\
		|| soap_response(psoap, SOAP_OK)		\
		|| soap_envelope_begin_out(psoap)		\
		|| soap_putheader(psoap)				\
		|| soap_body_begin_out(psoap)			\
		|| msg->soap_put(psoap, NULL, "")		\
		|| soap_body_end_out(psoap)				\
		|| soap_envelope_end_out(psoap)			\
		|| soap_end_send(psoap))				\
		return psoap->error;					\
	return SOAP_OK;								\
}

class soap_session_context : public http_session_context
{
protected:
	const std::string	_endpoint;
	usys_smartptr<soap_response_context>	_pending_response_ptr;
public:
	soap_session_context(const std::string& endpoint);

	const std::string& endpoint()const { return _endpoint; }

	virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& req_ptr, const usys_smartptr_mtbase_ptr& header_ptr, const usys_smartptr<usys_transceiver>& transceiver_ptr);
	virtual int proc_packet(const usys_smartptr<http_packet_context>& packetptr, const usys_smartptr<usys_data_block>& dataptr);

	virtual void proc_response(const usys_smartptr<soap_response_context>& response_ptr);
	virtual void proc_request(const usys_smartptr<soap_request_context>& request_ptr);
    virtual int handle_connect(const usys_smartptr<usys_transceiver>& transceiver_ptr);
    virtual void handle_close(const usys_smartptr<usys_transceiver>& transceiver_ptr);

	virtual int check_soap_header(const usys_smartptr<soap_request_context>& request_ptr);
protected:
	int send_datablock(const usys_smartptr<soap_response_context>& response_ptr, int timeout, bool oneway);
	void response_fault(const usys_smartptr<soap_request_context>& rptr);
};
typedef usys_smartptr<soap_session_context>	soap_session_context_ptr;

template<typename TBASE, typename TSESSION>
class soap_session_context_container
{
public:
	template<typename T> int request(T* msg, const usys_smartptr<soap_response_context>& response_ptr)
	{
		usys_smartptr<TSESSION> ctxptr = usys_smartptr<TSESSION>::__dynamic_cast(response_ptr->transceiver_ptr()->session_ptr());
		if (ctxptr == 0)
			return -1;

		int ret = ctxptr->request_msg(msg, response_ptr);
		if (ret < 0)
			return ret;

		usys_smartptr<soap_transceiver> sptr = usys_smartptr<soap_transceiver>::__dynamic_cast(response_ptr->transceiver_ptr());
		if (sptr)
			sptr->send_request();
		return -1;
	}
};

template<typename TMSG>
class soap_response_wrapper : public usys_smartptr_mtbase
{
	const usys_smartptr<soap_response_context>	resptr_;
	const TMSG*	msg_;
	const int	soaperror_;
public:
	soap_response_wrapper(const usys_smartptr<soap_response_context>& resptr, const TMSG* msg, int soaperror)
	: resptr_(resptr), msg_(msg), soaperror_(soaperror)
	{
	}
	virtual ~soap_response_wrapper()
	{
		delete msg_;
	}
public:
	const usys_smartptr<soap_response_context> resptr()const { return resptr_; }
	const TMSG* msg()const { return msg_; }
	int soaperror()const { return soaperror_; }
};

template<typename TMSG>
class soap_response_autodelete
{
	const TMSG*	msg_;
public:
	soap_response_autodelete(const TMSG* msg)
	: msg_(msg)
	{
	}
	~soap_response_autodelete()
	{
		delete msg_;
	}
};

#endif
