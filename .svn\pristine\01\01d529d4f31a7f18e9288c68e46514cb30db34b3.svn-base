<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Apr 10 13:26:06 CST 2025 -->
<title>com.android.rockchip.camera2 类分层结构</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-10">
<meta name="description" content="tree: package: com.android.rockchip.camera2">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">程序包com.android.rockchip.camera2的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">android.content.Context
<ul>
<li class="circle">android.content.ContextWrapper
<ul>
<li class="circle">android.view.ContextThemeWrapper
<ul>
<li class="circle">android.app.Activity (implements android.content.ComponentCallbacks2, android.view.KeyEvent.Callback, android.view.LayoutInflater.Factory2, android.view.View.OnCreateContextMenuListener, android.view.Window.Callback)
<ul>
<li class="circle">androidx.core.app.ComponentActivity (implements androidx.core.view.KeyEventDispatcher.Component, androidx.lifecycle.LifecycleOwner)
<ul>
<li class="circle">androidx.activity.ComponentActivity (implements androidx.activity.result.ActivityResultCaller, androidx.activity.result.ActivityResultRegistryOwner, androidx.activity.contextaware.ContextAware, androidx.activity.FullyDrawnReporterOwner, androidx.lifecycle.HasDefaultViewModelProviderFactory, androidx.lifecycle.LifecycleOwner, androidx.core.view.MenuHost, androidx.activity.OnBackPressedDispatcherOwner, androidx.core.content.OnConfigurationChangedProvider, androidx.core.app.OnMultiWindowModeChangedProvider, androidx.core.app.OnNewIntentProvider, androidx.core.app.OnPictureInPictureModeChangedProvider, androidx.core.content.OnTrimMemoryProvider, androidx.core.app.OnUserLeaveHintProvider, androidx.savedstate.SavedStateRegistryOwner, androidx.lifecycle.ViewModelStoreOwner)
<ul>
<li class="circle">androidx.fragment.app.FragmentActivity (implements androidx.core.app.ActivityCompat.OnRequestPermissionsResultCallback, androidx.core.app.ActivityCompat.RequestPermissionsRequestCodeValidator)
<ul>
<li class="circle">androidx.appcompat.app.AppCompatActivity (implements androidx.appcompat.app.ActionBarDrawerToggle.DelegateProvider, androidx.appcompat.app.AppCompatCallback, androidx.core.app.TaskStackBuilder.SupportParentable)
<ul>
<li class="circle">com.android.rockchip.camera2.<a href="ImageViewerActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">ImageViewerActivity</a></li>
<li class="circle">com.android.rockchip.camera2.<a href="MediaBrowserActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">MediaBrowserActivity</a></li>
<li class="circle">com.android.rockchip.camera2.<a href="VideoDecoderActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">VideoDecoderActivity</a></li>
<li class="circle">com.android.rockchip.camera2.<a href="VideoEncoderActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">VideoEncoderActivity</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">androidx.recyclerview.widget.RecyclerView.Adapter&lt;VH&gt;
<ul>
<li class="circle">com.android.rockchip.camera2.<a href="MediaAdapter.html" class="type-name-link" title="com.android.rockchip.camera2中的类">MediaAdapter</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">com.android.rockchip.camera2.<a href="MediaAdapter.OnMediaClickListener.html" class="type-name-link" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
