{"buildFiles": ["C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\.cxx\\Debug\\4p3v3w17\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\.cxx\\Debug\\4p3v3w17\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"SerialPort::@6890427a1f51a3e7e1df": {"artifactName": "SerialPort", "abi": "x86", "output": "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\build\\intermediates\\cxx\\Debug\\4p3v3w17\\obj\\x86\\libSerialPort.so", "runtimeFiles": []}}}