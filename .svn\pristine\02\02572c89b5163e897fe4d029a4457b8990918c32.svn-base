<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 09 08:53:38 CST 2025 -->
<title>API 帮助</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-09">
<meta name="description" content="help">
<meta name="generator" content="javadoc/HelpWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="help-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li class="nav-bar-cell1-rev">帮助</li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Help:&nbsp;</li>
<li><a href="#help-navigation">Navigation</a>&nbsp;|&nbsp;</li>
<li><a href="#help-pages">Pages</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<h1 class="title">此 API 文档的组织方式</h1>
<ul class="help-toc">
<li><a href="#help-navigation">Navigation</a>: 
<ul class="help-subtoc">
<li><a href="#help-search">搜索</a></li>
</ul>
</li>
<li><a href="#help-pages">Kinds of Pages</a>: 
<ul class="help-subtoc">
<li><a href="#overview">概览</a></li>
<li><a href="#package">程序包</a></li>
<li><a href="#class">类或接口</a></li>
<li><a href="#doc-file">Other Files</a></li>
<li><a href="#tree">树 (类分层结构)</a></li>
<li><a href="#deprecated">已过时的 API</a></li>
<li><a href="#all-packages">All Packages</a></li>
<li><a href="#all-classes">All Classes and Interfaces</a></li>
<li><a href="#index">索引</a></li>
</ul>
</li>
</ul>
<hr>
<div class="sub-title">
<h2 id="help-navigation">Navigation</h2>
Starting from the <a href="index.html">概览</a> page, you can browse the documentation using the links in each page, and in the navigation bar at the top of each page. The <a href="index-files/index-1.html">索引</a> and Search box allow you to navigate to specific declarations and summary pages, including: <a href="allpackages-index.html">所有程序包</a>, <a href="allclasses-index.html">All Classes and Interfaces</a>
<section class="help-section" id="help-search">
<h3>搜索</h3>
<p>可以使用部分或完整名称搜索模块、程序包、类型、字段、方法、系统属性以及 API 中定义的其他术语的定义，（可选）也可以使用“驼峰大小写式”缩写进行搜索。例如：</p>
<ul class="help-section-list">
<li><code>j.l.obj</code> 将与 "java.lang.Object" 相匹配</li>
<li><code>InpStr</code> 将与 "java.io.InputStream" 相匹配</li>
<li><code>HM.cK</code> 将与 "java.util.HashMap.containsKey(Object)" 相匹配</li>
</ul>
<p>有关搜索功能的完整说明，请参阅 <a href="https://docs.oracle.com/en/java/javase/17/docs/specs/javadoc/javadoc-search-spec.html">Javadoc 搜索规范</a>。</p>
</section>
</div>
<hr>
<div class="sub-title">
<h2 id="help-pages">Kinds of Pages</h2>
The following sections describe the different kinds of pages in this collection.
<section class="help-section" id="overview">
<h3>概览</h3>
<p><a href="index.html">概览</a> 页面是此 API 文档的首页, 提供了所有程序包的列表及其概要。此页面也可能包含这些程序包的总体说明。</p>
</section>
<section class="help-section" id="package">
<h3>程序包</h3>
<p>每个程序包都有一页，其中包含它的类和接口的列表及其概要。这些页可以包含六个类别：</p>
<ul class="help-section-list">
<li>接口</li>
<li>类</li>
<li>Enum Classes</li>
<li>异常错误</li>
<li>错误</li>
<li>Annotation Interfaces</li>
</ul>
</section>
<section class="help-section" id="class">
<h3>类或接口</h3>
<p>每个类, 接口, 嵌套类和嵌套接口都有各自的页面。其中每个页面都由三部分 (类/接口说明, 概要表, 以及详细的成员说明) 组成:</p>
<ul class="help-section-list">
<li>类继承图</li>
<li>直接子类</li>
<li>所有已知子接口</li>
<li>所有已知实现类</li>
<li>类或接口声明</li>
<li>类或接口说明</li>
</ul>
<br>
<ul class="help-section-list">
<li>嵌套类概要</li>
<li>枚举常量概要</li>
<li>字段概要</li>
<li>属性概要</li>
<li>构造器概要</li>
<li>方法概要</li>
<li>必需元素概要</li>
<li>可选元素概要</li>
</ul>
<br>
<ul class="help-section-list">
<li>枚举常量详细资料</li>
<li>字段详细资料</li>
<li>属性详细资料</li>
<li>构造器详细资料</li>
<li>方法详细资料</li>
<li>元素详细资料</li>
</ul>
<p><span class="help-note">Note:</span> Annotation interfaces have required and optional elements, but not methods. Only enum classes have enum constants. The components of a record class are displayed as part of the declaration of the record class. Properties are a feature of JavaFX.</p>
<p>The summary entries are alphabetical, while the detailed descriptions are in the order they appear in the source code. This preserves the logical groupings established by the programmer.</p>
</section>
<section class="help-section" id="doc-file">
<h3>Other Files</h3>
<p>Packages and modules may contain pages with additional information related to the declarations nearby.</p>
</section>
<section class="help-section" id="tree">
<h3>树 (类分层结构)</h3>
<p>对于所有程序包，都有一个 <a href="overview-tree.html">类分层结构</a> 页，以及每个程序包的分层结构。每个分层结构页都包含类的列表和接口的列表。从 <code>java.lang.Object</code> 开始，按继承结构对类进行排列。接口不从 <code>java.lang.Object</code> 继承。</p>
<ul class="help-section-list">
<li>查看“概览”页面时, 单击 "树" 将显示所有程序包的分层结构。</li>
<li>查看特定程序包、类或接口页时，单击“树”将仅显示该程序包的分层结构。</li>
</ul>
</section>
<section class="help-section" id="deprecated">
<h3>已过时的 API</h3>
<p><a href="deprecated-list.html">已过时的 API</a> 页面列出了所有已过时的 API。通常，由于已过时的 API 存在缺点并且已提供替代 API，不建议使用已过时的 API。在将来的实施中，可能会删除已过时的 API。</p>
</section>
<section class="help-section" id="all-packages">
<h3>All Packages</h3>
<p>The <a href="allpackages-index.html">所有程序包</a> page contains an alphabetic index of all packages contained in the documentation.</p>
</section>
<section class="help-section" id="all-classes">
<h3>All Classes and Interfaces</h3>
<p>The <a href="allclasses-index.html">All Classes and Interfaces</a> page contains an alphabetic index of all classes and interfaces contained in the documentation, including annotation interfaces, enum classes, and record classes.</p>
</section>
<section class="help-section" id="index">
<h3>索引</h3>
<p><a href="index-files/index-1.html">索引</a> 包含所有类、接口、构造器、方法和字段的按字母顺序排列的索引，以及所有程序包和所有类的列表。</p>
</section>
</div>
<hr>
<span class="help-footnote">此帮助文件适用于由标准 doclet 生成的 API 文档。</span></main>
</div>
</div>
</body>
</html>
