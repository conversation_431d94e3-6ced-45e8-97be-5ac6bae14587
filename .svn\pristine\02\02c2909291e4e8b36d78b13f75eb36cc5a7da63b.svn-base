<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <plurals name="mtrl_badge_content_description">
    <item quantity="one">%d new notification</item>
    <item quantity="other">%d new notifications</item>
  </plurals>
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"Navigate home"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"Navigate up"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"More options"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"Done"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"See all"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"Choose an app"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"OFF"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"ON"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt+"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Ctrl+"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"delete"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"enter"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Function+"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta+"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Shift+"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"space"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym+"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menu+"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"Search…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"Clear query"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"Search query"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"Search"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"Submit query"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"Voice search"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"Share with"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"Collapse"</string>
    <string name="bottomsheet_action_collapse">Collapse the bottom sheet</string>
    <string name="bottomsheet_action_expand">Expand the bottom sheet</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="bottomsheet_drag_handle_clicked">Drag handle double-tapped</string>
    <string name="bottomsheet_drag_handle_content_description">Drag handle</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show drop-down menu</string>
    <string name="icon_content_description">Dialogue Icon</string>
    <string name="item_view_role_description">Tab</string>
    <string name="material_clock_toggle_content_description">Select a.m. or p.m.</string>
    <string name="material_hour_24h_suffix">%1$s hours</string>
    <string name="material_hour_selection">Select hour</string>
    <string name="material_hour_suffix">%1$s o\'clock</string>
    <string name="material_minute_selection">Select minutes</string>
    <string name="material_minute_suffix">%1$s minutes</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string name="material_timepicker_hour">Hour</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description" ns2:ignore="PluralsCandidate">More than %1$d new notifications</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_range_selection">Start date selection: %1$s – end date selection: %2$s</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_announce_current_selection_none">none</string>
    <string name="mtrl_picker_cancel">Cancel</string>
    <string name="mtrl_picker_confirm">OK</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_end_date_description">End date %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_current_year_description">Navigate to current year %1$d</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$d</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_start_date_description">Start date %1$s</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_today_description">Today %1$s</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to calendar view</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to year view</string>
    <string name="mtrl_timepicker_cancel">Cancel</string>
    <string name="mtrl_timepicker_confirm">OK</string>
    <string name="password_toggle_content_description">Show password</string>
    <string msgid="6264217191555673260" name="search_menu_title">"Search"</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
</resources>