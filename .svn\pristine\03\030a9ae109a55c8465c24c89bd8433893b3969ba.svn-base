
# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-v4-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-compat-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/extras/app-toolkit/android-arch-lifecycle-runtime-nodeps/android_common/aar/proguard.txt
-keepattributes *Annotation*

-keepclassmembers enum android.arch.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements android.arch.lifecycle.LifecycleObserver {
}

-keep class * implements android.arch.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @android.arch.lifecycle.OnLifecycleEvent *;
}

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-versionedparcelable-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-media-compat-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-core-utils-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-documentfile-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-loader-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/extras/app-toolkit/android-arch-lifecycle-livedata-core-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/extras/app-toolkit/android-arch-core-runtime-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/extras/app-toolkit/android-arch-lifecycle-viewmodel-nodeps/android_common/aar/proguard.txt
-keep class * extends android.arch.lifecycle.ViewModel {
    <init>();
}
# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-localbroadcastmanager-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-print-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-core-ui-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-customview-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-viewpager-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-coordinatorlayout-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends android.support.design.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-drawerlayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-slidingpanelayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-interpolator-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-swiperefreshlayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-asynclayoutinflater-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-cursoradapter-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/support/android-support-fragment-nodeps/android_common/aar/proguard.txt
