<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 12:51:44 CST 2025 -->
<title>I - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: I">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#init()" class="member-name-link">init()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">初始化 HDMI 状态检测。</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#init(android.content.Context)" class="member-name-link">init(Context)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">初始化 TpIspParam 和串口通信。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#initialize(android.view.Surface)" class="member-name-link">initialize(Surface)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">初始化视频系统</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isCameraStarted()" class="member-name-link">isCameraStarted()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取相机启动状态</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html#isConnected()" class="member-name-link">isConnected()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a></dt>
<dd>
<div class="block">获取WiFi连接状态</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#isControlsVisible()" class="member-name-link">isControlsVisible()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">检查控制界面是否可见</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isCurrentVideoPlaybackCompleted()" class="member-name-link">isCurrentVideoPlaybackCompleted()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">检查当前视频播放是否已完成（简化版）</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.ParamData.html#isDisabled" class="member-name-link">isDisabled</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.ParamData.html" title="com.touptek.video中的类">TpIspParam.ParamData</a></dt>
<dd>
<div class="block">参数是否被禁用，true表示禁用，false表示启用</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#isDoubleTapEnabled()" class="member-name-link">isDoubleTapEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取是否启用双击缩放功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#isDoubleTapEnabled()" class="member-name-link">isDoubleTapEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取是否启用双击缩放功能</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.HotspotInfo.html#isEnabled()" class="member-name-link">isEnabled()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.HotspotInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a></dt>
<dd>
<div class="block">获取热点启用状态</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#isEnabled()" class="member-name-link">isEnabled()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">检查是否启用了Samba上传</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#isEnabled()" class="member-name-link">isEnabled()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#isEthernetConnected()" class="member-name-link">isEthernetConnected()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">获取当前以太网连接状态</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isInitialized()" class="member-name-link">isInitialized()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取初始化状态</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#isPanEnabled()" class="member-name-link">isPanEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取是否启用平移功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#isPanEnabled()" class="member-name-link">isPanEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取是否启用平移功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#isPlaybackCompleted()" class="member-name-link">isPlaybackCompleted()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">检查播放是否已完成</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#isPlaying()" class="member-name-link">isPlaying()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取当前播放状态</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#isProtected" class="member-name-link">isProtected</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#isRangeReceived()" class="member-name-link">isRangeReceived()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">检查所有参数范围是否已接收</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isRecording()" class="member-name-link">isRecording()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取录制状态</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#isROIEnabled()" class="member-name-link">isROIEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">判断ROI模式是否启用</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#isSerialConnected()" class="member-name-link">isSerialConnected()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">检查串口是否已连接</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isStreaming()" class="member-name-link">isStreaming()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取推流状态</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isTvMode()" class="member-name-link">isTvMode()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前是否为TV模式</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#isVideoPlaying()" class="member-name-link">isVideoPlaying()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">检查是否正在播放视频（简化版）</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#isZoomEnabled()" class="member-name-link">isZoomEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取是否启用缩放功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#isZoomEnabled()" class="member-name-link">isZoomEnabled()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取是否启用缩放功能</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
