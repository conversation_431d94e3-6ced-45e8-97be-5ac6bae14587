package com.android.rockchip.camera2.video;

import android.content.Context;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.util.Size;
import android.view.Surface;

import com.android.rockchip.camera2.util.FileStorageUtils;

import java.io.IOException;
import java.nio.ByteBuffer;

// 定义配置参数的接口
interface MediaRecorderConfig {
    void setVideoSize(Size videoSize);
    void setBitRate(int bitRate);
    void setFrameRate(int frameRate);
    void setIFrameInterval(int iFrameInterval);
    void setBitrateMode(int bitrateMode);
    void setColorRange(int colorRange);
    void setColorStandard(int colorStandard);
    void setOutputPath(String outputPath);
}

public class MediaRecorderHelper implements MediaRecorderConfig {

    private static final String TAG = "MediaRecorderHelper";
    private final Context context;
    private final CameraManagerHelper cameraManagerHelper;

    private MediaCodec videoEncoder;
    private Surface encoderInputSurface;
    private MediaMuxer mediaMuxer;
    private int videoTrackIndex = -1;
    private boolean muxerStarted = false;
    private Thread encoderThread;

    private Size videoSize = new Size(1920, 1080);
    private int bitRate = 50000000;
    private int frameRate = 60;
    private int iFrameInterval = 1;
    private int bitrateMode = 2;
    private int colorRange = 1;
    private int colorStandard = 1;
    private String outputPath;

    public MediaRecorderHelper(Context context, CameraManagerHelper cameraManagerHelper) {
        this.context = context;
        this.cameraManagerHelper = cameraManagerHelper;
    }

    @Override
    public void setVideoSize(Size videoSize) {
        this.videoSize = videoSize;
    }

    @Override
    public void setBitRate(int bitRate) {
        this.bitRate = bitRate;
    }

    @Override
    public void setFrameRate(int frameRate) {
        this.frameRate = frameRate;
    }

    @Override
    public void setIFrameInterval(int iFrameInterval) {
        this.iFrameInterval = iFrameInterval;
    }

    @Override
    public void setBitrateMode(int bitrateMode) {
        this.bitrateMode = bitrateMode;
    }

    @Override
    public void setColorRange(int colorRange) {
        this.colorRange = colorRange;
    }

    @Override
    public void setColorStandard(int colorStandard) {
        this.colorStandard = colorStandard;
    }

    @Override
    public void setOutputPath(String outputPath) {
        this.outputPath = outputPath;
    }

    public void startRecording() {
        try {
            MediaFormat format = MediaFormat.createVideoFormat("video/avc", videoSize.getWidth(), videoSize.getHeight());
            format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible);
            format.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
            format.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate);
            format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, iFrameInterval);
            format.setInteger(MediaFormat.KEY_BITRATE_MODE, bitrateMode);
            format.setInteger(MediaFormat.KEY_COLOR_RANGE, colorRange);
            format.setInteger(MediaFormat.KEY_COLOR_STANDARD, colorStandard);

            videoEncoder = MediaCodec.createEncoderByType("video/avc");
            videoEncoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            encoderInputSurface = videoEncoder.createInputSurface();
            videoEncoder.start();

            if (outputPath == null) {
                outputPath = FileStorageUtils.createOutputPath(context);
            }
            mediaMuxer = new MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            videoTrackIndex = -1;
            muxerStarted = false;

            cameraManagerHelper.startRecordingPreview(encoderInputSurface);

            encoderThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
                    drainEncoder();
                }
            });
            encoderThread.start();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void stopRecording() {
        if (cameraManagerHelper.getCaptureSession() != null) {
            try {
                cameraManagerHelper.getCaptureSession().stopRepeating();
                cameraManagerHelper.getCaptureSession().abortCaptures();
            } catch (android.hardware.camera2.CameraAccessException e) {
                e.printStackTrace();
            }
        }
        if (videoEncoder != null) {
            videoEncoder.signalEndOfInputStream();
        }
        try {
            if (encoderThread != null) {
                encoderThread.join();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (videoEncoder != null) {
            videoEncoder.stop();
            videoEncoder.release();
            videoEncoder = null;
        }
        if (mediaMuxer != null) {
            try {
                mediaMuxer.stop();
                mediaMuxer.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mediaMuxer = null;
        }
        cameraManagerHelper.startPreview();
    }

    private void drainEncoder() {
        final long timeoutUs = 10000;
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
        while (true) {
            int outputBufferId = videoEncoder.dequeueOutputBuffer(bufferInfo, timeoutUs);
            if (outputBufferId >= 0) {
                ByteBuffer encodedData = videoEncoder.getOutputBuffer(outputBufferId);
                if (encodedData == null) continue;

                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    bufferInfo.size = 0;
                }

                if (bufferInfo.size != 0) {
                    encodedData.position(bufferInfo.offset);
                    encodedData.limit(bufferInfo.offset + bufferInfo.size);
                    if (!muxerStarted) {
                        MediaFormat newFormat = videoEncoder.getOutputFormat();
                        videoTrackIndex = mediaMuxer.addTrack(newFormat);
                        mediaMuxer.start();
                        muxerStarted = true;
                    }
                    mediaMuxer.writeSampleData(videoTrackIndex, encodedData, bufferInfo);
                }
                videoEncoder.releaseOutputBuffer(outputBufferId, false);
                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) break;
            } else if (outputBufferId == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                if (muxerStarted) throw new RuntimeException("输出格式已经改变，重复设置");
                MediaFormat newFormat = videoEncoder.getOutputFormat();
                videoTrackIndex = mediaMuxer.addTrack(newFormat);
                mediaMuxer.start();
                muxerStarted = true;
            }
        }
    }

    public Size getVideoSize() {
        return videoSize;
    }
}