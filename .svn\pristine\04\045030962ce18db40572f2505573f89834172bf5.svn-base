package com.android.rockchip.camera2.util;

import android.content.Context;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/* 
 * FileStorageUtils 类提供文件存储相关的工具方法，包括创建输出路径、获取存储空间等。
 */
public class FileStorageUtils {

    /* 日志标签 */
    private static final String TAG = "FileUtils";

    /* 
     * 创建输出文件路径。
     * @param context 应用上下文
     * @return 输出文件的绝对路径
     */
    public static String createOutputPath(Context context) {
        /* 格式化当前日期和时间，生成文件名后缀 */
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String dateTimeSuffix = dateFormat.format(new Date());
        String fileName = "4k_video_" + dateTimeSuffix + ".mp4";

        /* 获取所有外部存储目录 */
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs) {
            /* 判断目录是否为可拆卸存储（如 U 盘）且已挂载 */
            if (file != null
                    && Environment.isExternalStorageRemovable(file)
                    && Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(file))) {
                /* 获取存储根目录 */
                String fullPath = file.getAbsolutePath();
                String basePath = fullPath.substring(0, fullPath.indexOf("/Android"));

                /* 创建 DCIM/Videos 目录 */
                File dir = new File(basePath, "DCIM/Videos");
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                Log.d(TAG, "检测到U盘: " + dir.getAbsolutePath());
                return new File(dir, fileName).getAbsolutePath();
            }
        }

        /* 如果没有检测到 U 盘，则使用默认主外部存储作为后备 */
        File primaryDir = context.getExternalFilesDir(null);
        File fallbackDir = new File(primaryDir, "Movies");
        if (!fallbackDir.exists()) {
            fallbackDir.mkdirs();
        }
        Log.d(TAG, "未检测到 U盘，使用主外部存储: " + primaryDir.getAbsolutePath());
        return new File(fallbackDir, fileName).getAbsolutePath();
    }

    /* 
     * 获取指定路径的可用存储空间。
     * @param path 文件路径
     * @return 可用存储空间（字节）
     */
    public static long getAvailableStorageSpace(String path) {
        try {
            StatFs statFs = new StatFs(path);
            long availableBlocks = statFs.getAvailableBlocksLong();
            long blockSize = statFs.getBlockSizeLong();
            return availableBlocks * blockSize;
        } catch (Exception e) {
            Log.e(TAG, "获取可用存储空间失败: " + e.getMessage());
            return -1;
        }
    }

    /* 
     * 判断文件系统的格式。
     * @param path 文件路径
     * @return 文件系统格式（如 FAT32、exFAT、NTFS 等）
     */
    public static String getFileSystemType(String path) {
        /* 将虚拟路径转换为实际路径 */
        String actualPath = convertToActualPath(path);
        try {
            String command = "mount";
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(actualPath)) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 5) {
                        return parts[4]; /* 返回文件系统类型 */
                    }
                }
            }
            reader.close();
        } catch (IOException e) {
            Log.e(TAG, "获取文件系统类型失败: " + e.getMessage());
        }
        return "Unknown";
    }

    /* 
     * 将虚拟路径转换为实际路径。
     * @param virtualPath 虚拟路径
     * @return 实际路径
     */
    private static String convertToActualPath(String virtualPath) {
        if (virtualPath.startsWith("/storage/")) {
            String partAfterStorage = virtualPath.substring("/storage/".length());
            String[] segments = partAfterStorage.split("/", 2);
            String targetPart = segments[0];
            return "/mnt/pass_through/0/" + targetPart;
        }
        return virtualPath;
    }
}
