package com.android.rockchip.camera2.integrated.settings.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.touptek.video.TpVideoSystem;
import com.android.rockchip.mediacodecnew.R;

/**
 * TV设置Fragment
 * 提供TV模式和Camera模式的切换功能
 */
public class TpTvSettingsFragment extends Fragment {
    private static final String TAG = "TpTvSettingsFragment";

    private TpVideoSystem videoSystem;

    // UI组件
    private Switch tvModeSwitch;
    private TextView tvModeStatusText;
    private Button switchToCameraButton;
    private Button switchToTvButton;
    private TextView tvModeDescriptionText;

    /**
     * 创建Fragment实例
     */
    public static TpTvSettingsFragment newInstance(TpVideoSystem videoSystem) {
        TpTvSettingsFragment fragment = new TpTvSettingsFragment();
        fragment.videoSystem = videoSystem;
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "TpTvSettingsFragment onCreate");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "TpTvSettingsFragment onCreateView");
        View view = inflater.inflate(R.layout.fragment_tp_tv_mode_settings, container, false);
        
        initViews(view);
        setupListeners();
        updateUI();
        
        return view;
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        tvModeSwitch = view.findViewById(R.id.tv_mode_switch);
        tvModeStatusText = view.findViewById(R.id.tv_mode_status_text);
        switchToCameraButton = view.findViewById(R.id.switch_to_camera_button);
        switchToTvButton = view.findViewById(R.id.switch_to_tv_button);
        tvModeDescriptionText = view.findViewById(R.id.tv_mode_description_text);
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        // TV模式开关监听
        tvModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) { // 只响应用户操作
                toggleTvMode(isChecked);
            }
        });

        // 切换到Camera模式按钮
        switchToCameraButton.setOnClickListener(v -> {
            switchToCameraMode();
        });

        // 切换到TV模式按钮
        switchToTvButton.setOnClickListener(v -> {
            switchToTvMode();
        });
    }

    /**
     * 更新UI状态
     */
    private void updateUI() {
        if (videoSystem != null) {
            boolean isTvMode = videoSystem.isTvMode();
            
            // 更新开关状态（不触发监听器）
            tvModeSwitch.setOnCheckedChangeListener(null);
            tvModeSwitch.setChecked(isTvMode);
            tvModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (buttonView.isPressed()) {
                    toggleTvMode(isChecked);
                }
            });
            
            // 更新状态文本
            tvModeStatusText.setText(isTvMode ? "当前模式：TV模式" : "当前模式：Camera模式");
            
            // 更新按钮状态
            switchToCameraButton.setEnabled(isTvMode);
            switchToTvButton.setEnabled(!isTvMode);
            
            // 更新描述文本
            updateDescriptionText(isTvMode);
        }
    }

    /**
     * 更新描述文本
     */
    private void updateDescriptionText(boolean isTvMode) {
        if (isTvMode) {
            tvModeDescriptionText.setText(
                "TV模式：显示TV预览画面\n" +
                "• 录制和拍照功能已禁用\n" +
                "• 适用于TV显示场景\n" +
                "• 支持HDMI输出"
            );
        } else {
            tvModeDescriptionText.setText(
                "Camera模式：显示相机预览画面\n" +
                "• 支持录制和拍照功能\n" +
                "• 支持手势缩放控制\n" +
                "• 支持RTSP推流"
            );
        }
    }

    /**
     * 切换TV模式
     */
    private void toggleTvMode(boolean enableTvMode) {
        if (videoSystem != null) {
            if (enableTvMode) {
                switchToTvMode();
            } else {
                switchToCameraMode();
            }
        }
    }

    /**
     * 切换到TV模式
     */
    private void switchToTvMode() {
        Log.d(TAG, "Fragment请求切换到TV模式");
        if (videoSystem != null) {
            try {
                videoSystem.switchToTvMode();
                updateUI();
                showToast("已切换到TV模式");

            } catch (IllegalStateException e) {
                Log.e(TAG, "切换到TV模式失败", e);
                showToast("切换到TV模式失败: " + e.getMessage());
            }
        }
    }

    /**
     * 切换到Camera模式
     */
    private void switchToCameraMode() {
        Log.d(TAG, "Fragment请求切换到Camera模式");
        if (videoSystem != null) {
            try {
                videoSystem.switchToCameraMode();
                updateUI();
                showToast("已切换到Camera模式");
            } catch (IllegalStateException e) {
                Log.e(TAG, "切换到Camera模式失败", e);
                showToast("切换到Camera模式失败: " + e.getMessage());
            }
        }
    }

    /**
     * 显示Toast提示
     */
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "TpTvSettingsFragment onResume");
        updateUI(); // 恢复时更新UI状态
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TpTvSettingsFragment onDestroy");
    }
}
