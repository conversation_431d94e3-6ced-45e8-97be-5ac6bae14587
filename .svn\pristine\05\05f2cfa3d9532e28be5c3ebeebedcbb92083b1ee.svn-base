/* ***************************************************************
//  gep_session_context   version:  1.0  date: 23/23/2009
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __oep_session_context_h__
#define __oep_session_context_h__

#include <utiny/utiny_config.h>
#include <utiny/usys_smartptr.h>
#include <utiny/usys_transceiver_tcp.h>
#include <utiny/usys_safemap.h>
#include <utiny/usys_data_block.h>
#include <utiny/usys_reactor.h>
#include <utiny/usys_atomic.h>
#include <utiny/usys_mutex.h>
#include <utiny/gep_header.h>

namespace gep
{
    class gep_response_context;
    class gep_request_context;

    class UBEDA_API gep_session_context : virtual public usys_smartptr_mtbase
    {
        unsigned session_id_;
        usys_atomic_int sequence_id_;
        unsigned short default_compress_flag_;
        unsigned packet_len_;
        usys_safemap<int, usys_smartptr_mtbase_ptr> response_ctx_map_;

    public:
        gep_session_context();
        virtual ~gep_session_context();

		unsigned get_session_id() { return session_id_; };
		void set_session_id(unsigned session_id) { session_id_ = session_id; };
		
		long get_sequence_id() { return ++sequence_id_; }

        virtual usys_smartptr<usys_transceiver_tcp> handle_accept(SOCKET fd);
        virtual int handle_connect(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
        virtual int handle_write(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
        virtual void handle_close(const usys_smartptr<usys_transceiver>& transceiver_ptr) { }
		virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& req_ptr, const usys_smartptr<usys_transceiver>& transceiver_ptr);

        virtual bool is_response_msg(unsigned short msg_type);
        virtual int proc_packet(const usys_smartptr<gep_packet_context>& req_ptr, const usys_smartptr<usys_data_block>& data_ptr) = 0;

        void pack_header(const usys_smartptr<usys_data_block>& newmb, int size, unsigned short compress_flag,
					unsigned short msg_type, int seq_id, unsigned short ie_num, int body_len, const GEP_CONTEXT_HEADER& ctx_header);

        template<typename REQT>
        int request(const REQT& msg_req, const usys_smartptr<gep_response_context>& response_ptr, int timeout = 0, bool oneway = false, unsigned short compress_flag = 0xffff);

        template<typename REST>
        int response(const REST& msg_res, const usys_smartptr<gep_request_context>& reqest_ctx, unsigned short compress_flag = 0xffff);

        void default_compress_flag(unsigned short compress_flag) { default_compress_flag_ = compress_flag; }
        unsigned short default_compress_flag() { return default_compress_flag_; }

        int proc_request_exception(const usys_smartptr<gep_response_context>& res_ptr);

        template<typename T, typename PACKET_T, bool hasbody>
        int proc_packet_tpl(const gep_packet_context_ptr& ctx, const usys_smartptr<usys_data_block>& data_ptr);
		
        int proc_spec_request(unsigned short msg_type, unsigned short ie_num, const usys_smartptr<gep_response_context>& response_ptr, 
				const usys_smartptr<usys_data_block>& orgmb, int body_len, unsigned short compress_flag);

        void proc_spec_response(unsigned short msg_type, unsigned short ie_num, const usys_smartptr<gep_request_context>& request_ptr, 
				const usys_smartptr<usys_data_block>& orgmb, int body_len, unsigned short compress_flag);

#ifndef UCE_HASNOT_COMPRESS
        int compress_mblk(unsigned short& compress_flag, int& body_len,usys_smartptr<usys_data_block>& newmb, const usys_smartptr<usys_data_block>& orgmb, int header_len);
#endif
	private:
        int proc_data_block(const GEP_HEADER& header, const usys_smartptr<usys_data_block>& data_ptr, const usys_smartptr<usys_transceiver>& transceiver_ptr);

        template<typename RESPONSE_CTX_PTR>
		int fetch_response(int sequence_id, RESPONSE_CTX_PTR& res_ptr);
        int remove_response(int sequence_id);
    };

    typedef usys_smartptr<gep_session_context> gep_session_context_ptr;
}

#include <utiny/gep_request_context.h>
#include <utiny/gep_response_context.h>

template<typename REQT>
int gep::gep_session_context::request(const REQT& msg_req, const usys_smartptr<gep_response_context>& response_ptr, int timeout, bool oneway, unsigned short compress_flag)
{
    int buflen = gep_total_length(msg_req, response_ptr->version());
    usys_smartptr<usys_data_block> orgmb(new usys_data_block(buflen));
    if (orgmb == NULL)
        return uce_error_prot_lackmem;

    int body_len = gep_pack(msg_req, (unsigned char*)orgmb->wr_ptr() + GEP_HEADER_SIZE, buflen - GEP_HEADER_SIZE, response_ptr->version());
    if (body_len < 0) {
        return uce_error_protpack;
    }

#ifndef UCE_HASNOT_COMPRESS
    usys_smartptr<usys_data_block> newmb;
    int ret = compress_mblk(compress_flag, body_len, newmb, orgmb, GEP_HEADER_SIZE);
    if (ret < 0)
        return ret;
#else
	usys_smartptr<usys_data_block>& newmb = orgmb;
	compress_flag = 0;
#endif

    int seqid = proc_spec_request(REQT::MSG_TYPE, msg_req.ie_num(), response_ptr, newmb, body_len, compress_flag);
    response_ptr->sequence_id(seqid);
    if (!oneway)
        response_ctx_map_.insert(seqid, usys_smartptr_mtbase_ptr::__dynamic_cast(response_ptr));
    usys_smartptr<usys_transceiver> trans_ptr = response_ptr->transceiver_ptr();
    if (trans_ptr == 0)
        return uce_error_invalid_context;
    if (trans_ptr->send_datablock(newmb) >= 0)
    {
        response_ptr->start_timer(timeout <= 0 ? 30 : timeout);
        return 0;
    }
    if (!oneway)
        response_ctx_map_.erase(seqid);
    return uce_error;
};

template<typename REST>
int gep::gep_session_context::response(const REST& msg_res, const usys_smartptr<gep_request_context>& reqest_ctx, unsigned short compress_flag)
{
    int buflen = gep_total_length(msg_res, reqest_ctx->response_version());
    usys_smartptr<usys_data_block> orgmb(new usys_data_block(buflen));
    if (orgmb == NULL)
        return uce_error_prot_lackmem;
    int body_len = gep_pack(msg_res, (unsigned char*)orgmb->wr_ptr() + GEP_HEADER_SIZE, buflen - GEP_HEADER_SIZE, reqest_ctx->response_version());
    if (body_len < 0) {
        return uce_error_protpack;
    }
    
#ifndef UCE_HASNOT_COMPRESS
    usys_smartptr<usys_data_block> newmb;
    int ret = compress_mblk(compress_flag, body_len, newmb, orgmb, GEP_HEADER_SIZE);
    if (ret < 0)
        return ret;
#else
	usys_smartptr<usys_data_block>& newmb = orgmb;
	compress_flag = 0;
#endif

    proc_spec_response(REST::MSG_TYPE, msg_res.ie_num(), reqest_ctx, newmb, body_len, compress_flag);

    usys_smartptr<usys_transceiver> trans_ptr = reqest_ctx->transceiver_ptr();
    if (trans_ptr == 0)
        return -1;
    if (trans_ptr->send_datablock(newmb) < 0)
        return -1;
    return 0;
}

template<typename RESPONSE_CTX_PTR>
int gep::gep_session_context::fetch_response(int sequence_id, RESPONSE_CTX_PTR& res_ptr)
{
    usys_smartptr_mtbase_ptr response_ptr;
    int ret = response_ctx_map_.query(sequence_id, response_ptr, true);
    res_ptr = RESPONSE_CTX_PTR::__dynamic_cast(response_ptr);
    return ret;
};

template<typename T, typename PACKET_T, bool hasbody>
int gep::gep_session_context::proc_packet_tpl(const gep_packet_context_ptr& ctx, const usys_smartptr<usys_data_block>& data_ptr)
{
    int unpack_ret = 0;
    usys_smartptr<PACKET_T> packet_ptr(new PACKET_T());
    if (hasbody /*&& org_body_mblk!=0*/)
    {
        if (data_ptr == 0 || data_ptr->length() <= GEP_HEADER_SIZE)
            unpack_ret = gep_unpack(*packet_ptr, 0, 0, ctx->version());
        else
            unpack_ret = gep_unpack(*packet_ptr, (unsigned char*)data_ptr->rd_ptr() + GEP_HEADER_SIZE, (int)data_ptr->length() - GEP_HEADER_SIZE, ctx->version());
        if (unpack_ret < 0)
            packet_ptr = 0;
    }

    if (ctx->is_request())
    {
        if (unpack_ret < 0)
        {
            USYS_ERROR("gep_prot_servant::proc_request_tpl unpack msg %d failed", PACKET_T::MSG_TYPE);
            return uce_error_protunpack;
        }
        ((T*)this)->proc_request(usys_smartptr<gep_request_context>::__dynamic_cast(ctx), packet_ptr);
    }
    else
    {
        usys_smartptr<gep_response_context> rep_ctx = usys_smartptr<gep_response_context>::__dynamic_cast(ctx);
        if (unpack_ret < 0 && rep_ctx != 0)
        {
            if (rep_ctx->stat() == gep_response_context::E_OK)
            {
                USYS_ERROR("gep_prot_servant::proc_request_tpl unpack msg %d failed", PACKET_T::MSG_TYPE);
                rep_ctx->stat(gep_response_context::E_EXCEPTION);
            }
        }
        ((T*)this)->proc_response(rep_ctx, packet_ptr);
    }

    return 0;
}

#endif
