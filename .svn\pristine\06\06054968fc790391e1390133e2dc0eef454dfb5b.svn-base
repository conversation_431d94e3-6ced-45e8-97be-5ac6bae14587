package com.android.rockchip.camera2.util;

public class SerialPort {

    // Used to load the 'myjni' library on application startup.
    static {
        System.loadLibrary("SerialPort");
    }
    /**
     * A native method that is implemented by the 'myjni' native library,
     * which is packaged with this application.
     */

    public native int SerialOpen();
    public native int SerialClose();
    public native int SerialWrite(String data);
    public native byte[] SerialRead();
}
