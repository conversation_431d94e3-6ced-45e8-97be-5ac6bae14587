{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "camera2", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "camera2::@6890427a1f51a3e7e1df", "jsonFile": "target-camera2-Debug-b684896dbf7c9433c2a3.json", "name": "camera2", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/RK3588/APP/APP_cangshiyizhirkcame2/rkCamer2/app/.cxx/Debug/5g4s4b2m/arm64-v8a", "source": "D:/RK3588/APP/APP_cangshiyizhirkcame2/rkCamer2/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}