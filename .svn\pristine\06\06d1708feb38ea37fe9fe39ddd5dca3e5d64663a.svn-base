C/C++ Build Metadata                A           c         \                                pC:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe   %--target=aarch64-none-linux-android29   r--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -DSerialPort_EXPORTS   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -D_FORTIFY_SOURCE=2   -Wformat   -Werror=format-security   
-std=c++11   -fno-limit-debug-info   -fPIC   JC:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\.cxx\Debug\54t2sa2v\arm64-v8a   
SerialPort   HC:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\cpp\native-lib.cpp   *CMakeFiles\SerialPort.dir\native-lib.cpp.o                              	   
         
            