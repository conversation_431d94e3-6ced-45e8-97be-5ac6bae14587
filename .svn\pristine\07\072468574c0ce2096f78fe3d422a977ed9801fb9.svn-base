[{"directory": "C:/hhx/svn/AndroidStudio/rkCamer2/app/.cxx/Debug/54t2sa2v/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DSerialPort_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++11 -fno-limit-debug-info  -fPIC -o CMakeFiles\\SerialPort.dir\\native-lib.cpp.o -c C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\native-lib.cpp", "file": "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\native-lib.cpp"}]