package com.android.rockchip.camera2.video;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.MimeTypeMap;
import android.widget.ImageView;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * ImageDecoder类用于处理图片的解码和显示。
 * <p>
 * 此类提供了图片解码、缩放和加载到ImageView的功能。
 * 支持同步和异步加载，以及内存优化的采样加载方式。
 * </p>
 */
public class ImageDecoder {
    private static final String TAG = "ImageDecoder";
    
    /* 用于异步任务的线程池 */
    private static final ExecutorService executor = Executors.newFixedThreadPool(3);
    
    /* 主线程Handler，用于更新UI */
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 加载图片到ImageView。
     * <p>
     * 此方法会将指定路径的图片加载到ImageView中，并自动进行内存优化。
     * 注意：此方法在调用线程中同步执行，不应在主线程中调用。
     * </p>
     *
     * @param imagePath 图片文件路径
     * @param imageView 目标ImageView
     * @return 如果加载成功返回true，否则返回false
     */
    public static boolean loadImage(String imagePath, ImageView imageView) {
        if (imagePath == null || imageView == null) {
            Log.e(TAG, "Invalid parameters: path or imageView is null");
            return false;
        }

        try {
            // 检查文件是否存在
            File imageFile = new File(imagePath);
            if (!imageFile.exists() || !imageFile.isFile()) {
                Log.e(TAG, "Image file not found: " + imagePath);
                return false;
            }
            
            // 记录图片格式信息
            String fileExtension = getFileExtension(imagePath);
            Log.d(TAG, "Loading image: " + imagePath + ", format: " + fileExtension);

            // 获取ImageView的宽高，用于计算采样率
            int targetWidth = imageView.getWidth();
            int targetHeight = imageView.getHeight();
            
            // 如果ImageView尚未测量完成，使用屏幕宽度作为默认值
            if (targetWidth <= 0 || targetHeight <= 0) {
                targetWidth = imageView.getResources().getDisplayMetrics().widthPixels;
                targetHeight = imageView.getResources().getDisplayMetrics().heightPixels;
            }

            // 加载适当采样率的Bitmap
            Bitmap bitmap = decodeSampledBitmapFromFile(imagePath, targetWidth, targetHeight);
            if (bitmap != null) {
                // 主线程更新UI
                mainHandler.post(() -> {
                    imageView.setImageBitmap(bitmap);
                });
                Log.d(TAG, "Successfully loaded image: " + imagePath + 
                        " (" + bitmap.getWidth() + "x" + bitmap.getHeight() + ")");
                return true;
            } else {
                Log.e(TAG, "Failed to decode bitmap: " + imagePath);
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading image: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步加载图片到ImageView。
     * <p>
     * 此方法会在后台线程加载图片，完成后在主线程更新ImageView。
     * 适合在主线程中调用，不会阻塞UI。
     * </p>
     *
     * @param imagePath 图片文件路径
     * @param imageView 目标ImageView
     * @param callback 加载回调（可为null）
     */
    public static void loadImageAsync(String imagePath, ImageView imageView, LoadCallback callback) {
        executor.execute(() -> {
            try {
                // 检查参数
                if (imagePath == null || imageView == null) {
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("Invalid parameters"));
                    }
                    return;
                }

                // 检查文件是否存在
                File imageFile = new File(imagePath);
                if (!imageFile.exists() || !imageFile.isFile()) {
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("Image file not found: " + imagePath));
                    }
                    return;
                }
                
                // 记录图片格式信息
                String fileExtension = getFileExtension(imagePath);
                Log.d(TAG, "Async loading image: " + imagePath + ", format: " + fileExtension);

                // 获取ImageView的尺寸
                int[] dimensions = new int[2];
                mainHandler.post(() -> {
                    dimensions[0] = imageView.getWidth();
                    dimensions[1] = imageView.getHeight();
                    
                    // 确保获取到有效尺寸
                    if (dimensions[0] <= 0 || dimensions[1] <= 0) {
                        dimensions[0] = imageView.getResources().getDisplayMetrics().widthPixels;
                        dimensions[1] = imageView.getResources().getDisplayMetrics().heightPixels;
                    }
                    
                    synchronized (dimensions) {
                        dimensions.notify();
                    }
                });
                
                // 等待获取尺寸完成
                synchronized (dimensions) {
                    try {
                        dimensions.wait(500); // 最多等待500ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }

                // 解码图片
                final Bitmap bitmap = decodeSampledBitmapFromFile(
                        imagePath, dimensions[0], dimensions[1]);

                // 在主线程更新UI
                mainHandler.post(() -> {
                    if (bitmap != null) {
                        imageView.setImageBitmap(bitmap);
                        Log.d(TAG, "Successfully async loaded image: " + imagePath + 
                                " (" + bitmap.getWidth() + "x" + bitmap.getHeight() + ")");
                        if (callback != null) {
                            callback.onSuccess(bitmap);
                        }
                    } else {
                        Log.e(TAG, "Failed to async decode bitmap: " + imagePath);
                        if (callback != null) {
                            callback.onError("Failed to decode bitmap");
                        }
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Error in async image loading: " + e.getMessage(), e);
                if (callback != null) {
                    final String errorMsg = e.getMessage();
                    mainHandler.post(() -> callback.onError(errorMsg));
                }
            }
        });
    }

    /**
     * 解码图片文件，使用内存优化的采样方式。
     * <p>
     * 此方法会根据目标尺寸计算合适的采样率，以减少内存占用。
     * </p>
     *
     * @param imagePath 图片文件路径
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 解码后的Bitmap，如果解码失败返回null
     */
    public static Bitmap decodeSampledBitmapFromFile(String imagePath, int reqWidth, int reqHeight) {
        try {
            // 先获取图片尺寸
            final BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(imagePath, options);
            
            // 检查图片格式
            String fileExtension = getFileExtension(imagePath);
            Log.d(TAG, "Decoding image: " + imagePath + 
                    ", format: " + fileExtension + 
                    ", size: " + options.outWidth + "x" + options.outHeight);

            // 计算合适的采样率
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
            Log.d(TAG, "Using sample size: " + options.inSampleSize);

            // 解码图片
            options.inJustDecodeBounds = false;
            return BitmapFactory.decodeFile(imagePath, options);
        } catch (Exception e) {
            Log.e(TAG, "Error decoding bitmap: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算适当的采样率。
     * <p>
     * 根据原始图片尺寸和目标尺寸计算合适的采样率，以减少内存占用。
     * </p>
     *
     * @param options 包含原始图片尺寸的选项
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 计算得到的采样率
     */
    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // 原始图片的高度和宽度
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            // 计算高度和宽度的比例
            final int heightRatio = Math.round((float) height / (float) reqHeight);
            final int widthRatio = Math.round((float) width / (float) reqWidth);

            // 选择较小的比例作为采样率，保证结果图片不小于请求的尺寸
            inSampleSize = Math.min(heightRatio, widthRatio);
            
            // 采样率必须是2的幂
            int power = 1;
            while (power * 2 <= inSampleSize) {
                power *= 2;
            }
            inSampleSize = power;
        }

        return inSampleSize;
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param filePath 文件路径
     * @return 文件扩展名，如果没有则返回空字符串
     */
    private static String getFileExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        
        int lastDot = filePath.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < filePath.length() - 1) {
            return filePath.substring(lastDot + 1).toLowerCase();
        }
        
        return "";
    }
    
    /**
     * 旋转图片到指定角度。
     *
     * @param bitmap 原始Bitmap
     * @param degrees 旋转角度
     * @return 旋转后的Bitmap
     */
    public static Bitmap rotateBitmap(Bitmap bitmap, float degrees) {
        if (bitmap == null) return null;
        
        Matrix matrix = new Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }
    
    /**
     * 加载回调接口。
     * <p>
     * 用于异步加载图片时接收加载结果。
     * </p>
     */
    public interface LoadCallback {
        /**
         * 加载成功时调用。
         *
         * @param bitmap 加载成功的Bitmap
         */
        void onSuccess(Bitmap bitmap);

        /**
         * 加载失败时调用。
         *
         * @param error 错误信息
         */
        void onError(String error);
    }
    
    /**
     * 简化版的异步加载方法，不需要回调。
     *
     * @param imagePath 图片文件路径
     * @param imageView 目标ImageView
     */
    public static void loadImageAsync(String imagePath, ImageView imageView) {
        loadImageAsync(imagePath, imageView, null);
    }
}