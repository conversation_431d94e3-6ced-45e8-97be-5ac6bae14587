<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Apr 10 12:54:47 CST 2025 -->
<title>All Classes and Interfaces</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-10">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">接口</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">类</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">CameraManagerHelper 类用于管理摄像头的打开、预览和资源释放。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">CaptureImageHelper 类用于处理摄像头图像的抓取和保存。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">回调接口，用于通知抓图结果</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/mediacodecnew/ExampleUnitTest.html" title="com.android.rockchip.mediacodecnew中的类">ExampleUnitTest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Example local unit test, which will execute on the development machine (host).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">FileStorageUtils 类提供文件存储相关的工具方法，包括创建输出路径、获取存储空间等。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/util/HdmiService.html" title="com.android.rockchip.camera2.util中的类">HdmiService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">HdmiService 类用于检测 HDMI 输入状态的变化。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/android/rockchip/camera2/util/HdmiService.HdmiListener.html" title="com.android.rockchip.camera2.util中的接口">HdmiService.HdmiListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">定义 HDMI 状态变化监听接口。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/ImageViewerActivity.html" title="com.android.rockchip.camera2中的类">ImageViewerActivity</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ImageViewerActivity 类用于显示选定的图片，并支持缩放和平移。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/MediaAdapter.html" title="com.android.rockchip.camera2中的类">MediaAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">MediaAdapter 类用于在 RecyclerView 中显示媒体文件的缩略图和名称。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/android/rockchip/camera2/MediaAdapter.OnMediaClickListener.html" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">定义媒体点击监听器接口。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/MediaBrowserActivity.html" title="com.android.rockchip.camera2中的类">MediaBrowserActivity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">MediaBrowserActivity 类用于显示媒体文件的网格视图，
 并允许用户点击查看视频或图片。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/util/TransformUtils.html" title="com.android.rockchip.camera2.util中的类">TransformUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">TransformUtils 类提供与视图变换相关的工具方法。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/VideoDecoderActivity.html" title="com.android.rockchip.camera2中的类">VideoDecoderActivity</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">VideoDecoderActivity 类负责视频解码和播放控制。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">VideoEncoder 类负责视频编码、存储监控以及文件大小限制的处理。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/android/rockchip/camera2/VideoEncoderActivity.html" title="com.android.rockchip.camera2中的类">VideoEncoderActivity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>
