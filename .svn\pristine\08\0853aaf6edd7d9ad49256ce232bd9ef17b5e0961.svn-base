{"buildFiles": ["D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\.cxx\\Debug\\5g4s4b2m\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\.cxx\\Debug\\5g4s4b2m\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"camera2::@6890427a1f51a3e7e1df": {"artifactName": "camera2", "abi": "arm64-v8a", "output": "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\build\\intermediates\\cxx\\Debug\\5g4s4b2m\\obj\\arm64-v8a\\libcamera2.so", "runtimeFiles": []}}}