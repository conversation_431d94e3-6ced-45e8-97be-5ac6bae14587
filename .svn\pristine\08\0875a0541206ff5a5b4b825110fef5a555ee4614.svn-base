<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Fri Jun 06 17:00:24 CST 2025 -->
<title>TpctrlSocketService</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-06">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.service, class: TpctrlSocketService">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.service</a></div>
<h1 title="类 TpctrlSocketService" class="title">类 TpctrlSocketService</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">android.content.Context
<div class="inheritance">android.content.ContextWrapper
<div class="inheritance">android.app.Service
<div class="inheritance">com.android.rockchip.camera2.service.TpctrlSocketService</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code>android.content.ComponentCallbacks</code>, <code>android.content.ComponentCallbacks2</code>, <code><a href="../util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TpctrlSocketService</span>
<span class="extends-implements">extends android.app.Service
implements <a href="../util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></span></div>
<div class="block">TpctrlSocketService - 处理图像Socket通信服务
 
 <p>此服务监听Socket连接，接收图像请求和ISP参数设置请求。
 使用CaptureImageHelper捕获图像并发送响应。同时监听ISP参数变化并转发给tpctrl进程。</p>
 
 <p>支持的功能：</p>
 <ul>
   <li>图像捕获和传输（支持缩略图和全尺寸图像）</li>
   <li>心跳包处理</li>
   <li>ISP参数设置和同步</li>
   <li>进程生命周期监控</li>
 </ul></div>
<dl class="notes">
<dt>从以下版本开始:</dt>
<dd>1.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpctrlSocketService.LogListener.html" class="type-name-link" title="com.android.rockchip.camera2.service中的接口">TpctrlSocketService.LogListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">日志监听接口，用于外部接收服务日志</div>
</div>
</div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-android.content.Context">从类继承的嵌套类/接口&nbsp;android.content.Context</h2>
<code>android.content.Context.BindServiceFlags</code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>字段概要</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-android.app.Service">从类继承的字段&nbsp;android.app.Service</h3>
<code>START_CONTINUATION_MASK, START_FLAG_REDELIVERY, START_FLAG_RETRY, START_NOT_STICKY, START_REDELIVER_INTENT, START_STICKY, START_STICKY_COMPATIBILITY, STOP_FOREGROUND_DETACH, STOP_FOREGROUND_LEGACY, STOP_FOREGROUND_REMOVE</code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-android.content.Context">从类继承的字段&nbsp;android.content.Context</h3>
<code>ACCESSIBILITY_SERVICE, ACCOUNT_SERVICE, ACTIVITY_SERVICE, ALARM_SERVICE, APP_OPS_SERVICE, APP_SEARCH_SERVICE, APPWIDGET_SERVICE, AUDIO_SERVICE, BATTERY_SERVICE, BIND_ABOVE_CLIENT, BIND_ADJUST_WITH_ACTIVITY, BIND_ALLOW_ACTIVITY_STARTS, BIND_ALLOW_OOM_MANAGEMENT, BIND_AUTO_CREATE, BIND_DEBUG_UNBIND, BIND_EXTERNAL_SERVICE, BIND_EXTERNAL_SERVICE_LONG, BIND_IMPORTANT, BIND_INCLUDE_CAPABILITIES, BIND_NOT_FOREGROUND, BIND_NOT_PERCEPTIBLE, BIND_PACKAGE_ISOLATED_PROCESS, BIND_SHARED_ISOLATED_PROCESS, BIND_WAIVE_PRIORITY, BIOMETRIC_SERVICE, BLOB_STORE_SERVICE, BLUETOOTH_SERVICE, BUGREPORT_SERVICE, CAMERA_SERVICE, CAPTIONING_SERVICE, CARRIER_CONFIG_SERVICE, CLIPBOARD_SERVICE, COMPANION_DEVICE_SERVICE, CONNECTIVITY_DIAGNOSTICS_SERVICE, CONNECTIVITY_SERVICE, CONSUMER_IR_SERVICE, CONTACT_KEYS_SERVICE, CONTEXT_IGNORE_SECURITY, CONTEXT_INCLUDE_CODE, CONTEXT_RESTRICTED, CREDENTIAL_SERVICE, CROSS_PROFILE_APPS_SERVICE, DEVICE_ID_DEFAULT, DEVICE_ID_INVALID, DEVICE_LOCK_SERVICE, DEVICE_POLICY_SERVICE, DISPLAY_HASH_SERVICE, DISPLAY_SERVICE, DOMAIN_VERIFICATION_SERVICE, DOWNLOAD_SERVICE, DROPBOX_SERVICE, EUICC_SERVICE, FILE_INTEGRITY_SERVICE, FINGERPRINT_SERVICE, GAME_SERVICE, GRAMMATICAL_INFLECTION_SERVICE, HARDWARE_PROPERTIES_SERVICE, HEALTHCONNECT_SERVICE, INPUT_METHOD_SERVICE, INPUT_SERVICE, IPSEC_SERVICE, JOB_SCHEDULER_SERVICE, KEYGUARD_SERVICE, LAUNCHER_APPS_SERVICE, LAYOUT_INFLATER_SERVICE, LOCALE_SERVICE, LOCATION_SERVICE, MEDIA_COMMUNICATION_SERVICE, MEDIA_METRICS_SERVICE, MEDIA_PROJECTION_SERVICE, MEDIA_ROUTER_SERVICE, MEDIA_SESSION_SERVICE, MIDI_SERVICE, MODE_APPEND, MODE_ENABLE_WRITE_AHEAD_LOGGING, MODE_MULTI_PROCESS, MODE_NO_LOCALIZED_COLLATORS, MODE_PRIVATE, MODE_WORLD_READABLE, MODE_WORLD_WRITEABLE, NETWORK_STATS_SERVICE, NFC_SERVICE, NOTIFICATION_SERVICE, NSD_SERVICE, OVERLAY_SERVICE, PEOPLE_SERVICE, PERFORMANCE_HINT_SERVICE, PERSISTENT_DATA_BLOCK_SERVICE, POWER_SERVICE, PRINT_SERVICE, PROFILING_SERVICE, RECEIVER_EXPORTED, RECEIVER_NOT_EXPORTED, RECEIVER_VISIBLE_TO_INSTANT_APPS, RESTRICTIONS_SERVICE, ROLE_SERVICE, SEARCH_SERVICE, SECURITY_STATE_SERVICE, SENSOR_SERVICE, SHORTCUT_SERVICE, STATUS_BAR_SERVICE, STORAGE_SERVICE, STORAGE_STATS_SERVICE, SYSTEM_HEALTH_SERVICE, TELECOM_SERVICE, TELEPHONY_IMS_SERVICE, TELEPHONY_SERVICE, TELEPHONY_SUBSCRIPTION_SERVICE, TEXT_CLASSIFICATION_SERVICE, TEXT_SERVICES_MANAGER_SERVICE, TV_INPUT_SERVICE, TV_INTERACTIVE_APP_SERVICE, UI_MODE_SERVICE, USAGE_STATS_SERVICE, USB_SERVICE, USER_SERVICE, VIBRATOR_MANAGER_SERVICE, VIBRATOR_SERVICE, VIRTUAL_DEVICE_SERVICE, VPN_MANAGEMENT_SERVICE, WALLPAPER_SERVICE, WIFI_AWARE_SERVICE, WIFI_P2P_SERVICE, WIFI_RTT_RANGING_SERVICE, WIFI_SERVICE, WINDOW_SERVICE</code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-android.content.ComponentCallbacks2">从接口继承的字段&nbsp;android.content.ComponentCallbacks2</h3>
<code>TRIM_MEMORY_BACKGROUND, TRIM_MEMORY_COMPLETE, TRIM_MEMORY_MODERATE, TRIM_MEMORY_RUNNING_CRITICAL, TRIM_MEMORY_RUNNING_LOW, TRIM_MEMORY_RUNNING_MODERATE, TRIM_MEMORY_UI_HIDDEN</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(com.android.rockchip.camera2.video.CaptureImageHelper,java.lang.String)" class="member-name-link">TpctrlSocketService</a><wbr>(<a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureHelper,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;tempImagePath)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRunning()" class="member-name-link">isRunning</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取服务运行状态</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>android.os.IBinder</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onBind(android.content.Intent)" class="member-name-link">onBind</a><wbr>(android.content.Intent&nbsp;intent)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">onDataChanged</a><wbr>(<a href="../util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;newValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">ISP参数变化回调（整型参数）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)" class="member-name-link">onLongDataChanged</a><wbr>(<a href="../util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;newValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">ISP参数变化回调（长整型参数）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogListener(com.android.rockchip.camera2.service.TpctrlSocketService.LogListener)" class="member-name-link">setLogListener</a><wbr>(<a href="TpctrlSocketService.LogListener.html" title="com.android.rockchip.camera2.service中的接口">TpctrlSocketService.LogListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置日志监听器</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#start()" class="member-name-link">start</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动Socket服务</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stop()" class="member-name-link">stop</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止Socket服务</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopProcessMonitoring()" class="member-name-link">stopProcessMonitoring</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止进程监控</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-android.app.Service">从类继承的方法&nbsp;android.app.Service</h3>
<code>attachBaseContext, dump, getApplication, getForegroundServiceType, onConfigurationChanged, onCreate, onDestroy, onLowMemory, onRebind, onStart, onStartCommand, onTaskRemoved, onTimeout, onTimeout, onTrimMemory, onUnbind, startForeground, startForeground, stopForeground, stopForeground, stopSelf, stopSelf, stopSelfResult</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-android.content.ContextWrapper">从类继承的方法&nbsp;android.content.ContextWrapper</h3>
<code>bindIsolatedService, bindService, bindService, bindService, bindService, bindServiceAsUser, bindServiceAsUser, checkCallingOrSelfPermission, checkCallingOrSelfUriPermission, checkCallingOrSelfUriPermissions, checkCallingPermission, checkCallingUriPermission, checkCallingUriPermissions, checkContentUriPermissionFull, checkPermission, checkSelfPermission, checkUriPermission, checkUriPermission, checkUriPermissions, clearWallpaper, createAttributionContext, createConfigurationContext, createContext, createContextForSplit, createDeviceContext, createDeviceProtectedStorageContext, createDisplayContext, createPackageContext, createWindowContext, createWindowContext, databaseList, deleteDatabase, deleteFile, deleteSharedPreferences, enforceCallingOrSelfPermission, enforceCallingOrSelfUriPermission, enforceCallingPermission, enforceCallingUriPermission, enforcePermission, enforceUriPermission, enforceUriPermission, fileList, getApplicationContext, getApplicationInfo, getAssets, getAttributionSource, getAttributionTag, getBaseContext, getCacheDir, getClassLoader, getCodeCacheDir, getContentResolver, getDatabasePath, getDataDir, getDeviceId, getDir, getDisplay, getExternalCacheDir, getExternalCacheDirs, getExternalFilesDir, getExternalFilesDirs, getExternalMediaDirs, getFilesDir, getFileStreamPath, getMainExecutor, getMainLooper, getNoBackupFilesDir, getObbDir, getObbDirs, getOpPackageName, getPackageCodePath, getPackageManager, getPackageName, getPackageResourcePath, getParams, getResources, getSharedPreferences, getSystemService, getSystemServiceName, getTheme, getWallpaper, getWallpaperDesiredMinimumHeight, getWallpaperDesiredMinimumWidth, grantUriPermission, isDeviceProtectedStorage, isRestricted, isUiContext, moveDatabaseFrom, moveSharedPreferencesFrom, openFileInput, openFileOutput, openOrCreateDatabase, openOrCreateDatabase, peekWallpaper, registerComponentCallbacks, registerDeviceIdChangeListener, registerReceiver, registerReceiver, registerReceiver, registerReceiver, removeStickyBroadcast, removeStickyBroadcastAsUser, revokeSelfPermissionsOnKill, revokeUriPermission, revokeUriPermission, sendBroadcast, sendBroadcast, sendBroadcast, sendBroadcastAsUser, sendBroadcastAsUser, sendOrderedBroadcast, sendOrderedBroadcast, sendOrderedBroadcast, sendOrderedBroadcast, sendOrderedBroadcast, sendOrderedBroadcast, sendOrderedBroadcastAsUser, sendStickyBroadcast, sendStickyBroadcast, sendStickyBroadcastAsUser, sendStickyOrderedBroadcast, sendStickyOrderedBroadcastAsUser, setTheme, setWallpaper, setWallpaper, startActivities, startActivities, startActivity, startActivity, startForegroundService, startInstrumentation, startIntentSender, startIntentSender, startService, stopService, unbindService, unregisterComponentCallbacks, unregisterDeviceIdChangeListener, unregisterReceiver, updateServiceGroup</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-android.content.Context">从类继承的方法&nbsp;android.content.Context</h3>
<code>bindIsolatedService, getColor, getColorStateList, getDrawable, getString, getString, getSystemService, getText, obtainStyledAttributes, obtainStyledAttributes, obtainStyledAttributes, obtainStyledAttributes, revokeSelfPermissionOnKill, sendBroadcastWithMultiplePermissions</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(com.android.rockchip.camera2.video.CaptureImageHelper,java.lang.String)">
<h3>TpctrlSocketService</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TpctrlSocketService</span><wbr><span class="parameters">(<a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureHelper,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;tempImagePath)</span></div>
<div class="block">构造函数</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>captureHelper</code> - 图像捕获助手实例，不能为null</dd>
<dd><code>tempImagePath</code> - 临时图像保存路径，必须是有效的文件路径</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onBind(android.content.Intent)">
<h3>onBind</h3>
<div class="member-signature"><span class="annotations">@Nullable
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">android.os.IBinder</span>&nbsp;<span class="element-name">onBind</span><wbr><span class="parameters">(android.content.Intent&nbsp;intent)</span></div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>onBind</code>&nbsp;在类中&nbsp;<code>android.app.Service</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogListener(com.android.rockchip.camera2.service.TpctrlSocketService.LogListener)">
<h3>setLogListener</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogListener</span><wbr><span class="parameters">(<a href="TpctrlSocketService.LogListener.html" title="com.android.rockchip.camera2.service中的接口">TpctrlSocketService.LogListener</a>&nbsp;listener)</span></div>
<div class="block">设置日志监听器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 日志监听器实例，可以为null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isRunning()">
<h3>isRunning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRunning</span>()</div>
<div class="block">获取服务运行状态</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果服务正在运行返回true，否则返回false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="start()">
<h3>start</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">start</span>()</div>
<div class="block">启动Socket服务
 
 <p>创建一个新线程来监听指定端口上的连接请求。
 如果服务已经在运行，此方法不会执行任何操作。
 同时注册ISP参数变化监听器。</p></div>
<dl class="notes">
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/SecurityException.html" title="java.lang中的类或接口" class="external-link">SecurityException</a></code> - 如果没有网络权限</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stop()">
<h3>stop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stop</span>()</div>
<div class="block">停止Socket服务
 
 <p>关闭服务器Socket和线程，清理资源。
 如果服务未运行，此方法不会执行任何操作。
 同时移除ISP参数监听器并关闭线程池。</p></div>
</section>
</li>
<li>
<section class="detail" id="stopProcessMonitoring()">
<h3>stopProcessMonitoring</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopProcessMonitoring</span>()</div>
<div class="block">停止进程监控
 
 <p>停止对tpctrl进程的生命周期监控，移除所有定时检查任务。</p></div>
</section>
</li>
<li>
<section class="detail" id="onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>onDataChanged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onDataChanged</span><wbr><span class="parameters">(<a href="../util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;newValue)</span></div>
<div class="block">ISP参数变化回调（整型参数）
 
 <p>当ISP参数发生变化时被调用，负责将参数变化同步到tpctrl进程。</p></div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../util/TouptekIspParam.OnDataChangedListener.html#onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)">onDataChanged</a></code>&nbsp;在接口中&nbsp;<code><a href="../util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></code></dd>
<dt>参数:</dt>
<dd><code>param</code> - 变化的ISP参数</dd>
<dd><code>newValue</code> - 新的参数值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)">
<h3>onLongDataChanged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onLongDataChanged</span><wbr><span class="parameters">(<a href="../util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;newValue)</span></div>
<div class="block">ISP参数变化回调（长整型参数）
 
 <p>当长整型ISP参数发生变化时被调用。目前长整型参数不需要同步到tpctrl。</p></div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../util/TouptekIspParam.OnDataChangedListener.html#onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)">onLongDataChanged</a></code>&nbsp;在接口中&nbsp;<code><a href="../util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></code></dd>
<dt>参数:</dt>
<dd><code>param</code> - 变化的ISP参数</dd>
<dd><code>newValue</code> - 新的参数值（长整型）</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
