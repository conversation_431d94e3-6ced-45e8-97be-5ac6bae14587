package com.android.rockchip.camera2.video;

import android.content.Context;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatActivity;

import com.android.rockchip.camera2.service.StreamingService;
import com.android.rockchip.camera2.util.FileStorageUtils;

/**
 * TpVideoSystem - ToupTek视频系统类
 * <p>
 * 基于VideoEncoderActivity的成功实现，提供统一的视频预览、录制和图像捕获功能。
 * 使用TpVideoConfig配置系统，支持灵活的视频参数配置，确保高质量的录制性能。
 * 采用双层API设计：外层简单API满足80%客户需求，内层专业API满足20%高级需求。
 * </p>
 *
 * <p><strong>⚠️ 重要：推流功能的正确使用方式</strong></p>
 * <p>为了支持RTSP推流功能，必须在Activity的onCreate()方法中创建TpVideoSystem实例：</p>
 * <pre>{@code
 * public class MainActivity extends AppCompatActivity {
 *     private TpVideoSystem videoSystem;
 *
 *     @Override
 *     protected void onCreate(Bundle savedInstanceState) {
 *         super.onCreate(savedInstanceState);
 *
 *         // ✅ 正确：在onCreate中创建，支持推流功能
 *         TpVideoConfig config = TpVideoConfig.createDefault1080P();
 *         videoSystem = new TpVideoSystem(this, config);
 *
 *         // 后续在TextureView准备好后调用initialize()
 *     }
 * }
 * }</pre>
 *
 * <p><strong>原因：</strong>RTSP推流功能需要注册ActivityResultLauncher来申请屏幕录制权限，
 * 而Android要求这种注册必须在Activity进入STARTED状态之前完成。</p>
 */
public class TpVideoSystem {
    private static final String TAG = "TpVideoSystem";
    
    // 核心组件
    private final Context context;
    private final AppCompatActivity activity; // 需要Activity用于StreamingService
    private TpVideoConfig videoConfig;
    private VideoEncoder videoEncoder;
    private CameraManagerHelper cameraManagerHelper;
    private CaptureImageHelper captureImageHelper;

    // 推流组件
    private StreamingService streamingService;

    // 内层专业API组件
    private VideoRecorder videoRecorder;
    private ImageCapture imageCapture;

    // 状态管理
    private boolean isInitialized = false;
    private boolean isCameraStarted = false;
    private boolean isRecording = false;

    // 流类型管理
    private StreamingService.StreamType currentStreamType = StreamingService.StreamType.CAMERA; // 默认摄像头流
    private boolean isStreaming = false;

    // TV模式相关组件（懒加载）
    private TvPreviewHelper tvPreviewHelper;
    private boolean isTvMode = false;
    private ViewGroup tvContainer; // TV预览容器

    // 回调接口
    private TpVideoSystemListener listener;

    // ===== 推流功能相关枚举 =====

    /**
     * 流类型枚举（对外API）
     */
    public enum StreamType {
        /** 摄像头流 */
        CAMERA,
        /** 屏幕流 */
        SCREEN
    }
    
    /**
     * 视频系统监听器接口
     * <p>使用默认方法支持，只需要实现关心的事件</p>
     */
    public interface TpVideoSystemListener {
        /**
         * 发生错误（核心方法，建议实现）
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
        
        /**
         * 系统初始化完成
         */
        default void onSystemInitialized() {
            // 默认空实现
        }
        
        /**
         * 相机启动完成
         */
        default void onCameraStarted() {
            // 默认空实现
        }
        
        /**
         * 录制开始
         * @param outputPath 输出文件路径
         */
        default void onRecordingStarted(String outputPath) {
            // 默认空实现
        }
        
        /**
         * 录制停止
         * @param outputPath 输出文件路径
         */
        default void onRecordingStopped(String outputPath) {
            // 默认空实现
        }
        
        /**
         * 图像捕获完成
         * @param imagePath 图像文件路径
         */
        default void onImageCaptured(String imagePath) {
            // 默认空实现
        }

        /**
         * 推流状态变化回调
         * @param isStreaming 是否正在推流
         * @param rtspUrl 推流地址
         */
        default void onStreamingStatusChanged(boolean isStreaming, String rtspUrl) {
            // 默认空实现
        }

        /**
         * TV模式状态变化回调
         * @param isTvMode 是否为TV模式
         */
        default void onTvModeChanged(boolean isTvMode) {
            // 默认空实现
        }

        /**
         * TV模式错误回调
         * @param error 错误信息
         */
        default void onTvModeError(String error) {
            // 默认空实现
        }

    }
    
    /**
     * 视频系统监听器适配器
     * <p>提供所有方法的默认实现，使用者只需重写关心的方法</p>
     */
    public static class TpVideoSystemAdapter implements TpVideoSystemListener {
        @Override
        public void onError(String errorMessage) {
            // 提供默认的错误处理 - 记录日志
            Log.e(TAG, "TpVideoSystem Error: " + errorMessage);
        }
        
        // 其他方法使用接口的默认实现
        // 子类可以选择性重写需要的方法
    }
    
    /**
     * 构造函数 - 使用默认1080P配置
     * @param activity Activity实例（推流功能需要）
     */
    public TpVideoSystem(AppCompatActivity activity) {
        this(activity, TpVideoConfig.createDefault1080P());
    }
    
    /**
     * 构造函数 - 使用自定义配置（推荐，支持推流功能）
     * @param activity Activity实例（推流功能需要）
     * @param config 视频配置
     */
    public TpVideoSystem(AppCompatActivity activity, TpVideoConfig config) {
        this.context = activity;
        this.activity = activity;
        this.videoConfig = config;

        // 立即进行StreamingService的第一步初始化（必须在onCreate中完成）
        initStreamingServiceEarly();

        // 创建内层专业API组件
        this.videoRecorder = new VideoRecorder();
        this.imageCapture = new ImageCapture();

        Log.d(TAG, "TpVideoSystem created with config: " + config.toString());
    }

    /**
     * 设置监听器
     * @param listener 监听器
     */
    public void setListener(TpVideoSystemListener listener) {
        this.listener = listener;
    }
    
    // ===== 外层简单API（80%客户使用） =====
    
    /**
     * 初始化视频系统
     * @param previewSurface 预览Surface
     */
    public void initialize(Surface previewSurface) {
        if (isInitialized) {
            Log.w(TAG, "系统已经初始化");
            return;
        }
        
        try {
            Log.d(TAG, "开始初始化视频系统，配置: " + videoConfig.toString());
            
            // 初始化图像捕获助手
            initCaptureHelper();
            
            // 初始化视频编码器 - 使用TpVideoConfig
            initVideoEncoder(previewSurface);
            
            isInitialized = true;
            Log.d(TAG, "视频系统初始化完成");
            
            if (listener != null) {
                listener.onSystemInitialized();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "视频系统初始化失败", e);
            if (listener != null) {
                listener.onError("初始化失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 开始录制
     * @param outputPath 输出路径，如果为null则使用默认路径
     */
    public void startRecording(String outputPath) {
        if (!isInitialized || !isCameraStarted) {
            Log.e(TAG, "系统未初始化或相机未启动");
            if (listener != null) {
                listener.onError("系统未初始化或相机未启动");
            }
            return;
        }

        if (isTvMode) {
            Log.e(TAG, "TV模式下不支持录制功能");
            if (listener != null) {
                listener.onError("TV模式下不支持录制功能，请切换到Camera模式");
            }
            return;
        }

        if (isRecording) {
            Log.w(TAG, "已经在录制中");
            return;
        }

        // 委托给内层VideoRecorder
        videoRecorder.startRecording(outputPath);
    }
    
    /**
     * 停止录制
     */
    public void stopRecording() {
        if (!isRecording) {
            Log.w(TAG, "未在录制中");
            return;
        }
        
        // 委托给内层VideoRecorder
        videoRecorder.stopRecording();
    }
    
    /**
     * 捕获图像
     * @param outputPath 输出路径，如果为null则使用默认路径
     */
    public void captureImage(String outputPath) {
        if (!isInitialized || !isCameraStarted) {
            Log.e(TAG, "系统未初始化或相机未启动");
            if (listener != null) {
                listener.onError("系统未初始化或相机未启动");
            }
            return;
        }

        if (isTvMode) {
            Log.e(TAG, "TV模式下不支持图像捕获功能");
            if (listener != null) {
                listener.onError("TV模式下不支持图像捕获功能，请切换到Camera模式");
            }
            return;
        }
        
        // 委托给内层ImageCapture
        imageCapture.captureImage(outputPath);
    }
    
    /**
     * 获取录制状态
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * 获取相机启动状态
     */
    public boolean isCameraStarted() {
        return isCameraStarted;
    }
    
    /**
     * 获取初始化状态
     */
    public boolean isInitialized() {
        return isInitialized;
    }


    
    // ===== 配置管理API =====

    /**
     * 获取当前视频配置
     */
    public TpVideoConfig getVideoConfig() {
        return videoConfig;
    }

    /**
     * 更新视频配置（仅在未初始化时允许）
     */
    public void updateVideoConfig(TpVideoConfig config) throws IllegalStateException {
        if (isInitialized) {
            throw new IllegalStateException("Cannot update config after initialization");
        }

        this.videoConfig = config;
        Log.d(TAG, "Video config updated: " + config.toString());
    }

    // ===== 80%简单API：分辨率设置方法 =====

    /**
     * 设置比特率（80%简单API）
     * <p>
     * 设置视频比特率。支持初始化前配置和运行时动态调整。
     * 运行时调整适用于根据网络状况或存储需求动态调整视频质量。
     * </p>
     *
     * @param bitRate 比特率（bps），例如：8_000_000 表示8Mbps
     * @throws IllegalArgumentException 如果比特率无效（必须大于0）
     * @return 是否成功设置比特率
     */
    public boolean updateBitRate(int bitRate) throws IllegalArgumentException {
        if (bitRate <= 0) {
            throw new IllegalArgumentException("比特率必须大于0: " + bitRate);
        }

        // 运行时调整：直接调用VideoEncoder的API
        if (isInitialized && videoEncoder != null) {
            Log.d(TAG, "运行时调整比特率: " + bitRate + " bps");
            boolean success = videoEncoder.setBitrate(bitRate);

            if (success) {
                // 同步更新配置对象，保持状态一致
                updateConfigBitRate(bitRate);
                Log.d(TAG, "运行时比特率调整成功");
            } else {
                Log.e(TAG, "运行时比特率调整失败");
            }

            return success;
        }

        // 初始化前配置：修改TpVideoConfig
        else {
            Log.d(TAG, "初始化前设置比特率: " + bitRate + " bps");

            // 基于当前配置创建新配置，只修改比特率
            TpVideoConfig currentConfig = getVideoConfig();
            TpVideoConfig newConfig = new TpVideoConfig.Builder(
                    currentConfig.getWidth(),
                    currentConfig.getHeight(),
                    currentConfig.getFrameRate())
                .setBitRate(bitRate)
                .setCodec(currentConfig.getCodec())
                .setBitrateMode(currentConfig.getBitrateMode())
                .setColorFormat(currentConfig.getColorFormat())
                .build();

            updateVideoConfig(newConfig);
            Log.d(TAG, "初始化前比特率配置完成");
            return true;
        }
    }

    /**
     * 同步更新配置中的比特率（内部方法）
     * 用于运行时比特率调整后保持配置对象与实际状态一致
     */
    private void updateConfigBitRate(int bitRate) {
        if (videoConfig != null) {
            // 创建新的配置对象，只更新比特率
            TpVideoConfig updatedConfig = new TpVideoConfig.Builder(
                    videoConfig.getWidth(),
                    videoConfig.getHeight(),
                    videoConfig.getFrameRate())
                .setBitRate(bitRate)
                .setCodec(videoConfig.getCodec())
                .setBitrateMode(videoConfig.getBitrateMode())
                .setColorFormat(videoConfig.getColorFormat())
                .build();

            this.videoConfig = updatedConfig;
            Log.d(TAG, "配置对象比特率已同步更新: " + bitRate);
        }
    }

    /**
     * 设置分辨率（80%简单API）
     * <p>
     * 通过HDMI重置机制实现分辨率动态调整。此方法会：
     * <ul>
     * <li>保存新的分辨率配置</li>
     * <li>执行HDMI重置（模拟拔出再插入）</li>
     * <li>在HDMI重新连接时自动应用新分辨率</li>
     * </ul>
     * </p>
     *
     * <p><b>⚠️ 注意事项：</b></p>
     * <ul>
     * <li>此操作会短暂中断视频预览（约1-2秒）</li>
     * <li>如果正在录制，会自动停止录制</li>
     * <li>如果正在推流，会自动停止推流</li>
     * <li>操作完成后需要重新开始录制或推流</li>
     * </ul>
     *
     * @param width 新的视频宽度
     * @param height 新的视频高度
     * @return 是否成功启动分辨率调整流程
     */
    public boolean updateResolution(int width, int height) {
        if (!isInitialized) {
            Log.w(TAG, "系统未初始化，将在初始化时应用分辨率设置");
            // 直接更新配置，在初始化时生效
            TpVideoConfig currentConfig = getVideoConfig();
            TpVideoConfig newConfig = new TpVideoConfig.Builder(width, height, currentConfig.getFrameRate())
                .setBitRate(currentConfig.getBitRate())
                .setCodec(currentConfig.getCodec())
                .setBitrateMode(currentConfig.getBitrateMode())
                .setColorFormat(currentConfig.getColorFormat())
                .build();
            this.videoConfig = newConfig;
            return true;
        }

        Log.d(TAG, "开始运行时分辨率调整: " + width + "x" + height);

        // 保存新的分辨率配置，供HDMI重新连接时使用
        TpVideoConfig currentConfig = getVideoConfig();
        TpVideoConfig newConfig = new TpVideoConfig.Builder(width, height, currentConfig.getFrameRate())
            .setBitRate(currentConfig.getBitRate())
            .setCodec(currentConfig.getCodec())
            .setBitrateMode(currentConfig.getBitrateMode())
            .setColorFormat(currentConfig.getColorFormat())
            .build();

        this.videoConfig = newConfig;

        // 停止当前的录制和推流
        if (isRecording) {
            Log.d(TAG, "停止录制以进行分辨率调整");
            stopRecording();
        }

        if (isStreaming()) {
            Log.d(TAG, "停止推流以进行分辨率调整");
            stopStreaming();
        }

        // 执行HDMI重置
        if (cameraManagerHelper != null) {
            Log.d(TAG, "执行HDMI重置...");
            boolean resetSuccess = cameraManagerHelper.resetHdmiRxViaScript();

            if (resetSuccess) {
                Log.d(TAG, "HDMI重置成功，等待重新连接并应用新分辨率");
                return true;
            } else {
                Log.e(TAG, "HDMI重置失败");
                return false;
            }
        } else {
            Log.e(TAG, "CameraManagerHelper未初始化，无法执行HDMI重置");
            return false;
        }
    }

    // ===== RTSP推流功能API =====

    /**
     * 启动推流（使用默认配置）
     * @return true表示启动成功
     */
    public boolean startStreaming() {
        return startStreaming(StreamType.CAMERA, null);
    }

    /**
     * 启动推流
     * @param streamType 流类型（CAMERA或SCREEN）
     * @return true表示启动成功
     */
    public boolean startStreaming(StreamType streamType) {
        return startStreaming(streamType, null);
    }

    /**
     * 启动推流
     * @param streamType 流类型
     * @param networkInterface 网络接口（null使用默认）
     * @return true表示启动成功
     */
    public boolean startStreaming(StreamType streamType, String networkInterface) {
        if (streamingService == null) {
            Log.e(TAG, "推流服务不可用");
            if (listener != null) {
                listener.onError("推流服务未初始化");
            }
            return false;
        }

        Log.d(TAG, "启动推流: " + streamType + ", 网络接口: " + networkInterface);

        // 内部处理线程管理和错误处理
        return executeStreamingOperation(() -> {
            StreamingService.StreamType internalType = convertStreamType(streamType);
            return streamingService.startRtspManually(internalType, networkInterface);
        });
    }

    /**
     * 停止推流
     * @return true表示停止成功
     */
    public boolean stopStreaming() {
        if (streamingService == null) {
            Log.w(TAG, "推流服务不可用");
            return false;
        }

        Log.d(TAG, "停止推流");

        return executeStreamingOperation(() -> {
            return streamingService.stopRtspManually();
        });
    }

    /**
     * 获取推流状态
     * 直接从StreamingService获取，与VideoEncoderActivity保持一致
     */
    public boolean isStreaming() {
        return streamingService != null && streamingService.isStreaming();
    }

    /**
     * 获取推流URL
     * @return 推流URL，如果未推流则返回null
     */
    public String getStreamUrl() {
        return streamingService != null ? streamingService.getStreamUrl() : null;
    }

    /**
     * 获取当前流类型
     * @return 当前流类型
     */
    public StreamType getCurrentStreamType() {
        if (streamingService == null) {
            return null;
        }
        return convertFromInternalStreamType(streamingService.getCurrentStreamType());
    }

    // ===== 流类型管理API =====

    /**
     * 设置流类型（新API）
     * @param streamType 流类型（CAMERA或SCREEN）
     */
    public void setStreamType(StreamType streamType) {
        if (streamType == null) {
            Log.e(TAG, "流类型不能为null");
            return;
        }

        Log.d(TAG, "设置流类型: " + streamType);
        StreamingService.StreamType internalType = convertStreamType(streamType);
        this.currentStreamType = internalType;

        // 如果StreamingService已初始化，同步设置流类型
        if (streamingService != null) {
            streamingService.setStreamType(internalType);
        }
    }

    /**
     * 切换流类型
     */
    public void toggleStreamType() {
        StreamingService.StreamType newType = currentStreamType == StreamingService.StreamType.CAMERA ?
                StreamingService.StreamType.SCREEN : StreamingService.StreamType.CAMERA;
        setStreamType(convertFromInternalStreamType(newType));
    }


    // ===== TV模式核心API（简单层 - 80%客户使用） =====

    /**
     * 切换到TV模式
     * <p>
     * 切换到TV模式后，将停止相机预览并启动TV预览。
     * 在TV模式下，录制和图像捕获功能将被禁用。
     * </p>
     *
     * @throws IllegalStateException 如果当前状态不允许切换（如正在录制）
     */
    public void switchToTvMode() throws IllegalStateException {
        Log.d(TAG, "请求切换到TV模式");

        // 状态检查
        if (isTvMode) {
            Log.d(TAG, "已经是TV模式，无需切换");
            return;
        }

        if (isRecording) {
            throw new IllegalStateException("正在录制时不能切换到TV模式，请先停止录制");
        }

        try {
            // 停止相机预览
            stopCameraPreview();

            // 启动TV预览
            startTvPreview();

            // 更新状态
            isTvMode = true;

            Log.d(TAG, "成功切换到TV模式");

            // 通知监听器
            if (listener != null) {
                listener.onTvModeChanged(true);
            }

        } catch (Exception e) {
            Log.e(TAG, "切换到TV模式失败", e);
            if (listener != null) {
                listener.onTvModeError("切换到TV模式失败: " + e.getMessage());
            }
            throw new IllegalStateException("切换到TV模式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 切换到Camera模式
     * <p>
     * 切换到Camera模式后，将停止TV预览并启动相机预览。
     * 在Camera模式下，录制和图像捕获功能将被启用。
     * </p>
     *
     * @throws IllegalStateException 如果当前状态不允许切换
     */
    public void switchToCameraMode() throws IllegalStateException {
        Log.d(TAG, "请求切换到Camera模式");

        // 状态检查
        if (!isTvMode) {
            Log.d(TAG, "已经是Camera模式，无需切换");
            return;
        }

        try {
            // 停止TV预览
            stopTvPreview();

            // 启动相机预览
            startCameraPreview();

            // 更新状态
            isTvMode = false;

            Log.d(TAG, "成功切换到Camera模式");

            // 通知监听器
            if (listener != null) {
                listener.onTvModeChanged(false);
            }

        } catch (Exception e) {
            Log.e(TAG, "切换到Camera模式失败", e);
            if (listener != null) {
                listener.onTvModeError("切换到Camera模式失败: " + e.getMessage());
            }
            throw new IllegalStateException("切换到Camera模式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前是否为TV模式
     *
     * @return true表示TV模式，false表示Camera模式
     */
    public boolean isTvMode() {
        return isTvMode;
    }

    /**
     * 检查是否可以切换到TV模式
     * <p>
     * 检查当前状态是否允许切换到TV模式。
     * 如果正在录制或系统未初始化，则不允许切换。
     * </p>
     *
     * @return true表示可以切换，false表示当前状态不允许切换
     */
    public boolean canSwitchToTvMode() {
        if (isTvMode) {
            return false; // 已经是TV模式
        }

        if (isRecording) {
            return false; // 正在录制
        }

        if (!isInitialized) {
            return false; // 系统未初始化
        }

        return true;
    }

    // ===== TV模式配置API =====

    /**
     * 设置TV预览容器
     * <p>
     * 必须在使用TV模式功能之前调用此方法设置TV预览的容器视图。
     * </p>
     *
     * @param container TV预览容器视图
     */
    public void setTvContainer(ViewGroup container) {
        this.tvContainer = container;
        Log.d(TAG, "TV容器已设置: " + (container != null ? container.getClass().getSimpleName() : "null"));

        // 如果TV预览助手已存在，需要重新初始化
        if (tvPreviewHelper != null) {
            tvPreviewHelper.release();
            tvPreviewHelper = null;
        }
    }



    // ===== 内层专业API（20%客户使用） =====
    
    /**
     * 获取VideoRecorder实例（高级功能）
     * @return VideoRecorder实例
     */
    public VideoRecorder getVideoRecorder() {
        return videoRecorder;
    }
    
    /**
     * 获取ImageCapture实例（高级功能）
     * @return ImageCapture实例
     */
    public ImageCapture getImageCapture() {
        return imageCapture;
    }

    /**
     * 获取StreamingService实例（高级功能）
     * @return StreamingService实例，可能为null
     */
    public StreamingService getStreamingService() {
        return streamingService;
    }

    /**
     * 获取TV预览助手实例（高级功能）
     * <p>
     * 此方法用于高级用户直接访问TV预览助手进行深度定制。
     * 如果TV预览助手尚未初始化，此方法会自动初始化它。
     * </p>
     *
     * @return TvPreviewHelper实例，如果TV容器未设置则返回null
     */
    public TvPreviewHelper getTvPreviewHelper() {
        initTvPreviewHelperIfNeeded();
        return tvPreviewHelper;
    }

    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "释放视频系统资源");

        // 停止录制
        if (isRecording) {
            stopRecording();
        }

        // 停止推流 - 按照VideoEncoderActivity的模式
        if (streamingService != null && streamingService.isStreaming()) {
            try {
                streamingService.stopRtspManually();
            } catch (Exception e) {
                Log.w(TAG, "停止推流服务时出现异常", e);
            }
        }

        // 释放相机资源
        if (cameraManagerHelper != null) {
            cameraManagerHelper.releaseCamera();
            cameraManagerHelper = null;
        }

        // 释放视频编码器
        if (videoEncoder != null) {
            videoEncoder.release();
            videoEncoder = null;
        }

        // 释放TV预览助手
        if (tvPreviewHelper != null) {
            tvPreviewHelper.release();
            tvPreviewHelper = null;
        }

        // 重置状态
        isInitialized = false;
        isCameraStarted = false;
        isRecording = false;
        isStreaming = false;
        isTvMode = false;

        Log.d(TAG, "视频系统资源释放完成");
    }

    // ===== 私有实现方法 =====

    /**
     * 初始化图像捕获助手
     */
    private void initCaptureHelper() {
        captureImageHelper = CaptureImageHelper.builder(new Size(3840, 2160))
                .onImageSaved(filePath -> {
                    Log.d(TAG, "图片已保存: " + filePath);
                    if (listener != null) {
                        listener.onImageCaptured(filePath);
                    }
                })
                .onError(errorMessage -> {
                    Log.e(TAG, "图片捕获失败: " + errorMessage);
                    if (listener != null) {
                        listener.onError("图片捕获失败: " + errorMessage);
                    }
                })
                .build();
    }

    /**
     * 初始化视频编码器
     */
    private void initVideoEncoder(Surface textureSurface) {
        Log.d(TAG, "初始化视频编码器，使用TpVideoConfig: " + videoConfig.toString());

        videoEncoder = VideoEncoder.builder()
            .setPreviewSurface(textureSurface)
            .setTpVideoConfig(videoConfig)
            .onSurfaceAvailable(encoderSurface -> {
                // 使用与VideoEncoderActivity相同的相机初始化流程
                cameraManagerHelper = CameraManagerHelper.builder(context)
                    .onCameraOpened(camera -> {
                        // 相机打开后配置输出
                        cameraManagerHelper.configCameraOutputs(
                            camera,
                            encoderSurface,
                            captureImageHelper.getImageReader().getSurface()
                        );

                        // 初始化推流服务
                        initializeStreamingService();

                        isCameraStarted = true;
                        Log.d(TAG, "相机启动完成，使用配置: " + videoConfig.toString());

                        if (listener != null) {
                            listener.onCameraStarted();
                        }
                    })
                    .onCameraDisconnected(camera -> {
                        camera.close();
                        isCameraStarted = false;
                        Log.d(TAG, "相机已断开");
                    })
                    .onCameraError((camera, error) -> {
                        camera.close();
                        isCameraStarted = false;
                        Log.e(TAG, "相机错误: " + error);
                        if (listener != null) {
                            listener.onError("相机错误: " + error);
                        }
                    })
                    .build();

                // 打开相机
                cameraManagerHelper.openCamera();
            })
            .onStorageFull(() -> {
                Log.w(TAG, "存储空间不足，录制已停止");
                stopRecording();
                if (listener != null) {
                    listener.onError("存储空间不足，录制已停止");
                }
            })
            .onError((errorType, e) -> {
                Log.e(TAG, "录制错误: " + errorType, e);
                stopRecording();
                if (listener != null) {
                    listener.onError("录制错误: " + errorType);
                }
            })
            .onFileSizeLimitReached(() -> {
                Log.w(TAG, "文件大小达到限制，录制已停止");
                stopRecording();
                if (listener != null) {
                    listener.onError("文件大小达到限制，录制已停止");
                }
            })
            .onSaveComplete(filePath -> {
                Log.d(TAG, "录制完成: " + filePath);
                isRecording = false;
                if (listener != null) {
                    listener.onRecordingStopped(filePath);
                }
            })
            .build();
    }

    /**
     * 第一步：提前初始化StreamingService（在构造函数中调用）
     */
    private void initStreamingServiceEarly() {
        // 如果没有Activity，跳过推流服务初始化
        if (activity == null) {
            Log.w(TAG, "跳过推流服务初始化：没有Activity实例");
            return;
        }

        try {
            Log.d(TAG, "初始化StreamingService...");

            // 按照VideoEncoderActivity的模式：在onCreate中调用initCore
            streamingService = StreamingService.initCore(activity, createHeartbeatListener());

            Log.d(TAG, "StreamingService初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "StreamingService初始化失败", e);
            streamingService = null;
        }
    }

    /**
     * 第二步：完成推流服务初始化
     * 完全按照VideoEncoderActivity.setupHeartbeatServiceComponents()的模式
     */
    private void initializeStreamingService() {
        // 如果没有Activity或StreamingService，跳过
        if (activity == null || streamingService == null) {
            Log.w(TAG, "跳过推流服务完成初始化：Activity或StreamingService不可用");
            return;
        }

        try {
            Log.d(TAG, "完成StreamingService初始化...");

            // 按照VideoEncoderActivity的确切模式，使用当前设置的流类型
            streamingService.initStreaming(
                videoEncoder,
                captureImageHelper,
                currentStreamType
            );

            // 按照VideoEncoderActivity的模式：启动服务
            streamingService.startService();

            Log.d(TAG, "StreamingService完整初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "StreamingService完成初始化失败", e);
            if (listener != null) {
                listener.onError("推流服务初始化失败: " + e.getMessage());
            }
        }
    }

    /**
     * 创建HeartbeatListener
     * 完全按照VideoEncoderActivity.createHeartbeatListener()的模式
     */
    private StreamingService.HeartbeatListener createHeartbeatListener() {
        return new StreamingService.HeartbeatListener() {
            @Override
            public void onStreamStatusChanged(boolean isStreaming, String url) {
                // 通知外部监听器，让MainActivity更新UI状态
                if (listener != null) {
                    listener.onStreamingStatusChanged(isStreaming, url);
                }
            }

            @Override
            public void onStreamError(String errorMessage) {
                if (listener != null) {
                    listener.onError("推流错误: " + errorMessage);
                }
            }
        };
    }







    // ===== 内层专业API组件类 =====

    /**
     * VideoRecorder - 专门的视频录制类（package-private）
     * 提供高级的视频录制功能和配置选项
     */
    class VideoRecorder {

        /**
         * 开始录制
         * @param outputPath 输出路径
         */
        void startRecording(String outputPath) {
            if (outputPath == null) {
                outputPath = FileStorageUtils.createVideoPath(context);
            }

            try {
                videoEncoder.startRecording(outputPath);
                isRecording = true;
                Log.d(TAG, "开始录制: " + outputPath);

                if (listener != null) {
                    listener.onRecordingStarted(outputPath);
                }
            } catch (Exception e) {
                Log.e(TAG, "开始录制失败", e);
                if (listener != null) {
                    listener.onError("开始录制失败: " + e.getMessage());
                }
            }
        }

        /**
         * 停止录制
         */
        void stopRecording() {
            try {
                videoEncoder.stopRecording();
                // isRecording状态会在onSaveComplete回调中设置为false
                Log.d(TAG, "停止录制");
            } catch (Exception e) {
                Log.e(TAG, "停止录制失败", e);
                isRecording = false;
                if (listener != null) {
                    listener.onError("停止录制失败: " + e.getMessage());
                }
            }
        }

        // ===== 高级功能方法（20%客户使用） =====


        /**
         * 获取录制状态
         */
        public boolean isRecording() {
            return TpVideoSystem.this.isRecording;
        }

        /**
         * 获取详细的录制状态
         */
        public String getDetailedStatus() {
            return String.format("Recording: %s, Camera: %s, Config: %s",
                isRecording, isCameraStarted, videoConfig.toString());
        }
    }

    /**
     * ImageCapture - 专门的图像捕获类（package-private）
     * 提供高级的图像捕获功能和处理选项
     */
    class ImageCapture {

        /**
         * 捕获图像
         * @param outputPath 输出路径
         */
        void captureImage(String outputPath) {
            if (outputPath == null) {
                outputPath = FileStorageUtils.createImagePath(context, "capture", true, "jpg");
            }

            try {
                Size imageSize = new Size(3840, 2160);
                captureImageHelper.requestCapture(imageSize, outputPath);
                Log.d(TAG, "请求捕获图像: " + outputPath);
            } catch (Exception e) {
                Log.e(TAG, "捕获图像失败", e);
                if (listener != null) {
                    listener.onError("捕获图像失败: " + e.getMessage());
                }
            }
        }

        // ===== 高级功能方法（20%客户使用） =====

        /**
         * 设置图像质量
         * @param quality 质量等级 (1-100)
         */
        public void setImageQuality(int quality) {
            Log.d(TAG, "设置图像质量: " + quality);
            // 这里可以添加图像质量设置逻辑
        }

        /**
         * 批量捕获图像
         * @param count 捕获数量
         * @param outputDir 输出目录
         */
        public void captureBurst(int count, String outputDir) {
            Log.d(TAG, "批量捕获图像: " + count + " 张");
            for (int i = 0; i < count; i++) {
                String outputPath = outputDir + "/capture_" + System.currentTimeMillis() + "_" + i + ".jpg";
                captureImage(outputPath);

                // 添加延迟避免文件名冲突
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    // ===== TV模式私有实现方法 =====

    /**
     * 初始化TV预览助手（懒加载）
     */
    private void initTvPreviewHelperIfNeeded() {
        if (tvPreviewHelper == null && tvContainer != null) {
            tvPreviewHelper = new TvPreviewHelper(context, tvContainer);
            Log.d(TAG, "TV预览助手已初始化");
        }
    }

    /**
     * 启动TV预览
     */
    private void startTvPreview() {
        Log.d(TAG, "启动TV预览");

        // 确保TV预览助手已初始化
        initTvPreviewHelperIfNeeded();

        if (tvPreviewHelper == null) {
            throw new IllegalStateException("TV预览助手未初始化，请先调用setTvContainer()设置TV容器");
        }

        // 启动TV预览
        tvPreviewHelper.startPreview();
        Log.d(TAG, "TV预览已启动");
    }

    /**
     * 停止TV预览
     */
    private void stopTvPreview() {
        Log.d(TAG, "停止TV预览");

        if (tvPreviewHelper != null) {
            tvPreviewHelper.stopPreview();
            Log.d(TAG, "TV预览已停止");
        }
    }

    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        Log.d(TAG, "停止相机预览");

        // 如果正在录制，先停止录制
        if (isRecording) {
            stopRecording();
        }

        // 释放相机资源
        if (cameraManagerHelper != null) {
            cameraManagerHelper.releaseCamera();
            isCameraStarted = false;
        }

        Log.d(TAG, "相机预览已停止");
    }

    /**
     * 启动相机预览
     */
    private void startCameraPreview() {
        Log.d(TAG, "启动相机预览");

        if (!isInitialized) {
            throw new IllegalStateException("系统未初始化，无法启动相机预览");
        }

        // 重新启动相机
        if (!isCameraStarted && cameraManagerHelper != null) {
            try {
                Log.d(TAG, "重新启动相机");

                // 调用openCamera重新启动相机
                cameraManagerHelper.openCamera();
                isCameraStarted = true;

                Log.d(TAG, "相机预览已重新启动");

                if (listener != null) {
                    listener.onCameraStarted();
                }
            } catch (Exception e) {
                Log.e(TAG, "重新启动相机预览失败", e);
                throw new IllegalStateException("重新启动相机预览失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 通知TV模式状态变化
     */
    private void notifyTvModeChanged(boolean isTvMode) {
        if (listener != null) {
            listener.onTvModeChanged(isTvMode);
        }
    }

    // ===== 推流功能内部辅助方法 =====

    /**
     * 执行推流操作（内部线程管理）
     */
    private boolean executeStreamingOperation(StreamingOperation operation) {
        try {
            // 直接执行操作（StreamingService内部已处理线程管理）
            return operation.execute();
        } catch (Exception e) {
            Log.e(TAG, "推流操作失败", e);
            if (listener != null) {
                listener.onError("推流操作失败: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * 转换流类型（外部API -> 内部API）
     */
    private StreamingService.StreamType convertStreamType(StreamType streamType) {
        return streamType == StreamType.CAMERA ?
            StreamingService.StreamType.CAMERA :
            StreamingService.StreamType.SCREEN;
    }

    /**
     * 从内部流类型转换（内部API -> 外部API）
     */
    private StreamType convertFromInternalStreamType(StreamingService.StreamType internalType) {
        return internalType == StreamingService.StreamType.CAMERA ?
            StreamType.CAMERA :
            StreamType.SCREEN;
    }

    /**
     * 推流操作函数式接口
     */
    @FunctionalInterface
    private interface StreamingOperation {
        boolean execute();
    }
}
