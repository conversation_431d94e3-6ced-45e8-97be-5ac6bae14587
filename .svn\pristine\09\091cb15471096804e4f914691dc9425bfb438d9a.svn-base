package com.android.rockchip.camera2;  // 请确保包名与项目一致

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class BootBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        // 确保接收到的广播是 BOOT_COMPLETED
        if (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)) {
            Log.d("BootBroadcastReceiver", "Boot completed, starting the app.");

            // 启动应用的主活动
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName("com.android.rockchip.camera2", "com.android.rockchip.camera2.activity.MainActivity"));
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // 必须设置为新的任务
            context.startActivity(launchIntent);
        }
    }
}