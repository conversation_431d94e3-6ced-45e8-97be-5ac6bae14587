package com.android.rockchip.camera2.activity.browse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.android.rockchip.camera2.R
import com.bumptech.glide.Glide
import java.io.File

class TpThumbGridAdapter(
    private var imageFiles: List<File>,  // 改为可变文件列表
    private var labels: List<String>,    // 改为可变标签列表
    private val onClick: (Int) -> Unit,
    private val onDoubleClick: (Int) -> Unit

) : RecyclerView.Adapter<TpThumbGridAdapter.ViewHolder>() {

    private val selectedItems = mutableSetOf<Int>()

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imageView: ImageView = view.findViewById(R.id.grid_image)
        val labelView: TextView = view.findViewById(R.id.grid_label)
        val selectedOverlay: View = view.findViewById(R.id.selected_overlay)

        var lastClickTime: Long = 0
        var pendingClickRunnable: Runnable? = null
    }

    // 新的updateData方法（支持文件加载）
    fun updateData(newFiles: List<File>, newLabels: List<String>) {
        imageFiles = newFiles.toMutableList() //toMutableList将list改为一个可变集合
        labels = newLabels.toMutableList()

        if(imageFiles.isNotEmpty()) {
            notifyDataSetChanged()
            val previousSelected = selectedItems.toList()
            selectedItems.clear()
            previousSelected.forEach { notifyItemChanged(it) }
        }
        else
        {
            selectedItems.clear()
            clearData()
        }

//        // 检查数据是否有效
//        if (newFiles.isEmpty() || newLabels.isEmpty()) {
//            clearData() // 清空 Adapter
//            return
//        }
//
//        // 过滤掉无效文件（U盘拔出后路径失效）
//        val validFiles = newFiles.filter { it.exists() && it.canRead() }
//        if (validFiles.isEmpty()) {
//            clearData()
//            return
//        }
//
//        imageFiles = validFiles.toMutableList()
//        labels = newLabels.toMutableList()
//
//        // 清理无效的选中项
//        selectedItems.removeAll { it >= imageFiles.size }
//        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.browse_grid_layout, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        // 使用Glide加载本地文件
        Glide.with(holder.itemView.context)
            .load(imageFiles[position])
            .into(holder.imageView)

        // 标签显示处理
        holder.labelView.text = labels.getOrElse(position) { "" }
        holder.itemView.setOnClickListener { onClick(position) }
        holder.selectedOverlay.visibility =
            if (selectedItems.contains(position)) View.VISIBLE else View.INVISIBLE

        //单击事件
        holder.itemView.setOnClickListener {

            holder.pendingClickRunnable?.let { runnable ->
                holder.itemView.removeCallbacks(runnable)
                holder.pendingClickRunnable = null
            }

            val currentTime = System.currentTimeMillis()
            if (currentTime - holder.lastClickTime < 300) { // 300ms 内视为双击
                holder.lastClickTime = 0
                onDoubleClick(position) // 触发双击回调
            } else {
                holder.lastClickTime = currentTime
                holder.pendingClickRunnable = Runnable {
                    // 单击处理
                    toggleSelection(position)
                    onClick(position)
                    holder.pendingClickRunnable = null
                }
                holder.itemView.postDelayed(holder.pendingClickRunnable!!, 300)
            }

        }

        if (imageFiles.isEmpty()) {
            holder.imageView.setImageResource(R.drawable.ic_close)
            holder.labelView.text = "暂无图片"
            return
        }

    }

    private fun toggleSelection(position: Int) {
        if (selectedItems.contains(position)) {
            selectedItems.remove(position)
        } else {
            selectedItems.add(position)
        }
        notifyItemChanged(position)
    }

    override fun getItemCount(): Int {
        return imageFiles.size
    }

    fun isPositionSelected(position: Int): Boolean {
        return selectedItems.contains(position)
    }

    fun clearData() {
        imageFiles = emptyList()
        labels = emptyList()
        selectedItems.clear()
        // 直接通知整个数据集变化
        notifyDataSetChanged()
    }

}