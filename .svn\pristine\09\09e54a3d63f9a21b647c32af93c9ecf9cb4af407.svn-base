package com.android.rockchip.camera2.activity;

import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;

import com.android.rockchip.camera2.databinding.ActivityMainBinding;
import com.android.rockchip.camera2.util.touptek_serial_rk;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;

    private touptek_serial_rk touptek_serial_rk = new touptek_serial_rk();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        TextView tv = binding.sampleText;

        // 初始化串口
//        touptek_serial_rk.initializeSerial(921600);
        touptek_serial_rk.startMonitor();
        Log.d("MainActivity", "1111");

        int[] data = {0x00, 0x00, 0x00, 0x00};
        touptek_serial_rk.isSerialConnected();

        // 发送第一次命令
//        touptek_serial_rk.sendCommandToSerial(0x00, 0x00, data);

        // 设置按钮点击事件监听器
        Button sampleButton = binding.sampleButton; // 获取按钮
        sampleButton.setOnClickListener(v -> {
            // 每次点击按钮时发送一次命令
            touptek_serial_rk.sendCommandToSerial(0x00, 0x00, data);
            Log.d("MainActivity", "Button clicked, command sent!");
        });

        // 关闭串口代码可以保留注释掉，直到需要时调用
        // touptek_serial_rk.closeSerial();
    }

    /**
     * A native method that is implemented by the 'camera2' native library,
     * which is packaged with this application.
     */
    // public native String stringFromJNI();
}
