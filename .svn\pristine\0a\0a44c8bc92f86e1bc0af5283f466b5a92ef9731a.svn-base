package com.android.rockchip.camera2.service;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.android.rockchip.camera2.rtsp.RTSPManager;
import com.android.rockchip.camera2.util.FileStorageUtils;
import com.android.rockchip.camera2.util.TouptekIspParam;
import com.android.rockchip.camera2.video.CaptureImageHelper;
import com.android.rockchip.camera2.video.VideoEncoder;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Tpctrl心跳检测服务
 * 
 * <p>此服务专门用于检测tpctrl进程的心跳包，当检测到心跳包时自动启动：</p>
 * <ul>
 *   <li>RTSP推流服务（支持摄像头流和屏幕流切换）</li>
 *   <li>TpctrlSocketService图像服务</li>
 * </ul>
 * 
 * <p>集成了RTSP管理功能，提供统一的流类型控制接口</p>
 * 
 * <p>使用方法（两步初始化）：</p>
 * <pre>{@code
 *  第一步：在Activity的onCreate()中调用，初始化不依赖其他组件的部分
 * TpctrlService service = TpctrlService.createEarlyInstance(this, listener);
 * 
 *  第二步：在VideoEncoder和CaptureImageHelper准备好后调用，完成初始化
 * service.completeInitialization(videoEncoder, captureImageHelper);
 * service.start(); // 启动服务
 * }</pre>
 * 
 * <div style="background-color:#f8f8f8;padding:8px;margin:8px 0;">
 * <strong>注意：</strong>需要在模块的build.gradle中导入以下依赖：<br>
 * implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")<br>
 * implementation("com.github.pedroSG94:RTSP-Server:1.2.1")
 * 使用屏幕推流需要在AndroidManifest中做声明
 *    要加入:service
 *       android:name="com.android.rockchip.camera2.rtsp.service.RTSPService"
 *       android:enabled="true"
 *       android:exported="false"
 *       android:foregroundServiceType="mediaProjection"
 *       android:stopWithTask="false" />
 * </div>
 */
public class TpctrlService 
{
    private static final String TAG = "TpctrlService";
    
    /* 心跳检测超时时间（毫秒） */
    private static final long HEARTBEAT_TIMEOUT = 1500; // 1.5秒
    
    /* 心跳包监听端口号 - 专用于心跳检测 */
    private static final int SOCKET_PORT = 12346;
    
    /* ISP参数服务端口号 */
    private static final int ISP_PARAM_PORT = 12347;

    /* TpctrlSocketService使用的图像服务端口号 */
    private static final int IMAGE_PORT = 12345;

    /* 心跳包命令码 */
    private static final int CMD_HEARTBEAT = 2;
    
    /* ISP 参数配置命令码 - 发送最大值、最小值、默认值等配置信息 */
    private static final int CMD_ISP_PARAM_CONFIG = 4;
    
    /* tpctrl的IP地址 */
    private static final String TPCTRL_IP = "127.0.0.1";
    
    /* 原子状态变量 */
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean servicesStarted = new AtomicBoolean(false);
    private final AtomicBoolean tpctrlExists = new AtomicBoolean(false);
    private final AtomicBoolean socketServerRunning = new AtomicBoolean(false);
    private final AtomicLong lastHeartbeatTime = new AtomicLong(0);
    
    /* 初始化状态标记 */
    private final AtomicBoolean earlyInitialized = new AtomicBoolean(false);
    private final AtomicBoolean fullyInitialized = new AtomicBoolean(false);
    
    /* 线程池 */
    private ExecutorService executorService;
    private ExecutorService socketExecutor;
    
    /* Socket服务器相关 */
    private ServerSocket serverSocket;
    private Thread socketServerThread;
    
    /* 主线程Handler */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /* 应用上下文 */
    private final Context context;
    
    /* Activity引用 */
    private AppCompatActivity activity;
    
    /* RTSP管理器 */
    private RTSPManager rtspManager;
    
    /* 视频编码器 */
    private VideoEncoder videoEncoder;
    
    /* 抓图助手 */
    private CaptureImageHelper captureImageHelper;
    
    /* 图像Socket服务 */
    private TpctrlSocketService tpctrlSocketService;
    
    /* 当前流类型 */
    private StreamType currentStreamType = StreamType.CAMERA;
    
    /* 重试计数器 */
    private int retryCount = 0;
    
    /* 心跳超时时间配置 */
    private final long heartbeatTimeout;
    
    /* 端口配置 */
    private final int socketPort;
    private final int ispPort;

    private HeartbeatListener heartbeatListener;
    
    /**
     * 流类型枚举
     */
    public enum StreamType 
    {
        /** 摄像头流 */
        CAMERA,
        /** 屏幕流 */
        SCREEN
    }
    
    /* 回调接口 */
    public interface HeartbeatListener 
    {
        /**
         * 检测到tpctrl进程时回调
         */
        void onTpctrlDetected();
        
        /**
         * 服务已启动时回调
         */
        void onServicesStarted();
        
        /**
         * tpctrl进程消失时回调
         */
        void onTpctrlLost();
        
        /**
         * 服务已停止时回调
         */
        void onServicesStopped();
        
        /**
         * RTSP推流状态变化回调
         */
        void onStreamStatusChanged(boolean isStreaming, String url);
        
        /**
         * RTSP推流错误回调
         */
        void onStreamError(String errorMessage);
    }
    
    /**
     * 私有构造函数
     */
    private TpctrlService(Context context, AppCompatActivity activity, HeartbeatListener listener) 
    {
        this.context = context.getApplicationContext();
        this.activity = activity;
        this.heartbeatListener = listener;
        this.heartbeatTimeout = HEARTBEAT_TIMEOUT;
        this.socketPort = SOCKET_PORT;
        this.ispPort = ISP_PARAM_PORT;
    }

    /**
     * 第一步：创建早期实例（必须在Activity的onCreate()中调用）
     * 
     * <p><strong> 重要：必须在Activity的onCreate()方法中调用此方法！</strong></p>
     * <p>这是因为RTSPManager需要注册ActivityResultLauncher，而这必须在Activity进入STARTED状态之前完成。</p>
     * 
     * <p>此方法会初始化：</p>
     * <ul>
     *   <li>基本配置和Context</li>
     *   <li>RTSPManager（需要registerForActivityResult）</li>
     *   <li>HeartbeatListener</li>
     * </ul>
     * 
     * @param activity Activity实例，用于RTSP屏幕推流权限申请
     * @param listener 心跳状态监听器
     * @return TpctrlService实例（尚未完全初始化）
     * 
     * @throws IllegalStateException 如果重复调用或Activity为null
     * 
     * @see #completeInitialization(VideoEncoder, CaptureImageHelper)
     */
    public static TpctrlService createEarlyInstance(AppCompatActivity activity, 
                                                   HeartbeatListener listener) 
    {
        if (activity == null) 
        {
            throw new IllegalArgumentException("Activity cannot be null");
        }
        if (listener == null) 
        {
            throw new IllegalArgumentException("HeartbeatListener cannot be null");
        }
        
        Log.d(TAG, "Step 1: Creating early instance of TpctrlService...");
        
        TpctrlService service = new TpctrlService(activity, activity, listener);
        
        try 
        {
            /* 早期初始化RTSPManager（必须在onCreate中完成）*/
            service.initRtspManagerEarly();
            service.earlyInitialized.set(true);
            
            Log.d(TAG, "TpctrlService early initialization completed");
            return service;
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "TpctrlService early initialization failed", e);
            throw new RuntimeException("Early initialization failed", e);
        }
    }

    /**
     * 第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）
     * 
     * <p><strong>⚠️ 重要：必须在VideoEncoder和CaptureImageHelper完全准备好后调用此方法！</strong></p>
     * <p>通常在视频编码器初始化完成回调中调用。</p>
     * 
     * <p>此方法会初始化：</p>
     * <ul>
     *   <li>VideoEncoder配置</li>
     *   <li>CaptureImageHelper配置</li>
     *   <li>RTSP回调设置</li>
     * </ul>
     * 
     * @param videoEncoder 视频编码器实例
     * @param captureImageHelper 抓图助手实例
     * @param streamType 初始流类型（可选，默认为CAMERA）
     * 
     * @throws IllegalStateException 如果早期初始化未完成或重复调用
     * @throws IllegalArgumentException 如果必需参数为null
     * 
     * @see #createEarlyInstance(AppCompatActivity, HeartbeatListener)
     */
    public void completeInitialization(VideoEncoder videoEncoder, 
                                     CaptureImageHelper captureImageHelper,
                                     StreamType streamType) 
    {
        if (!earlyInitialized.get()) 
        {
            throw new IllegalStateException("Must call createEarlyInstance() first in Activity.onCreate()");
        }
        if (captureImageHelper == null) 
        {
            throw new IllegalArgumentException("CaptureImageHelper cannot be null");
        }
        
        Log.d(TAG, "Step 2: Completing TpctrlService initialization...");
        
        this.videoEncoder = videoEncoder;
        this.captureImageHelper = captureImageHelper;
        this.currentStreamType = streamType != null ? streamType : StreamType.CAMERA;

        
        try 
        {
            /* 完成RTSP配置 */
            completeRtspSetup();
            
            /* 设置视频编码器 */
            setupVideoEncoder();
            
            fullyInitialized.set(true);
            
            Log.d(TAG, "TpctrlService fully initialized, ready to call start()");
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "TpctrlService complete initialization failed", e);
            throw new RuntimeException("Complete initialization failed", e);
        }
    }
    
    /**
     * 便捷的完成初始化方法（默认使用CAMERA流类型）
     */
    public void completeInitialization(VideoEncoder videoEncoder, 
                                     CaptureImageHelper captureImageHelper) 
    {
        completeInitialization(videoEncoder, captureImageHelper, StreamType.CAMERA);
    }

    /**
     * 早期初始化RTSPManager（在onCreate中调用）
     */
    private void initRtspManagerEarly() 
    {
        if (activity == null) 
        {
            throw new IllegalStateException("Activity is null, cannot initialize RTSPManager");
        }
        
        Log.d(TAG, "Early initialization of RTSPManager...");
        
        rtspManager = RTSPManager.getInstance().initialize(activity);
        
        Log.d(TAG, "RTSPManager initialized successfully in onCreate");
    }

    /**
     * 完成RTSP配置（在第二步初始化中调用）
     */
    private void completeRtspSetup() 
    {
        if (rtspManager == null) 
        {
            throw new IllegalStateException("RTSPManager not initialized, must call createEarlyInstance() first");
        }
        
        Log.d(TAG, "Setting up RTSP callbacks...");
        
        rtspManager.onStreamStarted(url -> 
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamStatusChanged(true, url));
                }
                Log.d(TAG, "RTSP stream started: " + url);
            })
            .onStreamStopped(() -> 
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamStatusChanged(false, null));
                }
                Log.d(TAG, "RTSP stream stopped");
            })
            .onStreamError(errorMessage -> 
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamError(errorMessage));
                }
                Log.e(TAG, "RTSP stream error: " + errorMessage);
            })
            .onPermissionGranted(message -> 
            {
                Log.d(TAG, "RTSP permission granted: " + message);
                /* 如果当前是屏幕流类型，且服务已启动，权限获取后开始推流 */
                if (currentStreamType == StreamType.SCREEN && servicesStarted.get()) 
                {
                    /* 权限获取成功后，延迟一小段时间再开始推流 */
                    mainHandler.postDelayed(this::startRtspService, 500);
                }
            });
    }

    /**
     * 启动tpctrl控制台进程（简化版）
     * 
     * @param netInterface 网络接口名称，例如"wlan0"
     * @return 是否成功启动
     */
    public boolean startTpctrlConsole(String netInterface) 
    {
        try 
        {
            /* 构建命令 */
//            String command = "/data/touptek/tpctrl/tpctrl  --console --netname " + netInterface + "--ap";
            String command = "/data/touptek/tpctrl/tpctrl --console --ap --netname " + netInterface;
            /* 直接执行命令 */
            Process process = Runtime.getRuntime().exec(command);
            
            /* 获取退出码（如果需要等待命令完成） */
            // int exitCode = process.waitFor();
            // return exitCode == 0;
            
            /* 不等待命令完成，假定启动成功 */
            return true;
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting tpctrl: " + e.getMessage());
            return false;
        }
    }

   /**
     * 更新视频编码器并重置RTSP会话
     * 
     * 当视频编码器实例发生变化时(例如HDMI断开后重连)，
     * 使用此方法更新RTSPManager中的视频编码器引用并重置RTSP会话。
     * 
     * @param newEncoder 新的视频编码器实例
     */
    public void updateVideoEncoder(VideoEncoder newEncoder) {
        if (newEncoder == null) {
            Log.e(TAG, "Cannot update with null VideoEncoder");
            return;
        }
        
        this.videoEncoder = newEncoder;
        
        // 更新RTSPManager中的VideoEncoder引用
        if (rtspManager != null) {
            rtspManager.setVideoEncoder(newEncoder);
            Log.d(TAG, "Updated VideoEncoder in RTSPManager");
            
            // 请求一个初始关键帧
            newEncoder.requestKeyFrame();
            
            // 检查RTSP是否正在运行，且当前是摄像头模式(CAMERA)
            // 屏幕推流模式下不需要重置RTSP会话
            if (isStreaming() && servicesStarted.get() && currentStreamType == StreamType.CAMERA) {
                Log.d(TAG, "HDMI重连后执行RTSP会话完全重置...");
                
                // 保存当前流类型
                final StreamType currentType = getCurrentStreamType();
                
                // 完全停止当前流
                rtspManager.stopStreaming();
                
                // 延迟后使用相同类型重新启动流
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    if (isRunning.get() && servicesStarted.get()) {
                        // 设置新的流类型（与当前相同，只是为了触发重置）
                        RTSPManager.StreamType rtspStreamType = currentType == StreamType.CAMERA ? 
                            RTSPManager.StreamType.CAMERA : RTSPManager.StreamType.SCREEN;
                        rtspManager.setStreamType(rtspStreamType);
                        
                        // 重新启动流
                        rtspManager.startStreaming();
                        Log.d(TAG, "HDMI重连后RTSP会话已完全重置");
                        
                        // 再次请求关键帧确保正常启动
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            newEncoder.requestKeyFrame();
                            Log.d(TAG, "RTSP重置后发送额外关键帧");
                        }, 300);
                    }
                }, 500);
            } else if (currentStreamType == StreamType.SCREEN) {
                Log.d(TAG, "当前为屏幕推流模式，HDMI重连不影响推流，跳过RTSP会话重置");
                
                // 对于屏幕模式，只需更新编码器引用，不需要重置RTSP会话
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    newEncoder.requestKeyFrame();
                    Log.d(TAG, "屏幕推流模式下请求关键帧");
                }, 500);
            } else {
                // 如果RTSP未运行，只需请求一个关键帧即可
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    newEncoder.requestKeyFrame();
                    Log.d(TAG, "Requested key frame after HDMI reconnection");
                }, 500);
            }
        }
    }

    /**
     * 设置视频编码器到RTSP管理器
     */
    public void setupVideoEncoder() 
    {
        if (rtspManager != null && videoEncoder != null) 
        {
            rtspManager.setVideoEncoder(videoEncoder);
            Log.d(TAG, "VideoEncoder set to RTSPManager");
        }
    }

    /**
     * 启动心跳检测服务
     * 
     * <p><strong>重要：必须在completeInitialization()之后调用！</strong></p>
     */
    public void start() 
    {
        if (!fullyInitialized.get()) 
        {
            throw new IllegalStateException("Service not fully initialized. Call completeInitialization() first.");
        }
        
        if (isRunning.get()) 
        {
            Log.w(TAG, "Service is already running");
            return;
        }
        
        Log.d(TAG, "Starting tpctrl heartbeat service");
        isRunning.set(true);
        servicesStarted.set(false);
        tpctrlExists.set(false);
        lastHeartbeatTime.set(0);
        
        executorService = Executors.newSingleThreadExecutor();
        socketExecutor = Executors.newCachedThreadPool();
        
        /* 启动Socket服务器 */
        startSocketServer();
        
        /* 启动心跳检测线程 */
        executorService.submit(this::monitorHeartBeat);
        startTpctrlConsole(getActiveNetworkInterface());
        
        Log.d(TAG, "TpctrlService started successfully");
    }

    /**
     * 检测当前活跃的网络接口
     * <p>
     * 此方法会检测当前设备上活跃的网络接口，优先级为：
     * 1. 以太网（eth0）
     * 2. WiFi（wlan0）
     * 3. 移动数据（rmnet0等）
     * </p>
     * 
     * @return 活跃的网络接口名称，如果没有活跃接口则返回默认值"wlan0"
     */
    public String getActiveNetworkInterface() 
    {
        String defaultInterface = "wlan0"; // 默认接口
        
        try 
        {
            /* 使用NetworkInterface API获取所有网络接口 */
            java.util.Enumeration<java.net.NetworkInterface> networkInterfaces = java.net.NetworkInterface.getNetworkInterfaces();
            
            /* 按优先级存储找到的接口 */
            String ethInterface = null;
            String wlanInterface = null;
            String cellularInterface = null;
            
            /* 遍历所有网络接口 */
            while (networkInterfaces.hasMoreElements()) 
            {
                java.net.NetworkInterface networkInterface = networkInterfaces.nextElement();
                String interfaceName = networkInterface.getName();
                
                /* 跳过非活跃或回环接口 */
                if (!networkInterface.isUp() || networkInterface.isLoopback()) 
                {
                    continue;
                }
                
                /* 检查是否有可用IP地址 */
                boolean hasIpv4 = false;
                java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) 
                {
                    java.net.InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) 
                    {
                        hasIpv4 = true;
                        break;
                    }
                }
                
                if (!hasIpv4) 
                {
                    continue; // 跳过没有IPv4地址的接口
                }
                
                /* 根据接口名称确定类型 */
                if (interfaceName.startsWith("eth")) 
                {
                    /* 以太网接口 */
                    ethInterface = interfaceName;
                    Log.d(TAG, "Found active Ethernet interface: " + interfaceName);
                } 
                else if (interfaceName.startsWith("wlan")) 
                {
                    /* WiFi接口 */
                    wlanInterface = interfaceName;
                    Log.d(TAG, "Found active WiFi interface: " + interfaceName);
                } 
                else if (interfaceName.startsWith("rmnet") || interfaceName.startsWith("ccmni") || 
                        interfaceName.startsWith("pdp") || interfaceName.startsWith("data")) 
                {
                    /* 移动数据接口 */
                    cellularInterface = interfaceName;
                    Log.d(TAG, "Found active cellular interface: " + interfaceName);
                }
            }
            
            /* 按优先级返回接口名称 */
            if (ethInterface != null) 
            {
                return ethInterface; // 优先使用以太网
            } 
            else if (wlanInterface != null) 
            {
                return wlanInterface; // 其次使用WiFi
            } 
            else if (cellularInterface != null) 
            {
                return cellularInterface; // 最后使用移动数据
            }
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error detecting network interface: " + e.getMessage());
        }
        
        /* 如果没有检测到活跃接口，返回默认值 */
        Log.d(TAG, "No active network interface found, using default: " + defaultInterface);
        return defaultInterface;
    }

    /**
     * 设置流类型
     */
    public void setStreamType(StreamType streamType) 
    {
        if (currentStreamType == streamType) 
        {
            return;
        }
        
        Log.d(TAG, "Switching stream type from " + currentStreamType + " to " + streamType);
        currentStreamType = streamType;
        
        /* 如果服务已启动，重新配置RTSP流类型 */
        if (servicesStarted.get() && rtspManager != null) 
        {
            /* 停止当前推流 */
            if (rtspManager.isStreaming()) 
            {
                rtspManager.stopStreaming();
            }
            
            /* 设置新的流类型 */
            RTSPManager.StreamType rtspStreamType = streamType == StreamType.CAMERA ? 
                RTSPManager.StreamType.CAMERA : RTSPManager.StreamType.SCREEN;
            rtspManager.setStreamType(rtspStreamType);
            
            if (streamType == StreamType.SCREEN) 
            {
                /* 对于屏幕流，先请求权限 */
                Log.d(TAG, "Requesting screen capture permission");
                rtspManager.requestScreenPermission();
                /* 权限回调中会处理启动推流 */
            } 
            else 
            {
                /* 摄像头流直接启动 */
                startRtspService();
            }
        }
    }

    
    /**
     * 获取当前推流类型
     */
    public StreamType getCurrentStreamType() 
    {
        return currentStreamType;
    }
    
    /**
     * 手动切换流类型
     */
    public void switchStreamType() 
    {
        StreamType newType = currentStreamType == StreamType.CAMERA ? 
            StreamType.SCREEN : StreamType.CAMERA;
        setStreamType(newType);
    }
    
    /**
     * 停止心跳检测服务
     */
    public void stop() 
    {
        if (!isRunning.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Stopping tpctrl heartbeat service");
        isRunning.set(false);
        
        /* 停止相关服务 */
        stopServices();
        
        /* 停止Socket服务器 */
        stopSocketServer();
        
        if (executorService != null) 
        {
            executorService.shutdown();
            executorService = null;
        }
        
        if (socketExecutor != null) 
        {
            socketExecutor.shutdown();
            socketExecutor = null;
        }
    }
    
    /**
     * 启动Socket服务器
     */
    private void startSocketServer() 
    {
        if (socketServerRunning.get()) 
        {
            return;
        }
        
        socketServerRunning.set(true);
        socketServerThread = new Thread(this::runSocketServer);
        socketServerThread.start();
        Log.d(TAG, "Heart-beat Socket server started on port: " + socketPort);
    }
    
    /**
     * 停止Socket服务器
     */
    private void stopSocketServer() 
    {
        if (!socketServerRunning.get()) 
        {
            return;
        }
        
        socketServerRunning.set(false);
        
        if (serverSocket != null) 
        {
            try 
            {
                serverSocket.close();
                Log.d(TAG, "Socket server closed");
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing socket server: " + e.getMessage());
            }
            serverSocket = null;
        }
        
        if (socketServerThread != null) 
        {
            socketServerThread.interrupt();
            socketServerThread = null;
        }
    }
    
    /**
     * Socket服务器主循环
     */
    private void runSocketServer() 
    {
        try 
        {
            serverSocket = new ServerSocket(socketPort);
            Log.d(TAG, "Socket server listening on port: " + socketPort);
            
            while (socketServerRunning.get() && !Thread.currentThread().isInterrupted()) 
            {
                try 
                {
                    Socket clientSocket = serverSocket.accept();
                    
                    /* 在新线程中处理客户端 */
                    socketExecutor.execute(() -> handleSocketClient(clientSocket));
                } 
                catch (IOException e) 
                {
                    if (socketServerRunning.get()) 
                    {
                        Log.e(TAG, "Error accepting client connection: " + e.getMessage());
                    }
                }
            }
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error creating socket server: " + e.getMessage());
        } 
        finally 
        {
            if (serverSocket != null && !serverSocket.isClosed()) 
            {
                try 
                {
                    serverSocket.close();
                } 
                catch (IOException e) 
                {
                    Log.e(TAG, "Error closing server socket: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 处理Socket客户端连接
     */
    private void handleSocketClient(Socket clientSocket) 
    {
        try 
        {
            clientSocket.setSoTimeout(10000); // 10秒超时
            
            InputStream inputStream = clientSocket.getInputStream();
            
            /* 读取命令 */
            byte[] cmdBuffer = new byte[2];
            int bytesRead = inputStream.read(cmdBuffer);
            
            if (bytesRead != 2) 
            {
                Log.e(TAG, "Invalid command length: " + bytesRead);
                return;
            }
            
            int cmd = cmdBuffer[0];
            
            if (cmd == CMD_HEARTBEAT) 
            {
                /* 处理心跳包 */
                handleHeartbeat();
            }
            
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error handling socket client: " + e.getMessage());
        } 
        finally 
        {
            try 
            {
                clientSocket.close();
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing client socket: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理心跳包
     */
    private void handleHeartbeat() 
    {
        lastHeartbeatTime.set(System.currentTimeMillis());
        
        /* 如果之前tpctrl不存在，现在检测到了心跳包，说明tpctrl出现了 */
        if (!tpctrlExists.get()) 
        {
            tpctrlExists.set(true);
            handleTpctrlDetected();
            
            /* 立即发送所有ISP参数配置到tpctrl端 */
            pushAllIspConfigs();
        }
    }
    
    /**
     * 心跳检测循环
     */
    private void monitorHeartBeat()
    {
        while (isRunning.get()) 
        {
            try 
            {
                long currentTime = System.currentTimeMillis();
                long lastHeartbeat = lastHeartbeatTime.get();
                boolean currentTpctrlExists = false;
                
                if (lastHeartbeat > 0 && (currentTime - lastHeartbeat) <= heartbeatTimeout) 
                {
                    /* 心跳包在超时时间内，认为tpctrl存在 */
                    currentTpctrlExists = true;
                }
                
                boolean previousTpctrlExists = tpctrlExists.get();
                
                /* 更新tpctrl状态 */
                tpctrlExists.set(currentTpctrlExists);
                
                if (!currentTpctrlExists && previousTpctrlExists) 
                {
                    /* tpctrl进程消失了（心跳包超时） */
                    handleTpctrlLost();
                } 
                else if (currentTpctrlExists && !servicesStarted.get()) 
                {
                    /* tpctrl进程存在，确保服务已启动 */
                    startServices();
                }
                
                Thread.sleep(1000); // 1秒检查一次
            } 
            catch (InterruptedException e) 
            {
                Thread.currentThread().interrupt();
                Log.d(TAG, "Heartbeat check loop interrupted");
                break;
            } 
            catch (Exception e) 
            {
                Log.e(TAG, "Error in heartbeat check loop", e);
                try 
                {
                    Thread.sleep(3000);
                } 
                catch (InterruptedException ie) 
                {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        Log.d(TAG, "Heartbeat check loop ended");
    }
    
    /**
     * 处理tpctrl进程被检测到
     */
    private void handleTpctrlDetected() 
    {
        Log.d(TAG, "Tpctrl process detected via heartbeat, starting services");
        
        startServices();
        
        if (heartbeatListener != null) 
        {
            mainHandler.post(() -> heartbeatListener.onTpctrlDetected());
        }
    }
    
    /**
     * 处理tpctrl进程消失
     */
    private void handleTpctrlLost() 
    {
        Log.d(TAG, "Tpctrl process lost (heartbeat timeout), stopping services");
        
        stopServices();
        
        if (heartbeatListener != null) 
        {
            mainHandler.post(() -> heartbeatListener.onTpctrlLost());
        }
    }
    
    /**
     * 启动相关服务
     */
    private void startServices() 
    {
        if (servicesStarted.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Starting RTSP and TpctrlSocket services");
        
        try 
        {
            /* 启动图像Socket服务 - 专门处理图像传输 */
            if (captureImageHelper != null) 
            {
                String tempImagePath = FileStorageUtils.createImagePath(context);
                tpctrlSocketService = new TpctrlSocketService(captureImageHelper, tempImagePath);
                tpctrlSocketService.setLogListener(message ->
                    Log.d(TAG, "TpctrlSocketService: " + message));
                tpctrlSocketService.start();
                Log.d(TAG, "TpctrlSocket service started");
            }
            
            /* 检查并启动RTSP服务 */
            if (rtspManager != null) 
            {
                if (currentStreamType == StreamType.CAMERA && videoEncoder != null) 
                {
                    /* 摄像头流需要视频编码器 */
                    if (isVideoEncoderReady()) 
                    {
                        startRtspService();
                    } 
                    else 
                    {
                        Log.d(TAG, "VideoEncoder not ready, delaying RTSP start");
                        mainHandler.postDelayed(this::startRtspServiceReliably, 2000);
                    }
                } 
                else if (currentStreamType == StreamType.SCREEN) 
                {
                    /* 屏幕流需要先请求权限，在权限回调中会自动开始推流 */
                    if (rtspManager.hasScreenCapturePermission()) 
                    {
                        /* 如果已经有权限，直接启动 */
                        startRtspService();
                    } 
                    else 
                    {
                        /* 请求权限，onPermissionGranted回调中会启动推流 */
                        rtspManager.requestScreenPermission();
                    }
                }
            }
            
            servicesStarted.set(true);
            
            if (heartbeatListener != null) 
            {
                mainHandler.post(() -> heartbeatListener.onServicesStarted());
            }
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting services", e);
        }
    }
    
    /**
     * 启动RTSP服务
     */
    private void startRtspService() 
    {
        if (rtspManager == null) 
        {
            Log.e(TAG, "RTSPManager is null");
            return;
        }
        
        try 
        {
            /* 如果是摄像头流，确保设置了视频编码器 */
            if (currentStreamType == StreamType.CAMERA && videoEncoder != null) 
            {
                rtspManager.setVideoEncoder(videoEncoder);
            }
            else if (currentStreamType == StreamType.SCREEN) 
            {
                /* 屏幕流需要检查权限 */
                if (!rtspManager.hasScreenCapturePermission()) 
                {
                    Log.w(TAG, "Screen capture permission not granted yet");
                    /* 如果权限未获取，主动请求权限 */
                    rtspManager.requestScreenPermission();
                    return; /* 先不启动推流，等权限回调 */
                }
            }
            
            /* 开始推流 */
            if (rtspManager.startStreaming()) 
            {
                Log.d(TAG, "RTSP streaming started successfully with type: " + currentStreamType);
            } 
            else 
            {
                Log.w(TAG, "Failed to start RTSP streaming");
            }
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting RTSP service", e);
        }
    }
    
    /**
     * 停止相关服务
     */
    private void stopServices() 
    {
        if (!servicesStarted.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Stopping RTSP and TpctrlSocket services");
        
        try 
        {
            /* 停止RTSP服务 */
            if (rtspManager != null && rtspManager.isStreaming()) 
            {
                rtspManager.stopStreaming();
                Log.d(TAG, "RTSP streaming stopped");
            }
            
            /* 停止ImageSocket服务 */
            if (tpctrlSocketService != null) 
            {
                tpctrlSocketService.stop();
                tpctrlSocketService = null;
                Log.d(TAG, "TpctrlSocket service stopped");
            }
            
            servicesStarted.set(false);
            
            if (heartbeatListener != null) 
            {
                mainHandler.post(() -> heartbeatListener.onServicesStopped());
            }
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error stopping services", e);
        }
    }
    
    /**
     * 检查视频编码器是否准备好
     */
    private boolean isVideoEncoderReady() 
    {
        return videoEncoder != null;
    }
    
    /**
     * 重试启动RTSP服务
     */
    private void startRtspServiceReliably()
    {
        if (!servicesStarted.get()) 
        {
            return; /* 如果服务已经被停止，不再重试 */
        }
        
        if (currentStreamType == StreamType.CAMERA && !isVideoEncoderReady()) 
        {
            /* 继续重试，最多重试3次 */
            retryCount++;
            if (retryCount < 3) 
            {
                Log.d(TAG, "VideoEncoder still not ready, retry " + retryCount);
                mainHandler.postDelayed(this::startRtspServiceReliably, 3000);
            } 
            else 
            {
                Log.w(TAG, "VideoEncoder not ready after 3 retries, giving up RTSP start");
                retryCount = 0;
            }
        } 
        else 
        {
            startRtspService();
            retryCount = 0;
        }
    }
    
    /* 状态查询方法 */
    public boolean isRunning()
    {
        return isRunning.get();
    }

    /**
     * 判断服务是否启动
     * @return ture已启动推流服务  false未启动推流服务
     */
    public boolean areServicesRunning()
    {
        return servicesStarted.get();
    }

    /**
     * 判断tpctrl是否已经开始运行（通过心跳包的方式）
     * @return ture已经接收到心跳包服务已经启动  false未接收到心跳包服务未启动
     */
    public boolean isTpctrlRunning()
    {
        return tpctrlExists.get();
    }

    /**
     * 是否已经开始RTSP的推流
     * @return true RTSP正在推流 false RTSP未开始推流
     */
    public boolean isStreaming()
    {
        return rtspManager != null && rtspManager.isStreaming();
    }

    /**
     * 获取RTSP推流的Uri地址
     * @return 返回string类型的Uri 例如：192.168.1.1
     */
    public String getStreamUrl()
    {
        return rtspManager != null ? rtspManager.getStreamUrl() : null;
    }

    /**
     * 获取心跳包超时时间（毫秒）
     */
    public long getHeartbeatTimeout()
    {
        return heartbeatTimeout;
    }

    /**
     * 获取心跳包的服务端口号
     */
    public int getSocketPort()
    {
        return socketPort;
    }

    /**
     * 获取ISP参数的服务器端口号
     */
    public int getIspPort()
    {
        return ispPort;
    }

    /**
     * 检查第一步初始化状态
     */
    public boolean isEarlyInitialized()
    {
        return earlyInitialized.get();
    }

    /**
     * 检查第二步是否完全初始化状态
     */
    public boolean isFullyInitialized()
    {
        return fullyInitialized.get();
    }
    
    /**
     * 释放资源
     */
    public void release() 
    {
        stop();
        if (rtspManager != null) 
        {
            rtspManager.release();
            rtspManager = null;
        }
        earlyInitialized.set(false);
        fullyInitialized.set(false);
    }

    /**
     * 发送所有ISP参数配置信息到tpctrl端
     */
    public void pushAllIspConfigs()
    {
        Log.i(TAG, "Start sending all ISP parameter configuration information to the tpctrl port....");

        int sentCount = 0;

        for (TouptekIspParam param : TouptekIspParam.values()) 
        {
            try 
            {
                /* 跳过版本号参数，因为它是只读的 */
                if (param == TouptekIspParam.TOUPTEK_PARAM_VERSION) {
                    continue;
                }
                /* 获取参数的完整配置信息 */
                TouptekIspParam.ParamData paramData = TouptekIspParam.getParamData(param);

                /* 发送参数配置到tpctrl端 */
                if (pushIspConfig(param, paramData))
                {
                    sentCount++;
                    Log.d(TAG, String.format("Parameter config sent to tpctrl: %s (ID: %d) - isDIsabel=%d, min=%d, max=%d, default=%d, current=%d",
                            param.name(), param.getValue(), paramData.isDisabled?1:0, paramData.min, paramData.max, paramData.defaultValue, paramData.current));
                } 
                else 
                {
                    Log.e(TAG, "Failed to send parameter config: " + param.name());
                }

                /* 添加小延时避免过于频繁的发送 */
                Thread.sleep(10);

            } 
            catch (Exception e) 
            {
                Log.e(TAG, "Error sending parameter config " + param.name() + ": " + e.getMessage());
            }
        }

        Log.i(TAG, "ISP parameter configuration sending completed, sent " + sentCount + " parameter configs");
    }

    /**
     * 发送单个ISP参数配置信息到tpctrl端
     */
    private boolean pushIspConfig(TouptekIspParam param, TouptekIspParam.ParamData paramData)
    {
        Socket socket = null;
        try 
        {
            /* 创建socket连接到tpctrl端的ISP参数专用端口 */
            socket = new Socket();
            socket.setSoTimeout(3000); // 3秒超时
            socket.connect(new java.net.InetSocketAddress("127.0.0.1", ispPort), 3000);

            /* 获取输出流 */
            OutputStream outputStream = socket.getOutputStream();

            /* 构造数据包：[CMD_ISP_PARAM_CONFIG(1字节)] + [PARAM_ID(1字节)] +[isDisable(4个字节)]+ [MIN(4字节)] + [MAX(4字节)] + [DEFAULT(4字节)] + [CURRENT(4字节)] */
            byte[] packet = new byte[22]; // 1 + 1 + 4*5 = 22字节

            int offset = 0;

            /* 命令码 */
            packet[offset++] = (byte)CMD_ISP_PARAM_CONFIG;

            /* 参数ID */
            packet[offset++] = (byte)param.getValue();

            /* 写入是否禁用（网络字节序） */
            writeIntToBytes(packet, offset, paramData.isDisabled ? 1 : 0);
            offset += 4;

            /* 写入最小值（网络字节序） */
            writeIntToBytes(packet, offset, paramData.min);
            offset += 4;

            /* 写入最大值（网络字节序） */
            writeIntToBytes(packet, offset, paramData.max);
            offset += 4;

            /* 写入默认值（网络字节序） */
            writeIntToBytes(packet, offset, paramData.defaultValue);
            offset += 4;

            /* 写入当前值（网络字节序） */
            writeIntToBytes(packet, offset, paramData.current);

            /* 发送数据 */
            outputStream.write(packet);
            outputStream.flush();

            return true;

        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Failed to send ISP parameter configuration to tpctrl: " + param.name() + ", error: " + e.getMessage());
            return false;
        } 
        finally 
        {
            if (socket != null) 
            {
                try 
                {
                    socket.close();
                } 
                catch (Exception e) 
                {
                    /* 忽略关闭时的错误 */
                }
            }
        }
    }

    /**
     * 将整数值写入字节数组（网络字节序/大端序）
     */
    private void writeIntToBytes(byte[] bytes, int offset, int value) 
    {
        bytes[offset] = (byte)((value >> 24) & 0xFF);
        bytes[offset + 1] = (byte)((value >> 16) & 0xFF);
        bytes[offset + 2] = (byte)((value >> 8) & 0xFF);
        bytes[offset + 3] = (byte)(value & 0xFF);
    }
}