
# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.appcompat_appcompat-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.core_core-nodeps/android_common/aar/proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.annotation_annotation-experimental-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Ignore missing Kotlin meta-annotations so that this library can be used
# without adding a compileOnly dependency on the Kotlin standard library.
-dontwarn kotlin.Deprecated
-dontwarn kotlin.Metadata
-dontwarn kotlin.ReplaceWith
-dontwarn kotlin.annotation.AnnotationRetention
-dontwarn kotlin.annotation.AnnotationTarget
-dontwarn kotlin.annotation.Retention
-dontwarn kotlin.annotation.Target

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-runtime-nodeps/android_common/aar/proguard.txt
-keepattributes *Annotation*

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.arch.core_core-runtime-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.versionedparcelable_versionedparcelable-nodeps/android_common/aar/proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.cursoradapter_cursoradapter-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.activity_activity-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-viewmodel-nodeps/android_common/aar/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.savedstate_savedstate-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-viewmodel-savedstate-nodeps/android_common/aar/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-livedata-core-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.tracing_tracing-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.fragment_fragment-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.viewpager_viewpager-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.customview_customview-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.loader_loader-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.appcompat_appcompat-resources-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.vectordrawable_vectordrawable-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.vectordrawable_vectordrawable-animated-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.interpolator_interpolator-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.drawerlayout_drawerlayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.dynamicanimation_dynamicanimation-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.recyclerview_recyclerview-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.media_media-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.legacy_legacy-support-core-utils-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.documentfile_documentfile-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.localbroadcastmanager_localbroadcastmanager-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.print_print-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx-legacy/androidx.legacy_legacy-support-core-ui-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.coordinatorlayout_coordinatorlayout-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.slidingpanelayout_slidingpanelayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.swiperefreshlayout_swiperefreshlayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.asynclayoutinflater_asynclayoutinflater-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.preference_preference-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Preference objects are inflated via reflection
-keep public class androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keep public class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.mediarouter_mediarouter-nodeps/android_common/aar/proguard.txt
# Copyright 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent MediaRouteActionProvider from being removed or renamed.
-keep class androidx.mediarouter.app.MediaRouteActionProvider { public <init>(...); }

# including external/lottie/proguard.flags
-keep public class com.airbnb.lottie.LottieAnimationView {
    public *;
}

# including out/soong/.intermediates/prebuilts/sdk/current/extras/constraint-layout-x/androidx-constraintlayout_constraintlayout-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/extras/material-design-x/com.google.android.material_material-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.cardview_cardview-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.transition_transition-nodeps/android_common/aar/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.viewpager2_viewpager2-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.palette_palette-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.leanback_leanback-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.slice_slice-core-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.slice_slice-view-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.slice_slice-builders-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.remotecallback_remotecallback-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx-legacy/androidx.lifecycle_lifecycle-extensions-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-livedata-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-process-nodeps/android_common/aar/proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.lifecycle_lifecycle-service-nodeps/android_common/aar/proguard.txt

# including out/soong/.intermediates/prebuilts/sdk/current/androidx/androidx.exifinterface_exifinterface-nodeps/android_common/aar/proguard.txt

# including packages/apps/Car/libs/car-ui-lib/car-ui-lib/proguard-rules.pro
# required for replacing elements in PreferenceFragment
-keep class com.android.car.ui.preference.CarUiDropDownPreference {*;}
-keep class com.android.car.ui.preference.CarUiListPreference {*;}
-keep class com.android.car.ui.preference.CarUiMultiSelectListPreference {*;}
-keep class com.android.car.ui.preference.CarUiEditTextPreference {*;}
-keep class com.android.car.ui.preference.CarUiSwitchPreference {*;}
-keep class com.android.car.ui.preference.CarUiPreference {*;}
-keep class com.android.car.ui.preference.** extends com.android.car.ui.preference.CarUiPreference {*;}

# required for default scrollbar implementation.
-keep class com.android.car.ui.recyclerview.DefaultScrollBar {*;}

# required for MenuItem click listeners
-keepclasseswithmembers class * extends android.app.Activity {
  public void * (com.android.car.ui.toolbar.MenuItem);
}

# We dynamically link the oem apis, and proguard can't see them
# when running, so it errors out without -dontwarn
-dontwarn com.android.car.ui.plugin.oemapis.**

# Required because the static lib doesn't call most of the methods
# on adapters, but instead passes it to the plugin, where they
# are called. Since proguard can't even see that those methods are
# overriding oem api interfaces (since the oem apis are dynamically
# linked and marked with -dontwarn), it thinks they're unused.
-keep class com.android.car.ui.**AdapterV* {*;}

# required for accessing oem apis
-keep class com.android.car.ui.pluginsupport.OemApiUtil {*;}

# Required for AppCompat instantiating our layout inflater factory,
# Otherwise it will be obfuscated and the reference to it in xml won't match
-keep class com.android.car.ui.CarUiLayoutInflaterFactory {*;}

# Required for reflection code in CarUiInstaller
-keep class com.android.car.ui.baselayout.Insets {*;}
-keep class com.android.car.ui.core.BaseLayoutController {*;}

# Required for car-lib APIs
# One of the GAS apps is failing during obfuscation by CrossReferenceValidator.
# The root cause is unknown, and the suggestion from the team was to
# suppress this warning.
-dontwarn android.car.Car

# This is needed so proguard would ignore the missing content provider.
-dontwarn com.android.car.ui.plugin.PluginNameProvider

# including packages/apps/Car/libs/car-ui-lib/car-ui-lib/proguard-rules-platform.pro
# This was added to fix issues with androidx CREATOR objects being removed.
# On google3 it is added automatically, so keep it in a separate file
# and don't use this file in google3.
-keepclassmembers class * implements android.os.Parcelable {
    static ** CREATOR;
}
