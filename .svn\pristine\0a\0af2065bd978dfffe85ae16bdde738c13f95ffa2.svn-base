package com.android.rockchip.camera2.activity.browse.imagemanagement

import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.databinding.ImageViewerBinding
import com.android.rockchip.camera2.util.TransformUtils
import com.android.rockchip.camera2.video.TpImageLoader
import kotlin.math.pow

class TpImageDecodeDialogFragment : DialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private var buttonsVisible = true
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    private var lastX = 0f
    private var lastY = 0f
    private val matrix = Matrix()
    private lateinit var scaleGestureDetector: ScaleGestureDetector

    private var startTime: Long = 0
    private var startX = 0f
    private var startY = 0f
    private val CLICK_DURATION = 300      // 最大点击持续时间（毫秒）
    private var isMoved = false
    private val touchSlop: Int by lazy {
        ViewConfiguration.get(requireContext()).scaledTouchSlop
    }


    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传递的参数
        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        // 加载当前图片
        loadCurrentImage()

//        // 设置点击图片切换按钮可见性
//        binding.imageView.setOnClickListener {
//            toggleButtons()
//        }

        // 按钮点击事件
        binding.btnPrevious.setOnClickListener {
            showPreviousImage()
        }

        binding.btnNext.setOnClickListener {
            showNextImage()
        }

        binding.btnBack.setOnClickListener {
            dismiss()
        }


        //Zoom
        binding.imageView.scaleType = ImageView.ScaleType.MATRIX
        initScaleGestureDetector()
        initScaleTouchEvent(view)


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog) // 全屏样式
    }

    override fun onStart() {
        super.onStart()
        // 设置对话框全屏
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            // 使用TpImageLoader加载高质量图片（用于详细查看）
            TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateButtonStates()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun updateButtonStates() {
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
    }

    private fun toggleButtons() {
        buttonsVisible = !buttonsVisible
        binding.buttonPanel.visibility = if (buttonsVisible) View.VISIBLE else View.GONE
    }


    private fun initScaleGestureDetector() {
        scaleGestureDetector = ScaleGestureDetector(requireContext(), object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                TransformUtils.applyZoom(binding.imageView, matrix, detector.scaleFactor, detector.focusX, detector.focusY)
                return true
            }
        })
    }

    private fun initScaleTouchEvent(view: View) {
        binding.imageView.setOnTouchListener { v, event ->
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    // 重置移动状态
                    isMoved = false
                    // 记录初始位置
                    lastX = event.x
                    lastY = event.y
                    startX = event.x
                    startY = event.y
                    startTime = System.currentTimeMillis()
                }
                MotionEvent.ACTION_MOVE -> {
                    // 检测是否发生了有效移动
                    if (!isMoved) {
                        val deltaX = event.x - startX
                        val deltaY = event.y - startY
                        val distance = Math.hypot(deltaX.toDouble(), deltaY.toDouble())

                        if (distance > touchSlop) {
                            isMoved = true  // 标记为已移动
                        }
                    }

                    // 处理平移操作
                    if (isMoved) {
                        val deltaX = event.x - lastX
                        val deltaY = event.y - lastY
                        TransformUtils.applyPan(binding.imageView, matrix, deltaX, deltaY)
                        lastX = event.x
                        lastY = event.y
                    }
                }
                MotionEvent.ACTION_UP -> {
                    // 只有在没有移动的情况下才识别为单击
                    if (!isMoved) {
                        // 检查是否在有效点击时间内
                        val duration = System.currentTimeMillis() - startTime
                        if (duration < CLICK_DURATION) {
                            toggleButtons()
                        }
                    }
                }
                MotionEvent.ACTION_CANCEL -> {
                    // 重置状态
                    isMoved = false
                }
            }

            // 将事件传递给缩放检测器
            scaleGestureDetector.onTouchEvent(event)
            true
        }
    }
}