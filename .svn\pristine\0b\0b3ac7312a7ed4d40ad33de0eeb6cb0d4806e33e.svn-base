package com.android.rockchip.camera2.settings.models;

/**
 * 设置菜单项数据模型
 * 用于表示左侧导航菜单中的每个设置分类
 */
public class SettingsMenuItem {
    
    public enum MenuType {
        NETWORK_SETTINGS,    // 网络设置
        IMAGE_SETTINGS,      // 图像设置（预留）
        RECORD_SETTINGS,     // 录制设置（预留）
        SYSTEM_SETTINGS      // 系统设置（预留）
    }
    
    private final MenuType type;
    private final String title;
    private final int iconResId;
    private final String description;
    private boolean isSelected;
    
    public SettingsMenuItem(MenuType type, String title, int iconResId, String description) {
        this.type = type;
        this.title = title;
        this.iconResId = iconResId;
        this.description = description;
        this.isSelected = false;
    }
    
    // Getters
    public MenuType getType() {
        return type;
    }
    
    public String getTitle() {
        return title;
    }
    
    public int getIconResId() {
        return iconResId;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isSelected() {
        return isSelected;
    }
    
    // Setters
    public void setSelected(boolean selected) {
        isSelected = selected;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SettingsMenuItem that = (SettingsMenuItem) obj;
        return type == that.type;
    }
    
    @Override
    public int hashCode() {
        return type.hashCode();
    }
    
    @Override
    public String toString() {
        return "SettingsMenuItem{" +
                "type=" + type +
                ", title='" + title + '\'' +
                ", isSelected=" + isSelected +
                '}';
    }
}
