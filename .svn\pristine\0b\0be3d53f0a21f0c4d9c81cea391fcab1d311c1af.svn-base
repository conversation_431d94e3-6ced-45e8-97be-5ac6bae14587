#ifndef __dev_service_h__
#define __dev_service_h__

#include <utiny/usys_service.h>
#include <utiny/usys_thread.h>

#define PROCESS_FLAG_CONSOLE	0x00000001
#define PROCESS_FLAG_SERVICE	0x00000002
#define PROCESS_FLAG_WORK		0x00000004

class dev_service : public usys_service
{
public:
	dev_service();

	virtual bool shutdown();
	
public:
	void force_to_restart();
	
protected:
	virtual bool start(int, char*[]);
	virtual void wait_for_shutdown();
	virtual bool stop();

private:
	bool startwork();
	bool stopwork();

private:
	int process_flag_;
	bool exit_success_;
	volatile bool running_;
#ifdef _WIN32
	HANDLE work_event_;
	HANDLE work_process_;
#else
	int pipe_[2];
	pid_t work_process_;
#endif

	usys_thread_ptr	discovery_;
	usys_thread_ptr	parram_;
};

extern dev_service* g_service;

#endif /* __dev_service_h__ */
