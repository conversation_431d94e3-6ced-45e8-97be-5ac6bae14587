<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <FrameLayout
        android:id="@+id/right_panel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F5F5F5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.2"> <!-- 占25%宽度 -->

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="操作面板"
            android:textSize="18sp"/>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="#FFFFFF"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 左侧按钮组：前两个按钮 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="horizontal">


        </LinearLayout>

        <!-- 占位空间：将右侧按钮推到最右边 -->
        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="26" />

        <!-- 右侧按钮组：后三个按钮 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="horizontal"
            android:background="#FFFFFF">
            <!-- 添加全选按钮 -->
            <TextView
                android:id="@+id/browse_select_all"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:gravity="center"
                android:text="全选"
                android:textSize="16sp"
                android:textColor="#000000"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp"/>

            <!-- 添加删除按钮 -->
            <TextView
                android:id="@+id/browse_delete"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:gravity="center"
                android:text="删除"
                android:textSize="16sp"
                android:textColor="#000000"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp"/>

        </LinearLayout>
        <ImageView
            android:id="@+id/browse_config"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="4dp"
            android:scaleType="centerInside"
            android:src="@drawable/config_n"
            android:background="#FFFFFF"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16dp"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/recycler_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F0FFFF"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="40dp" />

    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>