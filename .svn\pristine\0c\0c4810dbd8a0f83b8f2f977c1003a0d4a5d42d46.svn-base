package com.touptek.xcamview.activity.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseFragment

class TpStorageSettingsFragment : BaseFragment() {
    companion object {
        private const val TAG = "FormatSettings"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_storage_settings, container, false)
    }
}