#include "global.h"
#include "discovery.h"
#include <stdlib.h>
#include <utiny/usys_network.h>
#ifndef _WIN32
#include <sys/types.h>
#include <sys/select.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <ifaddrs.h>
#endif
#include <stdio.h>

#ifndef _WIN32
#define closesocket(s)	close(s)
#endif

extern void fill_find_res(DiscCmdFndRes& res);

bool g_discovery_sendto = true;
std::string g_StaMulticastIp;

static void encyo(const unsigned char input[16], unsigned char output[16])
{
	unsigned char sum = 0;
	unsigned i;
	memcpy(output, input, 16);
	for (i = 0; i < 16; ++i)
		sum += output[i];
	for (i = 0; i < 16; ++i)
	{
		output[i] |= sum;
		output[i] &= (sum ^ 0x5a);
		output[i] ^= (unsigned char)i;
		output[i] += input[i];
	}
	for (i = 0; i < 8; ++i)
	{
		sum = output[i];
		output[i] = output[i + 8];
		output[i + 8] = sum;
	}
	for (i = 0; i < 16; ++i)
	{
		output[i] |= sum;
		output[i] &= (sum ^ 0xa5);
		output[i] ^= (unsigned char)i;
		output[i] += input[i];
	}
}

static int pack_value(int value, char* buffer)
{
	value = htonl(value);
	memcpy(buffer, &value, 4);
	return 4;
}

static int pack_value(unsigned value, char* buffer)
{
	value = htonl(value);
	memcpy(buffer, &value, 4);
	return 4;
}

static int pack_value(unsigned short value, char* buffer)
{
	value = htons(value);
	memcpy(buffer, &value, 2);
	return 2;
}

static int pack_string(const std::string& str, char* buffer)
{
	int ret = 0;

	unsigned char len = (unsigned char)str.size();
	memcpy(buffer + ret, &len, 1);
	ret += 1;

	memcpy(buffer + ret, str.c_str(), str.size());
	ret += (int)str.size();

	return ret;
}

static int unpack_string(std::string& str, const char* buffer, int length)
{
	int ret = 0;

	if (length < 1)
		return -1;
	unsigned char len = *((unsigned char*)buffer); 
	ret += 1;
	length -= 1;
	if (0 == len)
		return ret;

	if (length < len)
		return -1;
	str.assign(buffer + ret, len);
	ret += len;

	return ret;
}

template<typename T>
static int unpack_value(T& val, const char* buffer, int length)
{
	if (length < sizeof(T))
		return -1;
	memcpy(&val, buffer, sizeof(T));
	if (sizeof(T) == 2)
		val = (T)ntohs((short)val);
	else if (sizeof(T) == 4)
		val = (T)ntohl((int)val);
	return (int)sizeof(T);
}

int pack_discovery_header(const DiscCmdHeader& header, char* buffer)
{
	int ret = 0;

	memcpy(buffer + ret, &header, sizeof(header));
	ret += sizeof(header);

	return ret;
}

#define PACK_RANGE(a)	\
do {	\
	ret += pack_value(a.idisable, buffer + ret);	\
	ret += pack_value(a.imin, buffer + ret);		\
	ret += pack_value(a.imax, buffer + ret);		\
	ret += pack_value(a.idef, buffer + ret);		\
}while(0)

int pack_discovery_cmd(const DiscCmdFndRes& res, char* buffer)
{
	int ret = 0;

	memcpy(buffer + ret, res.key_obscure, 16);
	ret += 16;
	ret += pack_string(res.name, buffer + ret);
	ret += pack_string(res.model, buffer + ret);
	ret += pack_string(res.version, buffer + ret);
	ret += pack_string(res.id, buffer + ret);
	memcpy(buffer + ret, &res.dhcp_enabled, 1);
	ret += 1;
	ret += pack_string(res.ip, buffer + ret);
	ret += pack_string(res.ipmask, buffer + ret);
	ret += pack_string(res.gw, buffer + ret);
	ret += pack_string(res.url, buffer + ret);
	memcpy(buffer + ret, &res.info.version, 1);
	ret += 1;
	memcpy(buffer + ret, res.info.reversed, 3);
	ret += 3;
	ret += pack_value(res.info.flag, buffer + ret);
	{
		unsigned char resnum = (unsigned char)res.info.reswidth.size();
		memcpy(buffer + ret, &resnum, 1);
		ret += 1;
	}
	for (size_t i = 0; i < res.info.reswidth.size(); ++i)
		ret += pack_value(res.info.reswidth[i], buffer + ret);
	for (size_t i = 0; i < res.info.resheight.size(); ++i)
		ret += pack_value(res.info.resheight[i], buffer + ret);
	PACK_RANGE(res.info.Expotime);
	PACK_RANGE(res.info.ExpoGain);
	PACK_RANGE(res.info.AutoExpoTarget);
	PACK_RANGE(res.info.Temp);
	PACK_RANGE(res.info.Tint);
	PACK_RANGE(res.info.Contrast);
	PACK_RANGE(res.info.Hue);
	PACK_RANGE(res.info.Saturation);
	PACK_RANGE(res.info.Brightness);
	PACK_RANGE(res.info.Gamma);
	PACK_RANGE(res.info.AExpo);
	PACK_RANGE(res.info.Awb);
	PACK_RANGE(res.info.Binskip);
	PACK_RANGE(res.info.Hz);
	PACK_RANGE(res.info.Bps);
	PACK_RANGE(res.info.KeyFrame);
	PACK_RANGE(res.info.LowLightCompensation);
	PACK_RANGE(res.info.Sharpness);
	if (res.info.flag & TOUPNAM_FLAG_WBGAIN)
	{
		PACK_RANGE(res.info.WbRedGain);
		PACK_RANGE(res.info.WbGreenGain);
		PACK_RANGE(res.info.WbBlueGain);
	}
	PACK_RANGE(res.info.Denoise);
	{
		unsigned char capnum = (unsigned char)res.info.capwidth.size();
		memcpy(buffer + ret, &capnum, 1);
		ret += 1;
	}
	for (size_t i = 0; i < res.info.capwidth.size(); ++i)
		ret += pack_value(res.info.capwidth[i], buffer + ret);
	for (size_t i = 0; i < res.info.capheight.size(); ++i)
		ret += pack_value(res.info.capheight[i], buffer + ret);

    PACK_RANGE(res.info.ApSta); /* for ap sta switch added by wheatfa */


	
	ret += pack_string(res.info.sn, buffer + ret);

	
	
	if (res.info.version > 6)
	{
		{
			unsigned char codecnum = (unsigned char)res.info.codec.size();
			memcpy(buffer + ret, &codecnum, 1);
			ret += 1;
		}
		for (size_t i = 0; i < res.info.codec.size(); ++i)
			ret += pack_value(res.info.codec[i], buffer + ret);
		PACK_RANGE(res.info.Codecp);
	}

	
	
	PACK_RANGE(res.info.AfPositon);
	PACK_RANGE(res.info.AfMode);
	PACK_RANGE(res.info.AfZone);
	PACK_RANGE(res.info.AfFeedback);
	PACK_RANGE(res.info.AfPositonAbsolute);


	PACK_RANGE(res.info.Status);
	PACK_RANGE(res.info.Event);

	PACK_RANGE(res.info.RoiLeft);
	PACK_RANGE(res.info.RoiTop);
	PACK_RANGE(res.info.RoiWidth);
	PACK_RANGE(res.info.RoiHeight);
	PACK_RANGE(res.info.VFlip);
	PACK_RANGE(res.info.HFlip);
	PACK_RANGE(res.info.ColorGray);
	PACK_RANGE(res.info.VideoResolution);
	PACK_RANGE(res.info.LightAdjustment);
	PACK_RANGE(res.info.Zoom);
	return ret;
}

int pack_discovery_cmd(const DiscCmdNetRes& res, char* buffer)
{
	int ret = 0;

	memcpy(buffer + ret, &res.result, sizeof(res.result));
	ret += sizeof(res.result);

	return ret;
}

#define UNPACK_VALUE(a)	\
do { \
	len = unpack_value(a, buffer + ret, length);	\
	if (len < 0)	\
		return len;	\
	ret += len;		\
	length -= len;	\
}while(0);

#define UNPACK_STRING(a)	\
do { \
	len = unpack_string(a, buffer + ret, length);	\
	if (len < 0)	\
		return len;	\
	ret += len;		\
	length -= len;	\
}while(0);

int unpack_discovery_cmd(DiscCmdNetReq& req, char* buffer, int length)
{
	int ret = 0, len = 0;

	UNPACK_STRING(req.id);
	UNPACK_VALUE(req.dhcp_enabled);
	UNPACK_STRING(req.ip);
	UNPACK_STRING(req.ipmask);
	UNPACK_STRING(req.gw);

	return ret;
}

std::string get_build_time()
{
	std::string datestr(8, '0');
	char monstr[4] = { 0 };
	int day = 0, month = 0, year = 0;
	const char* monthabbr[12] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
	sscanf(__DATE__, "%s %d %d", monstr, &day, &year);
	for (size_t i = 0; i < sizeof(monthabbr) / sizeof(monthabbr[0]); ++i)
	{
		if (0 == strcmp(monthabbr[i], monstr))
			month = (int)(1 + i);
	}
	sprintf((char*)datestr.data(), "%04d%02d%02d", year, month, day);
	return datestr;
}

#ifndef _WIN32
std::string getmacaddr()
{
	std::string str(12, '0');
	ifreq buffer;
	memset(&buffer, 0, sizeof(buffer));
	int s = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
	if (s >= 0)
	{
		strcpy(buffer.ifr_name, g_netname);
		if (ioctl(s, SIOCGIFHWADDR, &buffer) >= 0)
		{
			close(s);
	
			for (s = 0; s < 6; s++)
				sprintf((char*)str.data() + s * 2, "%.2X", (unsigned char)buffer.ifr_hwaddr.sa_data[s]);
		}
	}
	return str;
}

static void getipaddr(DiscCmdFndRes& res)
{
	ifaddrs* ifap = nullptr;
	if (getifaddrs(&ifap) >= 0)
	{
		ifaddrs* curr = ifap;
		while (curr != 0)
		{
			if (curr->ifa_addr)
			{
				if (0 == (curr->ifa_flags & IFF_LOOPBACK))
				{
					if (curr->ifa_addr->sa_family == AF_INET)
					{
						if (strcmp(curr->ifa_name, g_netname) == 0)
						{
							sockaddr_in* q = (sockaddr_in*)(curr->ifa_netmask);
							res.ipmask = inet_ntoa(q->sin_addr);
							sockaddr_in* p = (sockaddr_in*)(curr->ifa_addr);
							res.ip = inet_ntoa(p->sin_addr);
							return;
						}
					}
				}
			}

			curr = curr->ifa_next;
		}

		freeifaddrs(ifap);
	}
}
#endif

discovery::discovery()
	: sockrecv_(INVALID_SOCKET), socksend_(INVALID_SOCKET), sockuni_(INVALID_SOCKET), pfndres_(nullptr), fndreslen_(0)
{
	sockrecv_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
	sockaddr_in addr = { 0 };
	addr.sin_family = AF_INET;
	addr.sin_port = htons(21478);
	addr.sin_addr.s_addr = INADDR_ANY;
	if (bind(sockrecv_, (sockaddr*)&addr, sizeof(addr)) >= 0)
	{
		int b = 1;
		setsockopt(sockrecv_, SOL_SOCKET, SO_BROADCAST, (const char*)&b, sizeof(b));
        setsockopt(sockrecv_, SOL_SOCKET, SO_REUSEADDR, (const char*)&b, sizeof(b));
	}
	else {
        perror("Failed to bind receive socket");
    }

#ifndef _WIN32
	ifreq ifr;
	memset(&ifr, 0, sizeof(ifr));
	strcpy(ifr.ifr_name, g_netname);
	ioctl(sockrecv_, SIOCSIFBRDADDR, &ifr);

	if (strcmp(g_netname, "ra0"))
	{
		ioctl(sockrecv_, SIOCGIFNETMASK, &ifr);
		sockaddr_in* p = (sockaddr_in*)&ifr.ifr_ifru.ifru_addr;
		res_.ipmask = inet_ntoa(p->sin_addr);
		ioctl(sockrecv_, SIOCGIFADDR, &ifr);
		res_.ip = inet_ntoa(p->sin_addr);
	}
	else
	{
		getipaddr(res_);
	}
#endif

	res_.id = "tk";
#ifndef _WIN32
	res_.id += getmacaddr();
#else
	res_.id += "0123456789";
#endif

	if ((eSTA == g_netmode) || (eETH == g_netmode))
	{
		res_.url = "rtsp://";
		res_.url += res_.ip;
		if (eMULTICAST == g_nettype)
		{
			g_StaMulticastIp = MULTICAST_ADDR;

			size_t p = res_.ip.find_first_of('.');
			if (p != std::string::npos)
				g_StaMulticastIp.append(res_.ip.c_str() + p + 1);
		}
	}
	else
	{
		// 使用全局变量而非硬编码宏
		res_.url = g_url;
		res_.ip = g_ipaddr;
		res_.ipmask = g_ipmask;
		printf("AP mode using IP: %s, mask: %s, URL: %s\n", 
			   res_.ip.c_str(), res_.ipmask.c_str(), res_.url.c_str());
	}

	res_.info.flag = 0;
	if (eETH == g_netmode)
		res_.info.flag |= TOUPNAM_FLAG_ETHERNET;
	else if (eAP == g_netmode)
		res_.info.flag |= TOUPNAM_FLAG_WIFI_AP;
	else if (eSTA == g_netmode)
		res_.info.flag |= TOUPNAM_FLAG_WIFI_STA;

	if (eMULTICAST == g_nettype)
		res_.info.flag |= TOUPNAM_FLAG_MULTICAST;
	else if (eTCP == g_nettype)
		res_.info.flag |= TOUPNAM_FLAG_RTP_OVER_RTSP;

#if SD_SUPPORT
	res_.info.flag |= TOUPNAM_FLAG_SD;
#endif

	fill_find_res(res_);
		
	res_.name = res_.model;	
	if (res_.id.size() == 12)	/* use the sn last 6 char as the SSID */
		res_.name += res_.id.substr(6, 6);
/* Ϊ�˸�hdmi������ʾ�Ĺ̼��汾һ�£����ﲻ�ñ���ʱ�� */	
#if 0	
	res_.version += '_';
	res_.version += get_build_time();
#endif	

    // ====== AP模式特殊处理 ======
	if (g_netmode == eAP) {
		// 创建专门的发送套接字
		socksend_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
		if (socksend_ == INVALID_SOCKET) {
			perror("Failed to create send socket for AP mode");
			socksend_ = sockrecv_; // 回退到接收套接字
		} else {
			// 设置适当的套接字选项
			int b = 1;
			setsockopt(socksend_, SOL_SOCKET, SO_BROADCAST, (const char*)&b, sizeof(b));
			setsockopt(socksend_, SOL_SOCKET, SO_REUSEADDR, (const char*)&b, sizeof(b));
			
			// 准备地址结构体并绑定到AP的IP地址
			memset(&addr, 0, sizeof(addr));
			addr.sin_family = AF_INET;
			addr.sin_addr.s_addr = inet_addr(g_ipaddr.c_str());  // 使用g_ipaddr
			addr.sin_port = htons(0); // 系统分配端口
			
			if (bind(socksend_, (sockaddr*)&addr, sizeof(addr)) < 0) {
				perror("bind failed for send socket");
				closesocket(socksend_);
				socksend_ = sockrecv_; // 绑定失败，回退到接收套接字
			}
		}
	}
	// ====== STA模式特殊处理 ======
	else if (g_netname && strcmp(g_netname, "ra0")) {
		// 对于普通网络接口，直接复用接收套接字
		socksend_ = sockrecv_;
	}
	// ====== 其他情况 ======
	else {
		// 创建新的发送套接字
		socksend_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
		memset(&addr, 0, sizeof(addr));
		addr.sin_family = AF_INET;
		addr.sin_addr.s_addr = inet_addr(res_.ip.c_str());
        // 添加调试输出
        printf("AP mode binding to IP: %s\n", res_.ip.c_str());
		if (bind(socksend_, (sockaddr*)&addr, sizeof(addr)) < 0) {
            perror("bind failed");
        }
        
        // 设置广播权限
        int b = 1;
        setsockopt(socksend_, SOL_SOCKET, SO_BROADCAST, (const char*)&b, sizeof(b));
        setsockopt(socksend_, SOL_SOCKET, SO_REUSEADDR, (const char*)&b, sizeof(b));
	}

	// 创建单播套接字(用于点对点通信)
	sockuni_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
	memset(&addr, 0, sizeof(addr));
	addr.sin_family = AF_INET;
	addr.sin_port = htons(21479);
	addr.sin_addr.s_addr = INADDR_ANY;
	bind(sockuni_, (sockaddr*)&addr, sizeof(addr));
}

discovery::~discovery()
{
	if (pfndres_) {
		free(pfndres_);
		pfndres_ = nullptr;
	}
	
	if (sockrecv_ != INVALID_SOCKET) {
		closesocket(sockrecv_);
	}
	
	if (socksend_ != INVALID_SOCKET && socksend_ != sockrecv_) {
		closesocket(socksend_);
	}
	
	if (sockuni_ != INVALID_SOCKET) {
		closesocket(sockuni_);
	}
}

void discovery::stop()
{
}

void discovery::handle_read(SOCKET sock)
{
	char buf[MTU];
	sockaddr_in saddr = { 0 };
	socklen_t len = sizeof(saddr);
	int ret = recvfrom(sock, buf, sizeof(buf), 0, (sockaddr*)&saddr, &len);
	if (ret > (int)sizeof(DiscCmdHeader))
	{
		if ((sock == sockrecv_) && (sockrecv_ == socksend_))
			saddr.sin_addr.s_addr = INADDR_BROADCAST;
		DiscCmdHeader* header = (DiscCmdHeader*)buf;
		if ((0x54 == header->magic[0]) && (0x4b == header->magic[1]))
		{
			switch (header->cmd)
			{
			case AD_CMD_FND_REQ:
				if (ret == sizeof(DiscCmdHeader) + sizeof(DiscCmdFndReq))
				{
					DiscCmdFndReq* req = (DiscCmdFndReq*)(buf + sizeof(DiscCmdHeader));
					on_fnd(sock, header, req, saddr);
				}
				break;
			case AD_CMD_NET_REQ:
				{
					DiscCmdNetReq req;
					if (unpack_discovery_cmd(req, buf + sizeof(DiscCmdHeader), ret - sizeof(DiscCmdHeader)) > 0)
						on_net(sock, header, &req, saddr);
				}
				break;
			}
		}
	}
}

void discovery::loop()
{
	while (1)
	{
		timeval tv = { 1, 0 };
		fd_set fdset;
		FD_ZERO(&fdset);
		FD_SET(sockrecv_, &fdset);
		FD_SET(sockuni_, &fdset);
		
		if (select((sockrecv_ > sockuni_ ? sockrecv_:sockuni_) + 1, &fdset, nullptr, nullptr, &tv) > 0)
		{
			if (FD_ISSET(sockrecv_, &fdset))
				handle_read(sockrecv_);
			else if (FD_ISSET(sockuni_, &fdset))
				handle_read(sockuni_);
		}
	}
}

void discovery::on_fnd(SOCKET sock, const DiscCmdHeader* header, const DiscCmdFndReq* req, const sockaddr_in& toaddr)
{
	// 检查客户端地址是否是广播地址
	bool is_broadcast = (toaddr.sin_addr.s_addr == INADDR_BROADCAST || 
						toaddr.sin_addr.s_addr == inet_addr("255.255.255.255"));
	
	if (pfndres_)
	{
		memcpy(pfndres_, header, sizeof(DiscCmdHeader));

        ((DiscCmdHeader*)pfndres_)->cmd = AD_CMD_FND_RES;
		
		encyo((const unsigned char*)req->key, (unsigned char*)(pfndres_ + sizeof(DiscCmdHeader)));
	}
	else
	{
	    char buf[MTU];
		fndreslen_ = sizeof(DiscCmdHeader);
		memcpy(buf, header, sizeof(DiscCmdHeader));
        ((DiscCmdHeader*)buf)->cmd = AD_CMD_FND_RES;
		encyo((const unsigned char*)req->key, (unsigned char*)res_.key_obscure);
		fndreslen_ += pack_discovery_cmd(res_, buf + fndreslen_);

		pfndres_ = (char*)malloc(fndreslen_);
		memcpy(pfndres_, buf, fndreslen_);
	}

	if (g_discovery_sendto && pfndres_ && (fndreslen_ > 0)) {
        // 对于AP模式，我们需要确保使用正确的源IP
        struct sockaddr_in client_addr = toaddr;
        
        // 记录客户端的IP地址，这对调试很有用
        // printf("Discovery request from client IP: %s:%d\n", 
        //        inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
        
        // // 如果我们在AP模式，打印更多调试信息
        // if (g_netmode == eAP) {
        //     printf("AP mode discovery response with URL: %s, IP: %s\n", 
        //            res_.url.c_str(), res_.ip.c_str());
        // }
        
        // printf("Sending discovery response to %s:%d, length: %d\n", 
        //        inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port), fndreslen_);
        
        // 发送响应
        int result = sendto(socksend_, pfndres_, fndreslen_, 0, 
                    (sockaddr*)&client_addr, sizeof(client_addr));
        
        // if (result < 0) {
        //     perror("sendto failed");
        //     // 尝试使用备用套接字
        //     if (socksend_ != sockrecv_) {
        //         printf("Retrying with alternative socket...\n");
        //         result = sendto(sockrecv_, pfndres_, fndreslen_, 0, 
        //                     (sockaddr*)&client_addr, sizeof(client_addr));
        //         if (result < 0) {
        //             perror("Alternative socket sendto also failed");
        //         }
        //     }
        // }
    }
}

void discovery::on_net(SOCKET sock, const DiscCmdHeader* header, const DiscCmdNetReq* req, const sockaddr_in& toaddr)
{
	char buf[MTU];
	int ret = sizeof(DiscCmdHeader);
	memcpy(buf, header, sizeof(DiscCmdHeader));

	DiscCmdNetRes res;
	res.result = 0;
	ret += pack_discovery_cmd(res, buf + ret);

	if (g_discovery_sendto)			
		sendto(sock, buf, ret, 0, (sockaddr*)&toaddr, sizeof(toaddr));
}
