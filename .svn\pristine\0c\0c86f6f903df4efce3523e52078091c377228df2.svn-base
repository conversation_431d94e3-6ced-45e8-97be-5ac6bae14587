#include <jni.h>
#include <string>
#include <fcntl.h>
#include <unistd.h>
#include <termios.h>
#include <pthread.h>
#include <android/log.h>
#include <errno.h>
#include <dirent.h>
#include <sys/inotify.h>

#define LOG_TAG "SerialJNI"
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

int defBaudRate = 921600;


// 串口文件描述符
int serial_fd = -1;
pthread_t recv_thread;
bool running = false;
JavaVM *gJvm = nullptr;
jobject gObj = nullptr;
jmethodID gMethodID = nullptr;
unsigned char sendBuffer[10]; // 包装待发送的命令
bool hasDataToSend = false;
pthread_mutex_t sendMutex = PTHREAD_MUTEX_INITIALIZER;

pthread_t monitor_thread;
bool monitoring = true;
jmethodID gDeviceCallback = nullptr;
pthread_mutex_t serialMutex = PTHREAD_MUTEX_INITIALIZER;



//
//void *serial_recv_thread(void *arg) {
//    JNIEnv *env;
//    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
//        LOGE("Failed to attach thread to JVM");
//        return nullptr;
//    }
//
//    unsigned char byte;
//    unsigned char packetBuffer[10];
//    int packetIndex = 0;
//
//    while (running) {
//        int bytes_read = read(serial_fd, &byte, 1);  // 逐个字节读取
//        if (bytes_read < 0) {
////            usleep(100000);  // 如果没有数据则休眠 100ms，避免占用过多 CPU
//            continue;
//        }
////        LOGE("Rcvie data 0x%02X",byte);
//
//        // 根据字节的内容处理
//        if (packetIndex == 0 && byte == 0xFA) {
//            packetBuffer[packetIndex++] = byte;  // 第一个字节是 0xFA
//        } else if (packetIndex == 1 && byte == 0xFB) {
//            packetBuffer[packetIndex++] = byte;  // 第二个字节是 0xFB
//        } else if (packetIndex >= 2 && packetIndex < 8) {
//            packetBuffer[packetIndex++] = byte;  // 其余字节直接存储
//        } else if (packetIndex == 8 && byte == 0xFC) {
//            packetBuffer[packetIndex++] = byte;  // 倒数第二个字节是 0xFC
//        } else if (packetIndex == 9 && byte == 0xFD) {
//            packetBuffer[packetIndex++] = byte;  // 最后一个字节是 0xFD
//            // 已经收到了完整的数据包
//            LOGI("Received complete packet");
//
//            jint data[6];
//            data[0] = (jint)packetBuffer[2];  // 获取控制字节
//            for (int i = 0; i < 5; i++) {
//                data[i + 1] = (jint)packetBuffer[3 + i];  // 获取数据字节
//            }
//
//            jintArray result = env->NewIntArray(6);
//            if (result != nullptr) {
//                env->SetIntArrayRegion(result, 0, 6, data);
//                env->CallVoidMethod(gObj, gMethodID, result);  // 回调 Java 方法
//                env->DeleteLocalRef(result);
//            }
//
//            packetIndex = 0;  // 重置索引，准备接收下一个数据包
//            tcflush(serial_fd, TCIFLUSH);
//        } else {
//            // 如果接收到的字节不符合预期格式，重置接收状态
//            packetIndex = 0;
//            tcflush(serial_fd, TCIFLUSH);
//        }
//
//        usleep(100000);  // 每次循环休眠 100ms，防止占用过多 CPU
//    }
//
//    gJvm->DetachCurrentThread();
//    return nullptr;
//}
// 串口接收线程
void *serial_recv_thread(void *arg) {
    JNIEnv *env;
    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
        LOGE("Failed to attach thread to JVM");
        return nullptr;
    }

    unsigned char buffer[10];

    // 设置串口为非阻塞模式
    int flags = fcntl(serial_fd, F_GETFL, 0);
    if (flags < 0) {
        LOGE("Failed to get file descriptor flags");
        return nullptr;
    }
    fcntl(serial_fd, F_SETFL, flags | O_NONBLOCK);

    while (running) {
        int bytes_read = read(serial_fd, buffer, sizeof(buffer));
        if (bytes_read < 0) {
            usleep(10000); // 如果没有数据则休眠 100ms，避免占用过多 CPU
            continue;
        }

//        LOGI("Received %d bytes", bytes_read);

        if (bytes_read >= 10 && buffer[0] == 0xFA && buffer[1] == 0xFB &&
            buffer[8] == 0xFC && buffer[9] == 0xFD) {

            jint data[6];
            data[0] = (jint)buffer[2];
            for (int i = 0; i < 5; i++) {
                data[i + 1] = (jint)buffer[3 + i];
//                LOGI("data[%d] = %x",i,data[i] );
            }


            jintArray result = env->NewIntArray(6);
            if (result != nullptr) {
                env->SetIntArrayRegion(result, 0, 6, data);

                env->CallVoidMethod(gObj, gMethodID, result);
                env->DeleteLocalRef(result);
            }
        }

        usleep(10000); // 每次循环休眠 100ms，防止占用过多 CPU
    }

    gJvm->DetachCurrentThread();
    return nullptr;
}

// 串口配置
int configure_serial_port(int fd, int baudRate) {
    struct termios options;
    if (tcgetattr(fd, &options) < 0) {
        LOGE("获取串口属性失败");
        return -1;
    }

    cfsetispeed(&options, baudRate);
    cfsetospeed(&options, baudRate);
    options.c_cflag &= ~PARENB;
    options.c_cflag &= ~CSTOPB;
    options.c_cflag &= ~CSIZE;
    options.c_cflag |= CS8;
    options.c_cflag |= CLOCAL | CREAD;
    options.c_iflag &= ~(IXON | IXOFF | IXANY);
    options.c_oflag &= ~OPOST;
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

    if (tcsetattr(fd, TCSANOW, &options) < 0) {
        LOGE("设置串口属性失败");
        return -1;
    }
    return 0;
}


void *device_monitor_thread(void *arg) {
    JNIEnv *env;
    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
        LOGE("无法附加线程到 JVM");
        return nullptr;
    }

    // **1. 线程启动时，先检查是否已经存在 ttyACM 设备**
    DIR *dir = opendir("/dev/");
    if (dir != nullptr) {
        struct dirent *entry;
        while ((entry = readdir(dir)) != nullptr) {
            if (strncmp(entry->d_name, "ttyACM", 6) == 0) {
                LOGI("检测到已有设备: %s", entry->d_name);

                pthread_mutex_lock(&serialMutex);
                if (serial_fd < 0) {
                    char device_path[256];
                    snprintf(device_path, sizeof(device_path), "/dev/%s", entry->d_name);
                    serial_fd = open(device_path, O_RDWR | O_NOCTTY);
                    if (serial_fd >= 0) {
                        LOGI("成功打开串口设备: %s", device_path);
                        if (configure_serial_port(serial_fd, defBaudRate) == 0) {
                            running = true;
                            pthread_create(&recv_thread, nullptr, serial_recv_thread, nullptr);
                            env->CallVoidMethod(gObj, gDeviceCallback, JNI_TRUE);
                        } else {
                            close(serial_fd);
                            serial_fd = -1;
                        }
                    }
                }
                pthread_mutex_unlock(&serialMutex);
                break; // 只检查一个设备
            }
        }
        closedir(dir);
    }

    // **2. 继续执行 inotify 监听逻辑**
    int inotify_fd = inotify_init();
    if (inotify_fd < 0) {
        LOGE("无法初始化 inotify");
        gJvm->DetachCurrentThread();
        return nullptr;
    }

    int watch_fd = inotify_add_watch(inotify_fd, "/dev/", IN_CREATE | IN_DELETE);
    if (watch_fd < 0) {
        LOGE("无法监听 /dev/ 目录");
        close(inotify_fd);
        gJvm->DetachCurrentThread();
        return nullptr;
    }

    char buffer[1024];
    while (monitoring) {
        int length = read(inotify_fd, buffer, sizeof(buffer));
        if (length < 0) {
            usleep(50000);
            continue;
        }

        int i = 0;
        while (i < length) {
            struct inotify_event *event = (struct inotify_event *)&buffer[i];
            if (event->len > 0 && strncmp(event->name, "ttyACM", 6) == 0) {
                if (event->mask & IN_CREATE) {
                    LOGI("检测到设备插入: %s", event->name);

                    pthread_mutex_lock(&serialMutex);
                    if (serial_fd < 0) {
                        char device_path[256];
                        snprintf(device_path, sizeof(device_path), "/dev/%s", event->name);
                        serial_fd = open(device_path, O_RDWR | O_NOCTTY);
                        if (serial_fd >= 0) {
                            LOGI("成功打开串口设备: %s", device_path);
                            if (configure_serial_port(serial_fd, defBaudRate) == 0) {
                                running = true;
                                pthread_create(&recv_thread, nullptr, serial_recv_thread, nullptr);
                                env->CallVoidMethod(gObj, gDeviceCallback, JNI_TRUE);
                            } else {
                                close(serial_fd);
                                serial_fd = -1;
                            }
                        }
                    }
                    pthread_mutex_unlock(&serialMutex);
                } else if (event->mask & IN_DELETE) {
                    LOGI("检测到设备拔出: %s", event->name);
                    pthread_mutex_lock(&serialMutex);
                    if (serial_fd >= 0) {
                        running = false;
                        pthread_join(recv_thread, nullptr);
                        close(serial_fd);
                        serial_fd = -1;
                        env->CallVoidMethod(gObj, gDeviceCallback, JNI_FALSE);
                    }
                    pthread_mutex_unlock(&serialMutex);
                }
            }
            i += sizeof(struct inotify_event) + event->len;
        }
    }

    inotify_rm_watch(inotify_fd, watch_fd);
    close(inotify_fd);
    gJvm->DetachCurrentThread();
    return nullptr;
}



// 初始化 JNI
extern "C" JNIEXPORT int JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_startMonitor(JNIEnv *env, jobject obj) {
    // 获取 JavaVM 和其他需要的对象引用
    env->GetJavaVM(&gJvm);
    gObj = env->NewGlobalRef(obj);

    jclass clazz = env->GetObjectClass(obj);
    gMethodID = env->GetMethodID(clazz, "onSerialDataReceived", "([I)V");
    if (gMethodID == nullptr) {
        LOGE("Failed to find Java callback method");
        return JNI_FALSE;
    }

    gDeviceCallback = env->GetMethodID(clazz, "onDeviceStateChanged", "(Z)V");
    if (!gDeviceCallback) {
        LOGE("无法找到 Java 设备状态回调方法");
        return JNI_FALSE;
    }

    monitoring = true;
    if (pthread_create(&monitor_thread, nullptr, device_monitor_thread, nullptr) != 0) {
        LOGE("无法创建设备监视线程");
        return JNI_FALSE;
    }

    LOGI("设备监视线程已启动");
    return JNI_TRUE;
}

// 停止监视线程
extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_stopMonitor(JNIEnv *env, jobject obj) {
    monitoring = false;
    pthread_join(monitor_thread, nullptr);

    if (serial_fd >= 0) {
        running = false;
        pthread_join(recv_thread, nullptr);
        close(serial_fd);
        serial_fd = -1;
    }

    if (gObj) {
        env->DeleteGlobalRef(gObj);
        gObj = nullptr;
    }
    LOGI("设备监视线程已停止");
}

extern "C" JNIEXPORT int JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_initSerial(JNIEnv *env, jobject obj, jint baudRate) {
    // 获取 JavaVM 和其他需要的对象引用
    env->GetJavaVM(&gJvm);
    gObj = env->NewGlobalRef(obj);

    jclass clazz = env->GetObjectClass(obj);
    gMethodID = env->GetMethodID(clazz, "onSerialDataReceived", "([I)V");
    if (gMethodID == nullptr) {
        LOGE("Failed to find Java callback method");
        return JNI_FALSE;
    }

    // 打开 /dev 目录，查找所有 ttyACM 设备
    DIR *dir = opendir("/dev/");
    if (dir == nullptr) {
        LOGE("Failed to open /dev/ directory");
        return JNI_FALSE;
    }

    struct dirent *entry;
    bool found = false;

    while ((entry = readdir(dir)) != nullptr) {
        // 查找以 "ttyACM" 开头的设备
        if (strncmp(entry->d_name, "ttyACM", 6) == 0) {
            // 构造设备文件的完整路径
            char device_path[256];
            snprintf(device_path, sizeof(device_path), "/dev/%s", entry->d_name);

            // 尝试打开这个串口设备
            serial_fd = open(device_path, O_RDWR | O_NOCTTY);
            if (serial_fd >= 0) {
                LOGI("Successfully opened serial port: %s", device_path);
                found = true;
                break;
            }
        }
    }

    closedir(dir);

    // 如果没有找到可用的串口设备
    if (!found) {
        LOGE("Failed to open any serial port");
        return JNI_FALSE;
    }

    // 配置串口属性
    if (configure_serial_port(serial_fd, baudRate) != 0) {
        LOGE("Failed to configure serial port");
        close(serial_fd);
        return JNI_FALSE;
    }
    defBaudRate = baudRate;

    running = true;
    if (pthread_create(&recv_thread, nullptr, serial_recv_thread, nullptr) != 0) {
        LOGE("Failed to create receive thread");
        return JNI_FALSE;
    }

    LOGI("Serial port initialized successfully");
    return JNI_TRUE;
}

extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_sendCommand(JNIEnv *env, jobject obj, jint ctrl, jint command, jintArray data) {
    if ((serial_fd < 0) || (running == 0)) {
        LOGE("Serial port not open");
        return;
    }

    jint *data_arr = env->GetIntArrayElements(data, nullptr);
    if (data_arr == nullptr) {
        LOGE("Failed to get data array");
        return;
    }

    // 构造要发送的数据包
    sendBuffer[0] = 0xfa;
    sendBuffer[1] = 0xfb;
    sendBuffer[2] = (unsigned char)ctrl;
    sendBuffer[3] = (unsigned char)command;

    for (int i = 0; i < 4; i++) {
        sendBuffer[4 + i] = (unsigned char)(data_arr[i] & 0xFF);
    }
    sendBuffer[8] = 0xfc;
    sendBuffer[9] = 0xfd;

    // 一次性发送所有字节
    pthread_mutex_lock(&sendMutex);

    ssize_t bytes_written = write(serial_fd, sendBuffer, 10);
    if (bytes_written != sizeof(sendBuffer)) {
        LOGE("Failed to send data, error=%s", strerror(errno));
    } else {
        LOGI("Sent successfully");
    }


    // 等待发送缓冲区清空，确保数据完全发送
//    tcdrain(serial_fd);
    usleep(10000);

    pthread_mutex_unlock(&sendMutex);

    env->ReleaseIntArrayElements(data, data_arr, 0);
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_isSerialConnected(JNIEnv *env, jobject thiz) {
    // 打开 /dev 目录
    DIR *dir = opendir("/dev/");
    if (dir == nullptr) {
        LOGI("Failed to open /dev directory");
        return JNI_FALSE; // 目录打开失败，直接返回
    }

    struct dirent *entry;
    jboolean result = JNI_FALSE; // 默认认为串口未连接

    while ((entry = readdir(dir)) != nullptr) {
        if (strncmp(entry->d_name, "ttyACM", 6) == 0) { // 找到 ttyACM 设备
            LOGI("Serial port alive: %s", entry->d_name);
            result = JNI_TRUE;
            break; // 发现第一个匹配的设备后跳出循环
        }
    }

    closedir(dir); // 关闭目录，防止资源泄漏
    return result;
}


// 关闭串口
extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_closeSerial(JNIEnv *env, jobject obj) {
    running = false;
    pthread_join(recv_thread, nullptr);

    if (serial_fd >= 0) {
        close(serial_fd);
        serial_fd = -1;
    }

    if (gObj != nullptr) {
        env->DeleteGlobalRef(gObj);
        gObj = nullptr;
    }

    LOGI("Serial port closed");
}
