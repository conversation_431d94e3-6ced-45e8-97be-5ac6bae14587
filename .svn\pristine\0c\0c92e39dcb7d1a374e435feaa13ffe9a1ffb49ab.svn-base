<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 23 11:03:20 CST 2025 -->
<title>TouptekIspParam</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-23">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, enum: TouptekIspParam">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#enum-constant-summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li><a href="#enum-constant-detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="Enum Class TouptekIspParam" class="title">Enum Class TouptekIspParam</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">java.lang.Enum</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;
<div class="inheritance">com.android.rockchip.camera2.util.TouptekIspParam</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public enum </span><span class="element-name type-name-label">TouptekIspParam</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;</span></div>
<div class="block">枚举类 TouptekIspParam 定义了摄像头 ISP 参数的键值对。
 <p>
 每个枚举项对应一个 ISP 参数及其唯一的整数值。
 此类同时提供了参数的存储、读取、更新和监听机制。
 支持与设备串口通信，可以向设备发送参数或从设备读取参数。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TouptekIspParam.OnDataChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">定义数据变化监听器接口。</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TouptekIspParam.OnSerialStateChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a></code></div>
<div class="col-last odd-row-color">
<div class="block">定义串口状态变化监听器接口。</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TouptekIspParam.ParamData.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></code></div>
<div class="col-last even-row-color">
<div class="block">参数数据结构</div>
</div>
</div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-java.lang.Enum">从类继承的嵌套类/接口&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a></h2>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">Enum.EnumDesc</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">E</a> extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">E</a>&gt;&gt;</code></div>
</section>
</li>
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<li>
<section class="constants-summary" id="enum-constant-summary">
<h2>枚举常量概要</h2>
<div class="caption"><span>枚举常量</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">枚举常量</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_BANDWIDTH" class="member-name-link">TOUPTEK_PARAM_BANDWIDTH</a></code></div>
<div class="col-last even-row-color">
<div class="block">带宽控制（影响图像处理的速度和质量）</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_BRIGHTNESS" class="member-name-link">TOUPTEK_PARAM_BRIGHTNESS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">亮度</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_COLORORGRAY" class="member-name-link">TOUPTEK_PARAM_COLORORGRAY</a></code></div>
<div class="col-last even-row-color">
<div class="block">彩色/灰度模式选择</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_COLORTONE" class="member-name-link">TOUPTEK_PARAM_COLORTONE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">色彩色调</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_CONTRAST" class="member-name-link">TOUPTEK_PARAM_CONTRAST</a></code></div>
<div class="col-last even-row-color">
<div class="block">对比度</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_CTBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_CTBLUEGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">彩色温度蓝色通道增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_CTGREENGAIN" class="member-name-link">TOUPTEK_PARAM_CTGREENGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">彩色温度绿色通道增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_CTREDGAIN" class="member-name-link">TOUPTEK_PARAM_CTREDGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">彩色温度红色通道增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_DARKENHANCE" class="member-name-link">TOUPTEK_PARAM_DARKENHANCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">暗部增强</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_DENOISE" class="member-name-link">TOUPTEK_PARAM_DENOISE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">降噪参数</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURECHOICE" class="member-name-link">TOUPTEK_PARAM_EXPOSURECHOICE</a></code></div>
<div class="col-last even-row-color">
<div class="block">曝光模式选择</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURECOMPENSATION" class="member-name-link">TOUPTEK_PARAM_EXPOSURECOMPENSATION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">曝光补偿</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSUREGAIN" class="member-name-link">TOUPTEK_PARAM_EXPOSUREGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">曝光增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURETIME" class="member-name-link">TOUPTEK_PARAM_EXPOSURETIME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">曝光时间</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_FLIP" class="member-name-link">TOUPTEK_PARAM_FLIP</a></code></div>
<div class="col-last even-row-color">
<div class="block">翻转效果（水平/垂直翻转）</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_GAMMA" class="member-name-link">TOUPTEK_PARAM_GAMMA</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Gamma 校正</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_HUE" class="member-name-link">TOUPTEK_PARAM_HUE</a></code></div>
<div class="col-last even-row-color">
<div class="block">色调</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_HZ" class="member-name-link">TOUPTEK_PARAM_HZ</a></code></div>
<div class="col-last odd-row-color">
<div class="block">频率</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_ISP_DEFAULT_TYPE" class="member-name-link">TOUPTEK_PARAM_ISP_DEFAULT_TYPE</a></code></div>
<div class="col-last even-row-color">
<div class="block">场景选择参数</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_LDCRATIO" class="member-name-link">TOUPTEK_PARAM_LDCRATIO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">低动态范围对比度比率</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_MIRROR" class="member-name-link">TOUPTEK_PARAM_MIRROR</a></code></div>
<div class="col-last even-row-color">
<div class="block">镜像效果（水平/垂直镜像）</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_ROI_HEIGHT" class="member-name-link">TOUPTEK_PARAM_ROI_HEIGHT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_ROI_LEFT" class="member-name-link">TOUPTEK_PARAM_ROI_LEFT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_ROI_TOP" class="member-name-link">TOUPTEK_PARAM_ROI_TOP</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_ROI_WIDTH" class="member-name-link">TOUPTEK_PARAM_ROI_WIDTH</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_SATURATION" class="member-name-link">TOUPTEK_PARAM_SATURATION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">饱和度</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_SHARPNESS" class="member-name-link">TOUPTEK_PARAM_SHARPNESS</a></code></div>
<div class="col-last even-row-color">
<div class="block">锐化参数</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_VERSION" class="member-name-link">TOUPTEK_PARAM_VERSION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">版本号</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_WBBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_WBBLUEGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">蓝色通道的白平衡增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_WBCHOICE" class="member-name-link">TOUPTEK_PARAM_WBCHOICE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">白平衡模式选择</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_WBGREENGAIN" class="member-name-link">TOUPTEK_PARAM_WBGREENGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">绿色通道的白平衡增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_WBREDGAIN" class="member-name-link">TOUPTEK_PARAM_WBREDGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">红色通道的白平衡增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_WDREXPRATIO" class="member-name-link">TOUPTEK_PARAM_WDREXPRATIO</a></code></div>
<div class="col-last even-row-color">
<div class="block">宽动态范围曝光比率</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">已过时的方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)" class="member-name-link">addOnDataChangedListener</a><wbr>(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">添加数据变化监听器</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromInt(int)" class="member-name-link">fromInt</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">根据整数值获取枚举项。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr>?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAllData()" class="member-name-link">getAllData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取所有存储的数据。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAllParamData()" class="member-name-link">getAllParamData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取所有参数的数据信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getData(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取存储的数据。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDefaultValue(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getDefaultValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取参数的默认值</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getIsDisableValue(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getIsDisableValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取参数的禁用状态</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getLongData(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getLongData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取存储的 long 类型数据。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMaxValue(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getMaxValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取参数的最大值。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMinValue(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getMinValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取参数的最小值。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getParamByIndex(int)" class="member-name-link">getParamByIndex</a><wbr>(int&nbsp;index)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">根据索引获取对应的枚举项。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getParamData(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getParamData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取参数的完整数据信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getParamsRangeReceived()" class="member-name-link">getParamsRangeReceived</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取所有参数范围是否已接收</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static com.android.rockchip.camera2.util.touptek_serial_rk</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getSerialInstance()" class="member-name-link">getSerialInstance</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取串口实例</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getValue()" class="member-name-link">getValue</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取枚举值的整数值。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#handleReceivedData(com.android.rockchip.camera2.util.TouptekIspParam,long,boolean)" class="member-name-link">handleReceivedData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;value,
 boolean&nbsp;isLongValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">接收到设备数据后处理更新</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#init(android.content.Context)" class="member-name-link">init</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">初始化 TouptekIspParam 和串口通信。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isSerialConnected()" class="member-name-link">isSerialConnected</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">检查串口是否已连接</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">停止串口监控和相关资源。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)" class="member-name-link">removeOnDataChangedListener</a><wbr>(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">移除数据变化监听器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#requestAllParamRanges()" class="member-name-link">requestAllParamRanges</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">从相机设备中获取所有默认参数</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#saveAllDefaultValuesToLocal(boolean)" class="member-name-link">saveAllDefaultValuesToLocal</a><wbr>(boolean&nbsp;sendToDevice)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">将所有参数恢复为默认值</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#sendAllParamsToDevice()" class="member-name-link">sendAllParamsToDevice</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">发送所有已保存的参数到设备</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int)" class="member-name-link">sendToDevice</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value,
 int&nbsp;ctrl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">仅发送参数到设备，不保存到本地，带控制字节</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)" class="member-name-link">setOnDataChangedListener</a><wbr>(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">已过时。</span>
<div class="deprecation-comment">推荐使用 addOnDataChangedListener</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnSerialStateChangedListener)" class="member-name-link">setOnSerialStateChangedListener</a><wbr>(<a href="TouptekIspParam.OnSerialStateChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a>&nbsp;listener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置串口状态变化监听器。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamDefault</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;defaultValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置参数的自定义默认值</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean)" class="member-name-link">setParamDisabled</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 boolean&nbsp;isDisable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置参数的禁用状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamMaxValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;maxValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置参数的自定义最大值</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamMinValue</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;minValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置参数的自定义最小值</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int)" class="member-name-link">setParamRange</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 boolean&nbsp;isDisable,
 int&nbsp;minValue,
 int&nbsp;maxValue,
 int&nbsp;defaultValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置参数的完整范围（包含禁用状态）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setParamsRangeReceived(boolean)" class="member-name-link">setParamsRangeReceived</a><wbr>(boolean&nbsp;ParamsRangeReceived)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置所有参数范围已接收标志</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">updateParam</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">更新本地参数值，并发送到设备</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int,int)" class="member-name-link">updateParam</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value,
 int&nbsp;ctrl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">更新本地参数值，并发送到设备，带控制字节</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#valueOf(java.lang.String)" class="member-name-link">valueOf</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the enum constant of this class with the specified name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#values()" class="member-name-link">values</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Enum">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#compareTo(E)" title="java.lang中的类或接口" class="external-link">compareTo</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#describeConstable()" title="java.lang中的类或接口" class="external-link">describeConstable</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#getDeclaringClass()" title="java.lang中的类或接口" class="external-link">getDeclaringClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#name()" title="java.lang中的类或接口" class="external-link">name</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#ordinal()" title="java.lang中的类或接口" class="external-link">ordinal</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#valueOf(java.lang.Class,java.lang.String)" title="java.lang中的类或接口" class="external-link">valueOf</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<li>
<section class="constant-details" id="enum-constant-detail">
<h2>枚举常量详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="TOUPTEK_PARAM_VERSION">
<h3>TOUPTEK_PARAM_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_VERSION</span></div>
<div class="block">版本号</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURECHOICE">
<h3>TOUPTEK_PARAM_EXPOSURECHOICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURECHOICE</span></div>
<div class="block">曝光模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURECOMPENSATION">
<h3>TOUPTEK_PARAM_EXPOSURECOMPENSATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURECOMPENSATION</span></div>
<div class="block">曝光补偿</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURETIME">
<h3>TOUPTEK_PARAM_EXPOSURETIME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURETIME</span></div>
<div class="block">曝光时间</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSUREGAIN">
<h3>TOUPTEK_PARAM_EXPOSUREGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSUREGAIN</span></div>
<div class="block">曝光增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBCHOICE">
<h3>TOUPTEK_PARAM_WBCHOICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBCHOICE</span></div>
<div class="block">白平衡模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBREDGAIN">
<h3>TOUPTEK_PARAM_WBREDGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBREDGAIN</span></div>
<div class="block">红色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBGREENGAIN">
<h3>TOUPTEK_PARAM_WBGREENGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBGREENGAIN</span></div>
<div class="block">绿色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBBLUEGAIN">
<h3>TOUPTEK_PARAM_WBBLUEGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBBLUEGAIN</span></div>
<div class="block">蓝色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_SHARPNESS">
<h3>TOUPTEK_PARAM_SHARPNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_SHARPNESS</span></div>
<div class="block">锐化参数</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_DENOISE">
<h3>TOUPTEK_PARAM_DENOISE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_DENOISE</span></div>
<div class="block">降噪参数</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_MIRROR">
<h3>TOUPTEK_PARAM_MIRROR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_MIRROR</span></div>
<div class="block">镜像效果（水平/垂直镜像）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_FLIP">
<h3>TOUPTEK_PARAM_FLIP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_FLIP</span></div>
<div class="block">翻转效果（水平/垂直翻转）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_SATURATION">
<h3>TOUPTEK_PARAM_SATURATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_SATURATION</span></div>
<div class="block">饱和度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_GAMMA">
<h3>TOUPTEK_PARAM_GAMMA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_GAMMA</span></div>
<div class="block">Gamma 校正</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CONTRAST">
<h3>TOUPTEK_PARAM_CONTRAST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CONTRAST</span></div>
<div class="block">对比度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_HZ">
<h3>TOUPTEK_PARAM_HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_HZ</span></div>
<div class="block">频率</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_BRIGHTNESS">
<h3>TOUPTEK_PARAM_BRIGHTNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_BRIGHTNESS</span></div>
<div class="block">亮度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_HUE">
<h3>TOUPTEK_PARAM_HUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_HUE</span></div>
<div class="block">色调</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_COLORORGRAY">
<h3>TOUPTEK_PARAM_COLORORGRAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_COLORORGRAY</span></div>
<div class="block">彩色/灰度模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_BANDWIDTH">
<h3>TOUPTEK_PARAM_BANDWIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_BANDWIDTH</span></div>
<div class="block">带宽控制（影响图像处理的速度和质量）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_COLORTONE">
<h3>TOUPTEK_PARAM_COLORTONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_COLORTONE</span></div>
<div class="block">色彩色调</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTREDGAIN">
<h3>TOUPTEK_PARAM_CTREDGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTREDGAIN</span></div>
<div class="block">彩色温度红色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTGREENGAIN">
<h3>TOUPTEK_PARAM_CTGREENGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTGREENGAIN</span></div>
<div class="block">彩色温度绿色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTBLUEGAIN">
<h3>TOUPTEK_PARAM_CTBLUEGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTBLUEGAIN</span></div>
<div class="block">彩色温度蓝色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_DARKENHANCE">
<h3>TOUPTEK_PARAM_DARKENHANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_DARKENHANCE</span></div>
<div class="block">暗部增强</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WDREXPRATIO">
<h3>TOUPTEK_PARAM_WDREXPRATIO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WDREXPRATIO</span></div>
<div class="block">宽动态范围曝光比率</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_LDCRATIO">
<h3>TOUPTEK_PARAM_LDCRATIO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_LDCRATIO</span></div>
<div class="block">低动态范围对比度比率</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_ROI_LEFT">
<h3>TOUPTEK_PARAM_ROI_LEFT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_ROI_LEFT</span></div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_ROI_TOP">
<h3>TOUPTEK_PARAM_ROI_TOP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_ROI_TOP</span></div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_ROI_WIDTH">
<h3>TOUPTEK_PARAM_ROI_WIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_ROI_WIDTH</span></div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_ROI_HEIGHT">
<h3>TOUPTEK_PARAM_ROI_HEIGHT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_ROI_HEIGHT</span></div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_ISP_DEFAULT_TYPE">
<h3>TOUPTEK_PARAM_ISP_DEFAULT_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_ISP_DEFAULT_TYPE</span></div>
<div class="block">场景选择参数</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="values()">
<h3>values</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>[]</span>&nbsp;<span class="element-name">values</span>()</div>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
<dl class="notes">
<dt>返回:</dt>
<dd>an array containing the constants of this enum class, in the order they are declared</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="valueOf(java.lang.String)">
<h3>valueOf</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">valueOf</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns the enum constant of this class with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this class.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>name</code> - 要返回的枚举常量的名称。</dd>
<dt>返回:</dt>
<dd>返回带有指定名称的枚举常量</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></code> - if this enum class has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/NullPointerException.html" title="java.lang中的类或接口" class="external-link">NullPointerException</a></code> - 如果参数为空值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getValue()">
<h3>getValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getValue</span>()</div>
<div class="block">获取枚举值的整数值。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前枚举项对应的整数值。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>setParamMinValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamMinValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;minValue)</span></div>
<div class="block">设置参数的自定义最小值</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dd><code>minValue</code> - 自定义最小值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>setParamMaxValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamMaxValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;maxValue)</span></div>
<div class="block">设置参数的自定义最大值</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dd><code>maxValue</code> - 自定义最大值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>setParamDefault</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamDefault</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;defaultValue)</span></div>
<div class="block">设置参数的自定义默认值</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dd><code>defaultValue</code> - 自定义默认值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean)">
<h3>setParamDisabled</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamDisabled</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 boolean&nbsp;isDisable)</span></div>
<div class="block">设置参数的禁用状态</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dd><code>isDisable</code> - true表示禁用，false表示启用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int)">
<h3>setParamRange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamRange</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 boolean&nbsp;isDisable,
 int&nbsp;minValue,
 int&nbsp;maxValue,
 int&nbsp;defaultValue)</span></div>
<div class="block">设置参数的完整范围（包含禁用状态）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dd><code>minValue</code> - 最小值</dd>
<dd><code>maxValue</code> - 最大值</dd>
<dd><code>defaultValue</code> - 默认值</dd>
<dd><code>isDisable</code> - 是否禁用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParamsRangeReceived(boolean)">
<h3>setParamsRangeReceived</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamsRangeReceived</span><wbr><span class="parameters">(boolean&nbsp;ParamsRangeReceived)</span></div>
<div class="block">设置所有参数范围已接收标志</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>ParamsRangeReceived</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParamsRangeReceived()">
<h3>getParamsRangeReceived</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getParamsRangeReceived</span>()</div>
<div class="block">获取所有参数范围是否已接收</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示所有参数范围已接收，false表示未接收</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMinValue(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getMinValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMinValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取参数的最小值。
 <p>
 根据不同参数类型返回其有效范围的最小值。
 优先使用串口接收到的值，如果没有则使用系统默认值。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 需要获取最小值的参数</dd>
<dt>返回:</dt>
<dd>参数的最小值
 <p>
 曝光时间最小值返回0，实际下位机相机设置的值为0.019ms</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMaxValue(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getMaxValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取参数的最大值。
 <p>
 根据不同参数类型返回其有效范围的最大值。
 优先使用串口接收到的值，如果没有则使用系统默认值。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 需要获取最大值的参数</dd>
<dt>返回:</dt>
<dd>参数的最大值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultValue(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getDefaultValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDefaultValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取参数的默认值
 <p>
 根据不同参数类型返回其默认值。
 优先使用串口接收到的值，如果没有则使用系统默认值。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 需要获取默认值的参数</dd>
<dt>返回:</dt>
<dd>参数的默认值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIsDisableValue(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getIsDisableValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIsDisableValue</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取参数的禁用状态
 <p>
 根据不同参数类型返回其使能状态。
 优先使用串口接收到的值，如果没有则使用系统默认值。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 参数类型</dd>
<dt>返回:</dt>
<dd>true表示禁用，false表示启用，默认为false（启用）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParamData(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getParamData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></span>&nbsp;<span class="element-name">getParamData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取参数的完整数据信息
 <p>
 返回包含最小值、最大值、默认值、当前值和禁用状态的参数数据结构。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 需要获取数据的参数</dd>
<dt>返回:</dt>
<dd>参数的完整数据信息</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllParamData()">
<h3>getAllParamData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a>&gt;</span>&nbsp;<span class="element-name">getAllParamData</span>()</div>
<div class="block">获取所有参数的数据信息
 <p>
 返回所有ISP参数的完整数据信息列表。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>包含所有参数数据的列表</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sendAllParamsToDevice()">
<h3>sendAllParamsToDevice</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendAllParamsToDevice</span>()</div>
<div class="block">发送所有已保存的参数到设备
 <p>
 遍历所有参数，将SharedPreferences中保存的值发送到设备。
 只发送已有值的参数。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="fromInt(int)">
<h3>fromInt</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">fromInt</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
<div class="block">根据整数值获取枚举项。
 <p>
 将设备返回的命令值转换为对应的枚举项。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>i</code> - 整数值，对应枚举项的value</dd>
<dt>返回:</dt>
<dd>匹配的枚举项，如果没有匹配项则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="init(android.content.Context)">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">初始化 TouptekIspParam 和串口通信。
 <p>
 此方法应在应用启动时调用，用于初始化参数存储和串口监控。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于获取 SharedPreferences 实例。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSerialInstance()">
<h3>getSerialInstance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">com.android.rockchip.camera2.util.touptek_serial_rk</span>&nbsp;<span class="element-name">getSerialInstance</span>()</div>
<div class="block">获取串口实例
 <p>
 返回用于串口通信的实例对象。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>touptek_serial_rk 实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSerialConnected()">
<h3>isSerialConnected</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSerialConnected</span>()</div>
<div class="block">检查串口是否已连接
 <p>
 用于判断当前串口的连接状态。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>串口连接状态，true 表示已连接，false 表示未连接</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnSerialStateChangedListener)">
<h3>setOnSerialStateChangedListener</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOnSerialStateChangedListener</span><wbr><span class="parameters">(<a href="TouptekIspParam.OnSerialStateChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a>&nbsp;listener)</span></div>
<div class="block">设置串口状态变化监听器。
 <p>
 当串口连接状态变化时，会触发此监听器的回调方法。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 串口状态变化监听器实例。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">停止串口监控和相关资源。
 <p>
 在应用退出或不再需要串口通信时调用。
 释放所有相关资源。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="addOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)">
<h3>addOnDataChangedListener</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOnDataChangedListener</span><wbr><span class="parameters">(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</span></div>
<div class="block">添加数据变化监听器
 <p>
 支持多个监听器同时监听参数变化。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 数据变化监听器实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)">
<h3>removeOnDataChangedListener</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">removeOnDataChangedListener</span><wbr><span class="parameters">(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</span></div>
<div class="block">移除数据变化监听器
 <p>
 从监听器列表中移除指定的监听器。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 要移除的监听器实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)">
<h3>setOnDataChangedListener</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Deprecated.html" title="java.lang中的类或接口" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOnDataChangedListener</span><wbr><span class="parameters">(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</span></div>
<div class="deprecation-block"><span class="deprecated-label">已过时。</span>
<div class="deprecation-comment">推荐使用 addOnDataChangedListener</div>
</div>
<div class="block">设置数据变化监听器（保留兼容性）
 <p>
 为了保持向后兼容，保留此方法。
 推荐使用 addOnDataChangedListener 方法。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 数据变化监听器实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveAllDefaultValuesToLocal(boolean)">
<h3>saveAllDefaultValuesToLocal</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">saveAllDefaultValuesToLocal</span><wbr><span class="parameters">(boolean&nbsp;sendToDevice)</span></div>
<div class="block">将所有参数恢复为默认值
 <p>
 此方法会等待参数范围请求完成，然后再保存默认值
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>sendToDevice</code> - 是否将默认值同时发送到设备</dd>
<dt>返回:</dt>
<dd>成功保存的参数数量，-1表示等待超时</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>updateParam</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">updateParam</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value)</span></div>
<div class="block">更新本地参数值，并发送到设备
 <p>
 此方法将执行三个操作：保存到本地、发送到设备
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要更新的参数</dd>
<dd><code>value</code> - 要更新的 int 类型数据</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int,int)">
<h3>updateParam</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">updateParam</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value,
 int&nbsp;ctrl)</span></div>
<div class="block">更新本地参数值，并发送到设备，带控制字节
 <p>
 此方法将执行三个操作：保存到本地、发送到设备
 可以指定是读取操作还是写入操作。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要更新的参数</dd>
<dd><code>value</code> - 要更新的 int 类型数据</dd>
<dd><code>ctrl</code> - 控制字节，0x01表示写操作，0x00表示读操作</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="requestAllParamRanges()">
<h3>requestAllParamRanges</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">requestAllParamRanges</span>()</div>
<div class="block">从相机设备中获取所有默认参数
 <p>
 通过串口获取设备的所有默认参数范围数据。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int)">
<h3>sendToDevice</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendToDevice</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;value,
 int&nbsp;ctrl)</span></div>
<div class="block">仅发送参数到设备，不保存到本地，带控制字节
 <p>
 通过串口发送命令到设备，可以指定控制字节。
 0x00表示读取操作，0x01表示写入操作。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要发送的参数</dd>
<dd><code>value</code> - 要发送的数据值</dd>
<dd><code>ctrl</code> - 控制字节，0x01表示写操作，0x00表示读操作</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleReceivedData(com.android.rockchip.camera2.util.TouptekIspParam,long,boolean)">
<h3>handleReceivedData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleReceivedData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;value,
 boolean&nbsp;isLongValue)</span></div>
<div class="block">接收到设备数据后处理更新
 <p>
 此方法用于从串口接收到数据后，更新本地参数值并触发回调。
 由<code>touptek_serial_rk.onSerialDataReceived(int[])</code>调用。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 接收到的参数类型</dd>
<dd><code>value</code> - 接收到的参数值</dd>
<dd><code>isLongValue</code> - 是否为长整型值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getData(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取存储的数据。
 <p>
 从SharedPreferences中读取保存的参数值。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要获取的参数。</dd>
<dt>返回:</dt>
<dd>存储的 int 类型数据，如果未找到则返回 -1。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLongData(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getLongData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取存储的 long 类型数据。
 <p>
 从SharedPreferences中读取保存的长整型参数值，主要用于版本号等。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要获取的参数。</dd>
<dt>返回:</dt>
<dd>存储的 long 类型数据，如果未找到则返回 -1。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllData()">
<h3>getAllData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr>?&gt;</span>&nbsp;<span class="element-name">getAllData</span>()</div>
<div class="block">获取所有存储的数据。
 <p>
 返回SharedPreferences中所有保存的参数值。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>包含所有存储键值对的 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link"><code>Map</code></a>，键为参数名称，值为对应的存储数据。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParamByIndex(int)">
<h3>getParamByIndex</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">getParamByIndex</span><wbr><span class="parameters">(int&nbsp;index)</span></div>
<div class="block">根据索引获取对应的枚举项。
 <p>
 用于在UI中通过索引选择参数。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>index</code> - 索引值，从 0 开始。</dd>
<dt>返回:</dt>
<dd>对应的 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a> 枚举项，如果索引无效则返回 null。</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
