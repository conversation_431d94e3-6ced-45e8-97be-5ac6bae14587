/* ***************************************************************
//  gep_transceiver_tcp   version:  1.0  date: 27/27/2009
//  -------------------------------------------------------------
//  Yongming <PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __sip_transceiver_udp_h__
#define __sip_transceiver_udp_h__

#include <utiny/usys_transceiver_udp.h>

class sip_session_context;

class UBEDA_API sip_transceiver_udp : public usys_transceiver_udp
{
public:
    sip_transceiver_udp(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size, unsigned recv_buffer_size);
	virtual ~sip_transceiver_udp();
    usys_smartptr<sip_session_context> sip_session_ptr();
    virtual int get_message_len(const usys_smartptr<usys_data_block>& data_ptr, usys_smartptr_mtbase_ptr& header_ptr);
    virtual int handle_connect();
    virtual int handle_write();
    virtual void handle_close();
	virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& data_ptr, const usys_smartptr_mtbase_ptr& header_ptr);
};

#endif // __gep_transceiver_tcp_h__
