<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Apr 10 13:26:06 CST 2025 -->
<title>C - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-10">
<meta name="description" content="index: C">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">CameraManagerHelper 类用于管理摄像头的打开、预览和资源释放。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#%3Cinit%3E(android.content.Context)" class="member-name-link">CameraManagerHelper(Context)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">构造函数，初始化 CameraManager。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">CaptureImageHelper 类用于处理摄像头图像的抓取和保存。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html#%3Cinit%3E(android.util.Size)" class="member-name-link">CaptureImageHelper(Size)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></dt>
<dd>
<div class="block">构造函数，初始化 ImageReader 和后台线程。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的接口</dt>
<dd>
<div class="block">回调接口，用于通知抓图结果</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a> - 程序包 com.android.rockchip.camera2</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a> - 程序包 com.android.rockchip.camera2.util</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a> - 程序包 com.android.rockchip.camera2.video</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/mediacodecnew/package-summary.html">com.android.rockchip.mediacodecnew</a> - 程序包 com.android.rockchip.mediacodecnew</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface)" class="member-name-link">configCameraOutputs(CameraDevice, Surface, Surface)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">配置摄像头输出的 Surface。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#createImagePath(android.content.Context)" class="member-name-link">createImagePath(Context)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">创建带日期后缀的图片文件路径。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#createVideoPath(android.content.Context)" class="member-name-link">createVideoPath(Context)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">创建带日期后缀的视频文件路径。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
