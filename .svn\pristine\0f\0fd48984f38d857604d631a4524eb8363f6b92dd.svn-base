package com.android.rockchip.camera2.view;

import android.content.Context;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

/**
 * TpImageView - 模仿Android系统相册的可缩放ImageView
 * <p>
 * 全新实现，提供与系统相册一致的缩放和平移体验：
 * - 硬边界限制，无阻尼效果
 * - 双击缩放：适应屏幕 → 1:1原图 → 适应屏幕
 * - 双指缩放时焦点稳定，无跳跃感
 * - 解决手势冲突问题
 * </p>
 * 
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>双指缩放（焦点稳定）</li>
 *   <li>单指平移（硬边界限制）</li>
 *   <li>双击缩放切换</li>
 *   <li>小图自动居中</li>
 *   <li>大图边缘对齐</li>
 * </ul>
 * 
 * <h3>使用方式：</h3>
 * <pre>{@code
 * // 在布局文件中使用
 * <com.android.rockchip.camera2.view.TpImageView
 *     android:id="@+id/zoomable_image"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent" />
 * 
 * // 在代码中使用
 * TpImageView imageView = findViewById(R.id.zoomable_image);
 * TpImageLoader.loadFullImage(imagePath, imageView);
 * }</pre>
 */
public class TpImageView extends AppCompatImageView {
    private static final String TAG = "TpImageView";
    
    /**
     * 手势状态枚举
     */
    private enum GestureState {
        IDLE,       // 空闲状态
        SCALING,    // 缩放中
        PANNING     // 平移中
    }
    
    /**
     * 缩放信息类
     */
    private static class ScaleInfo {
        float currentScale = 1.0f;      // 当前缩放比例
        float fitScreenScale = 1.0f;    // 适应屏幕的缩放比例
        float originalScale = 1.0f;     // 1:1原图的缩放比例
        float minScale = 1f;          // 最小缩放比例
        float maxScale = 10.0f;         // 最大缩放比例
        boolean userSetScaleRange = false; // 用户是否手动设置了缩放范围

        void updateFitScreenScale(float viewWidth, float viewHeight, float imageWidth, float imageHeight) {
            if (imageWidth > 0 && imageHeight > 0 && viewWidth > 0 && viewHeight > 0) {
                float scaleX = viewWidth / imageWidth;
                float scaleY = viewHeight / imageHeight;
                fitScreenScale = Math.min(scaleX, scaleY);
                originalScale = 1.0f;

                // 只有在用户没有手动设置缩放范围时，才使用默认计算
                if (!userSetScaleRange) {
                    minScale = Math.min(0.1f, fitScreenScale * 0.5f); // 允许比适应屏幕更小
                    maxScale = Math.max(10.0f, fitScreenScale * 10.0f);
                }
            }
        }

        void setUserScaleRange(float userMinScale, float userMaxScale) {
            minScale = userMinScale;
            maxScale = userMaxScale;
            userSetScaleRange = true;
        }
        
        float getNextDoubleTapScale() {
            // 简化的双击缩放：始终返回到适应屏幕大小（最小缩放）
            return fitScreenScale;
        }
    }
    
    /**
     * 边界信息类
     */
    private static class BoundaryInfo {
        RectF viewBounds = new RectF();     // 视图边界
        RectF imageBounds = new RectF();    // 图片边界
        
        void updateViewBounds(float width, float height) {
            viewBounds.set(0, 0, width, height);
        }
        
        void updateImageBounds(Matrix matrix, float imageWidth, float imageHeight) {
            imageBounds.set(0, 0, imageWidth, imageHeight);
            matrix.mapRect(imageBounds);
        }
        
        boolean isImageSmallerThanView() {
            return imageBounds.width() <= viewBounds.width() && 
                   imageBounds.height() <= viewBounds.height();
        }
    }
    
    // 核心组件
    private Matrix mMatrix = new Matrix();
    private ScaleInfo mScaleInfo = new ScaleInfo();
    private BoundaryInfo mBoundaryInfo = new BoundaryInfo();
    private GestureState mGestureState = GestureState.IDLE;
    
    // 手势检测器
    private ScaleGestureDetector mScaleGestureDetector;
    private GestureDetector mGestureDetector;
    
    // 触摸状态
    private float mLastTouchX = 0f;
    private float mLastTouchY = 0f;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mResetGestureStateRunnable;
    
    // 配置选项
    private boolean mZoomEnabled = true;
    private boolean mPanEnabled = true;
    private boolean mDoubleTapEnabled = true;
    
    // 监听器
    private OnZoomChangeListener mZoomChangeListener;
    
    /**
     * 缩放变化监听器
     */
    public interface OnZoomChangeListener {
        void onZoomChanged(float scale, float focusX, float focusY);
    }
    
    public TpImageView(Context context) {
        super(context);
        init(context);
    }
    
    public TpImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public TpImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    /**
     * 初始化组件
     */
    private void init(Context context) {
        // 设置ScaleType为MATRIX，支持自定义变换
        setScaleType(ScaleType.MATRIX);
        
        // 初始化手势检测器
        initGestureDetectors(context);
        
        Log.d(TAG, "TpZoomableImageView初始化完成 - 系统相册风格");
    }
    
    /**
     * 初始化手势检测器
     */
    private void initGestureDetectors(Context context) {
        // 缩放手势检测器
        mScaleGestureDetector = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!mZoomEnabled) return false;
                
                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();
                
                return performScale(scaleFactor, focusX, focusY);
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                mGestureState = GestureState.SCALING;
                cancelResetGestureState();
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                // 由于在performScale中已经进行了实时边界修正，这里不需要再次检查
                scheduleResetGestureState();
            }
        });
        
        // 通用手势检测器（用于双击等）
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!mDoubleTapEnabled || !mZoomEnabled) return false;
                
                float targetScale = mScaleInfo.getNextDoubleTapScale();
                float scaleFactor = targetScale / mScaleInfo.currentScale;
                
                return performScale(scaleFactor, e.getX(), e.getY());
            }
            
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                performClick();
                return true;
            }
        });
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = false;

        // 先让手势检测器处理
        if (mScaleGestureDetector != null) {
            handled = mScaleGestureDetector.onTouchEvent(event);
        }

        if (mGestureDetector != null) {
            handled = mGestureDetector.onTouchEvent(event) || handled;
        }

        // 处理平移手势（只在非缩放状态下）
        if (mGestureState != GestureState.SCALING && mPanEnabled) {
            handled = handlePanGesture(event) || handled;
        }

        return handled || super.onTouchEvent(event);
    }

    /**
     * 处理平移手势
     */
    private boolean handlePanGesture(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mGestureState = GestureState.PANNING;
                mLastTouchX = event.getX();
                mLastTouchY = event.getY();
                return true;

            case MotionEvent.ACTION_MOVE:
                if (event.getPointerCount() == 1 && mGestureState == GestureState.PANNING) {
                    float deltaX = event.getX() - mLastTouchX;
                    float deltaY = event.getY() - mLastTouchY;

                    mLastTouchX = event.getX();
                    mLastTouchY = event.getY();

                    return performTranslate(deltaX, deltaY);
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mGestureState = GestureState.IDLE;
                break;
        }

        return false;
    }

    /**
     * 执行缩放操作（优化版本，防止黑边闪烁，支持大倍数缩放）
     */
    private boolean performScale(float scaleFactor, float focusX, float focusY) {
        if (!mZoomEnabled || getDrawable() == null) return false;

        // 更新边界信息
        updateBoundaryInfo();

        // 计算新的缩放比例
        float newScale = mScaleInfo.currentScale * scaleFactor;

        // 限制缩放范围（使用用户设置的最小/最大值）
        if (newScale < mScaleInfo.minScale) {
            scaleFactor = mScaleInfo.minScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.minScale;
        } else if (newScale > mScaleInfo.maxScale) {
            scaleFactor = mScaleInfo.maxScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.maxScale;
        }

        // 对于大倍数缩放，添加性能检查
        if (newScale > 20.0f) {
            Log.d(TAG, "大倍数缩放警告: " + newScale + "倍，可能影响性能");
        }

        // 保存当前Matrix状态
        Matrix tempMatrix = new Matrix(mMatrix);

        // 应用缩放到临时Matrix
        tempMatrix.postScale(scaleFactor, scaleFactor, focusX, focusY);

        // 检查缩放后是否需要边界修正
        float correctionX = 0, correctionY = 0;
        Drawable drawable = getDrawable();
        if (drawable != null) {
            // 计算缩放后的图片边界
            RectF tempImageBounds = new RectF(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            tempMatrix.mapRect(tempImageBounds);

            // 计算需要的边界修正
            correctionX = calculateBoundaryCorrection(tempImageBounds, mBoundaryInfo.viewBounds, true);
            correctionY = calculateBoundaryCorrection(tempImageBounds, mBoundaryInfo.viewBounds, false);

            // 应用边界修正到临时Matrix
            if (correctionX != 0 || correctionY != 0) {
                tempMatrix.postTranslate(correctionX, correctionY);
            }
        }

        // 应用最终的Matrix
        mMatrix.set(tempMatrix);
        setImageMatrix(mMatrix);

        // 更新当前缩放比例
        mScaleInfo.currentScale = getCurrentScale();

        // 通知监听器
        if (mZoomChangeListener != null) {
            mZoomChangeListener.onZoomChanged(mScaleInfo.currentScale, focusX, focusY);
        }

        Log.d(TAG, "缩放: " + mScaleInfo.currentScale + ", 焦点: (" + focusX + ", " + focusY + "), 修正: (" + correctionX + ", " + correctionY + ")");
        return true;
    }

    /**
     * 计算边界修正值
     */
    private float calculateBoundaryCorrection(RectF imageBounds, RectF viewBounds, boolean isHorizontal) {
        if (isHorizontal) {
            if (imageBounds.width() <= viewBounds.width()) {
                // 图片宽度小于等于视图宽度，居中显示
                return viewBounds.centerX() - imageBounds.centerX();
            } else {
                // 图片宽度大于视图宽度，检查边界
                if (imageBounds.left > viewBounds.left) {
                    return viewBounds.left - imageBounds.left;
                } else if (imageBounds.right < viewBounds.right) {
                    return viewBounds.right - imageBounds.right;
                }
            }
        } else {
            if (imageBounds.height() <= viewBounds.height()) {
                // 图片高度小于等于视图高度，居中显示
                return viewBounds.centerY() - imageBounds.centerY();
            } else {
                // 图片高度大于视图高度，检查边界
                if (imageBounds.top > viewBounds.top) {
                    return viewBounds.top - imageBounds.top;
                } else if (imageBounds.bottom < viewBounds.bottom) {
                    return viewBounds.bottom - imageBounds.bottom;
                }
            }
        }
        return 0;
    }

    /**
     * 执行平移操作（参考TransformUtils的硬边界限制）
     */
    private boolean performTranslate(float deltaX, float deltaY) {
        if (!mPanEnabled || getDrawable() == null) return false;

        // 更新边界信息
        updateBoundaryInfo();

        // 计算限制后的平移距离（参考TransformUtils实现）
        float limitedDeltaX = calculateLimitedDeltaX(deltaX);
        float limitedDeltaY = calculateLimitedDeltaY(deltaY);

        // 应用平移
        if (limitedDeltaX != 0 || limitedDeltaY != 0) {
            mMatrix.postTranslate(limitedDeltaX, limitedDeltaY);
            setImageMatrix(mMatrix);

            Log.d(TAG, "平移: deltaX=" + limitedDeltaX + ", deltaY=" + limitedDeltaY);
            return true;
        }

        return false;
    }

    /**
     * 计算水平方向限制后的平移距离（参考TransformUtils）
     */
    private float calculateLimitedDeltaX(float deltaX) {
        if (mBoundaryInfo.imageBounds.width() <= mBoundaryInfo.viewBounds.width()) {
            // 图片宽度小于等于视图宽度，居中显示，不允许平移
            float centerOffset = (mBoundaryInfo.viewBounds.width() - mBoundaryInfo.imageBounds.width()) / 2;
            return centerOffset - mBoundaryInfo.imageBounds.left;
        } else {
            // 图片宽度大于视图宽度，限制边界
            float limitedDeltaX = deltaX;
            if (mBoundaryInfo.imageBounds.left + deltaX > mBoundaryInfo.viewBounds.left) {
                limitedDeltaX = mBoundaryInfo.viewBounds.left - mBoundaryInfo.imageBounds.left;
            } else if (mBoundaryInfo.imageBounds.right + deltaX < mBoundaryInfo.viewBounds.right) {
                limitedDeltaX = mBoundaryInfo.viewBounds.right - mBoundaryInfo.imageBounds.right;
            }
            return limitedDeltaX;
        }
    }

    /**
     * 计算垂直方向限制后的平移距离（参考TransformUtils）
     */
    private float calculateLimitedDeltaY(float deltaY) {
        if (mBoundaryInfo.imageBounds.height() <= mBoundaryInfo.viewBounds.height()) {
            // 图片高度小于等于视图高度，居中显示，不允许平移
            float centerOffset = (mBoundaryInfo.viewBounds.height() - mBoundaryInfo.imageBounds.height()) / 2;
            return centerOffset - mBoundaryInfo.imageBounds.top;
        } else {
            // 图片高度大于视图高度，限制边界
            float limitedDeltaY = deltaY;
            if (mBoundaryInfo.imageBounds.top + deltaY > mBoundaryInfo.viewBounds.top) {
                limitedDeltaY = mBoundaryInfo.viewBounds.top - mBoundaryInfo.imageBounds.top;
            } else if (mBoundaryInfo.imageBounds.bottom + deltaY < mBoundaryInfo.viewBounds.bottom) {
                limitedDeltaY = mBoundaryInfo.viewBounds.bottom - mBoundaryInfo.imageBounds.bottom;
            }
            return limitedDeltaY;
        }
    }

    /**
     * 检查并修正边界
     */
    private void checkAndCorrectBounds() {
        if (getDrawable() == null) return;

        updateBoundaryInfo();

        float deltaX = calculateLimitedDeltaX(0);
        float deltaY = calculateLimitedDeltaY(0);

        if (deltaX != 0 || deltaY != 0) {
            mMatrix.postTranslate(deltaX, deltaY);
            setImageMatrix(mMatrix);
            Log.d(TAG, "边界修正: deltaX=" + deltaX + ", deltaY=" + deltaY);
        }
    }

    /**
     * 更新边界信息
     */
    private void updateBoundaryInfo() {
        Drawable drawable = getDrawable();
        if (drawable == null) return;

        // 更新视图边界
        mBoundaryInfo.updateViewBounds(getWidth(), getHeight());

        // 更新图片边界
        mBoundaryInfo.updateImageBounds(mMatrix, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
    }

    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        float[] values = new float[9];
        mMatrix.getValues(values);
        return values[Matrix.MSCALE_X];
    }

    /**
     * 手势状态重置相关方法
     */
    private void scheduleResetGestureState() {
        cancelResetGestureState();
        mResetGestureStateRunnable = () -> {
            if (mGestureState == GestureState.SCALING) {
                mGestureState = GestureState.IDLE;
                Log.d(TAG, "手势状态重置为IDLE");
            }
        };
        mHandler.postDelayed(mResetGestureStateRunnable, 200);
    }

    private void cancelResetGestureState() {
        if (mResetGestureStateRunnable != null) {
            mHandler.removeCallbacks(mResetGestureStateRunnable);
            mResetGestureStateRunnable = null;
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        resetMatrix();
    }

    @Override
    public void setImageDrawable(@Nullable Drawable drawable) {
        super.setImageDrawable(drawable);
        resetMatrix();
    }

    /**
     * 重置Matrix到初始状态
     */
    public void resetMatrix() {
        cancelResetGestureState();
        mGestureState = GestureState.IDLE;

        Drawable drawable = getDrawable();
        if (drawable == null || getWidth() == 0 || getHeight() == 0) {
            mMatrix.reset();
            setImageMatrix(mMatrix);
            return;
        }

        // 计算适应屏幕的缩放和居中
        float imageWidth = drawable.getIntrinsicWidth();
        float imageHeight = drawable.getIntrinsicHeight();
        float viewWidth = getWidth();
        float viewHeight = getHeight();

        // 更新缩放信息
        mScaleInfo.updateFitScreenScale(viewWidth, viewHeight, imageWidth, imageHeight);

        // 重置Matrix并应用适应屏幕的变换
        mMatrix.reset();

        // 应用缩放
        mMatrix.postScale(mScaleInfo.fitScreenScale, mScaleInfo.fitScreenScale);

        // 居中显示
        float dx = (viewWidth - imageWidth * mScaleInfo.fitScreenScale) / 2;
        float dy = (viewHeight - imageHeight * mScaleInfo.fitScreenScale) / 2;
        mMatrix.postTranslate(dx, dy);

        setImageMatrix(mMatrix);
        mScaleInfo.currentScale = mScaleInfo.fitScreenScale;

        Log.d(TAG, "Matrix重置完成，适应屏幕缩放: " + mScaleInfo.fitScreenScale);
    }

    // ==================== 公共API方法（保持兼容性） ====================

    /**
     * 设置最大缩放倍数（推荐API）
     * <p>
     * 最小缩放固定为适应屏幕大小，最大缩放由用户指定。
     * 这种设计符合专业图像查看应用的实际使用需求。
     * </p>
     *
     * @param maxScale 最大缩放倍数，建议范围：5.0f - 50.0f
     */
    public void setMaxScale(float maxScale) {
        if (maxScale > 1.0f) {
            // 只设置最大缩放，最小缩放保持为适应屏幕
            float currentMinScale = mScaleInfo.minScale;
            mScaleInfo.setUserScaleRange(currentMinScale, maxScale);
            Log.d(TAG, "最大缩放设置为: " + maxScale + " (最小缩放保持为适应屏幕)");
        }
    }



    /**
     * 设置是否启用缩放功能
     */
    public void setZoomEnabled(boolean enabled) {
        this.mZoomEnabled = enabled;
        Log.d(TAG, "缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用平移功能
     */
    public void setPanEnabled(boolean enabled) {
        this.mPanEnabled = enabled;
        Log.d(TAG, "平移功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用双击缩放功能
     */
    public void setDoubleTapEnabled(boolean enabled) {
        this.mDoubleTapEnabled = enabled;
        Log.d(TAG, "双击缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置缩放变化监听器
     */
    public void setOnZoomChangeListener(OnZoomChangeListener listener) {
        this.mZoomChangeListener = listener;
    }

    /**
     * 获取是否启用缩放功能
     */
    public boolean isZoomEnabled() {
        return mZoomEnabled;
    }

    /**
     * 获取是否启用平移功能
     */
    public boolean isPanEnabled() {
        return mPanEnabled;
    }

    /**
     * 获取是否启用双击缩放功能
     */
    public boolean isDoubleTapEnabled() {
        return mDoubleTapEnabled;
    }

    /**
     * 获取当前设置的最大缩放倍数
     *
     * @return 最大缩放倍数
     */
    public float getMaxScale() {
        return mScaleInfo.maxScale;
    }


}
