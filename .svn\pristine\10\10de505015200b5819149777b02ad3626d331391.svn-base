{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "SerialPort", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "SerialPort::@6890427a1f51a3e7e1df", "jsonFile": "target-SerialPort-Debug-1db8646db76dbeb8f4b9.json", "name": "SerialPort", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/hhx/svn/AndroidStudio/rkCamer2/app/.cxx/Debug/54t2sa2v/armeabi-v7a", "source": "C:/hhx/svn/AndroidStudio/rkCamer2/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}