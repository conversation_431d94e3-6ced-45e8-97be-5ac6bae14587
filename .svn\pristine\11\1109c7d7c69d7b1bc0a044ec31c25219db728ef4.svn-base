<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Jun 19 14:36:05 CST 2025 -->
<title>TpctrlService</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-19">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.service, class: TpctrlService">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.service</a></div>
<h1 title="类 TpctrlService" class="title">类 TpctrlService</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.service.TpctrlService</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TpctrlService</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">Tpctrl心跳检测服务
 
 <p>此服务专门用于检测tpctrl进程的心跳包，当检测到心跳包时自动启动：</p>
 <ul>
   <li>RTSP推流服务（支持摄像头流和屏幕流切换）</li>
   <li>TpctrlSocketService图像服务</li>
 </ul>
 
 <p>集成了RTSP管理功能，提供统一的流类型控制接口</p>
 
 <p>使用方法（两步初始化）：</p>
 <pre><code>
  第一步：在Activity的onCreate()中调用，初始化不依赖其他组件的部分
 TpctrlService service = TpctrlService.createEarlyInstance(this, listener);
 
  第二步：在VideoEncoder和CaptureImageHelper准备好后调用，完成初始化
 service.completeInitialization(videoEncoder, captureImageHelper);
 service.start(); // 启动服务
 </code></pre>
 
 <div style="background-color:#f8f8f8;padding:8px;margin:8px 0;">
 <strong>注意：</strong>需要在模块的build.gradle中导入以下依赖：<br>
 implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")<br>
 implementation("com.github.pedroSG94:RTSP-Server:1.2.1")
 使用屏幕推流需要在AndroidManifest中做声明
    要加入:service
       android:name="com.android.rockchip.camera2.rtsp.service.RTSPService"
       android:enabled="true"
       android:exported="false"
       android:foregroundServiceType="mediaProjection"
       android:stopWithTask="false" />
 </div></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpctrlService.HeartbeatListener.html" class="type-name-link" title="com.android.rockchip.camera2.service中的接口">TpctrlService.HeartbeatListener</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TpctrlService.StreamType.html" class="type-name-link" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a></code></div>
<div class="col-last odd-row-color">
<div class="block">流类型枚举</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#areServicesRunning()" class="member-name-link">areServicesRunning</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">判断服务是否启动</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper)" class="member-name-link">completeInitialization</a><wbr>(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureImageHelper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">便捷的完成初始化方法（默认使用CAMERA流类型）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper,com.android.rockchip.camera2.service.TpctrlService.StreamType)" class="member-name-link">completeInitialization</a><wbr>(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureImageHelper,
 <a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a>&nbsp;streamType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createEarlyInstance(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.TpctrlService.HeartbeatListener)" class="member-name-link">createEarlyInstance</a><wbr>(androidx.appcompat.app.AppCompatActivity&nbsp;activity,
 <a href="TpctrlService.HeartbeatListener.html" title="com.android.rockchip.camera2.service中的接口">TpctrlService.HeartbeatListener</a>&nbsp;listener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">第一步：创建早期实例（必须在Activity的onCreate()中调用）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getActiveNetworkInterface()" class="member-name-link">getActiveNetworkInterface</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检测当前活跃的网络接口</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentStreamType()" class="member-name-link">getCurrentStreamType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前推流类型</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeartbeatTimeout()" class="member-name-link">getHeartbeatTimeout</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取心跳包超时时间（毫秒）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIspPort()" class="member-name-link">getIspPort</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取ISP参数的服务器端口号</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSocketPort()" class="member-name-link">getSocketPort</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取心跳包的服务端口号</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStreamUrl()" class="member-name-link">getStreamUrl</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取RTSP推流的Uri地址</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEarlyInitialized()" class="member-name-link">isEarlyInitialized</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查第一步初始化状态</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFullyInitialized()" class="member-name-link">isFullyInitialized</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查第二步是否完全初始化状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRunning()" class="member-name-link">isRunning</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isStreaming()" class="member-name-link">isStreaming</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">是否已经开始RTSP的推流</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isTpctrlRunning()" class="member-name-link">isTpctrlRunning</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">判断tpctrl是否已经开始运行（通过心跳包的方式）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushAllIspConfigs()" class="member-name-link">pushAllIspConfigs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">发送所有ISP参数配置信息到tpctrl端</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">释放资源</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStreamType(com.android.rockchip.camera2.service.TpctrlService.StreamType)" class="member-name-link">setStreamType</a><wbr>(<a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a>&nbsp;streamType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置流类型</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#start()" class="member-name-link">start</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动心跳检测服务</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startTpctrlConsole(java.lang.String)" class="member-name-link">startTpctrlConsole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;netInterface)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动tpctrl控制台进程（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stop()" class="member-name-link">stop</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止心跳检测服务</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#switchStreamType()" class="member-name-link">switchStreamType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">手动切换流类型</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="createEarlyInstance(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.TpctrlService.HeartbeatListener)">
<h3>createEarlyInstance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></span>&nbsp;<span class="element-name">createEarlyInstance</span><wbr><span class="parameters">(androidx.appcompat.app.AppCompatActivity&nbsp;activity,
 <a href="TpctrlService.HeartbeatListener.html" title="com.android.rockchip.camera2.service中的接口">TpctrlService.HeartbeatListener</a>&nbsp;listener)</span></div>
<div class="block">第一步：创建早期实例（必须在Activity的onCreate()中调用）
 
 <p><strong> 重要：必须在Activity的onCreate()方法中调用此方法！</strong></p>
 <p>这是因为RTSPManager需要注册ActivityResultLauncher，而这必须在Activity进入STARTED状态之前完成。</p>
 
 <p>此方法会初始化：</p>
 <ul>
   <li>基本配置和Context</li>
   <li>RTSPManager（需要registerForActivityResult）</li>
   <li>HeartbeatListener</li>
 </ul></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>activity</code> - Activity实例，用于RTSP屏幕推流权限申请</dd>
<dd><code>listener</code> - 心跳状态监听器</dd>
<dt>返回:</dt>
<dd>TpctrlService实例（尚未完全初始化）</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></code> - 如果重复调用或Activity为null</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="see-list-long">
<li><a href="#completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper)"><code>completeInitialization(VideoEncoder, CaptureImageHelper)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper,com.android.rockchip.camera2.service.TpctrlService.StreamType)">
<h3>completeInitialization</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">completeInitialization</span><wbr><span class="parameters">(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureImageHelper,
 <a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a>&nbsp;streamType)</span></div>
<div class="block">第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）
 
 <p><strong>⚠️ 重要：必须在VideoEncoder和CaptureImageHelper完全准备好后调用此方法！</strong></p>
 <p>通常在视频编码器初始化完成回调中调用。</p>
 
 <p>此方法会初始化：</p>
 <ul>
   <li>VideoEncoder配置</li>
   <li>CaptureImageHelper配置</li>
   <li>RTSP回调设置</li>
 </ul></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoEncoder</code> - 视频编码器实例</dd>
<dd><code>captureImageHelper</code> - 抓图助手实例</dd>
<dd><code>streamType</code> - 初始流类型（可选，默认为CAMERA）</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></code> - 如果早期初始化未完成或重复调用</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></code> - 如果必需参数为null</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="see-list-long">
<li><a href="#createEarlyInstance(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.TpctrlService.HeartbeatListener)"><code>createEarlyInstance(AppCompatActivity, HeartbeatListener)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper)">
<h3>completeInitialization</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">completeInitialization</span><wbr><span class="parameters">(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a>&nbsp;captureImageHelper)</span></div>
<div class="block">便捷的完成初始化方法（默认使用CAMERA流类型）</div>
</section>
</li>
<li>
<section class="detail" id="startTpctrlConsole(java.lang.String)">
<h3>startTpctrlConsole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startTpctrlConsole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;netInterface)</span></div>
<div class="block">启动tpctrl控制台进程（简化版）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>netInterface</code> - 网络接口名称，例如"wlan0"</dd>
<dt>返回:</dt>
<dd>是否成功启动</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="start()">
<h3>start</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">start</span>()</div>
<div class="block">启动心跳检测服务
 
 <p><strong>重要：必须在completeInitialization()之后调用！</strong></p></div>
</section>
</li>
<li>
<section class="detail" id="getActiveNetworkInterface()">
<h3>getActiveNetworkInterface</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getActiveNetworkInterface</span>()</div>
<div class="block">检测当前活跃的网络接口
 <p>
 此方法会检测当前设备上活跃的网络接口，优先级为：
 1. 以太网（eth0）
 2. WiFi（wlan0）
 3. 移动数据（rmnet0等）
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>活跃的网络接口名称，如果没有活跃接口则返回默认值"wlan0"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStreamType(com.android.rockchip.camera2.service.TpctrlService.StreamType)">
<h3>setStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStreamType</span><wbr><span class="parameters">(<a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a>&nbsp;streamType)</span></div>
<div class="block">设置流类型</div>
</section>
</li>
<li>
<section class="detail" id="getCurrentStreamType()">
<h3>getCurrentStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a></span>&nbsp;<span class="element-name">getCurrentStreamType</span>()</div>
<div class="block">获取当前推流类型</div>
</section>
</li>
<li>
<section class="detail" id="switchStreamType()">
<h3>switchStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">switchStreamType</span>()</div>
<div class="block">手动切换流类型</div>
</section>
</li>
<li>
<section class="detail" id="stop()">
<h3>stop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stop</span>()</div>
<div class="block">停止心跳检测服务</div>
</section>
</li>
<li>
<section class="detail" id="isRunning()">
<h3>isRunning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRunning</span>()</div>
</section>
</li>
<li>
<section class="detail" id="areServicesRunning()">
<h3>areServicesRunning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">areServicesRunning</span>()</div>
<div class="block">判断服务是否启动</div>
<dl class="notes">
<dt>返回:</dt>
<dd>ture已启动推流服务  false未启动推流服务</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isTpctrlRunning()">
<h3>isTpctrlRunning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isTpctrlRunning</span>()</div>
<div class="block">判断tpctrl是否已经开始运行（通过心跳包的方式）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>ture已经接收到心跳包服务已经启动  false未接收到心跳包服务未启动</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isStreaming()">
<h3>isStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isStreaming</span>()</div>
<div class="block">是否已经开始RTSP的推流</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true RTSP正在推流 false RTSP未开始推流</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStreamUrl()">
<h3>getStreamUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getStreamUrl</span>()</div>
<div class="block">获取RTSP推流的Uri地址</div>
<dl class="notes">
<dt>返回:</dt>
<dd>返回string类型的Uri 例如：192.168.1.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getHeartbeatTimeout()">
<h3>getHeartbeatTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getHeartbeatTimeout</span>()</div>
<div class="block">获取心跳包超时时间（毫秒）</div>
</section>
</li>
<li>
<section class="detail" id="getSocketPort()">
<h3>getSocketPort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getSocketPort</span>()</div>
<div class="block">获取心跳包的服务端口号</div>
</section>
</li>
<li>
<section class="detail" id="getIspPort()">
<h3>getIspPort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getIspPort</span>()</div>
<div class="block">获取ISP参数的服务器端口号</div>
</section>
</li>
<li>
<section class="detail" id="isEarlyInitialized()">
<h3>isEarlyInitialized</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEarlyInitialized</span>()</div>
<div class="block">检查第一步初始化状态</div>
</section>
</li>
<li>
<section class="detail" id="isFullyInitialized()">
<h3>isFullyInitialized</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFullyInitialized</span>()</div>
<div class="block">检查第二步是否完全初始化状态</div>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">释放资源</div>
</section>
</li>
<li>
<section class="detail" id="pushAllIspConfigs()">
<h3>pushAllIspConfigs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushAllIspConfigs</span>()</div>
<div class="block">发送所有ISP参数配置信息到tpctrl端</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
