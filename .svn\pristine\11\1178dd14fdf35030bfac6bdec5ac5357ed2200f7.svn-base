package com.android.rockchip.mediacodecnew;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class FileUtils {

    private static final String TAG = "FileUtils";

    public static String createOutputPath(Context context) {
        // 格式化当前日期和时间，生成后缀
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String dateTimeSuffix = dateFormat.format(new Date());
        String fileName = "4k_video_" + dateTimeSuffix + ".mp4";

        // 使用 getExternalFilesDirs() 来获取所有外部存储目录
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        for (File file : externalFilesDirs) {
            // 判断该目录是否存在、是否可写、是否为可拆卸存储且已挂载
            if (file != null
                    && Environment.isExternalStorageRemovable(file)
                    && Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(file))) {
                File dir = new File(file, "Movies");
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                Log.d(TAG, "检测到U盘: " + file.getAbsolutePath());
                return new File(dir, fileName).getAbsolutePath();
            }
        }

        // 如果没有检测到 U 盘，则使用默认主外部存储作为后备
        File primaryDir = context.getExternalFilesDir(null);
        File fallbackDir = new File(primaryDir, "Movies");
        if (!fallbackDir.exists()) {
            fallbackDir.mkdirs();
        }
        Log.d(TAG, "未检测到 U盘，使用主外部存储: " + primaryDir.getAbsolutePath());
        return new File(fallbackDir, fileName).getAbsolutePath();
    }
}
