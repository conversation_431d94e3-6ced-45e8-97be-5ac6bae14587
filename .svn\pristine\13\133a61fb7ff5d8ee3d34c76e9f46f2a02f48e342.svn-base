package com.android.rockchip.camera2.activity.browse

import FolderAdapter
import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.storage.StorageManager
import android.text.InputFilter
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.activity.MainActivity
import com.android.rockchip.camera2.activity.browse.imagemanagement.TpImageDecodeDialogFragment
import com.android.rockchip.camera2.activity.browse.videomanagement.TpVideoDecoderDialogFragment
import com.android.rockchip.camera2.databinding.BrowseLayoutBinding
import com.android.rockchip.camera2.databinding.RightPanelLayoutBinding
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.util.dpToPx
import com.android.rockchip.camera2.util.getStorageDCIMPath
import com.android.rockchip.camera2.util.getStoragePicturePath
import com.android.rockchip.camera2.util.getStorageVideoPath
import java.io.File
import java.io.FileInputStream
import java.nio.file.Files
import java.nio.file.LinkOption
import java.nio.file.Paths
import java.nio.file.attribute.BasicFileAttributes
import java.util.Locale

class TpVideoBrowse : AppCompatActivity() {
    private val TAG = "TpVideoBrowse"
    private lateinit var mainbinding: BrowseLayoutBinding
    private lateinit var rightPanelBinding: RightPanelLayoutBinding
    private lateinit var imageFiles: List<File>
    private lateinit var imageLabels: List<String>
    private lateinit var adapter: TpThumbGridAdapter
    private var currentPage = 1
    private val pageSize = 12
    private lateinit var allImageFiles: List<File>
    private lateinit var folder: File
    private var totalPages = 1
    private var currentFolder: File? = null
    private lateinit var initialRootFolder: File
    private lateinit var folderAdapter: FolderAdapter

    private var DirPages = 0


    private val toolIcons = intArrayOf(

        R.drawable.ic_picture,
        R.drawable.ic_video,
        R.drawable.ic_settings,
        R.drawable.ic_folder,
        R.drawable.ic_return
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mainbinding = BrowseLayoutBinding.inflate(layoutInflater)
        setContentView(mainbinding.root)

        initToolbar()

        // 初始化右侧面板
        rightPanelBinding = RightPanelLayoutBinding.inflate(layoutInflater)
        mainbinding.rightPanel.addView(rightPanelBinding.root)

        // 初始化文件夹视图
        if (checkStoragePermission()) {
            loadImageData()
            initViews()
//            showVideos()  //加入该函数后插拔U盘会挂
        } else {
            requestStoragePermission()
        }

        initFolderView()
        showFolders()

        // 设置右侧面板中的返回按钮点击事件
        setupBackButton()
    }

    private fun setupBackButton() {
        // 使用 View Binding 直接访问按钮
        rightPanelBinding.btnBack.setOnClickListener {
            // 处理返回逻辑
            btnReturnClicked()
        }
    }

    private fun initFolderView() {
        val usbRoot = getUsbRootDirectory() ?: return
        val dcimPath = File(usbRoot, getStorageDCIMPath())
        currentFolder = dcimPath
        initialRootFolder = dcimPath

        // 设置文件夹列表
        rightPanelBinding.rvFolderList.layoutManager = LinearLayoutManager(this)

        folderAdapter = FolderAdapter(getSubFolders(dcimPath)) { folder ->
            if (DirPages != 0) {
                // 返回上一级
                goToParentFolder()
                showFolders()
                DirPages = 0
            } else {
                // 进入子文件夹
                loadFolderContents(folder!!)
                DirPages = 1

            }
        }

        rightPanelBinding.rvFolderList.adapter = folderAdapter
        updateFolderTitle(dcimPath)

        rightPanelBinding.btnCreateFolder.setOnClickListener {
            startCreateFolder()
        }
    }


    private fun initToolbar() {
        val toolClick = View.OnClickListener { view ->
            when(view.id) {
                R.id.browse_preview -> TopBtnClick(1)
                R.id.browse_picture -> TopBtnClick(2)
                R.id.browse_video -> TopBtnClick(3)
                R.id.browse_cell_count -> TopBtnClick(4)
                R.id.browse_folder -> TopBtnClick(5)
                R.id.browse_return -> TopBtnClick(6)
            }
        }

        mainbinding.browsePreview.setOnClickListener(toolClick)
        mainbinding.browsePicture.setOnClickListener(toolClick)
        mainbinding.browseVideo.setOnClickListener(toolClick)
        mainbinding.browseFolder.setOnClickListener(toolClick)
        mainbinding.browseCellCount.setOnClickListener(toolClick)
        mainbinding.browseReturn.setOnClickListener(toolClick)



        mainbinding.browsePicture.visibility = View.GONE
        mainbinding.browseVideo.visibility = View.GONE
        mainbinding.browseCellCount.visibility = View.GONE
//        mainbinding.browseFolder.visibility = View.GONE

    }

    private fun TopBtnClick(position: Int) {
        when(position) {
            1 -> showPreview()
            2 -> showPictures()
            3 -> showVideos()
//            4 -> startCreateFolder()
            4 -> {
                // 确保当前没有焦点元素阻碍输入法
                currentFocus?.clearFocus()
                startCreateFolder()
            }
            5 -> showFolders()
            6 -> btnReturnClicked()
        }
    }

    private fun loadImageData() {
        val storageManager = getSystemService(Context.STORAGE_SERVICE) as StorageManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            storageManager.storageVolumes.forEach { volume ->
                // 判断是否为 USB 设备
                if (volume.isRemovable) {
                    try {
                        val getPathMethod = volume.javaClass.getMethod("getPath")
                        val path = getPathMethod.invoke(volume) as? String
                        path?.let {
                            Log.d("USB_PATH", "检测到 USB 挂载路径: $it")
                            if (File(it).canRead()) {
                                // 执行文件操作...
                                folder = File(it, getStoragePicturePath())
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }

        // 加载所有文件
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        // 计算总页数
        totalPages = if (allImageFiles.isEmpty()) 1 else (allImageFiles.size - 1) / pageSize + 1

        // 初始化第一页数据
        updatePageData()
    }

    private fun updatePageData() {
        imageFiles = allImageFiles
        imageLabels = imageFiles.map { it.nameWithoutExtension }
    }

    private fun initViews() {
        mainbinding.recyclerContainer.setOnClickListener {
            // 新增处理选择模式
            if (adapter.isSelectionMode) {
                adapter.exitSelectionMode()
            } else {
                adapter.clearSelections()
            }
        }

        adapter = TpThumbGridAdapter(
            imageFiles,
            imageLabels,
            { position -> // 单击
                updatePageData()
                imageFiles.forEach{file->
                    println("#### imageFiles:${file.absolutePath}")
                }
                val selectedItem = allImageFiles[position]
                // 如果是文件夹，进入该文件夹
                if (selectedItem.isDirectory) {

//                    currentFolder = selectedItem
                    showFolderFile(selectedItem)
                }
                // 如果是文件，正常处理
                else {
                    if (getFileType(selectedItem) == "MP4") {
                        StartVideoDecode(selectedItem)
                    } else if (getFileType(selectedItem) == "JPEG image"
                        || getFileType(selectedItem) == "PNG image"
                        || getFileType(selectedItem) == "BMP image") {
                        val currentPosition = allImageFiles.indexOf(selectedItem)
                        StartImageDecode(allImageFiles, currentPosition)
                    }
                }
            },
            { position -> // 双击
                val selectedItem = allImageFiles[position]

                // 如果是文件夹，进入该文件夹
                if (selectedItem.isDirectory) {
                    currentFolder = selectedItem
                    showFolders()
                }
                // 如果是文件，正常处理
                else {
                    if (getFileType(selectedItem) == "MP4") {
                        StartVideoDecode(selectedItem)
                    } else {
                        val currentPosition = allImageFiles.indexOf(selectedItem)
                        StartImageDecode(allImageFiles, currentPosition)
                    }
                }
            }
        )

        mainbinding.recyclerView.apply {
            layoutManager = GridLayoutManager(this@TpVideoBrowse, 4)
            adapter = <EMAIL>
            addItemDecoration(TpThumbSpacingDecoration(4, 16.dpToPx(), true))
            setHasFixedSize(true)
            itemAnimator = null
        }
    }

    private fun checkStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
            STORAGE_PERMISSION_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == STORAGE_PERMISSION_CODE && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        ) {
            loadImageData()
            mainbinding.recyclerView.adapter?.notifyDataSetChanged()
        } else {
            Toast.makeText(this, "需要存储权限才能显示图片", Toast.LENGTH_SHORT).show()
        }
    }

    companion object {
        private const val STORAGE_PERMISSION_CODE = 1001
    }

    private fun Context.showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    //右侧信息面板
    private fun updateRightPanel(file: File) {
        Log.d("文件调试", "完整路径: ${file.absolutePath}")

    }

    private fun getFileType(file: File): String {
        // 方法1：通过扩展名快速判断
        val extension = file.extension.toLowerCase(Locale.US)
        return when (extension) {
            "png" -> "PNG image"
            "jpg", "jpeg" -> "JPEG image"
            "gif" -> "GIF image"
            "bmp" -> "BMP image"
            "mp4", "mov", "avi", "mkv" -> "MP4"
            else -> {
                // 方法2：通过文件头魔数精准判断（示例仅实现PNG检测）
                if (isRealPng(file)) "PNG image"
                else "File"
            }
        }
    }

    // 检测是否为真实PNG文件
    private fun isRealPng(file: File): Boolean {
        return try {
            FileInputStream(file).use { stream ->
                val header = ByteArray(8)
                stream.read(header)
                header.contentEquals(byteArrayOf(0x89.toByte(), 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A))
            }
        } catch (e: Exception) {
            false
        }
    }

    private fun StartVideoDecode(file: File) {
        val fragment = TpVideoDecoderDialogFragment.newInstance(file.absolutePath)
        fragment.show(supportFragmentManager, "video_dialog")
    }

    private fun StartImageDecode(imageFiles: List<File>, currentPosition: Int) {
        val dialogFragment = TpImageDecodeDialogFragment.newInstance(
            imageFiles.map { it.absolutePath },
            currentPosition
        )
        dialogFragment.show(supportFragmentManager, "image_dialog")
    }

    private fun showPreview() {
        val root = getUsbRootDirectory() ?: File(FileStorageUtils.getExternalStoragePath(this))
        val dcimPath = root.resolve(getStorageDCIMPath())

        allImageFiles = dcimPath.listFiles()
            ?.filter { it.isDirectory }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()

        adapter.updateData(imageFiles, imageLabels)
    }


    private fun showPictures() {
        folder = File(FileStorageUtils.getExternalStoragePath(this), getStoragePicturePath())
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()

        adapter.updateData(imageFiles, imageLabels)
    }

    private fun showVideos() {
        folder = File(FileStorageUtils.getExternalStoragePath(this), getStorageVideoPath())
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()
        adapter.updateData(imageFiles, imageLabels)
    }

    private fun showFolders() {
        val root = getUsbRootDirectory() ?: File(FileStorageUtils.getExternalStoragePath(this))
        val dcimPath = root.resolve(getStorageDCIMPath())
        println("##### show Folders :${dcimPath}")
        // 获取所有文件夹
//        val folders = root.listFiles()
        val folders = currentFolder!!.listFiles()
            ?.filter { it.isDirectory }
            ?.sortedBy { it.name }
            ?: emptyList()

        allImageFiles = currentFolder!!.listFiles()
            ?.filter { it.isDirectory }
            ?.sortedBy { it.name }
            ?: emptyList()

        // 准备文件夹名称列表
        val folderNames = folders.map { it.name }
        // 更新适配器数据
        updatePageData()
        adapter.updateData(folders, folderNames)
        // 更新当前文件夹引用
        currentFolder = dcimPath

    }

    private fun showFolderFile(file: File) {
        println("########@@@ showFolderFile:${file}")
        folder = file
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedByDescending { it.name }
            ?: emptyList()

        updatePageData()
        adapter.updateData(imageFiles, imageLabels)
    }

    private fun startCreateFolder() {
// 确定当前所在的目标文件夹
        val usbpath = File(FileStorageUtils.getExternalStoragePath(this))
        val targetFolder = usbpath.resolve(getStorageDCIMPath())

        if (!checkStoragePermission()) {
            requestStoragePermission()
            return
        }
        if (!targetFolder.canWrite()) {
            Toast.makeText(this, "该位置不可写入", Toast.LENGTH_SHORT).show()
            return
        }

        // 1. 创建并保留 EditText 引用
        val input = EditText(this).apply {
            hint = "文件夹名称"
            filters = arrayOf(InputFilter { source, start, end, dest, dstart, dend ->
                if (source.any { it in "\\/:*?\"<>|" }) "" else null
            })
        }

        val inputDialog = AlertDialog.Builder(this).apply {
            setTitle("新建文件夹")
            setMessage("请输入文件夹名称")
            setView(input)  // 添加 EditText
            setPositiveButton("创建") { dialog, which ->
                val folderName = input.text.toString().trim()
//                val folderName = "ABC"
                if (folderName.isNotEmpty()) {

                    createNewFolder(targetFolder, folderName)
                } else {
                    Toast.makeText(this@TpVideoBrowse, "文件夹名称不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            setNegativeButton("取消", null)
        }.create()

        // 2. 使用保存的引用访问 EditText
        inputDialog.setOnShowListener {
            input.postDelayed({
                input.requestFocus()
                showSoftInput(input)
            }, 200)
        }

        inputDialog.show()

        // 3. RK3588 特殊处理：确保键盘不会遮挡视图
        if (Build.MODEL.contains("RK3588")) {
            inputDialog.window?.let { window ->
                window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
        }

    }

    private fun createNewFolder(parent: File, folderName: String) {
        // 检查名称是否合法
        if (folderName.isBlank()) {
            Toast.makeText(this, "文件夹名称不能为空", Toast.LENGTH_SHORT).show()
            return
        }

        // 检查名称是否包含非法字符
        if (folderName.any { it in "\\/:*?\"<>|" }) {
            Toast.makeText(this, "名称包含非法字符: \\/:*?\"<>|", Toast.LENGTH_SHORT).show()
            return
        }

        // 检查文件夹是否已存在
        val newFolder = File(parent, folderName)
        if (newFolder.exists()) {
            Toast.makeText(this, "文件夹已存在", Toast.LENGTH_SHORT).show()
            return
        }

        // 尝试创建文件夹
        if (newFolder.mkdirs()) {
            Toast.makeText(this, "文件夹创建成功", Toast.LENGTH_SHORT).show()

            // 刷新当前视图
            when {
                mainbinding.browsePicture.visibility == View.VISIBLE -> showPictures()
                mainbinding.browseFolder.visibility == View.VISIBLE -> showFolders()
                else -> showFolders()
            }

            //刷新文件夹列表
            refreshFolderList(parent)

        } else {
            Toast.makeText(this, "文件夹创建失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showSoftInput(editText: EditText) {
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    private fun btnReturnClicked() {
//
        if (adapter.isSelectionMode) {
            adapter.exitSelectionMode()
        } else {
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
            finish()
        }
    }



    private fun getUsbRootDirectory(): File? {
        val storageManager = getSystemService(Context.STORAGE_SERVICE) as StorageManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            storageManager.storageVolumes.forEach { volume ->
                if (volume.isRemovable) {
                    try {
                        val getPathMethod = volume.javaClass.getMethod("getPath")
                        val path = getPathMethod.invoke(volume) as? String
                        path?.let {
                            if (File(it).canRead()) {
                                return File(it)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
        return null
    }

    fun File.getCreationTime(): Long {
        return try {
            // 使用 NIO 获取文件属性
            val path = Paths.get(absolutePath)
            val attr = Files.readAttributes(path, BasicFileAttributes::class.java, LinkOption.NOFOLLOW_LINKS)
            attr.creationTime().toMillis()
        } catch (e: Exception) {
            // 失败时回退到最后修改时间
            lastModified()
        }
    }

    //文件夹加载
    private fun getExternalStoragePath(): String {
        return FileStorageUtils.getExternalStoragePath(this) ?: ""
    }

    private fun getRootFolders(): List<File> {

        val storagePath = FileStorageUtils.getExternalStoragePath(this)
        val rootPath = File(storagePath, getStorageDCIMPath()).path
        val rootDir = File(rootPath)
        val folderList = rootDir.listFiles { file -> file.isDirectory }?.toList() ?: emptyList()
        return rootDir.listFiles { file -> file.isDirectory }?.toList() ?: emptyList()
    }

    private fun getSubFolders(parent: File): List<File> {
        return parent.listFiles { file ->
            file.isDirectory && !file.isHidden
        }?.sortedByDescending { it.lastModified() } ?: emptyList()
    }

    private fun loadFolderContents(folder: File) {
        // 加载该文件夹下的子文件夹


        val subFolders = getSubFolders(folder)
        currentFolder = folder
        // 更新适配器
        folderAdapter.updateFolders(subFolders)
        // 只有当不是初始根目录时才显示返回上一级
        folderAdapter.setShowBackToParent(true)
        // 更新标题
        updateFolderTitle(folder)


        //更新右侧显示
        println("######@@@@@ Name:${folder}")

        showFolderFile(folder)
    }

    private fun goToParentFolder() {
        currentFolder?.parentFile?.let { parent ->
            if (parent.exists() && parent.canRead()) {
                currentFolder = parent
                val subFolders = getSubFolders(parent)
                folderAdapter.updateFolders(subFolders)
                rightPanelBinding.tvFolderTitle.text = parent.name

                // 在主内容区显示父文件夹内容
                showFolderFile(parent)
            }
        }

        folderAdapter.setShowBackToParent(false)
    }

    private fun updateFolderTitle(folder: File) {
        // 判断当前目录是否为初始根目录
        val isRoot = folder == initialRootFolder

        val titlePrefix = if (isRoot) "U盘文件夹" else folder.name
        rightPanelBinding.tvFolderTitle.text = "$titlePrefix (${folderAdapter.itemCount})"
    }

    private fun refreshFolderList(parent: File) {
        currentFolder?.let {
            val subFolders = getSubFolders(it)
            folderAdapter.updateFolders(subFolders)
        }
    }

    private fun showFolderContents(folder: File) {
        println("######### folder file: ${folder}")
    }
}