<!-- res/layout/fragment_format_settings.xml -->
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分辨率设置"
            android:textSize="18sp"/>

        <RadioGroup
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <RadioButton
                android:id="@+id/radio_jpeg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="3840x2160"/>

            <RadioButton
                android:id="@+id/radio_png"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1920x1080"/>
        </RadioGroup>
    </LinearLayout>
</ScrollView>