package com.android.rockchip.camera2.activity
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.android.rockchip.camera2.R

class TpBrowseActivity : AppCompatActivity(){
    private val TAG = "MediaCodecDemo"
    private lateinit var btnReturn: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.browse_layout)
        // 初始化视图组件
        btnReturn = findViewById(R.id.btn_return)

        /* 设置切换按钮点击事件 */
        setupButtonClickListeners()

    }

    private fun handleHdmiDisconnected() {
    }

    private fun setupButtonClickListeners() {
        btnReturn.setOnClickListener {
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
        }

    }
}