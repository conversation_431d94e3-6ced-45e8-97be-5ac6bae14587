<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Apr 10 15:48:54 CST 2025 -->
<title>TransformUtils</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-10">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, class: TransformUtils">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="类 TransformUtils" class="title">类 TransformUtils</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.util.TransformUtils</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TransformUtils</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">TransformUtils 类提供与视图变换相关的工具方法。
 <p>
 包括对 TextureView 和 ImageView 的缩放和平移操作。</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TransformUtils</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#applyPan(android.view.TextureView,float,float)" class="member-name-link">applyPan</a><wbr>(android.view.TextureView&nbsp;textureView,
 float&nbsp;deltaX,
 float&nbsp;deltaY)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">应用平移变换，限制平移范围不超出 TextureView 的边界。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#applyPan(android.widget.ImageView,android.graphics.Matrix,float,float)" class="member-name-link">applyPan</a><wbr>(android.widget.ImageView&nbsp;imageView,
 android.graphics.Matrix&nbsp;matrix,
 float&nbsp;deltaX,
 float&nbsp;deltaY)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">应用平移变换，限制平移范围不超出 ImageView 的边界。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#applyZoom(android.view.TextureView,float,float,float)" class="member-name-link">applyZoom</a><wbr>(android.view.TextureView&nbsp;textureView,
 float&nbsp;scaleFactor,
 float&nbsp;focusX,
 float&nbsp;focusY)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">应用缩放变换，限制最小缩放比例为原始比例。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#applyZoom(android.widget.ImageView,android.graphics.Matrix,float,float,float)" class="member-name-link">applyZoom</a><wbr>(android.widget.ImageView&nbsp;imageView,
 android.graphics.Matrix&nbsp;matrix,
 float&nbsp;scaleFactor,
 float&nbsp;focusX,
 float&nbsp;focusY)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">应用缩放变换，限制最小缩放比例为原始比例。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TransformUtils</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TransformUtils</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="applyZoom(android.view.TextureView,float,float,float)">
<h3>applyZoom</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyZoom</span><wbr><span class="parameters">(android.view.TextureView&nbsp;textureView,
 float&nbsp;scaleFactor,
 float&nbsp;focusX,
 float&nbsp;focusY)</span></div>
<div class="block">应用缩放变换，限制最小缩放比例为原始比例。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>textureView</code> - 目标 TextureView。</dd>
<dd><code>scaleFactor</code> - 缩放比例。</dd>
<dd><code>focusX</code> - 手势中心点的 X 坐标。</dd>
<dd><code>focusY</code> - 手势中心点的 Y 坐标。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyPan(android.view.TextureView,float,float)">
<h3>applyPan</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyPan</span><wbr><span class="parameters">(android.view.TextureView&nbsp;textureView,
 float&nbsp;deltaX,
 float&nbsp;deltaY)</span></div>
<div class="block">应用平移变换，限制平移范围不超出 TextureView 的边界。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>textureView</code> - 目标 TextureView。</dd>
<dd><code>deltaX</code> - 平移的 X 轴距离。</dd>
<dd><code>deltaY</code> - 平移的 Y 轴距离。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyZoom(android.widget.ImageView,android.graphics.Matrix,float,float,float)">
<h3>applyZoom</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyZoom</span><wbr><span class="parameters">(android.widget.ImageView&nbsp;imageView,
 android.graphics.Matrix&nbsp;matrix,
 float&nbsp;scaleFactor,
 float&nbsp;focusX,
 float&nbsp;focusY)</span></div>
<div class="block">应用缩放变换，限制最小缩放比例为原始比例。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imageView</code> - 目标 ImageView。</dd>
<dd><code>matrix</code> - 当前的变换矩阵。</dd>
<dd><code>scaleFactor</code> - 缩放比例。</dd>
<dd><code>focusX</code> - 手势中心点的 X 坐标。</dd>
<dd><code>focusY</code> - 手势中心点的 Y 坐标。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyPan(android.widget.ImageView,android.graphics.Matrix,float,float)">
<h3>applyPan</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyPan</span><wbr><span class="parameters">(android.widget.ImageView&nbsp;imageView,
 android.graphics.Matrix&nbsp;matrix,
 float&nbsp;deltaX,
 float&nbsp;deltaY)</span></div>
<div class="block">应用平移变换，限制平移范围不超出 ImageView 的边界。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imageView</code> - 目标 ImageView。</dd>
<dd><code>matrix</code> - 当前的变换矩阵。</dd>
<dd><code>deltaX</code> - 平移的 X 轴距离。</dd>
<dd><code>deltaY</code> - 平移的 Y 轴距离。</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
