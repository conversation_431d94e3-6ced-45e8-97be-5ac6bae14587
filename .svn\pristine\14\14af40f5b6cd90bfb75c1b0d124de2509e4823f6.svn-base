package com.android.rockchip.camera2;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;
import android.graphics.SurfaceTexture;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.Toast;
import android.content.Intent;
import android.view.ScaleGestureDetector;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import com.android.rockchip.camera2.service.TpctrlService;
import com.android.rockchip.camera2.util.TouptekIspParam;
import com.android.rockchip.camera2.video.EncoderConfig;
import com.android.rockchip.mediacodecnew.R;
import com.android.rockchip.camera2.video.VideoEncoder;
import com.android.rockchip.camera2.video.CameraManagerHelper;
import com.android.rockchip.camera2.util.FileStorageUtils;
import com.android.rockchip.camera2.util.HdmiService;
import com.android.rockchip.camera2.util.TransformUtils;
import com.android.rockchip.camera2.video.CaptureImageHelper;
import com.android.rockchip.camera2.video.TvPreviewHelper;
import com.android.rockchip.camera2.view.ROIView;
import com.android.rockchip.camera2.network.NetworkSettingsDialog;

import java.util.Objects;


public class VideoEncoderActivity extends AppCompatActivity implements TouptekIspParam.OnDataChangedListener
{
    /* 日志标签 */
    private static final String TAG = "VideoEncoderActivity";

    /* 摄像头权限请求代码 */
    private static final int CAMERA_PERMISSION_REQUEST_CODE = 100;
    
    /* 网络权限请求代码 */
    private static final int NETWORK_PERMISSION_REQUEST_CODE = 101;

    /* 视频编码器实例 */
    private VideoEncoder videoEncoder;

    /* 摄像头管理助手实例 */
    private CameraManagerHelper cameraManagerHelper;

    /* 录制按钮 */
    private Button recordButton;

    /* 抓图按钮 */
    private Button captureButton;

    /* 切换模式按钮 */
    private Button switchModeButton;
    
    /* 服务状态显示按钮 */
    private Button serviceStatusButton;

    /* 流类型切换按钮 */
    private Button streamTypeButton;

    /* 是否正在录制 */
    private boolean isRecording = false;
    
    /* 是否处于TV模式 */
    private boolean isTvMode = false;

    /* 预览的 TextureView */
    private TextureView textureView;
    
    /* TV预览助手实例 */
    private TvPreviewHelper tvPreviewHelper;

    /* HDMI 服务实例 */
    private HdmiService hdmiService;

    /* 抓图助手实例 */
    private CaptureImageHelper captureImageHelper;

    /* 缩放手势检测器 */
    private ScaleGestureDetector scaleGestureDetector;

    /* 平移手势检测器 */
    private GestureDetector gestureDetector;
    
    /* Tpctrl心跳检测服务 */
    private TpctrlService heartbeatService;
    
    /* 主线程Handler */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    /* ROI相关 */
    private ROIView roiView;
    
    /* 缩放比例显示 */
    private TextView tvZoomScale;
    
    /* 网络设置对话框 */
    private NetworkSettingsDialog networkSettingsDialog;
    private ActivityResultLauncher<Intent> networkPanelLauncher;

    /**
     * 显示或隐藏设置菜单
     */
    private void toggleSettingsMenu() {
        View settingsPanel = findViewById(R.id.settings_panel);

        if (settingsPanel.getVisibility() == View.VISIBLE) {
            // 如果菜单已显示，则隐藏并关闭输入法
            hideKeyboard(settingsPanel);
            settingsPanel.setVisibility(View.GONE);
        } else {
            // 如果菜单未显示，则显示并初始化设置
            // 获取ROI视图
            roiView = findViewById(R.id.roi_view);

            // 设置ROI开关状态
            Switch roiSwitch = settingsPanel.findViewById(R.id.switch_roi_mode);
            if(TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE) == 2)
            {
                roiSwitch.setChecked(true);
                roiView.setROIEnabled(true);
            }
            else
            {
                roiSwitch.setChecked(false);
                roiView.setROIEnabled(false);
            }

            Switch btnswitch_vertical_flip = findViewById(R.id.switch_vertical_flip);
            Switch btnswitch_horizontal_flip = findViewById(R.id.switch_horizontal_flip);

            // 设置初始状态
            btnswitch_vertical_flip.setChecked(TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_FLIP) == 1);
            btnswitch_horizontal_flip.setChecked(TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_MIRROR) == 1);

            /* 相机分辨率 */
            // 默认4K
            int cameraWidth = Objects.requireNonNull(HdmiService.getHdmiResolution()).getWidth();
            // 默认4K
            int cameraHeight = Objects.requireNonNull(HdmiService.getHdmiResolution()).getHeight();
            // 设置相机分辨率
            roiView.setCameraResolution(cameraWidth, cameraHeight);
            Log.e(TAG, "initializeRoiComponents: " + "Camera Resolution: " + cameraWidth + "x" + cameraHeight + " " + HdmiService.getFrameRate());


            btnswitch_vertical_flip.setOnClickListener(v -> {
                TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_FLIP,
                        TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_FLIP) == 0 ? 1 : 0);
            });
            btnswitch_horizontal_flip.setOnClickListener(v -> {
                TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_MIRROR,
                        TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_MIRROR) == 0 ? 1 : 0);
            });

            // 设置开关监听器
            roiSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                roiView.setROIEnabled(isChecked);

                // 根据开关状态设置白平衡模式
                if (isChecked) {
                    // ROI开启时，设置为ROI白平衡模式
                    TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 2);
                } else {
                    // ROI关闭时，设置为自动白平衡模式
                    TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 1);
                }
            });

            // 设置整行点击事件
            settingsPanel.findViewById(R.id.menu_roi_mode).setOnClickListener(v -> {
                roiSwitch.setChecked(!roiSwitch.isChecked());
            });


            // 设置场景值输入框
            EditText sceneInput = settingsPanel.findViewById(R.id.input_scene_value);
            sceneInput.requestFocus();
            sceneInput.setText(String.valueOf(TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_ISP_DEFAULT_TYPE)));
            Toast.makeText(VideoEncoderActivity.this,
                    "当前场景值：" + TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_ISP_DEFAULT_TYPE), Toast.LENGTH_SHORT).show();

            // 设置应用按钮点击事件
            settingsPanel.findViewById(R.id.btn_apply_scene).setOnClickListener(v -> {
                try {
                    // 隐藏输入法
                    hideKeyboard(v);

                    String inputText = sceneInput.getText().toString();
                    if (!inputText.isEmpty()) {
                        int value = Integer.parseInt(inputText);
                        // 限制输入范围在0-100之间
                        if (value < 0) value = 0;
                        if (value > 100) value = 100;

                        // 显示处理中提示
                        Toast.makeText(VideoEncoderActivity.this,
                                "正在应用场景值...", Toast.LENGTH_SHORT).show();

                        // 在后台线程中执行可能的阻塞操作
                        int finalValue = value;
                        new Thread(() -> {
                            // 应用场景值
                            TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_ISP_DEFAULT_TYPE, finalValue);

                            // 请求参数范围
                            TouptekIspParam.requestAllParamRanges();

                            // 保存默认值
                            boolean success = true;
                            try {
                                TouptekIspParam.saveAllDefaultValuesToLocal(true);
                            } catch (Exception e) {
                                success = false;
                            }

                            // 更新UI必须在主线程
                            final boolean finalSuccess = success;
                            runOnUiThread(() -> {
                                if (finalSuccess) {
                                    Toast.makeText(VideoEncoderActivity.this,
                                            "已设置场景值：" + finalValue, Toast.LENGTH_SHORT).show();
                                } else {
                                    Toast.makeText(VideoEncoderActivity.this,
                                            "设置场景值时出错，请重试", Toast.LENGTH_SHORT).show();
                                }
                            });
                        }).start();
                    }
                } catch (NumberFormatException e) {
                    Toast.makeText(VideoEncoderActivity.this,
                        "请输入0-100之间的数字", Toast.LENGTH_SHORT).show();
                }
            });

            // 设置关闭按钮点击事件
            settingsPanel.findViewById(R.id.btn_close_popup).setOnClickListener(v -> {
                hideKeyboard(settingsPanel);
                settingsPanel.setVisibility(View.GONE);
            });
            
            // 设置网络设置按钮点击事件
            settingsPanel.findViewById(R.id.btn_open_network).setOnClickListener(v -> {
                // 打开网络设置对话框
                showNetworkSettingsDialog();
            });

            // 防止点击面板内部关闭面板
            settingsPanel.setOnClickListener(v -> {
                // 只消费点击事件，不做任何操作
            });

            // 为输入框添加焦点变化监听
            sceneInput.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus) {
                    hideKeyboard(v);
                }
            });

            // 显示菜单
            settingsPanel.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏软键盘
     */
    private void hideKeyboard(View view) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.encoder); /* 加载布局文件 */

        networkPanelLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            // Can handle the result here if needed, for now, we don't need to do anything.
            Log.d(TAG, "Returned from network settings panel.");
        });

        // 第一步：在onCreate中创建TpctrlService早期实例
        initHeartbeatServiceEarly();

        TouptekIspParam.init(this);
        
        // 直接注册VideoEncoderActivity作为ISP参数监听器
        TouptekIspParam.addOnDataChangedListener(this);

        /* 初始化 HDMI 服务 */
        initHdmiService();

        /* 初始化视图组件 */
        initViews();

        /* 检查摄像头权限 */
        checkCameraPermission();

        /*初始化预览录像等服务 */
        initialize();

        /* 初始化缩放和平移手势检测器 */
        initScaleGestureDetector();
        initPanGestureDetector();

        /* 设置触摸监听器 */
        textureView.setOnTouchListener((v, event) -> 
        {
             // 检测单击事件
            if (event.getAction() == MotionEvent.ACTION_UP) {
                // 判断是否是简单的点击(不是缩放或平移)
                if (!scaleGestureDetector.isInProgress() && event.getEventTime() - event.getDownTime() < 200) {
                    // 切换显示/隐藏设置菜单
                    toggleSettingsMenu();
                    return true;
                }
            }

            boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);
            boolean panHandled = gestureDetector.onTouchEvent(event);
            return scaleHandled || panHandled; // 确保触摸事件被正确处理
        });

        FileStorageUtils.startUsbDriveMonitor(this, new FileStorageUtils.StorageListener()
        {
            @Override
            public void onUsbDriveConnected(String rootPath)
            {
                Log.d(TAG,"USB Drive Connected:" + rootPath);
            }
            @Override
            public void onUsbDriveDisconnected(String rootPath)
            {
                Log.d(TAG,"USB Drive Disconnected"+rootPath);
            }
        });
    }

    /**
     * 第一步：在onCreate中创建TpctrlService早期实例
     *
     * <p><strong>注意：必须在Activity的onCreate()中调用此方法！</strong></p>
     * <p>这是因为RTSPManager需要注册ActivityResultLauncher，而这必须在Activity进入STARTED状态之前完成。</p>
     */
    private void initHeartbeatServiceEarly()
    {
        try
        {
            Log.d(TAG, "第一步：创建TpctrlService早期实例...");

            heartbeatService = TpctrlService.createEarlyInstance(this, createHeartbeatListener());

            Log.d(TAG, "TpctrlService早期实例创建成功");

        }
        catch (Exception e)
        {
            Log.e(TAG, "TpctrlService早期实例创建失败", e);
            heartbeatService = null;
            runOnUiThread(() ->
                Toast.makeText(this, "心跳服务预初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show()
            );
        }
    }

    /**
     * 更新流类型按钮显示
     */
    private void updateStreamTypeButton()
    {
        if (heartbeatService != null)
        {
            TpctrlService.StreamType currentType = heartbeatService.getCurrentStreamType();
            streamTypeButton.setText(currentType == TpctrlService.StreamType.CAMERA ?
                "摄像头流" : "屏幕流");
            streamTypeButton.setEnabled(heartbeatService.areServicesRunning());
        }
    }

    /**
     * 初始化 HDMI 服务
     */
    private void initHdmiService()
    {
        hdmiService = HdmiService.getInstance();
        hdmiService.setHdmiListener(isConnected -> runOnUiThread(() -> handleHdmiStatusChange(isConnected)));
        hdmiService.init();
    }

    /**
     * 初始化视图组件
     */
    private void initViews()
    {
        textureView = findViewById(R.id.texture_view);
        recordButton = findViewById(R.id.btn_record);
        captureButton = findViewById(R.id.btn_capture);
        tvZoomScale = findViewById(R.id.tv_zoom_scale);

        if (textureView == null || recordButton == null)
        {
            Log.e(TAG, "Missing required views. Check your layout file.");
            return;
        }

        // 初始化缩放比例显示
        if (tvZoomScale != null) {
            tvZoomScale.setText("1.0x");
        }

        /* 设置录制按钮点击事件 */
        recordButton.setOnClickListener(v -> toggleRecording());

        /* 设置抓图按钮点击事件 */
        if (captureButton != null)
        {
            captureButton.setOnClickListener(v -> captureImage());
        }

        /* 初始化浏览媒体按钮 */
        Button btnBrowseMedia = findViewById(R.id.btn_browse_media);
        if (btnBrowseMedia != null)
        {
            btnBrowseMedia.setOnClickListener(v ->
            {
                Intent intent = new Intent(VideoEncoderActivity.this, MediaBrowserActivity.class);
                startActivity(intent);
            });
        }

        /* 初始化切换模式按钮 */
        switchModeButton = findViewById(R.id.btn_switch_mode);
        if (switchModeButton != null)
        {
            switchModeButton.setOnClickListener(v -> togglePreviewMode());
        }

        /* 初始化服务状态按钮 */
        serviceStatusButton = findViewById(R.id.btn_rtsp_stream);
        if (serviceStatusButton != null)
        {
            serviceStatusButton.setText("等待tpctrl");
            serviceStatusButton.setEnabled(false); // 仅显示状态，不可点击
        }

        /* 初始化流类型切换按钮 - 使用现有的切换推流模式按钮 */
        streamTypeButton = findViewById(R.id.btn_switch_stream_mode);
        if (streamTypeButton != null)
        {
            streamTypeButton.setText("摄像头流");
            streamTypeButton.setEnabled(false);
            streamTypeButton.setOnClickListener(v -> switchStreamType());
        }

        /* 初始化TV预览助手 */
        ViewGroup previewContainer = findViewById(R.id.preview_container);
        if (previewContainer != null)
        {
            tvPreviewHelper = new TvPreviewHelper(this, previewContainer);
        }
    }

    /**
     * 切换流类型
     */
    private void switchStreamType()
    {
        if (heartbeatService != null && heartbeatService.areServicesRunning())
        {
            TpctrlService.StreamType newType = heartbeatService.getCurrentStreamType() == TpctrlService.StreamType.CAMERA ?
                TpctrlService.StreamType.SCREEN : TpctrlService.StreamType.CAMERA;

            // 显示切换提示
            if (newType == TpctrlService.StreamType.SCREEN)
            {
                Toast.makeText(this, "正在切换到屏幕流...", Toast.LENGTH_SHORT).show();
            }
            else
            {
                Toast.makeText(this, "正在切换到摄像头流...", Toast.LENGTH_SHORT).show();
            }

            heartbeatService.switchStreamType();
            updateStreamTypeButton();
        }
    }

    /**
     * 切换预览模式，在普通模式和TV模式之间切换
     */
    private void togglePreviewMode()
    {
        isTvMode = !isTvMode;

        if (isTvMode)
        {
            // 切换到TV模式
            if (isRecording)
            {
                stopRecording();
            }
            releaseResources();
            textureView.setVisibility(View.INVISIBLE);
            tvPreviewHelper.startPreview();
            switchModeButton.setText("切换到camera模式");

            // TV模式下禁用录制和抓图按钮
            recordButton.setEnabled(false);
            captureButton.setEnabled(false);
        }
        else
        {
            // 切换回常规模式
            tvPreviewHelper.stopPreview();
            textureView.setVisibility(View.VISIBLE);
            if (textureView.isAvailable())
            {
                SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
                if (surfaceTexture != null)
                {
                    initVideoEncoder(new Surface(surfaceTexture));
                }
            }
            switchModeButton.setText("切换到TV模式");

            // 启用录制和抓图按钮
            recordButton.setEnabled(true);
            captureButton.setEnabled(true);
            
            // 重置所有变换
            resetZoomAndPan();
        }
    }

    /**
     * 检查摄像头权限
     */
    private void checkCameraPermission()
    {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED)
        {
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.CAMERA}, CAMERA_PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults)
    {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == CAMERA_PERMISSION_REQUEST_CODE && grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED)
        {
            initialize();
        }
        else if (requestCode == NETWORK_PERMISSION_REQUEST_CODE)
        {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                // 如果网络权限获取成功，显示网络设置对话框
                showNetworkSettingsDialog();
            } else {
                Toast.makeText(this, "需要网络相关权限才能使用网络功能", Toast.LENGTH_SHORT).show();
            }
        }
        else
        {
            Log.e(TAG, "Permission denied: " + requestCode);
        }
    }

    /**
     * 初始化应用
     */
    private void initialize() 
    {
        /* 初始化摄像头管理助手 */
        cameraManagerHelper = CameraManagerHelper.builder(this)
            .onCameraOpened(camera -> 
            {
                // 相机打开后的处理
                Log.d(TAG, "Camera opened successfully");
            })
            .onCameraDisconnected(camera -> 
            {
                // 相机断开连接后的处理
                Log.d(TAG, "Camera disconnected");
            })
            .onCameraError((camera, error) -> 
            {
                // 相机错误处理
                Log.e(TAG, "Camera error: " + error);
            })
            .build();

        /* 初始化抓图助手 - 使用更简洁的回调设置方式 */
        captureImageHelper = CaptureImageHelper.builder(new Size(3840, 2160))
            .onImageSaved(filePath -> 
            {
                // 只在需要时取消注释，显示Toast提示
//                 runOnUiThread(() -> Toast.makeText(this, "图片已保存: " + filePath, Toast.LENGTH_SHORT).show());
            })
            .onError(errorMessage -> 
            {
                // 只在需要时取消注释，显示错误提示
                // runOnUiThread(() -> Toast.makeText(this, "抓图失败: " + errorMessage, Toast.LENGTH_SHORT).show());
            })
            .build();

        /* 设置 TextureView 的 SurfaceTextureListener */
        textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener()
        {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height)
            {
                initVideoEncoder(new Surface(surface));
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) { }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface)
            {
                releaseResources();
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) { }
        });
    }

    /**
     * 重置缩放和平移状态
     */
    private void resetZoomAndPan() {
        ROIView roiView = findViewById(R.id.roi_view);
        TransformUtils.resetTransform(textureView, roiView);
        updateZoomScaleDisplay();
        Log.d(TAG, "缩放和平移已重置");
    }

    /**
     * 初始化视频编码器
     *
     * @param textureSurface 预览的 Surface
     */
    private void initVideoEncoder(Surface textureSurface) 
    {
        EncoderConfig encoderConfig = EncoderConfig.createDefault4K();

        videoEncoder = VideoEncoder.builder()
            .setPreviewSurface(textureSurface)
            .setEncoderConfig(encoderConfig)
            .onSurfaceAvailable(encoderSurface -> 
            {
                // 使用链式调用风格开启相机并配置输出
                cameraManagerHelper = CameraManagerHelper.builder(this)
                    .onCameraOpened(camera -> 
                    {
                        // 相机打开后配置输出
                        cameraManagerHelper.configCameraOutputs(
                            camera, 
                            encoderSurface, 
                            captureImageHelper.getImageReader().getSurface()
                        );
                        
                        // 相机打开后重置缩放和平移
                        runOnUiThread(() -> resetZoomAndPan());
                    })
                    .onCameraDisconnected(camera -> camera.close())
                    .onCameraError((camera, error) -> camera.close())
                    .build();
                
                // 打开相机
                cameraManagerHelper.openCamera();
                
                // 第二步：当视频编码器准备好后，完成TpctrlService初始化并启动
                setupHeartbeatServiceComponents();
            })
            .onStorageFull(() -> 
            {
                Toast.makeText(this, "存储空间不足，录制已停止", Toast.LENGTH_LONG).show();
                stopRecording();
            })
            .onError((errorType, e) -> 
            {
                Toast.makeText(this, "录制错误: " + errorType, Toast.LENGTH_LONG).show();
                stopRecording();
            })
            .onFileSizeLimitReached(() -> 
            {
                Toast.makeText(this, "文件大小达到限制，录制已停止", Toast.LENGTH_LONG).show();
                stopRecording();
            })
            .onSaveComplete(filePath -> 
            {
                Toast.makeText(this, "录制完成: " + filePath, Toast.LENGTH_LONG).show();
            })
            .build();
    }

    /**
     * 第二步：完成TpctrlService初始化并启动服务
     * 
     * <p><strong>注意：必须在VideoEncoder和CaptureImageHelper准备好后调用此方法！</strong></p>
     * <p>通常在视频编码器初始化完成回调中调用。</p>
     */
    private void setupHeartbeatServiceComponents() 
    {
        Log.d(TAG, "第二步：完成TpctrlService初始化...");

        // 检查早期实例是否创建成功
        if (heartbeatService == null) 
        {
            Log.e(TAG, "TpctrlService早期实例不存在，无法完成初始化");
            runOnUiThread(() -> 
                Toast.makeText(this, "心跳服务早期实例不存在，请重启应用", Toast.LENGTH_LONG).show()
            );
            return;
        }

        // 详细检查每个组件的状态
        if (videoEncoder == null) 
        {
            Log.w(TAG, "VideoEncoder is null, cannot complete TpctrlService initialization yet");
            return;
        }

        if (captureImageHelper == null) 
        {
            Log.w(TAG, "CaptureImageHelper is null, cannot complete TpctrlService initialization yet");
            return;
        }

        Log.d(TAG, "所有组件检查通过，延迟完成心跳服务初始化...");

        // 延迟一点时间确保组件完全初始化
        mainHandler.postDelayed(() -> 
        {
            // 再次检查组件状态
            if (videoEncoder == null || captureImageHelper == null) 
            {
                Log.w(TAG, "组件在延迟后仍未准备好，跳过心跳服务完成初始化");
                return;
            }

            try 
            {
                Log.d(TAG, "完成TpctrlService初始化...");

                // 第二步：完成初始化
                heartbeatService.completeInitialization(
                    videoEncoder,           // VideoEncoder
                    captureImageHelper,     // CaptureImageHelper
                    TpctrlService.StreamType.CAMERA  // 默认流类型
                );

                Log.d(TAG, "启动心跳检测服务...");
                
                // 启动服务
                heartbeatService.start();

                Log.d(TAG, "TpctrlService完成初始化并启动成功！");

            } 
            catch (IllegalArgumentException e) 
            {
                Log.e(TAG, "TpctrlService参数错误: " + e.getMessage(), e);
                runOnUiThread(() ->
                    Toast.makeText(this, "心跳服务参数错误: " + e.getMessage(), Toast.LENGTH_LONG).show()
                );
            } 
            catch (Exception e) 
            {
                Log.e(TAG, "TpctrlService完成初始化失败: " + e.getMessage(), e);
                runOnUiThread(() ->
                    Toast.makeText(this, "心跳服务完成初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show()
                );
            }
        }, 1000); // 延迟1秒确保组件完全就绪
    }

    /**
     * 创建心跳监听器
     */
    private TpctrlService.HeartbeatListener createHeartbeatListener() 
    {
        return new TpctrlService.HeartbeatListener() 
        {
            @Override
            public void onTpctrlDetected() 
            {
                Log.d(TAG, "Tpctrl process detected");
            }
            
            @Override
            public void onServicesStarted() 
            {
                runOnUiThread(() -> 
                {
                    serviceStatusButton.setText("服务运行中");
                    updateStreamTypeButton();
                    Toast.makeText(VideoEncoderActivity.this, 
                        "检测到tpctrl，已自动启动服务", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onTpctrlLost() 
            {
                Log.d(TAG, "Tpctrl process lost");
            }
            
            @Override
            public void onServicesStopped() 
            {
                runOnUiThread(() -> 
                {
                    serviceStatusButton.setText("等待tpctrl");
                    streamTypeButton.setEnabled(false);
                    Toast.makeText(VideoEncoderActivity.this, 
                        "tpctrl进程消失，已停止服务", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onStreamStatusChanged(boolean isStreaming, String url) 
            {
                runOnUiThread(() -> 
                {
                    if (isStreaming) 
                    {
                        serviceStatusButton.setText("推流中");
                        streamTypeButton.setEnabled(true);
                        
                        // 显示成功切换的提示
                        TpctrlService.StreamType currentType = heartbeatService.getCurrentStreamType();
                        String streamTypeName = currentType == TpctrlService.StreamType.CAMERA ? "摄像头流" : "屏幕流";
                        Toast.makeText(VideoEncoderActivity.this, 
                            "已成功切换到" + streamTypeName, Toast.LENGTH_SHORT).show();
                        Toast.makeText(VideoEncoderActivity.this,
                                "RTSP stream URL" + url, Toast.LENGTH_SHORT).show();
                        Log.d(TAG, "RTSP stream URL: " + url);
                    } 
                    else 
                    {
                        serviceStatusButton.setText("服务运行中");
                        updateStreamTypeButton();
                    }
                });
            }
            
            @Override
            public void onStreamError(String errorMessage) 
            {
                runOnUiThread(() -> 
                {
                    Toast.makeText(VideoEncoderActivity.this, 
                        "推流错误: " + errorMessage, Toast.LENGTH_SHORT).show();
                });
            }
        };
    }

    /**
     * 切换录制状态
     */
    private void toggleRecording()
    {
        if (isRecording)
        {
            stopRecording();
        }
        else
        {
            startRecording();
        }
    }

    /**
     * 开始录制
     */
    private void startRecording()
    {
        String newOutputPath = FileStorageUtils.createVideoPath(this);
        videoEncoder.startRecording(newOutputPath);
        recordButton.setText("停止录制");
        isRecording = true;
    }

    /**
     * 停止录制
     */
    private void stopRecording()
    {
        videoEncoder.stopRecording();
        recordButton.setText("开始录制");
        isRecording = false;
    }

    /**
     * 抓图
     */
    private void captureImage()
    {
        Size imageSize = new Size(3840, 2160); // Example size, can be dynamic
        String outputPath = FileStorageUtils.createImagePath(this);
        captureImageHelper.requestCapture(imageSize, outputPath);
    }

    /**
     * 释放资源
     */
    private void releaseResources()
    {
        cameraManagerHelper.releaseCamera();
        if (videoEncoder != null)
        {
            videoEncoder.release();
        }
    }

    /**
     * 处理 HDMI 状态变化
     *
     * @param isConnected HDMI 状态
     */
    private void handleHdmiStatusChange(boolean isConnected)
    {
        if (isConnected)
        {
            Log.d(TAG, "HDMI 插入");

            releaseResources();

            if (textureView.isAvailable())
            {
                SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
                if (surfaceTexture != null)
                {
                    initVideoEncoder(new Surface(surfaceTexture));
                    videoEncoder.setBitrate(5_000_000);
                    // 将新的VideoEncoder重新设置到RTSPManager
                    if (heartbeatService != null) 
                    {
                       heartbeatService.updateVideoEncoder(videoEncoder);
                    }
                    
                    // 重置所有变换
                    resetZoomAndPan();
                }
            }
        }
        else
        {
            Log.d(TAG, "HDMI 拔出");
            if (isRecording)
            {
                stopRecording();
            }
            releaseResources();
        }
    }

    /**
     * 初始化缩放手势检测器
     */
    private void initScaleGestureDetector() 
    {
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() 
        {
            @Override
            public boolean onScale(ScaleGestureDetector detector) 
            {
                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                // 获取ROI视图
                ROIView roiView = findViewById(R.id.roi_view);
                
                // 使用新的方法同时应用变换到TextureView和ROIView
                TransformUtils.applyZoom(textureView, roiView, scaleFactor, focusX, focusY);
                
                // 更新缩放比例显示
                updateZoomScaleDisplay();
                
                return true;
            }
        });
    }

    /**
     * 初始化平移手势检测器
     */
    private void initPanGestureDetector() 
    {
        gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() 
        {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) 
            {
                // 获取ROI视图
                ROIView roiView = findViewById(R.id.roi_view);
                
                // 使用新的方法同时应用变换到TextureView和ROIView
                TransformUtils.applyPan(textureView, roiView, -distanceX, -distanceY);
                
                return true;
            }
        });
    }
    
    /**
     * 更新缩放比例显示
     */
    private void updateZoomScaleDisplay() {
        if (tvZoomScale != null && textureView != null) {
            float currentScale = TransformUtils.getCurrentScale(textureView);
            tvZoomScale.setText(String.format("%.1fx", currentScale));
        }
    }

    @Override
    public void onDataChanged(TouptekIspParam param, int newValue) 
    {
        Log.i(TAG, "ISP Parameter changed in VideoEncoderActivity: " + param.name() + " = " + newValue);
        
        // 在主线程中处理UI更新
        runOnUiThread(() -> 
        {
            // 根据不同的ISP参数进行相应的UI更新
            switch (param) 
            {
                case TOUPTEK_PARAM_BRIGHTNESS:
                    // 亮度变化时的处理
                    break;
                case TOUPTEK_PARAM_CONTRAST:
                    // 对比度变化时的处理
                    break;
                case TOUPTEK_PARAM_EXPOSUREGAIN:
                    // 曝光增益变化时的处理
                    break;
                default:
                    break;
            }
        });
    }

    
    @Override
    public void onLongDataChanged(TouptekIspParam param, long newValue) 
    {
        Log.i(TAG, "ISP Parameter (long) changed in VideoEncoderActivity: " + param.name() + " = " + newValue);
        
        // 处理长整型参数变化，比如版本号等
        if (param == TouptekIspParam.TOUPTEK_PARAM_VERSION) 
        {
            Log.i(TAG, "Version updated to: " + newValue);
        }
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        
        // 移除ISP参数监听器
        TouptekIspParam.removeOnDataChangedListener(this);
        
        // 使用新的release方法停止心跳检测服务
        if (heartbeatService != null) 
        {
            heartbeatService.release();
            heartbeatService = null; // 避免内存泄漏
        }
        
        if (hdmiService != null)
        {
            hdmiService.stop();
        }
        releaseResources();
        
        // 释放TV预览资源
        if (tvPreviewHelper != null) 
        {
            tvPreviewHelper.release();
        }
        
        // 关闭网络设置对话框
        if (networkSettingsDialog != null && networkSettingsDialog.isShowing()) {
            networkSettingsDialog.dismiss();
            networkSettingsDialog = null;
        }
        
        // 强制回收，帮助释放端口资源
        System.gc();
    }

    /**
     * 显示网络设置对话框
     */
    private void showNetworkSettingsDialog() {
        // 检查网络相关权限
        if (checkNetworkPermissions()) {
            if (networkSettingsDialog == null) {
                networkSettingsDialog = new NetworkSettingsDialog(this, networkPanelLauncher, heartbeatService);
            }
            networkSettingsDialog.show();
        } else {
            requestNetworkPermissions();
        }
    }
    
    /**
     * 检查网络相关权限
     */
    private boolean checkNetworkPermissions() {
        return ContextCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, android.Manifest.permission.CHANGE_WIFI_STATE) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_WIFI_STATE) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求网络相关权限
     */
    private void requestNetworkPermissions() {
        ActivityCompat.requestPermissions(this,
                new String[]{
                        android.Manifest.permission.ACCESS_FINE_LOCATION,
                        android.Manifest.permission.CHANGE_WIFI_STATE,
                        android.Manifest.permission.ACCESS_WIFI_STATE
                },
                NETWORK_PERMISSION_REQUEST_CODE);
    }

}