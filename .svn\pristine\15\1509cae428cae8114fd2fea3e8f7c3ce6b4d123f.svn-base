package com.android.rockchip.camera2.activity

import android.content.Intent
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.Size
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GestureDetectorCompat
import com.android.rockchip.camera2.activity.browse.TpVideoBrowse
import com.android.rockchip.camera2.databinding.ActivityMainBinding
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.util.TransformUtils
import com.android.rockchip.camera2.util.getStorageDCIMPath
import com.android.rockchip.camera2.util.getStoragePicturePath
import com.android.rockchip.camera2.util.getStorageVideoPath
import com.android.rockchip.camera2.util.setupEdgeToEdgeFullScreen
import com.android.rockchip.camera2.video.CameraManagerHelper
import com.android.rockchip.camera2.video.CaptureImageHelper
import com.android.rockchip.camera2.video.EncoderConfig
import com.android.rockchip.camera2.video.TvPreviewHelper
import com.android.rockchip.camera2.video.VideoEncoder
import com.android.rockchip.camera2.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView
import android.view.ViewGroup
import android.widget.FrameLayout
import com.android.rockchip.camera2.util.touptek_serial_rk
import java.io.File

class StatusBanner(private val view: TextView) {
    // 添加带默认值的duration参数
    fun show(text: String, duration: Long = 3000) {
        view.post {
            view.apply {
                this.text = text
                visibility = View.VISIBLE
                // 使用传入的duration参数
                postDelayed({ visibility = View.GONE }, duration)
            }
        }
    }
}

class MainActivity : AppCompatActivity(), View.OnAttachStateChangeListener, MainMenu.OnRectangleVisibilityListener, touptek_serial_rk.DeviceStateCallback{
    private lateinit var binding: ActivityMainBinding
    private var tvpreview: TvPreviewHelper? = null
    private val TAG = "MainActivity"
    private lateinit var gestureDetector: GestureDetectorCompat
    private var videoEncoder: VideoEncoder? = null
    private var cameraManagerHelper: CameraManagerHelper? = null
    private var captureImageHelper: CaptureImageHelper? = null

    private var startTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var updateTimeRunnable: Runnable

    private lateinit var banner: StatusBanner  //提示标签
    private var isRecording = false

    //zoom
    private var scaleGestureDetector: ScaleGestureDetector? = null

    //roi
    private var rectangleOverlayView: TpRectangleOverlayView? = null

    //串口
    private var touptek_serial: touptek_serial_rk? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupEdgeToEdgeFullScreen() //全屏
        setupGestureControls()   //状态

//        initSerial()
        initCameraManager() //用于抓图
//        initializeTvComponents() //开启预览模式
        initCameraMode()  //打开摄像头
        initStorageMonitor()

        //初始化zoom功能
        initScaleGestureDetector()
        initPanGestureDetector()
        banner = StatusBanner(binding.mainCenterInfoLabel)
    }

    private fun initializeTvComponents() {
        tvpreview = TvPreviewHelper(this, binding.rootView)
        tvpreview?.startPreview()
    }

    override fun onViewAttachedToWindow(v: View) = Unit //空实现
    override fun onViewDetachedFromWindow(v: View) = Unit

    override fun onDeviceStateChanged(connected: Boolean) {
        val message = if (connected) "设备已连接 111" else "设备已断开 11"
        Log.d("SerialMonitor", message)

    }

    private fun showMenu() {
        MainMenu().apply {
            setRectangleVisibilityListener(this@MainActivity)
        }.show(supportFragmentManager, "MainMenu")
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            if (it.action == MotionEvent.ACTION_UP) {
                showMenu()
                return true
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    fun showBrowseActivity() {
        releaseResources()
        releaseTvView()
        startActivity(Intent(this, TpVideoBrowse::class.java))
        finish()
    }

    fun SetZoomIn() {
        println("######## ZOOM In")
//        val scaleFactor = detector.scaleFactor
//        val focusX = detector.focusX
//        val focusY = detector.focusY
        val scaleFactor = 0.96f  // 或 10.0F
        val focusX = 800.0f
        val focusY = 600.0f
        // 调用 TransformUtils 的 applyZoom 方法
        if (binding.blueTextureView == null) {
            Log.e(TAG, "blueTextureView is null!")
            return
        }
        TransformUtils.applyZoom(binding.blueTextureView, scaleFactor, focusX, focusY)

    }

    fun SetZoomOut() {
        println("######## ZOOM Out")
    }

    fun releaseTvView() {
        tvpreview?.stopPreview()
        tvpreview = null
    }

    fun ActivityCaptureImage() {
        val imageSize = Size(3840, 2160) // Example size, can be dynamic
        val outputPath = FileStorageUtils.createImagePath(this)
        captureImageHelper!!.requestCapture(imageSize, outputPath)
    }

    fun initCameraMode() {
        binding.blueTextureView.visibility = View.VISIBLE
        binding.blueTextureView.bringToFront()
        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化
        setupRectangleOverlay() // 添加这行
    }

    fun isRecording(): Boolean = isRecording

    fun startRecord() {
        isRecording = true
        updateMainMenuRecordButton()

        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化

        Handler(Looper.getMainLooper()).postDelayed({
            val newOutputPath = FileStorageUtils.createVideoPath(this)
            println("Video Path: $newOutputPath")
            videoEncoder?.startRecording(newOutputPath)
        }, 500)

        banner.show("开始录像",1000)

        startTime = System.currentTimeMillis()
        binding.tvTimer.visibility = View.VISIBLE
        binding.tvTimer.bringToFront()
        startTimer()
    }

    fun stopRecord() {
        isRecording = false
        updateMainMenuRecordButton()
        binding.tvTimer.visibility = View.INVISIBLE
        videoEncoder?.stopRecording()
        banner.show("结束录像，正在保存",20000)
    }

    private fun updateMainMenuRecordButton() {
        // 查找并更新MainMenu中的按钮状态
//        supportFragmentManager.findFragmentByTag("MainMenu")?.view?.let { view ->
//            val recordButton = view.findViewById<ImageButton>(R.id.btn_record_video)
//            val resId = if (isRecording) R.drawable.btn_record_video_pressed else R.drawable.btn_record_video_n
//            recordButton.setImageResource(resId)
//        }

    }

    private fun setupGestureControls() {
        gestureDetector = GestureDetectorCompat(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                showMenu()
                return true
            }
        })

        binding.rootView.setOnTouchListener { view, event ->
            gestureDetector.onTouchEvent(event)
            if (event.action == MotionEvent.ACTION_UP) {
                view.performClick()
            }
            false
        }
    }

//    private fun initSerial() {
//        touptek_serial = touptek_serial_rk()
//        touptek_serial?.initializeSerial(1)
//        touptek_serial?.startMonitor()
//        touptek_serial?.setDeviceStateCallback(this)
//    }

    private fun initCameraManager() {
        cameraManagerHelper = CameraManagerHelper(this)
        captureImageHelper = CaptureImageHelper.builder(Size(3840, 2160))
            .onImageSaved { filePath ->
                banner.show("捕获成功",1000)
            }
            .onError { errorMessage ->
                banner.show("捕获失败",1000)
            }
            .build()
    }

    private fun setupViewListener() {
        binding.blueTextureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                initVideoEncode(Surface(surface))
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean = true
            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }
    }

    private fun initVideoEncode(textureSurface: Surface) {
        val encoderConfig = EncoderConfig.createDefault4K()
        videoEncoder = VideoEncoder.builder()
            .setPreviewSurface(textureSurface)
            .setEncoderConfig(encoderConfig)
            .onSurfaceAvailable { encoderSurface ->
                cameraManagerHelper = CameraManagerHelper.builder(this)
                    .onCameraOpened { camera ->
                        cameraManagerHelper?.configCameraOutputs(
                            camera,
                            encoderSurface,
                            captureImageHelper!!.imageReader!!.surface
                        )
                    }
                    .onCameraDisconnected { camera -> camera.close() }
                    .onCameraError { camera, error -> camera.close() }
                    .build()

                cameraManagerHelper!!.openCamera()
            }
            .onStorageFull {
                Toast.makeText(this, "存储空间不足，录制已停止", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()
            }
            .onError { errorType, e ->
                Toast.makeText(this, "录制错误: $errorType", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()
            }
            .onFileSizeLimitReached {
                Toast.makeText(this, "文件大小达到限制，录制已停止", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()
            }
            .onSaveComplete { filePath ->
                Toast.makeText(this, "录制完成: $filePath", Toast.LENGTH_LONG).show()
                val fileName = filePath.trimEnd('/').substringAfterLast("/")
                banner.show("${fileName}已保存", 1500)
            }
            .build()
    }

    private fun releaseResources() {
        cameraManagerHelper?.releaseCamera()
        videoEncoder?.release()
    }

    private fun startTimer() {
        updateTimeRunnable = object : Runnable {
            override fun run() {
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - startTime
                updateTimerText(duration)
                handler.postDelayed(this, 1000)
            }
        }
        handler.post(updateTimeRunnable)
    }

    private fun updateTimerText(duration: Long) {
        val seconds = (duration / 1000) % 60
        val minutes = (duration / (1000 * 60)) % 60
        val hours = (duration / (1000 * 60 * 60)) % 24
        binding.tvTimer.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    override fun onDestroy() {
        super.onDestroy()
//        handler.removeCallbacks(updateTimeRunnable)
//        releaseResources()
    }

    override fun onPause() {
        super.onPause()
        Log.d("Lifecycle", "onPause")
    }

    override fun onStop() {
        super.onStop()
        Log.d("Lifecycle", "onStop")
    }

    override fun onRestart() {
        super.onRestart()

        initializeTvComponents()
        Log.d("Lifecycle", "onRestart")
    }

    override fun onStart() {
        super.onStart()
        Log.d("Lifecycle", "onStart")
    }

    override fun onResume() {
        super.onResume()
        Log.d("Lifecycle", "onResume")
    }

    private fun initStorageMonitor(){
        FileStorageUtils.startUsbDriveMonitor(this, object : FileStorageUtils.StorageListener {
            override fun onUsbDriveConnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已连接", Toast.LENGTH_SHORT).show()
                createStorageDefaultPath()
            }

            override fun onUsbDriveDisconnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已断开", Toast.LENGTH_SHORT).show()
            }
        })
    }

    fun createStorageDefaultPath()
    {
        val storagePath = FileStorageUtils.getExternalStoragePath(this)

        val dcimPath = File(storagePath, getStorageDCIMPath()).path
        val videosPath = File(storagePath, getStorageVideoPath()).path
        val picturesPath = File(storagePath, getStoragePicturePath()).path

        // 创建 DCIM 目录（如果不存在）
        val dcimDir = File(dcimPath)
        if (!dcimDir.exists()) {
            dcimDir.mkdirs()
        }

        // 创建 Videos 目录（如果不存在）
        val videosDir = File(videosPath)
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }

        // 创建 Pictures 目录（如果不存在）
        val picturesDir = File(picturesPath)
        if (!picturesDir.exists()) {
            picturesDir.mkdirs()
        }
    }


    private fun initScaleGestureDetector() {
        scaleGestureDetector = ScaleGestureDetector(this, object : SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                val focusX = detector.focusX
                val focusY = detector.focusY
                Log.d(TAG, "缩放因子: ${"%.3f".format(scaleFactor)}")

                // 调用 TransformUtils 的 applyZoom 方法
                TransformUtils.applyZoom(binding.blueTextureView, scaleFactor, focusX, focusY)
                return true
            }
        })
    }

    private fun initPanGestureDetector() {
//        gestureDetector = GestureDetector(this, object : SimpleOnGestureListener() {
//            override fun onScroll(
//                e1: MotionEvent?,
//                e2: MotionEvent,
//                distanceX: Float,
//                distanceY: Float
//            ): Boolean {
//                // 调用 TransformUtils 的 applyPan 方法
//                TransformUtils.applyPan(binding.blueTextureView, -distanceX, -distanceY)
//                return true
//            }
//        })
    }

    private fun setupRectangleOverlay() {
        // 确保blueTextureView已经初始化
        binding.blueTextureView.post {
            // 创建FrameLayout作为容器
            val container = FrameLayout(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            }

            // 将blueTextureView从原有父布局移除并添加到新容器
            (binding.blueTextureView.parent as? ViewGroup)?.removeView(binding.blueTextureView)
            container.addView(binding.blueTextureView)

            // 添加矩形覆盖层
            rectangleOverlayView = TpRectangleOverlayView(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 设置初始位置（居中）
                val width = 200f
                val height = 120f
                val left = (binding.blueTextureView.width - width) / 2
                val top = (binding.blueTextureView.height - height) / 2
                setRectanglePosition(left, top, left + width, top + height)
                //默认隐藏
                visibility = View.GONE

                onPositionChanged = { rect ->
                    Log.d(TAG, "Rectangle position: $rect")
                }
            }
            container.addView(rectangleOverlayView)

            // 将容器添加回原布局
            binding.root.addView(container, 0)
        }
    }

    override fun onShowRectangle() {
        showRectangleOverlay()
    }

    override fun onHideRectangle() {
        hideRectangleOverlay()
    }

    fun showRectangleOverlay() {
        rectangleOverlayView?.visibility = View.VISIBLE
    }

    fun hideRectangleOverlay() {
        rectangleOverlayView?.visibility = View.GONE
    }

    fun isRectangleVisible(): Boolean {
        return rectangleOverlayView?.visibility == View.VISIBLE
    }
}