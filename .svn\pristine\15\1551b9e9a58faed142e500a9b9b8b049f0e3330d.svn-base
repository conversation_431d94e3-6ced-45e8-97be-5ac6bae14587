package com.android.rockchip.camera2.view;

import android.content.Context;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.TextureView;

import com.android.rockchip.camera2.util.TransformUtils;

/**
 * TpTextureView - 支持缩放和平移的TextureView
 * <p>
 * 基于TpZoomableImageView的设计，为TextureView提供缩放和平移功能。
 * 支持与ROIView的同步变换，提供与系统相册一致的用户体验。
 * </p>
 * 
 * 主要功能：
 * <ul>
 *   <li>双指缩放（焦点稳定）</li>
 *   <li>单指平移（硬边界限制）</li>
 *   <li>双击缩放切换</li>
 *   <li>ROIView同步变换</li>
 *   <li>手势冲突解决</li>
 * </ul>
 * 
 * 使用方式：
 * <pre>{@code
 * // 在布局文件中使用
 * <com.android.rockchip.camera2.view.TpTextureView
 *     android:id="@+id/textureView"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent" />
 * 
 * // 在代码中使用
 * TpTextureView textureView = findViewById(R.id.textureView);
 * textureView.setZoomEnabled(true);
 * textureView.setPanEnabled(true);
 * textureView.setRoiView(roiView);
 * }</pre>
 */
public class TpTextureView extends TextureView {
    private static final String TAG = "TpTextureView";
    
    /**
     * 手势状态枚举
     */
    private enum GestureState {
        IDLE,       // 空闲状态
        SCALING,    // 缩放中
        PANNING     // 平移中
    }
    
    /**
     * 缩放信息类（简化版本）
     */
    private static class ScaleInfo {
        float currentScale = 1.0f;      // 当前缩放比例
        float maxScale = 5.0f;          // 最大缩放比例
        boolean userSetMaxScale = false; // 用户是否手动设置了最大缩放

        void setUserMaxScale(float userMaxScale) {
            maxScale = userMaxScale;
            userSetMaxScale = true;
        }

        float getNextDoubleTapScale() {
            // 简化的双击缩放：始终返回1.0倍
            return 1.0f;
        }
    }
    

    
    // 核心组件
    private Matrix mMatrix = new Matrix();
    private ScaleInfo mScaleInfo = new ScaleInfo();
    private GestureState mGestureState = GestureState.IDLE;
    
    // 手势检测器
    private ScaleGestureDetector mScaleGestureDetector;
    private GestureDetector mGestureDetector;
    
    // 触摸状态
    private float mLastTouchX = 0f;
    private float mLastTouchY = 0f;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mResetGestureStateRunnable;

    // 超灵敏双指缩放相关变量
    private float mLastDistance = 0f;
    private float mLastFocusX = 0f;
    private float mLastFocusY = 0f;
    private boolean mIsMultiTouch = false;
    private static final float MIN_SCALE_DISTANCE = 5f; // 最小识别距离仅5像素
    
    // 配置选项
    private boolean mZoomEnabled = true;
    private boolean mPanEnabled = true;
    private boolean mDoubleTapEnabled = true;
    
    // ROI视图支持
    private TpRoiView mTpRoiView;
    
    // 监听器
    private OnZoomChangeListener mZoomChangeListener;
    
    /**
     * 缩放变化监听器
     */
    public interface OnZoomChangeListener {
        void onZoomChanged(float scale, float focusX, float focusY);
    }
    
    public TpTextureView(Context context) {
        super(context);
        init(context);
    }
    
    public TpTextureView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public TpTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    /**
     * 初始化组件
     */
    private void init(Context context) {
        // 初始化手势检测器
        initGestureDetectors(context);
        
        Log.d(TAG, "TpZoomableTextureView初始化完成");
    }
    
    /**
     * 初始化手势检测器
     */
    private void initGestureDetectors(Context context) {
        // 创建高敏感度的缩放手势检测器
        mScaleGestureDetector = createSensitiveScaleGestureDetector(context);
        
        // 通用手势检测器（用于双击等）
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!mDoubleTapEnabled || !mZoomEnabled) return false;

                float targetScale = mScaleInfo.getNextDoubleTapScale();
                float scaleFactor = targetScale / mScaleInfo.currentScale;

                return performScale(scaleFactor, e.getX(), e.getY());
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                performClick();
                return true;
            }
        });
    }

    /**
     * 创建高敏感度的缩放手势检测器
     * 通过自定义实现降低最小识别距离，提升近距离双指缩放的响应性
     */
    private ScaleGestureDetector createSensitiveScaleGestureDetector(Context context) {
        return new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!mZoomEnabled) return false;

                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                return performScale(scaleFactor, focusX, focusY);
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                // 降低最小跨度要求，提升近距离识别能力
                // 通过检查当前跨度而不是依赖系统默认阈值
                float currentSpan = detector.getCurrentSpan();

                // 设置更低的最小跨度阈值（默认约为27dp，我们降低到10dp）
                float minSpan = 10 * context.getResources().getDisplayMetrics().density;

                if (currentSpan >= minSpan) {
                    mGestureState = GestureState.SCALING;
                    cancelResetGestureState();
                    Log.d(TAG, "缩放开始 - 当前跨度: " + currentSpan + "px, 最小跨度: " + minSpan + "px");
                    return true;
                } else {
                    Log.d(TAG, "跨度太小，未触发缩放 - 当前跨度: " + currentSpan + "px");
                    return false;
                }
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                scheduleResetGestureState();
                Log.d(TAG, "缩放结束");
            }
        });
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = false;

        // 优先处理超灵敏双指缩放
        handled = handleSuperSensitiveScale(event);

        // 如果不是多点触控，则使用原有的手势检测器
        if (!mIsMultiTouch) {
            if (mScaleGestureDetector != null) {
                handled = mScaleGestureDetector.onTouchEvent(event) || handled;
            }

            if (mGestureDetector != null) {
                handled = mGestureDetector.onTouchEvent(event) || handled;
            }

            // 处理平移手势（只在非缩放状态下）
            if (mGestureState != GestureState.SCALING && mPanEnabled) {
                handled = handlePanGesture(event) || handled;
            }
        }

        return handled || super.onTouchEvent(event);
    }

    /**
     * 处理超灵敏双指缩放
     * 直接计算两点距离变化，无需依赖ScaleGestureDetector的阈值限制
     */
    private boolean handleSuperSensitiveScale(MotionEvent event) {
        if (!mZoomEnabled) return false;

        int pointerCount = event.getPointerCount();

        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                mIsMultiTouch = false;
                return false;

            case MotionEvent.ACTION_POINTER_DOWN:
                if (pointerCount == 2) {
                    // 开始双指操作
                    mIsMultiTouch = true;
                    mGestureState = GestureState.SCALING;
                    cancelResetGestureState();

                    // 计算初始距离和焦点
                    mLastDistance = getDistance(event);
                    float[] focus = getFocusPoint(event);
                    mLastFocusX = focus[0];
                    mLastFocusY = focus[1];

                    Log.d(TAG, "TextureView超灵敏缩放开始 - 初始距离: " + mLastDistance + "px");
                    return true;
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (mIsMultiTouch && pointerCount == 2) {
                    float currentDistance = getDistance(event);
                    float[] currentFocus = getFocusPoint(event);

                    // 只要距离变化超过最小阈值就触发缩放
                    if (Math.abs(currentDistance - mLastDistance) > MIN_SCALE_DISTANCE) {
                        float scaleFactor = currentDistance / mLastDistance;

                        // 限制缩放因子范围，避免过度敏感
                        scaleFactor = Math.max(0.8f, Math.min(1.2f, scaleFactor));

                        boolean result = performScale(scaleFactor, currentFocus[0], currentFocus[1]);

                        // 更新记录值
                        mLastDistance = currentDistance;
                        mLastFocusX = currentFocus[0];
                        mLastFocusY = currentFocus[1];

                        Log.v(TAG, "TextureView超灵敏缩放 - 距离: " + currentDistance + "px, 因子: " + scaleFactor);
                        return result;
                    }
                    return true;
                }
                break;

            case MotionEvent.ACTION_POINTER_UP:
            case MotionEvent.ACTION_UP:
                if (mIsMultiTouch) {
                    mIsMultiTouch = false;
                    scheduleResetGestureState();
                    Log.d(TAG, "TextureView超灵敏缩放结束");
                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * 计算两个触摸点之间的距离
     */
    private float getDistance(MotionEvent event) {
        if (event.getPointerCount() < 2) return 0f;

        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(x * x + y * y);
    }

    /**
     * 计算两个触摸点的中心焦点
     */
    private float[] getFocusPoint(MotionEvent event) {
        if (event.getPointerCount() < 2) {
            return new float[]{event.getX(), event.getY()};
        }

        float focusX = (event.getX(0) + event.getX(1)) / 2f;
        float focusY = (event.getY(0) + event.getY(1)) / 2f;
        return new float[]{focusX, focusY};
    }
    
    /**
     * 处理平移手势
     */
    private boolean handlePanGesture(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mGestureState = GestureState.PANNING;
                mLastTouchX = event.getX();
                mLastTouchY = event.getY();
                return true;

            case MotionEvent.ACTION_MOVE:
                if (event.getPointerCount() == 1 && mGestureState == GestureState.PANNING) {
                    float deltaX = event.getX() - mLastTouchX;
                    float deltaY = event.getY() - mLastTouchY;

                    mLastTouchX = event.getX();
                    mLastTouchY = event.getY();

                    return performTranslate(deltaX, deltaY);
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mGestureState = GestureState.IDLE;
                break;
        }

        return false;
    }

    /**
     * 执行缩放操作（优化版本，防止黑边闪烁）
     */
    private boolean performScale(float scaleFactor, float focusX, float focusY) {
        if (!mZoomEnabled) return false;

        // 计算新的缩放比例
        float newScale = mScaleInfo.currentScale * scaleFactor;

        // 限制缩放范围（最小1.0倍，最大为用户设置值）
        if (newScale < 1.0f) {
            scaleFactor = 1.0f / mScaleInfo.currentScale;
            newScale = 1.0f;
        } else if (newScale > mScaleInfo.maxScale) {
            scaleFactor = mScaleInfo.maxScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.maxScale;
        }

        // 使用TransformUtils进行缩放（支持ROI同步）
        if (mTpRoiView != null) {
            TransformUtils.applyZoom(this, mTpRoiView, scaleFactor, focusX, focusY);
        } else {
            TransformUtils.applyZoom(this, scaleFactor, focusX, focusY);
        }

        // 更新当前缩放比例
        mScaleInfo.currentScale = getCurrentScale();

        // 通知监听器
        if (mZoomChangeListener != null) {
            mZoomChangeListener.onZoomChanged(mScaleInfo.currentScale, focusX, focusY);
        }

        Log.d(TAG, "缩放: " + mScaleInfo.currentScale + ", 焦点: (" + focusX + ", " + focusY + ")");
        return true;
    }

    /**
     * 执行平移操作（使用TransformUtils的硬边界限制）
     */
    private boolean performTranslate(float deltaX, float deltaY) {
        if (!mPanEnabled) return false;

        // 使用TransformUtils进行平移（支持ROI同步）
        if (mTpRoiView != null) {
            TransformUtils.applyPan(this, mTpRoiView, deltaX, deltaY);
        } else {
            TransformUtils.applyPan(this, deltaX, deltaY);
        }

        Log.d(TAG, "平移: deltaX=" + deltaX + ", deltaY=" + deltaY);
        return true;
    }

    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        Matrix matrix = getTransform(null);
        if (matrix != null) {
            float[] values = new float[9];
            matrix.getValues(values);
            return values[Matrix.MSCALE_X];
        }
        return 1.0f;
    }

    /**
     * 手势状态重置相关方法
     */
    private void scheduleResetGestureState() {
        cancelResetGestureState();
        mResetGestureStateRunnable = () -> {
            if (mGestureState == GestureState.SCALING) {
                mGestureState = GestureState.IDLE;
                Log.d(TAG, "手势状态重置为IDLE");
            }
        };
        mHandler.postDelayed(mResetGestureStateRunnable, 200);
    }

    private void cancelResetGestureState() {
        if (mResetGestureStateRunnable != null) {
            mHandler.removeCallbacks(mResetGestureStateRunnable);
            mResetGestureStateRunnable = null;
        }
    }

    /**
     * 重置变换到初始状态（1.0倍缩放）
     */
    public void resetTransform() {
        cancelResetGestureState();
        mGestureState = GestureState.IDLE;

        // 重置变换矩阵到1.0倍状态
        Matrix resetMatrix = new Matrix();
        setTransform(resetMatrix);

        // 如果有ROI视图，也重置它
        if (mTpRoiView != null) {
            mTpRoiView.applyTransform(resetMatrix);
        }

        mScaleInfo.currentScale = 1.0f;
        Log.d(TAG, "变换已重置到1.0倍状态");
    }

    // ==================== 公共API方法 ====================

    /**
     * 设置最大缩放倍数（推荐API）
     * <p>
     * 最小缩放固定为适应屏幕大小，最大缩放由用户指定。
     * 这种设计符合专业相机应用的实际使用需求。
     * </p>
     *
     * @param maxScale 最大缩放倍数，必须大于1.0
     */
    public void setMaxScale(float maxScale) {
        if (maxScale > 1.0f) {
            mScaleInfo.setUserMaxScale(maxScale);
            Log.d(TAG, "最大缩放设置为: " + maxScale + " (最小缩放固定为适应屏幕)");
        }
    }



    /**
     * 设置是否启用缩放功能
     */
    public void setZoomEnabled(boolean enabled) {
        this.mZoomEnabled = enabled;
        Log.d(TAG, "缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用平移功能
     */
    public void setPanEnabled(boolean enabled) {
        this.mPanEnabled = enabled;
        Log.d(TAG, "平移功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用双击缩放功能
     */
    public void setDoubleTapEnabled(boolean enabled) {
        this.mDoubleTapEnabled = enabled;
        Log.d(TAG, "双击缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置ROI视图（用于同步变换）
     */
    public void setRoiView(TpRoiView tpRoiView) {
        this.mTpRoiView = tpRoiView;
        Log.d(TAG, "ROI视图已" + (tpRoiView != null ? "设置" : "移除"));
    }

    /**
     * 设置缩放变化监听器
     */
    public void setOnZoomChangeListener(OnZoomChangeListener listener) {
        this.mZoomChangeListener = listener;
    }

    /**
     * 获取是否启用缩放功能
     */
    public boolean isZoomEnabled() {
        return mZoomEnabled;
    }

    /**
     * 获取是否启用平移功能
     */
    public boolean isPanEnabled() {
        return mPanEnabled;
    }

    /**
     * 获取是否启用双击缩放功能
     */
    public boolean isDoubleTapEnabled() {
        return mDoubleTapEnabled;
    }

    /**
     * 获取当前设置的最大缩放倍数
     *
     * @return 最大缩放倍数
     */
    public float getMaxScale() {
        return mScaleInfo.maxScale;
    }

}
