package com.android.rockchip.camera2.util;

import android.graphics.Matrix;
import android.graphics.RectF;
import android.util.Log;
import android.view.TextureView;
import android.widget.ImageView;

/**
 * TransformUtils 类提供与视图变换相关的工具方法。
 * <p>
 * 包括对 TextureView 和 ImageView 的缩放和平移操作。
 */
public class TransformUtils {
    private static final String TAG = "TransformUtils";

    /**
     * 应用缩放变换，限制最小缩放比例为原始比例。
     *
     * @param textureView 目标 TextureView。
     * @param scaleFactor 缩放比例。
     * @param focusX      手势中心点的 X 坐标。
     * @param focusY      手势中心点的 Y 坐标。
     */
    public static void applyZoom(TextureView textureView, float scaleFactor, float focusX, float focusY) {
        if (textureView == null || scaleFactor <= 0) {
            Log.e(TAG, "Invalid parameters for applyZoom");
            return;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);

        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算当前的缩放比例
        float currentScaleX = transformedRect.width() / viewRect.width();
        float currentScaleY = transformedRect.height() / viewRect.height();
        float currentScale = Math.min(currentScaleX, currentScaleY);

        // 限制最小缩放比例为 1.0（原始比例）
        float newScale = currentScale * scaleFactor;
        if (newScale < 1.0f) {
            scaleFactor = 1.0f / currentScale;
        }

        // 以手势中心点为缩放中心进行缩放
        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);

        // 应用变换到 TextureView
        textureView.setTransform(matrix);

        // 强制刷新界面
        textureView.invalidate();

        Log.d(TAG, "Zoom applied with scaleFactor: " + scaleFactor + ", focusX: " + focusX + ", focusY: " + focusY);
    }

    /**
     * 应用平移变换，限制平移范围不超出 TextureView 的边界。
     *
     * @param textureView 目标 TextureView。
     * @param deltaX      平移的 X 轴距离。
     * @param deltaY      平移的 Y 轴距离。
     */
    public static void applyPan(TextureView textureView, float deltaX, float deltaY) {
        if (textureView == null) {
            Log.e(TAG, "Invalid parameters for applyPan");
            return;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);

        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算限制后的平移距离
        float limitedDeltaX = deltaX;
        float limitedDeltaY = deltaY;

        if (transformedRect.left + deltaX > 0) {
            limitedDeltaX = -transformedRect.left;
        } else if (transformedRect.right + deltaX < textureView.getWidth()) {
            limitedDeltaX = textureView.getWidth() - transformedRect.right;
        }

        if (transformedRect.top + deltaY > 0) {
            limitedDeltaY = -transformedRect.top;
        } else if (transformedRect.bottom + deltaY < textureView.getHeight()) {
            limitedDeltaY = textureView.getHeight() - transformedRect.bottom;
        }

        // 应用平移变换
        matrix.postTranslate(limitedDeltaX, limitedDeltaY);

        // 应用变换到 TextureView
        textureView.setTransform(matrix);

        // 强制刷新界面
        textureView.invalidate();

        Log.d(TAG, "Pan applied with limitedDeltaX: " + limitedDeltaX + ", limitedDeltaY: " + limitedDeltaY);
    }

    /**
     * 应用缩放变换，限制最小缩放比例为原始比例。
     *
     * @param imageView   目标 ImageView。
     * @param matrix      当前的变换矩阵。
     * @param scaleFactor 缩放比例。
     * @param focusX      手势中心点的 X 坐标。
     * @param focusY      手势中心点的 Y 坐标。
     */
    public static void applyZoom(ImageView imageView, Matrix matrix, float scaleFactor, float focusX, float focusY) {
        RectF viewRect = new RectF(0f, 0f, imageView.getWidth(), imageView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        float currentScaleX = transformedRect.width() / viewRect.width();
        float currentScaleY = transformedRect.height() / viewRect.height();
        float currentScale = Math.min(currentScaleX, currentScaleY);

        float newScale = currentScale * scaleFactor;
        if (newScale < 1.0f) {
            matrix.postScale(1.0f / currentScale, 1.0f / currentScale, focusX, focusY);
        } else {
            matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
        }

        imageView.setImageMatrix(matrix);
        imageView.invalidate();

        Log.d(TAG, "Zoom applied with scaleFactor: " + scaleFactor + ", focusX: " + focusX + ", focusY: " + focusY);
    }

    /**
     * 应用平移变换，限制平移范围不超出 ImageView 的边界。
     *
     * @param imageView 目标 ImageView。
     * @param matrix    当前的变换矩阵。
     * @param deltaX    平移的 X 轴距离。
     * @param deltaY    平移的 Y 轴距离。
     */
    public static void applyPan(ImageView imageView, Matrix matrix, float deltaX, float deltaY) {
        RectF viewRect = new RectF(0f, 0f, imageView.getWidth(), imageView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        float limitedDeltaX = deltaX;
        float limitedDeltaY = deltaY;

        if (transformedRect.left + deltaX > 0) {
            limitedDeltaX = -transformedRect.left;
        } else if (transformedRect.right + deltaX < imageView.getWidth()) {
            limitedDeltaX = imageView.getWidth() - transformedRect.right;
        }

        if (transformedRect.top + deltaY > 0) {
            limitedDeltaY = -transformedRect.top;
        } else if (transformedRect.bottom + deltaY < imageView.getHeight()) {
            limitedDeltaY = imageView.getHeight() - transformedRect.bottom;
        }

        matrix.postTranslate(limitedDeltaX, limitedDeltaY);
        imageView.setImageMatrix(matrix);
        imageView.invalidate();

        Log.d(TAG, "Pan applied with limitedDeltaX: " + limitedDeltaX + ", limitedDeltaY: " + limitedDeltaY);
    }

    /**
     * 获取当前的缩放比例
     *
     * @param textureView 目标 TextureView
     * @return 当前的缩放比例，如果无法获取则返回1.0（原始比例）
     */
    public static float getCurrentScale(TextureView textureView) {
        if (textureView == null) {
            Log.e(TAG, "Invalid parameter for getCurrentScale");
            return 1.0f;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);

        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算当前的缩放比例
        float currentScaleX = transformedRect.width() / viewRect.width();
        float currentScaleY = transformedRect.height() / viewRect.height();
        
        // 返回较小的缩放比例，通常X和Y的缩放是一致的
        return Math.min(currentScaleX, currentScaleY);
    }
}
