// ***************************************************************
//  gep_request_context   version:  1.0     date: 04/26/2007
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2007 - All Rights Reserved
// ***************************************************************
// 
// ***************************************************************
#include "utiny/usys_data_block.h"
#include "utiny/gep_request_context.h"
#include "utiny/gep_header.h"
#include "utiny/usys_transceiver.h"

using namespace gep;

gep_request_context::gep_request_context(const usys_smartptr<usys_transceiver>& transceiver_ptr, const gep::GEP_HEADER& header)
:gep_packet_context(true, header.msg_type, header.session_id, header.seq_num, header.compress_flag, GEP_CONTEXT_HEADER(header.dst_dmn, header.dst_id, header.src_dmn, header.src_id, (unsigned char)header.version), transceiver_ptr),
  response_user_(header.src_dmn, header.src_id, header.dst_dmn, header.dst_id)
{
}

#include <utiny/usys_reactor.h>
#include <utiny/gep_response_context.h>

gep_response_context::gep_response_context(const usys_smartptr<usys_transceiver>& transceiver_ptr, unsigned short msg_type, const GEP_CONTEXT_HEADER& header)
    : gep_packet_context(false, msg_type, 0, 0, 0, header, transceiver_ptr), stat_(E_OK), response_stat_(0), timer_id_(0)
{
}

void gep_response_context::start_timer(int second)
{
    usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
    if (trans_ptr)
        timer_id_ = trans_ptr->reactor_ptr()->register_timer_msec(second * 1000, false, usys_smartptr<usys_timer_doozer>(this), 0);
}

void gep_response_context::cancel_timer()
{
    if (timer_id_)
    {
        usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
        if (trans_ptr)
            trans_ptr->reactor_ptr()->unregister_timer_msec(timer_id_);
        timer_id_ = 0;
    }
}

void gep_response_context::handle_timeout(const void *arg)
{
    ACE_UNUSED_ARG(arg);
    stat_ = E_TIMEOUT;
    timer_id_ = 0;
    usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
    if (trans_ptr)
    {
        usys_smartptr<gep_session_context> session_ptr = usys_smartptr<gep_session_context>::__dynamic_cast(trans_ptr->session_ptr());
        if (session_ptr)
            session_ptr->proc_request_exception(usys_smartptr<gep_response_context>(this));
    }
}
