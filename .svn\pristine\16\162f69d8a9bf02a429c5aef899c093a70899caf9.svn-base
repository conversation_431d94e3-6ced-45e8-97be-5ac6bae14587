{"buildFiles": ["C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"SerialPort::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "SerialPort", "output": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\jniLibs\\x86\\libSerialPort.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}