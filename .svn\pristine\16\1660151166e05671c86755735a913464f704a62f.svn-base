<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 30 14:13:27 CST 2025 -->
<title>R - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-30">
<meta name="description" content="index: R">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">R</a>&nbsp;<a href="index-17.html">S</a>&nbsp;<a href="index-18.html">T</a>&nbsp;<a href="index-19.html">U</a>&nbsp;<a href="index-20.html">V</a>&nbsp;<a href="index-21.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#release()" class="member-name-link">release()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>
<div class="block">释放资源</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#release()" class="member-name-link">release()</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">停止串口监控和相关资源。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html#release()" class="member-name-link">release()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></dt>
<dd>
<div class="block">释放资源，停止后台线程。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html#release()" class="member-name-link">release()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a></dt>
<dd>
<div class="block">释放资源。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.html#release()" class="member-name-link">release()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></dt>
<dd>
<div class="block">释放编码器和解码器的资源。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#releaseCamera()" class="member-name-link">releaseCamera()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">释放摄像头资源。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)" class="member-name-link">removeOnDataChangedListener(TouptekIspParam.OnDataChangedListener)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">移除数据变化监听器</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#requestAllParamRanges()" class="member-name-link">requestAllParamRanges()</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">从相机设备中获取所有默认参数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html#requestCapture(android.util.Size,java.lang.String)" class="member-name-link">requestCapture(Size, String)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></dt>
<dd>
<div class="block">请求抓图。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html#requestCapture(android.util.Size,java.lang.String,int)" class="member-name-link">requestCapture(Size, String, int)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></dt>
<dd>
<div class="block">请求抓图并指定输出格式。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.html#requestKeyFrame()" class="member-name-link">requestKeyFrame()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></dt>
<dd>
<div class="block">请求生成关键帧</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#resetHdmiRxViaScript()" class="member-name-link">resetHdmiRxViaScript()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">通过执行外部脚本重置HDMI RX</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TransformUtils.html#resetImageViewTransform(android.widget.ImageView)" class="member-name-link">resetImageViewTransform(ImageView)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TransformUtils.html" title="com.android.rockchip.camera2.util中的类">TransformUtils</a></dt>
<dd>
<div class="block">重置ImageView的变换矩阵</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#resetToStart()" class="member-name-link">resetToStart()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">重置播放到开始位置</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TransformUtils.html#resetTransform(android.view.TextureView,com.android.rockchip.camera2.view.ROIView)" class="member-name-link">resetTransform(TextureView, ROIView)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TransformUtils.html" title="com.android.rockchip.camera2.util中的类">TransformUtils</a></dt>
<dd>
<div class="block">重置变换矩阵为单位矩阵</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/NetworkManager.html#resumeHotspotMonitoring()" class="member-name-link">resumeHotspotMonitoring()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/NetworkManager.html" title="com.android.rockchip.camera2.util中的类">NetworkManager</a></dt>
<dd>
<div class="block">恢复热点状态检查（在Activity onResume时调用）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/view/ROIView.html" class="type-name-link" title="com.android.rockchip.camera2.view中的类">ROIView</a> - <a href="../com/android/rockchip/camera2/view/package-summary.html">com.android.rockchip.camera2.view</a>中的类</dt>
<dd>
<div class="block">ROIView - 可拖动的感兴趣区域(ROI)选择框</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/view/ROIView.html#%3Cinit%3E(android.content.Context)" class="member-name-link">ROIView(Context)</a> - 类的构造器 com.android.rockchip.camera2.view.<a href="../com/android/rockchip/camera2/view/ROIView.html" title="com.android.rockchip.camera2.view中的类">ROIView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/view/ROIView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet)" class="member-name-link">ROIView(Context, AttributeSet)</a> - 类的构造器 com.android.rockchip.camera2.view.<a href="../com/android/rockchip/camera2/view/ROIView.html" title="com.android.rockchip.camera2.view中的类">ROIView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/view/ROIView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)" class="member-name-link">ROIView(Context, AttributeSet, int)</a> - 类的构造器 com.android.rockchip.camera2.view.<a href="../com/android/rockchip/camera2/view/ROIView.html" title="com.android.rockchip.camera2.view中的类">ROIView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/ImageDecoder.html#rotateBitmap(android.graphics.Bitmap,float)" class="member-name-link">rotateBitmap(Bitmap, float)</a> - 类中的静态方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/ImageDecoder.html" title="com.android.rockchip.camera2.video中的类">ImageDecoder</a></dt>
<dd>
<div class="block">旋转图片到指定角度。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">R</a>&nbsp;<a href="index-17.html">S</a>&nbsp;<a href="index-18.html">T</a>&nbsp;<a href="index-19.html">U</a>&nbsp;<a href="index-20.html">V</a>&nbsp;<a href="index-21.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
