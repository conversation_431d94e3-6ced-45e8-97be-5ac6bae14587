package com.android.rockchip.camera2.video;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * CaptureImageHelper 类用于处理摄像头图像的抓取和保存。
 * <p>
 * 此类提供了图像抓取、保存为 JPEG/PNG/BMP 文件以及资源释放的功能。
 * 它支持异步操作，避免阻塞主线程。
 * </p>
 */
public class CaptureImageHelper
{
    private static final String TAG = "CaptureImageHelper";
    
    // 图像格式常量
    public static final int FORMAT_JPEG = 0;
    public static final int FORMAT_PNG = 1;
    public static final int FORMAT_BMP = 2;

    /* ImageReader 用于接收摄像头输出的图像数据 */
    private final ImageReader imageReader;

    /* 后台线程的 Handler，用于处理耗时操作 */
    private final Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private final HandlerThread backgroundThread;

    /* 回调接口，用于通知抓图结果 */
    private CaptureCallback captureCallback;

    /* 标志位，表示是否有抓图请求 */
    private volatile boolean isCaptureRequested = false;

    /* 请求抓图的目标尺寸 */
    private Size requestedSize;

    /* 抓图保存的输出路径 */
    private String outputPath;
    
    /* 图像保存格式 */
    private int imageOutputFormat = FORMAT_JPEG;

    /**
     * 回调接口，用于通知抓图结果
     */
    public interface CaptureCallback
    {
        /**
         * 当图像保存成功时调用
         */
        void onImageSaved(String filePath);

        /**
         * 当抓图失败时调用
         */
        void onError(String errorMessage);
    }

    private CaptureImageHelper(Builder builder) {
        this.requestedSize = builder.imageSize;
        this.captureCallback = builder.captureCallback;
        this.imageReader = ImageReader.newInstance(
            builder.imageSize.getWidth(),
            builder.imageSize.getHeight(),
            builder.imageFormat,
            builder.maxImages
        );
        this.imageReader.setOnImageAvailableListener(this::onImageAvailable, builder.backgroundHandler);
        this.backgroundHandler = builder.backgroundHandler;
        this.backgroundThread = builder.backgroundThread;
        this.imageOutputFormat = builder.imageOutputFormat;
    }

    /**
     * 构造函数，初始化 ImageReader 和后台线程。
     * <p>
     * 此方法会启动一个后台线程，并创建一个 ImageReader 实例，用于接收摄像头输出的图像数据。
     * </p>
     *
     * @param imageSize
     *        ImageReader 的图像尺寸。
     */
    public CaptureImageHelper(Size imageSize)
    {
        // 调用建造者模式创建实例，保持向后兼容性
        CaptureImageHelper helper = builder(imageSize).build();
        this.imageReader = helper.imageReader;
        this.backgroundHandler = helper.backgroundHandler;
        this.backgroundThread = helper.backgroundThread;
        this.imageOutputFormat = FORMAT_JPEG; // 默认JPEG格式，保持向后兼容
    }

    public static Builder builder(Size imageSize) {
        return new Builder(imageSize);
    }

    public static class Builder {
        private final Size imageSize;
        private CaptureCallback captureCallback;
        private int imageFormat = ImageFormat.YUV_420_888;
        private int maxImages = 50;
        private Handler backgroundHandler;
        private HandlerThread backgroundThread;
        private int imageOutputFormat = FORMAT_JPEG; // 默认JPEG格式
        
        // 单独的回调处理器
        private java.util.function.Consumer<String> onImageSavedHandler;
        private java.util.function.Consumer<String> onErrorHandler;

        private Builder(Size imageSize) {
            this.imageSize = imageSize;
            this.backgroundThread = new HandlerThread("CaptureImageBackground");
            this.backgroundThread.start();
            this.backgroundHandler = new Handler(backgroundThread.getLooper());
        }

        public Builder setCaptureCallback(CaptureCallback captureCallback) {
            this.captureCallback = captureCallback;
            return this;
        }
        
        /**
         * 设置图像保存成功的回调
         * @param handler 处理保存路径的回调函数
         */
        public Builder onImageSaved(java.util.function.Consumer<String> handler) {
            this.onImageSavedHandler = handler;
            return this;
        }
        
        /**
         * 设置图像保存失败的回调
         * @param handler 处理错误信息的回调函数
         */
        public Builder onError(java.util.function.Consumer<String> handler) {
            this.onErrorHandler = handler;
            return this;
        }

        public Builder setImageFormat(int imageFormat) {
            this.imageFormat = imageFormat;
            return this;
        }

        public Builder setMaxImages(int maxImages) {
            this.maxImages = maxImages;
            return this;
        }
        
        /**
         * 设置图像输出格式
         * @param format 输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
         */
        public Builder setImageOutputFormat(int format) {
            this.imageOutputFormat = format;
            return this;
        }

        public CaptureImageHelper build() {
            // 如果设置了单独的回调处理器，但没有设置完整的CaptureCallback
            if (captureCallback == null && (onImageSavedHandler != null || onErrorHandler != null)) {
                captureCallback = new CaptureCallback() {
                    @Override
                    public void onImageSaved(String filePath) {
                        if (onImageSavedHandler != null) {
                            onImageSavedHandler.accept(filePath);
                        }
                    }
                    
                    @Override
                    public void onError(String errorMessage) {
                        if (onErrorHandler != null) {
                            onErrorHandler.accept(errorMessage);
                        }
                    }
                };
            }
            
            return new CaptureImageHelper(this);
        }
    }

    /**
     * 获取 ImageReader 实例。
     * <p>
     * ImageReader 用于得到ImageReader的实例，在传入摄像头的输出surface会用到。
     * </p>
     *
     * @return
     *        返回 ImageReader 实例。
     */
    public ImageReader getImageReader()
    {
        return imageReader;
    }

    /**
     * 设置抓图回调。
     * <p>
     * 此方法用于设置一个回调接口，当图像保存成功或失败时会触发回调。
     * </p>
     *
     * @param callback
     *        回调接口，用于通知抓图结果。
     */
    public void setCaptureCallback(CaptureCallback callback)
    {
        this.captureCallback = callback;
    }
    
    /**
     * 设置图像输出格式
     * @param format 输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     */
    public void setImageOutputFormat(int format) {
        this.imageOutputFormat = format;
    }

    /**
     * 请求抓图。
     * <p>
     * 此方法会设置抓图的目标尺寸和保存路径，并触发抓图操作。
     * 会自动根据文件后缀名确定保存格式。
     * </p>
     *
     * @param size
     *        抓图的目标尺寸。
     * @param outputPath
     *        抓图保存的输出路径。
     */
    public void requestCapture(Size size, String outputPath)
    {
        // 根据文件后缀名自动确定格式
        int format = detectFormatFromPath(outputPath);
        
        // 调用带格式参数的方法
        requestCapture(size, outputPath, format);
    }
    
    /**
     * 根据文件路径检测图像格式
     * 
     * @param path 文件路径
     * @return 检测到的图像格式常量
     */
    private int detectFormatFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return FORMAT_JPEG; // 默认为JPEG
        }
        
        String lowercasePath = path.toLowerCase();
        
        if (lowercasePath.endsWith(".png")) {
            return FORMAT_PNG;
        } else if (lowercasePath.endsWith(".bmp")) {
            return FORMAT_BMP;
        } else {
            // .jpg, .jpeg 或其他格式默认使用JPEG
            return FORMAT_JPEG;
        }
    }

    /**
     * 请求抓图并指定输出格式。
     * <p>
     * 此方法会设置抓图的目标尺寸、保存路径和输出格式，并触发抓图操作。
     * </p>
     *
     * @param size
     *        抓图的目标尺寸。
     * @param outputPath
     *        抓图保存的输出路径。
     * @param format
     *        图像输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     */
    public void requestCapture(Size size, String outputPath, int format)
    {
        this.imageOutputFormat = format;
        this.isCaptureRequested = true;
        this.requestedSize = size;
        this.outputPath = outputPath;
    }

    /**
     * 当 ImageReader 有新的图像数据时调用。
     * <p>
     * 此方法会处理新的图像数据，并根据抓图请求将图像保存为指定格式文件。
     * 如果没有抓图请求，则直接释放图像。
     * </p>
     *
     * @param reader
     *        ImageReader 实例。
     */
    private void onImageAvailable(ImageReader reader)
    {
        /* 获取最新的图像 */
        Image image = reader.acquireLatestImage();
        if (image == null) return;

        if (isCaptureRequested)
        {
            isCaptureRequested = false; // 重置抓图请求标志

            /* 在主线程复制图像数据并立即释放图像 */
            Size cameraSize = new Size(image.getWidth(), image.getHeight());
            ByteBuffer yuvData = copyImageToBuffer(image);
            image.close(); // 立即释放图像

            /* 在后台线程处理图像数据 */
            backgroundHandler.post(() ->
            {
                try
                {
                    /* 将 YUV 数据转换为 Bitmap */
                    Bitmap bitmap = convertYUVToBitmap(yuvData, cameraSize, requestedSize);

                    /* 根据设置的格式保存图像 */
                    saveImageWithFormat(bitmap, outputPath, imageOutputFormat);

                    /* 通知回调图像保存成功 */
                    if (captureCallback != null)
                    {
                        captureCallback.onImageSaved(outputPath);
                    }
                }
                catch (Exception e)
                {
                    Log.e(TAG, "Error processing image", e);

                    /* 通知回调图像保存失败 */
                    if (captureCallback != null)
                    {
                        captureCallback.onError("Failed to process image");
                    }
                }
            });
        }
        else
        {
            /* 如果没有抓图请求，立即释放图像 */
            image.close();
        }
    }

    /**
     * 将 YUV 数据转换为 Bitmap。
     * <p>
     * 此方法会将摄像头输出的 YUV 数据转换为 Bitmap，并支持缩放到目标尺寸。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param cameraSize
     *        摄像头的原始图像尺寸。
     * @param targetSize
     *        目标图像尺寸。
     * @return
     *        返回处理后的Bitmap。
     */
    private Bitmap convertYUVToBitmap(ByteBuffer yuvData, Size cameraSize, Size targetSize)
    {
        /* 将 YUV 数据转换为 NV21 格式 */
        byte[] nv21 = convertYUV420ToNV21(yuvData, cameraSize.getWidth(), cameraSize.getHeight());

        /* 使用 YuvImage 将 NV21 数据转换为 JPEG */
        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, cameraSize.getWidth(), cameraSize.getHeight(), null);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, cameraSize.getWidth(), cameraSize.getHeight()), 100, out);

        /* 将 JPEG 数据解码为 Bitmap */
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(out.toByteArray(), 0, out.size());

        /* 如果目标尺寸与原始尺寸相同，直接返回原始Bitmap */
        if (targetSize.equals(cameraSize))
        {
            return originalBitmap;
        }
        else
        {
            /* 缩放 Bitmap 到目标尺寸 */
            return Bitmap.createScaledBitmap(originalBitmap, targetSize.getWidth(), targetSize.getHeight(), true);
        }
    }
    
    /**
     * 将 YUV 数据转换为 JPEG 格式（保留原方法以兼容旧代码）。
     */
    private byte[] convertYUVToJPEG(ByteBuffer yuvData, Size cameraSize, Size targetSize)
    {
        Bitmap bitmap = convertYUVToBitmap(yuvData, cameraSize, targetSize);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
        return out.toByteArray();
    }

    /**
     * 将 Image 数据复制到缓冲区。
     * <p>
     * 此方法会将 Image 对象中的 YUV 数据复制到一个 ByteBuffer 中。
     * </p>
     *
     * @param image
     *        YUV_420_888 格式的 Image。
     * @return
     *        返回包含 YUV 数据的 ByteBuffer。
     */
    private ByteBuffer copyImageToBuffer(Image image)
    {
        int bufferSize = 0;
        for (Image.Plane plane : image.getPlanes())
        {
            bufferSize += plane.getBuffer().remaining();
        }

        ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);
        for (Image.Plane plane : image.getPlanes())
        {
            ByteBuffer planeBuffer = plane.getBuffer();
            byte[] planeData = new byte[planeBuffer.remaining()];
            planeBuffer.get(planeData);
            buffer.put(planeData);
        }
        buffer.rewind();
        return buffer;
    }

    /**
     * 将 YUV_420_888 格式转换为 NV21 格式。
     * <p>
     * 此方法会将 YUV 数据转换为 NV21 格式，以便使用 YuvImage 进行处理。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param width
     *        图像宽度。
     * @param height
     *        图像高度。
     * @return
     *        返回 NV21 格式的字节数组。
     */
    private byte[] convertYUV420ToNV21(ByteBuffer yuvData, int width, int height)
    {
        int frameSize = width * height;
        int chromaSize = frameSize / 4;
        byte[] nv21 = new byte[frameSize + frameSize / 2];

        byte[] yPlane = new byte[frameSize];
        byte[] uvPlane = new byte[2 * chromaSize];

        yuvData.position(0);
        yuvData.get(yPlane, 0, frameSize);
        yuvData.get(uvPlane, 0, 2 * chromaSize);

        System.arraycopy(yPlane, 0, nv21, 0, frameSize);
        for (int i = 0; i < chromaSize; i++)
        {
            nv21[frameSize + i * 2] = uvPlane[i * 2 + 1];
            nv21[frameSize + i * 2 + 1] = uvPlane[i * 2];
        }

        return nv21;
    }

    /**
     * 保存Bitmap为BMP格式文件。
     * <p>
     * 由于Android没有直接支持BMP格式的压缩，此方法实现了BMP文件格式的写入。
     * 使用缓冲区批量写入提高性能。
     * </p>
     *
     * @param bitmap
     *        要保存的Bitmap对象。
     * @param outputPath
     *        输出文件路径。
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveBitmap(Bitmap bitmap, String outputPath) throws IOException {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int[] pixels = new int[width * height];
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
        
        // 计算文件大小
        int fileSize = 54 + 4 * width * height; // 文件头(14) + DIB头(40) + 像素数据(4*width*height)
        
        try (FileOutputStream output = new FileOutputStream(outputPath)) {
            // 准备文件头 (14 bytes)
            byte[] bmpHeader = new byte[14];
            bmpHeader[0] = 'B';
            bmpHeader[1] = 'M';
            System.arraycopy(intToBytes(fileSize), 0, bmpHeader, 2, 4); // 文件大小
            System.arraycopy(intToBytes(0), 0, bmpHeader, 6, 4); // 保留字段
            System.arraycopy(intToBytes(54), 0, bmpHeader, 10, 4); // 数据偏移量
            
            // 准备DIB头 (40 bytes)
            byte[] dibHeader = new byte[40];
            System.arraycopy(intToBytes(40), 0, dibHeader, 0, 4); // DIB头大小
            System.arraycopy(intToBytes(width), 0, dibHeader, 4, 4); // 宽度
            System.arraycopy(intToBytes(height), 0, dibHeader, 8, 4); // 高度
            dibHeader[12] = 1; // 色彩平面数
            dibHeader[14] = 32; // 每像素位数
            System.arraycopy(intToBytes(0), 0, dibHeader, 16, 4); // 压缩方式
            System.arraycopy(intToBytes(4 * width * height), 0, dibHeader, 20, 4); // 图像数据大小
            // 其余参数保持为0
            
            // 写入文件头和DIB头
            output.write(bmpHeader);
            output.write(dibHeader);
            
            // 为像素数据创建缓冲区
            byte[] pixelBuffer = new byte[width * 4]; // 每行的缓冲区
            
            // 从下到上写入像素数据
            for (int y = height - 1; y >= 0; y--) {
                // 填充一行的缓冲区
                for (int x = 0; x < width; x++) {
                    int pixel = pixels[y * width + x];
                    int offset = x * 4;
                    pixelBuffer[offset] = (byte) (pixel & 0xFF); // Blue
                    pixelBuffer[offset + 1] = (byte) ((pixel >> 8) & 0xFF); // Green
                    pixelBuffer[offset + 2] = (byte) ((pixel >> 16) & 0xFF); // Red
                    pixelBuffer[offset + 3] = (byte) ((pixel >> 24) & 0xFF); // Alpha
                }
                // 一次性写入整行
                output.write(pixelBuffer);
            }
        }
    }
    
    /**
     * 异步保存BMP图片，避免阻塞主线程或相机处理线程。
     * 仅当需要保存大尺寸BMP图片时使用此方法。
     *
     * @param bitmap 要保存的Bitmap对象
     * @param outputPath 输出文件路径
     * @param callback 保存完成后的回调
     */
    private void saveBitmapAsync(Bitmap bitmap, String outputPath, CaptureCallback callback) {
        // 创建专用的线程进行BMP保存，避免阻塞相机处理线程
        new Thread(() -> {
            try {
                saveBitmap(bitmap, outputPath);
                
                // 在原始后台线程中执行回调，保持一致性
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                }
            } catch (IOException e) {
                Log.e(TAG, "Error saving BMP image", e);
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onError("Failed to save BMP image"));
                }
            }
        }).start();
    }
    
    /**
     * 异步保存图片，避免阻塞主线程或相机处理线程。
     *
     * @param bitmap 要保存的Bitmap对象
     * @param outputPath 输出文件路径
     * @param format 输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     * @param callback 保存完成后的回调
     */
    private void saveImageAsync(Bitmap bitmap, String outputPath, int format, CaptureCallback callback) {
        // 创建专用的线程进行图片保存，避免阻塞相机处理线程
        new Thread(() -> {
            try {
                Bitmap.CompressFormat compressFormat;
                switch (format) {
                    case FORMAT_PNG:
                        compressFormat = Bitmap.CompressFormat.PNG;
                        break;
                    case FORMAT_BMP:
                        saveBitmap(bitmap, outputPath);
                        if (callback != null) {
                            backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                        }
                        return;
                    case FORMAT_JPEG:
                    default:
                        compressFormat = Bitmap.CompressFormat.JPEG;
                        break;
                }
                
                try (FileOutputStream output = new FileOutputStream(outputPath)) {
                    bitmap.compress(compressFormat, 100, output);
                }
                
                // 在原始后台线程中执行回调，保持一致性
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onImageSaved(outputPath));
                }
            } catch (IOException e) {
                Log.e(TAG, "Error saving image", e);
                if (callback != null) {
                    backgroundHandler.post(() -> callback.onError("Failed to save image"));
                }
            }
        }).start();
    }
    
    /**
     * 根据指定格式保存图像到文件。
     * <p>
     * 此方法会将Bitmap保存为指定格式的文件。
     * 所有格式均使用异步方式保存以避免阻塞相机处理线程。
     * </p>
     *
     * @param bitmap
     *        要保存的Bitmap对象。
     * @param outputPath
     *        输出文件路径。
     * @param format
     *        输出格式 (FORMAT_JPEG, FORMAT_PNG, FORMAT_BMP)
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveImageWithFormat(Bitmap bitmap, String outputPath, int format) throws IOException
    {
        // 所有格式都使用异步保存
        Log.d(TAG, "Using async save for image: " + outputPath + ", format: " + 
              (format == FORMAT_PNG ? "PNG" : format == FORMAT_BMP ? "BMP" : "JPEG"));
        
        saveImageAsync(bitmap, outputPath, format, captureCallback);
    }

    /**
     * 将int转换为小端字节数组。
     */
    private byte[] intToBytes(int value) {
        return new byte[] {
            (byte) (value & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 24) & 0xFF)
        };
    }

    /**
     * 释放资源，停止后台线程。
     * <p>
     * 此方法会停止后台线程并释放相关资源。
     * </p>
     */
    public void release()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }

}
