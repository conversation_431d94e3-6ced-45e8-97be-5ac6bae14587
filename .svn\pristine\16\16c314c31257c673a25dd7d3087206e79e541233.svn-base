                        -HC:\hhx\rk3588\XCamView\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=27
-DANDROID_PLATFORM=android-27
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++11
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\hhx\rk3588\XCamView\app\build\intermediates\cxx\Debug\3t3vu28a\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\hhx\rk3588\XCamView\app\build\intermediates\cxx\Debug\3t3vu28a\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\hhx\rk3588\XCamView\app\.cxx\Debug\3t3vu28a\x86_64
-GNinja
                        Build command args: []
                        Version: 2