package com.android.rockchip.mediacodecnew;

import android.util.Log;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

public class HdmiService {
    private static final String TAG = "HdmiService";
    // HDMI IN 状态文件路径（需注意权限配置）
    private final String mHdmiRxDevicePath = "/sys/kernel/debug/hdmirx/status";
    private File mHdmiRxFile;
    // 保存上一次状态，防止重复触发
    private String lastStatus = "";
    // 控制线程终止
    private boolean threadStatus = false;

    private HdmiListener listener;
    public interface HdmiListener {
        void onHdmiStatusChanged(String status);
    }
    public void setHdmiListener(HdmiListener listener) {
        this.listener = listener;
    }
    private static HdmiService instance = new HdmiService();
    public static HdmiService getInstance() {
        return instance;
    }
    public void init() {
        Log.d(TAG, "init: 开始读取 HDMI 状态");
        mHdmiRxFile = new File(mHdmiRxDevicePath);
        threadStatus = false;
        new ReadThread().start();
    }
    private class ReadThread extends Thread {
        @Override
        public void run() {
            while (!threadStatus) {
                try {
                    // 每800毫秒检测一次
                    Thread.sleep(800);
                    FileReader reader = new FileReader(mHdmiRxFile);
                    BufferedReader bufReader = new BufferedReader(reader);
                    String currentStatus = bufReader.readLine();
                    bufReader.close();

                    // 如果状态有变化则触发回调
                    if (currentStatus != null && !currentStatus.equals(lastStatus)) {
                        lastStatus = currentStatus;
                        // HDMI 插入状态：包含 "plugin"
                        if (currentStatus.contains("plugin")) {
                            Log.d(TAG, "检测到 HDMI 插入，延时 2 秒等待驱动稳定");
                            Thread.sleep(2000);
                        }
                        // HDMI 拔出状态：包含 "plugout"
                        //（拔出时不需要延时）
                        if (listener != null) {
                            listener.onHdmiStatusChanged(currentStatus);
                        }
                    }
                } catch (IOException e) {
                    Log.d(TAG, "读取 HDMI 状态异常：" + e.toString());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    // 调用此方法可在不需要检测时终止线程
    public void stop(){
        threadStatus = true;
    }
}
