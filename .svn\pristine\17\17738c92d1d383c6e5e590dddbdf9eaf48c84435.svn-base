// ***************************************************************
//  gep_header   version:  1.0     date: 26/4/2007
//  -------------------------------------------------------------
//  <PERSON><PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2007 - All Rights Reserved
// ***************************************************************
// 
// ***************************************************************
#ifndef __zdp_header_h__
#define __zdp_header_h__

#include <vector>
#include <string>
#include <utiny/usys_smartptr.h>
#include <utiny/usys_bytes.h>
#include <utiny/ustdcpp_inc.h>

#define GEP_HEADER_SIZE 76
#define ZDP_HEADER_SIZE 10

class usys_data_block;
class usys_transceiver;

namespace gep
{
    struct UBEDA_API GEP_HEADER
    {
        int pack(uidl_uchar* buf, int size, uidl_uchar version) const;
        int pack_z(uidl_uchar* buf, int size, uidl_uchar version) const;
        int unpack(const uidl_uchar* buf, int size, uidl_uchar version);
        int unpack_z(const uidl_uchar* buf, int size, uidl_uchar version);
        uidl_int32 body_length(uidl_uchar version = 0) const;
        uidl_int32 total_length(uidl_uchar version = 0) const;
        int msg_length();
        void msg_length(int msglen);
        uidl_ushort    magic;
        uidl_uchar     version;
        uidl_ushort    compress_flag;
        uidl_ushort    msg_type;
    public:
        uidl_ushort    seq_num;
    private:
        uidl_uint32    msg_len;
    };

    struct UBEDA_API GEP_CONTEXT_HEADER 
    {
        GEP_CONTEXT_HEADER(unsigned short magic = 'Z', unsigned char version = 2/*1: GBK; 2: UTF8*/);

        GEP_CONTEXT_HEADER(const GEP_CONTEXT_HEADER& header);
        GEP_CONTEXT_HEADER(const GEP_CONTEXT_HEADER& header, unsigned char ver);

        unsigned short  magic;
        unsigned char   version;
    };

    class UBEDA_API gep_packet_context : virtual public usys_smartptr_mtbase
    {
    private:
        unsigned sequence_id_;
        unsigned short msg_type_;
        unsigned short compress_flag_;
        bool is_request_;
        GEP_CONTEXT_HEADER header_user_;
        usys_smartptr<usys_transceiver> transceiver_ptr_;
    public:
        gep_packet_context(bool is_request, unsigned short msg_type, unsigned sequence_id,
            unsigned short compress_flag, const GEP_CONTEXT_HEADER& header_user, const usys_smartptr<usys_transceiver>& transceiver_ptr)
            : sequence_id_(sequence_id), msg_type_(msg_type), compress_flag_(compress_flag), is_request_(is_request),
            header_user_(header_user), transceiver_ptr_(transceiver_ptr)
        {
        }

        inline int header_length() const { return (header_user_.magic == 'Z') ? ZDP_HEADER_SIZE : GEP_HEADER_SIZE; }
        inline void magic(unsigned short v) { header_user_.magic = v; }
        virtual ~gep_packet_context() {};
        inline bool is_request() const { return is_request_; }
        inline unsigned sequence_id() const { return sequence_id_; }
        inline void sequence_id(unsigned val) { sequence_id_ = val;}
        inline unsigned short msg_type() const { return msg_type_; } 
        inline const GEP_CONTEXT_HEADER& context_header() const { return header_user_; }
        inline unsigned char version() const { return (unsigned char)header_user_.version; }
        inline void version(unsigned char val) { header_user_.version = val; }
        inline unsigned short compress_flag() const { return compress_flag_; }
        inline void compress_flag(unsigned short val) { compress_flag_ = val; }
        const usys_smartptr<usys_transceiver>& transceiver_ptr() { return transceiver_ptr_; }
    };
    typedef usys_smartptr<gep_packet_context> gep_packet_context_ptr;
} //namespace gep

#endif
