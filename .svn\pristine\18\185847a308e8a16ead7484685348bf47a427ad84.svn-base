package com.android.rockchip.camera2.activity

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.Size
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import com.android.rockchip.camera2.activity.browse.TpVideoBrowse
import com.android.rockchip.camera2.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView
import com.android.rockchip.camera2.databinding.ActivityMainBinding
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.util.HdmiService
import com.android.rockchip.camera2.util.HdmiService.HdmiListener
import com.android.rockchip.camera2.util.TouptekIspParam
import com.android.rockchip.camera2.util.TransformUtils
import com.android.rockchip.camera2.util.getStorageDCIMPath
import com.android.rockchip.camera2.util.getStoragePicturePath
import com.android.rockchip.camera2.util.getStorageVideoPath
import com.android.rockchip.camera2.util.setupEdgeToEdgeFullScreen
import com.android.rockchip.camera2.util.touptek_serial_rk
import com.android.rockchip.camera2.video.CaptureImage
import com.android.rockchip.camera2.video.EncoderConfig
import com.android.rockchip.camera2.video.TvPreviewHelper
import com.android.rockchip.camera2.video.VideoEncoder
import com.android.rockchip.camera2.view.ROIView
import java.io.File

import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.video.CameraManager

import java.io.FileOutputStream

class StatusBanner(private val view: TextView) {
    // 添加带默认值的duration参数
    private var hideRunnable: Runnable? = null

    fun show(text: String, duration: Long = 3000) {
        view.post {
            // 取消之前的隐藏任务（如果存在）
            hideRunnable?.let { view.removeCallbacks(it) }

            // 显示横幅
            view.apply {
                this.text = text
                visibility = View.VISIBLE
            }

            // 设置新的隐藏任务
            hideRunnable = Runnable {
                view.visibility = View.GONE
            }
            view.postDelayed(hideRunnable, duration)
        }
    }


    fun hide() {
        view.post {
            // 取消等待中的隐藏任务
            hideRunnable?.let { view.removeCallbacks(it) }
            // 立即隐藏横幅
            view.visibility = View.GONE
        }
    }
}

class MainActivity : AppCompatActivity(), View.OnAttachStateChangeListener, MainMenu.OnRectangleVisibilityListener, touptek_serial_rk.DeviceStateCallback{
    private lateinit var binding: ActivityMainBinding
    private var isActivityResumed = false
    private var isSerialConnected = false
    private var isHDMIConnected = false

    private var tvpreview: TvPreviewHelper? = null
    private val TAG = "MainActivity"
    private var videoEncoder: VideoEncoder? = null
    private var cameraManager: CameraManager? = null
    private var captureImage: CaptureImage? = null

    //HDMI
    private var hdmiService: HdmiService? = null
    private var isMenuEnabled: Boolean = true
    private var isVideoEncoderInit: Boolean = false
    private var isCameraOpened: Boolean = false
    fun isMenuEnabled(): Boolean = isMenuEnabled

    private var startTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var updateTimeRunnable: Runnable

    private lateinit var banner: StatusBanner  //提示标签
    private var isRecording = false

    //zoom
    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var gestureDetector: GestureDetector? = null
    //----Test
    // 在类中添加这些成员变量
    private var initialX = 0f
    private var initialY = 0f
    private var touchCount = 0
    private var hasMovedSignificantly = false
    private var hasPerformedZoom = false
    private val MOVE_THRESHOLD = 20f // 移动阈值50像素
    //----

    //roi
    private var rectangleOverlayView: TpRectangleOverlayView? = null
    private var roiView: ROIView? = null

    //关联串口
    private fun getMainMenuFragment(): MainMenu? {
        return supportFragmentManager.findFragmentByTag("MainMenu") as? MainMenu
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupEdgeToEdgeFullScreen() //全屏
        initializeTvComponents() //初始化预览管理

//        /* 初始化 HDMI 服务 */
//        initHdmiService()
        initCameraManager() //用于抓图
        initCameraMode()  //打开摄像头,开启录像功能
        initStorageMonitor()

        //初始化zoom功能
        initScaleGestureDetector()
        initPanGestureDetector()
        initTouchEvent()
        //设置ISP开机记忆参数，恢复上一次的参数设置
        initISP()
        initCameraMemoryParameter()
        //ROI
        // 初始化ROI相关控件
        initializeRoiComponents()

        banner = StatusBanner(binding.mainCenterInfoLabel)

        /* 初始化 HDMI 服务 */
        initHdmiService()
    }

    private fun initializeTvComponents() {
        tvpreview = TvPreviewHelper(this, binding.rootView)
    }

    override fun onViewAttachedToWindow(v: View) = Unit //空实现
    override fun onViewDetachedFromWindow(v: View) = Unit

    override fun onDeviceStateChanged(connected: Boolean) {
        val message = if (connected) "设备已连接 111" else "设备已断开 11"
        Log.d("SerialMonitor", message)

    }

    private fun initHdmiService() {
        println("#### initHDMI")  // 目前点击浏览返回后依然会初始化HDMI，需要优化，停止反复初始化
//        getMainMenuFragment()?.disableAllMenuButtons()
//        isMenuEnabled = false

        hdmiService = HdmiService.getInstance()
        hdmiService?.setHdmiListener(HdmiListener { isConnected: Boolean ->
            runOnUiThread {
                handleHdmiStatusChange(
                    isConnected
                )
            }
        })

        hdmiService?.init()
        setupViewListener()
    }

    private fun handleHdmiStatusChange(isConnected: Boolean) {
        isHDMIConnected = isConnected

        if (<EMAIL>()) {
            runOnUiThread {
            if (isConnected) {
                if(!isCameraOpened) {
                    cameraManager!!.openCamera()
                    banner.show("HDMI连接", 1000)
                    isCameraOpened = true
                }

                if(isSerialConnected){
                    getMainMenuFragment()?.enableAllMenuButtons()
                }
            } else {
                if (isRecording) stopRecord()
                banner.show("HDMI断开连接", 1000000)
                isCameraOpened = false

                getMainMenuFragment()?.disableAllMenuButtons()
            }
        }
        }

    }

    private fun showMenu() {
        val menu = MainMenu().apply {
            setRectangleVisibilityListener(this@MainActivity)
        }

        menu.show(supportFragmentManager, "MainMenu")


        if(!isSerialConnected) {
            getMainMenuFragment()?.disableAllMenuButtons()
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
//        ev?.let {
//            if (it.action == MotionEvent.ACTION_UP) {
//                Log.d(TAG, "dispatchTouchEvent: 2222")
//                showMenu()
//                return true
//            }
//        }
//        return super.dispatchTouchEvent(ev)
        return super.dispatchTouchEvent(ev)
    }

    fun showBrowseActivity() {
        releaseResources()
        releaseTvView()
        startActivity(Intent(this, TpVideoBrowse::class.java))
        finish()
    }

    fun SetZoomIn() {
    }

    fun SetZoomOut() {
    }

    fun releaseTvView() {
        tvpreview?.stopPreview()
        tvpreview = null
    }

    fun ActivityCaptureImage() {
        val imageSize = Size(3840, 2160) // Example size, can be dynamic
        val outputPath = FileStorageUtils.createImagePath(this)
        println("######### ${outputPath}")
        captureImage!!.requestCapture(imageSize, outputPath)
    }

    fun initCameraMode() {
        binding.blueTextureView.visibility = View.VISIBLE
        binding.blueTextureView.bringToFront()
//        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化
        setupRectangleOverlay() // 添加这行
    }

    fun initTVMode() {
        binding.blueTextureView.visibility = View.GONE
        binding.tvView.visibility = View.VISIBLE
        binding.tvView.bringToFront()


        // 切换到TV模式
        if (isRecording) stopRecord()
        releaseResources()
        tvpreview?.startPreview()
    }

    private fun debugTouchEvent(event: MotionEvent) {
        val action = when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> "DOWN"
            MotionEvent.ACTION_UP -> "UP"
            MotionEvent.ACTION_MOVE -> "MOVE"
            MotionEvent.ACTION_POINTER_DOWN -> "POINTER_DOWN"
            MotionEvent.ACTION_POINTER_UP -> "POINTER_UP"
            else -> "OTHER: ${event.actionMasked}"
        }

        Log.d("TOUCH_DEBUG", "Action: $action | View: ${binding.rootView.id} | X: ${event.x} Y: ${event.y} | TouchCount: $touchCount")
    }


    private val touchListener = View.OnTouchListener { v, event ->
        debugTouchEvent(event)
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 单点触摸开始
                hasPerformedZoom = false
                touchCount = 1
                initialX = event.x
                initialY = event.y
                hasMovedSignificantly = false
                true
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 多点触摸开始
                touchCount = event.pointerCount
                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (touchCount == 1) {
                    // 单点移动，检查移动距离
                    val dx = event.x - initialX
                    val dy = event.y - initialY
                    val distance = Math.sqrt((dx * dx + dy * dy).toDouble()).toFloat()

                    if (distance > MOVE_THRESHOLD) {
                        hasMovedSignificantly = true
                        // 触发平移
                        gestureDetector?.onTouchEvent(event)
                        scaleGestureDetector?.onTouchEvent(event)
                        true
                    } else {
                        false
                    }
                } else if (touchCount > 1) {
                    // 多点触摸，触发缩放
                    val scaleHandled = scaleGestureDetector?.onTouchEvent(event) ?: false
                    if (scaleHandled) {
                        hasPerformedZoom = true
                    }
                    val panHandled = gestureDetector?.onTouchEvent(event) ?: false
                    scaleHandled || panHandled
                } else {
                    false
                }
            }
            MotionEvent.ACTION_UP -> {
                if (touchCount == 1 && !hasMovedSignificantly && !hasPerformedZoom && isMenuEnabled) {
                    // 满足点击条件时调用 performClick()
                    v.performClick()
                    showMenu()
                }
                touchCount = 0
                true
            }
            MotionEvent.ACTION_POINTER_UP -> {
                touchCount = event.pointerCount - 1
                true
            }
            else -> false
        }
    }

    // 在initTouchEvent()中设置监听器

    fun TvModeToCameraMode(){
        // 切换回常规模式
        tvpreview!!.stopPreview()
//        initCameraManager()
//        binding.rootView.visibility = View.GONE
        binding.tvView.visibility = View.GONE
        binding.blueTextureView.visibility = View.VISIBLE
        binding.blueTextureView.bringToFront()
        setupViewListener()

        //roi
        setupRectangleOverlay()
    }

    fun isRecording(): Boolean = isRecording

    fun startRecord() {
        isRecording = true
        updateMainMenuRecordButton()

        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化

        Handler(Looper.getMainLooper()).postDelayed({
            val newOutputPath = FileStorageUtils.createVideoPath(this)
            videoEncoder?.startRecording(newOutputPath)
        }, 500)

        banner.show("开始录像",1000)

        startTime = System.currentTimeMillis()
        binding.tvTimer.visibility = View.VISIBLE
        binding.tvTimer.bringToFront()
        startTimer()
    }

    fun stopRecord() {
        isRecording = false
        updateMainMenuRecordButton()
        binding.tvTimer.visibility = View.INVISIBLE
        videoEncoder?.stopRecording()
        banner.show("结束录像，正在保存",20000)
    }

    private fun updateMainMenuRecordButton() {
        // 查找并更新MainMenu中的按钮状态
//        supportFragmentManager.findFragmentByTag("MainMenu")?.view?.let { view ->
//            val recordButton = view.findViewById<ImageButton>(R.id.btn_record_video)
//            val resId = if (isRecording) R.drawable.btn_record_video_pressed else R.drawable.btn_record_video_n
//            recordButton.setImageResource(resId)
//        }

    }

    private fun initCameraManager() {
//        cameraManager = CameraManager(this)
        captureImage = CaptureImage.builder(Size(3840, 2160))
            .onImageSaved { filePath ->
                banner.show("捕获成功",1000)
            }
            .onError { errorMessage ->
                banner.show("捕获失败",1000)
            }
            .build()
    }

    private fun setupViewListener() {
        binding.blueTextureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                initVideoEncode(Surface(surface))
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean = true
            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }
    }

    private fun initVideoEncode(textureSurface: Surface) {
        val encoderConfig = EncoderConfig.createDefault4K()
        videoEncoder = VideoEncoder.builder()
            .setPreviewSurface(textureSurface)
            .setEncoderConfig(encoderConfig)
            .onSurfaceAvailable { encoderSurface ->
                cameraManager = CameraManager.builder(this)
                    .onCameraOpened { camera ->
                        cameraManager?.configCameraOutputs(
                            camera,
                            encoderSurface,
                            captureImage!!.imageReader!!.surface
                        )
                    }
                    .onCameraDisconnected { camera -> camera.close() }
                    .onCameraError { camera, error -> camera.close() }
                    .build()

                isVideoEncoderInit = true

                cameraManager!!.openCamera()

            }
            .onStorageFull {
                Toast.makeText(this, "存储空间不足，录制已停止", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()
            }
            .onError { errorType, e ->
                Toast.makeText(this, "录制错误-----: $errorType", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()

                //关闭录像显示相关操作
                isRecording = false
                updateMainMenuRecordButton()
                binding.tvTimer.visibility = View.INVISIBLE
            }
            .onFileSizeLimitReached {
                Toast.makeText(this, "文件大小达到限制，录制已停止", Toast.LENGTH_LONG).show()
                videoEncoder?.stopRecording()
            }
            .onSaveComplete { filePath ->
                Toast.makeText(this, "录制完成: $filePath", Toast.LENGTH_LONG).show()
                val fileName = filePath.trimEnd('/').substringAfterLast("/")
                banner.show("${fileName}已保存", 1500)
            }
            .build()
    }

    private fun releaseResources() {
        cameraManager?.releaseCamera()
        videoEncoder?.release()
    }

    private fun startTimer() {
        updateTimeRunnable = object : Runnable {
            override fun run() {
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - startTime
                updateTimerText(duration)
                handler.postDelayed(this, 1000)
            }
        }
        handler.post(updateTimeRunnable)
    }

    private fun updateTimerText(duration: Long) {
        val seconds = (duration / 1000) % 60
        val minutes = (duration / (1000 * 60)) % 60
        val hours = (duration / (1000 * 60 * 60)) % 24
        binding.tvTimer.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    override fun onDestroy() {
        super.onDestroy()
//        handler.removeCallbacks(updateTimeRunnable)
        releaseResources()
    }

    override fun onPause() {
        super.onPause()
        isActivityResumed = false
        Log.d("Lifecycle", "onPause")
    }

    override fun onStop() {
        super.onStop()
        Log.d("Lifecycle", "onStop")
    }

    override fun onRestart() {
        super.onRestart()

        initializeTvComponents()
        Log.d("Lifecycle", "onRestart")
    }

    override fun onStart() {
        super.onStart()
        Log.d("Lifecycle", "onStart")
    }

    override fun onResume() {
        super.onResume()
        isActivityResumed = true
        Log.d("Lifecycle", "onResume")
    }

    fun isActivityForeground(): Boolean {
        return isActivityResumed
    }

    private fun initStorageMonitor(){
        FileStorageUtils.startUsbDriveMonitor(this, object : FileStorageUtils.StorageListener {
            override fun onUsbDriveConnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已连接", Toast.LENGTH_SHORT).show()
                createStorageDefaultPath()
            }

            override fun onUsbDriveDisconnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已断开", Toast.LENGTH_SHORT).show()

            }
        })
    }

    fun createStorageDefaultPath()
    {
        val storagePath = FileStorageUtils.getExternalStoragePath(this)

        val dcimPath = File(storagePath, getStorageDCIMPath()).path
        val videosPath = File(storagePath, getStorageVideoPath()).path
        val picturesPath = File(storagePath, getStoragePicturePath()).path

        // 创建 DCIM 目录（如果不存在）
        val dcimDir = File(dcimPath)
        if (!dcimDir.exists()) {
            dcimDir.mkdirs()
        }

        // 创建 Videos 目录（如果不存在）
        val videosDir = File(videosPath)
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }

        // 创建 Pictures 目录（如果不存在）
        val picturesDir = File(picturesPath)
        if (!picturesDir.exists()) {
            picturesDir.mkdirs()
        }
    }


    private fun initScaleGestureDetector() {
        scaleGestureDetector = ScaleGestureDetector(this, object : SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                val focusX = detector.focusX
                val focusY = detector.focusY
                Log.d(TAG, "缩放因子: ${"%.3f".format(scaleFactor)}")

                // 调用 TransformUtils 的 applyZoom 方法
                TransformUtils.applyZoom(binding.blueTextureView, scaleFactor, focusX, focusY)

                Log.d(TAG, "onScroll: 开始缩放")
                return true
            }
        })
    }

    private fun initPanGestureDetector() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                TransformUtils.applyPan(binding.blueTextureView, -distanceX, -distanceY)
                Log.d(TAG, "onScroll: 平移")
                return true
            }
            override fun onDown(e: MotionEvent): Boolean = true
        })
    }

    private fun setupRectangleOverlay() {
        // 确保blueTextureView已经初始化
        binding.blueTextureView.post {
            // 创建FrameLayout作为容器
            val container = FrameLayout(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            }

            // 将blueTextureView从原有父布局移除并添加到新容器
            (binding.blueTextureView.parent as? ViewGroup)?.removeView(binding.blueTextureView)
            container.addView(binding.blueTextureView)

            // 添加矩形覆盖层
            rectangleOverlayView = TpRectangleOverlayView(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 设置初始位置（居中）
                val width = 200f
                val height = 120f
                val left = (binding.blueTextureView.width - width) / 2
                val top = (binding.blueTextureView.height - height) / 2
                setRectanglePosition(left, top, left + width, top + height)
                //默认隐藏
                visibility = View.GONE

                onPositionChanged = { rect ->
                    Log.d(TAG, "Rectangle position: $rect")
                }
            }
            container.addView(rectangleOverlayView)

            // 将容器添加回原布局
            binding.root.addView(container, 0)
        }
    }

    private fun initTouchEvent() {
        println("########### initTouchEvent() 11")
        binding.blueTextureView.setOnTouchListener(touchListener)
        binding.tvView.setOnTouchListener(touchListener)

        // 添加点击监听器确保点击事件触发
        binding.blueTextureView.setOnClickListener {
            if (isMenuEnabled) showMenu()
            println("########### CameraMode to showMenu() 22")
        }

        binding.tvView.setOnClickListener {
            if (isMenuEnabled) showMenu()
            println("########### TVMode to showMenu() 33")
        }
    }

    override fun onShowRectangle() {
        showRectangleOverlay()
    }

    override fun onHideRectangle() {
        hideRectangleOverlay()
    }

    fun showRectangleOverlay() {
//        rectangleOverlayView?.visibility = View.VISIBLE
        binding.roiView.isROIEnabled = true
    }

    fun hideRectangleOverlay() {
//        rectangleOverlayView?.visibility = View.GONE
        binding.roiView.isROIEnabled = false
    }

    fun isRectangleVisible(): Boolean {
        return rectangleOverlayView?.visibility == View.VISIBLE
    }

    private fun initISP() {
        TouptekIspParam.init(this) //初始化isp
        TouptekIspParam.setOnSerialStateChangedListener { connected ->
            if (connected){
                getMainMenuFragment()?.enableMenuButton()

                if(isHDMIConnected) {
                    getMainMenuFragment()?.enableAllMenuButtons()
                }

                isSerialConnected = true

                banner.show("相机已连接", 2000)

            }else{
                getMainMenuFragment()?.disableMenuButton()
                getMainMenuFragment()?.disableAllMenuButtons()
                if(isRecording){
                    stopRecord()
                }

                isSerialConnected = false
                banner.show("相机断开连接", 1000000000000)


            }
        }
    }

    private fun initCameraMemoryParameter() {
//        val red = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN)
//        TouptekIspParam.sendToDevice(TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN, red)
//
//        val green = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_WBGREENGAIN)
//        TouptekIspParam.sendToDevice(TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN, green)
//
//        val sharpness = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_SHARPNESS)
//        TouptekIspParam.sendToDevice(TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN, sharpness)
//
//        Log.d("MainActivity 111","red:${red}, green:${green}, sharpness:${sharpness}")

//        TouptekIspParam.requestAllParamRanges()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun initializeRoiComponents() {
        binding.roiView.setCameraResolution(3840, 2160)
    }



    private fun handleTouchEvent(v: View, event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 单点触摸开始
                hasPerformedZoom = false
                touchCount = 1
                initialX = event.x
                initialY = event.y
                hasMovedSignificantly = false
                true
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 多点触摸开始
                touchCount = event.pointerCount
                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (touchCount == 1) {
                    // 单点移动，检查移动距离
                    val dx = event.x - initialX
                    val dy = event.y - initialY
                    val distance = Math.sqrt((dx * dx + dy * dy).toDouble()).toFloat()

                    if (distance > MOVE_THRESHOLD) {
                        hasMovedSignificantly = true
                        // 触发平移
                        gestureDetector?.onTouchEvent(event)
                        scaleGestureDetector?.onTouchEvent(event)
                        return true
                    }
                } else if (touchCount > 1) {
                    // 多点触摸，触发缩放
                    val scaleHandled = scaleGestureDetector?.onTouchEvent(event) ?: false
                    if (scaleHandled) {
                        hasPerformedZoom = true
                    }
                    val panHandled = gestureDetector?.onTouchEvent(event) ?: false
                    return scaleHandled || panHandled
                }
                false
            }
            MotionEvent.ACTION_UP -> {
                if (touchCount == 1 && !hasMovedSignificantly && !hasPerformedZoom && isMenuEnabled) {
                    // 满足点击条件时调用 performClick()
                    v.performClick()
                    showMenu()
                }
                touchCount = 0
                true
            }
            MotionEvent.ACTION_POINTER_UP -> {
                touchCount = event.pointerCount - 1
                true
            }
            else -> false
        }
        return false
    }

    private fun canTriggerMenu(): Boolean {
        return touchCount == 1 &&
                !hasMovedSignificantly &&
                !hasPerformedZoom &&
                isMenuEnabled
    }


    private fun takeScreenshot() {
        val rootView = window.decorView.rootView
        rootView.isDrawingCacheEnabled = true
        val screenshotBitmap = Bitmap.createBitmap(rootView.drawingCache)
        rootView.isDrawingCacheEnabled = false

        // 保存截图
        saveBitmapToStorage(screenshotBitmap)
    }

    /**
     * 保存Bitmap到外部存储
     */
    private fun saveBitmapToStorage(bitmap: Bitmap) {
        val fileName = "screenshot_${System.currentTimeMillis()}.png"
//        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val picturesDir = "/storage/2627-B03B0/DCIM/Images/"
        val screenshotFile = File(picturesDir, fileName)

        try {
            FileOutputStream(screenshotFile).use { fos ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                Toast.makeText(this, "截图已保存: ${screenshotFile.path}", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Log.e("Screenshot", "保存截图失败", e)
            Toast.makeText(this, "保存截图失败", Toast.LENGTH_SHORT).show()
        }
    }

    fun captureDialogFragment(dialogFragment: DialogFragment): Bitmap? {
        val dialog = dialogFragment.dialog
        if (dialog == null) {
            Log.e("DialogCapture", "Dialog is null")
            return null
        }

        // 获取对话框的窗口视图
        val decorView = dialog.window?.decorView
        if (decorView == null) {
            Log.e("DialogCapture", "DecorView is null")
            return null
        }

        // 确保视图已经正确测量和布局
        decorView.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )

        decorView.layout(0, 0, decorView.measuredWidth, decorView.measuredHeight)

        // 创建位图并绘制视图
        val bitmap = Bitmap.createBitmap(
            decorView.measuredWidth,
            decorView.measuredHeight,
            Bitmap.Config.ARGB_8888
        )

        val canvas = Canvas(bitmap)
        decorView.draw(canvas)

        return bitmap
    }
}
