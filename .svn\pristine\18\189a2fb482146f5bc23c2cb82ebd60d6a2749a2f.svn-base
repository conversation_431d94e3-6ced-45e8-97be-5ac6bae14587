<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 09 08:53:38 CST 2025 -->
<title>C - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-09">
<meta name="description" content="index: C">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.StreamType.html#CAMERA" class="member-name-link">CAMERA</a> - enum class 中的枚举常量 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.StreamType.html" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a></dt>
<dd>
<div class="block">摄像头流</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">CameraManagerHelper 类用于管理摄像头的打开、预览和资源释放。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#%3Cinit%3E(android.content.Context)" class="member-name-link">CameraManagerHelper(Context)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">构造函数，初始化 CameraManager。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper.Builder</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">CaptureImageHelper 类用于处理摄像头图像的抓取和保存。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html#%3Cinit%3E(android.util.Size)" class="member-name-link">CaptureImageHelper(Size)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></dt>
<dd>
<div class="block">构造函数，初始化 ImageReader 和后台线程。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper.Builder</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的接口</dt>
<dd>
<div class="block">回调接口，用于通知抓图结果</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#close()" class="member-name-link">close()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">关闭串口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/package-summary.html">com.android.rockchip.camera2.service</a> - 程序包 com.android.rockchip.camera2.service</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a> - 程序包 com.android.rockchip.camera2.util</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a> - 程序包 com.android.rockchip.camera2.video</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper)" class="member-name-link">completeInitialization(VideoEncoder, CaptureImageHelper)</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>
<div class="block">第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）便捷的完成初始化方法（默认使用CAMERA流类型）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper,com.android.rockchip.camera2.service.TpctrlService.StreamType)" class="member-name-link">completeInitialization(VideoEncoder, CaptureImageHelper, TpctrlService.StreamType)</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>
<div class="block">第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface)" class="member-name-link">configCameraOutputs(CameraDevice, Surface, Surface)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">配置摄像头输出的 Surface。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/EncoderConfig.html#createDefault1080P()" class="member-name-link">createDefault1080P()</a> - 类中的静态方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></dt>
<dd>
<div class="block">创建默认的1080P配置</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/EncoderConfig.html#createDefault4K()" class="member-name-link">createDefault4K()</a> - 类中的静态方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></dt>
<dd>
<div class="block">创建默认的4K配置</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/EncoderConfig.html#createDefault720P()" class="member-name-link">createDefault720P()</a> - 类中的静态方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></dt>
<dd>
<div class="block">创建默认的720P配置</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#createEarlyInstance(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.TpctrlService.HeartbeatListener)" class="member-name-link">createEarlyInstance(AppCompatActivity, TpctrlService.HeartbeatListener)</a> - 类中的静态方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>
<div class="block">第一步：创建早期实例（必须在Activity的onCreate()中调用）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#createImagePath(android.content.Context)" class="member-name-link">createImagePath(Context)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">创建带日期后缀的图片文件路径。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/EncoderConfig.html#createMediaFormat()" class="member-name-link">createMediaFormat()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></dt>
<dd>
<div class="block">创建MediaFormat对象</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#createVideoPath(android.content.Context)" class="member-name-link">createVideoPath(Context)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">创建带日期后缀的视频文件路径。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html#current" class="member-name-link">current</a> - 类中的变量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
