<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="网络设置"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="16dp" />

        <!-- 以太网状态卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="以太网连接"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/ethernet_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:text="以太网状态：未连接" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- WiFi状态卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="WiFi连接"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/wifi_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:text="WIFI状态：未连接"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/wifi_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="连接WIFI"
                    android:background="#2196F3"
                    android:textColor="#FFFFFF" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- WiFi热点卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="WiFi热点"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/hotspot_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:text="热点状态：未开启"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/hotspot_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开启热点"
                    android:background="#FF9800"
                    android:textColor="#FFFFFF" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- RTSP推流控制卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="RTSP推流控制"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <!-- RTSP状态 -->
                <TextView
                    android:id="@+id/rtsp_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:text="RTSP状态：未启动"
                    android:layout_marginBottom="8dp" />

                <!-- RTSP URL -->
                <TextView
                    android:id="@+id/rtsp_url_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:text="RTSP URL："
                    android:layout_marginBottom="12dp" />

                <!-- 网络接口选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:text="网络接口："
                        android:layout_marginEnd="8dp" />

                    <Spinner
                        android:id="@+id/network_interface_spinner"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                </LinearLayout>

                <!-- 流类型选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:text="推流类型："
                        android:layout_marginEnd="8dp" />

                    <RadioGroup
                        android:id="@+id/stream_type_radio_group"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/radio_camera_stream"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="摄像头流"
                            android:textColor="#333333"
                            android:checked="true"
                            android:layout_marginEnd="16dp" />

                        <RadioButton
                            android:id="@+id/radio_screen_stream"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="屏幕流"
                            android:textColor="#333333" />

                    </RadioGroup>

                </LinearLayout>

                <!-- RTSP推流控制按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/start_rtsp_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="启动推流"
                        android:background="#4CAF50"
                        android:textColor="#FFFFFF"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/stop_rtsp_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="停止推流"
                        android:background="#F44336"
                        android:textColor="#FFFFFF"
                        android:layout_marginStart="8dp"
                        android:enabled="false" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 网络信息显示区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="网络信息"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/network_info_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:minHeight="80dp"
                    android:background="#F5F5F5"
                    android:padding="12dp"
                    android:text="等待网络状态更新..." />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
