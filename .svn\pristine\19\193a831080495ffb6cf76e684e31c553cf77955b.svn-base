package com.android.rockchip.camera2.video;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * CaptureImageHelper 类用于处理摄像头图像的抓取和保存。
 * <p>
 * 此类提供了图像抓取、保存为 JPEG 文件以及资源释放的功能。
 * 它支持异步操作，避免阻塞主线程。
 * </p>
 */
public class CaptureImageHelper
{
    private static final String TAG = "CaptureImageHelper";

    /* ImageReader 用于接收摄像头输出的图像数据 */
    private final ImageReader imageReader;

    /* 后台线程的 Handler，用于处理耗时操作 */
    private final Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private final HandlerThread backgroundThread;

    /* 回调接口，用于通知抓图结果 */
    private CaptureCallback captureCallback;

    /* 标志位，表示是否有抓图请求 */
    private volatile boolean isCaptureRequested = false;

    /* 请求抓图的目标尺寸 */
    private Size requestedSize;

    /* 抓图保存的输出路径 */
    private String outputPath;

    /**
     * 回调接口，用于通知抓图结果
     */
    public interface CaptureCallback
    {
        /**
         * 当图像保存成功时调用
         */
        void onImageSaved(String filePath);

        /**
         * 当抓图失败时调用
         */
        void onError(String errorMessage);
    }

    private CaptureImageHelper(Builder builder) {
        this.requestedSize = builder.imageSize;
        this.captureCallback = builder.captureCallback;
        this.imageReader = ImageReader.newInstance(
            builder.imageSize.getWidth(),
            builder.imageSize.getHeight(),
            builder.imageFormat,
            builder.maxImages
        );
        this.imageReader.setOnImageAvailableListener(this::onImageAvailable, builder.backgroundHandler);
        this.backgroundHandler = builder.backgroundHandler;
        this.backgroundThread = builder.backgroundThread;
    }

    /**
     * 构造函数，初始化 ImageReader 和后台线程。
     * <p>
     * 此方法会启动一个后台线程，并创建一个 ImageReader 实例，用于接收摄像头输出的图像数据。
     * </p>
     *
     * @param imageSize
     *        ImageReader 的图像尺寸。
     */
    public CaptureImageHelper(Size imageSize)
    {
        // 调用建造者模式创建实例，保持向后兼容性
        CaptureImageHelper helper = builder(imageSize).build();
        this.imageReader = helper.imageReader;
        this.backgroundHandler = helper.backgroundHandler;
        this.backgroundThread = helper.backgroundThread;
    }

    public static Builder builder(Size imageSize) {
        return new Builder(imageSize);
    }

    public static class Builder {
        private final Size imageSize;
        private CaptureCallback captureCallback;
        private int imageFormat = ImageFormat.YUV_420_888;
        private int maxImages = 50;
        private Handler backgroundHandler;
        private HandlerThread backgroundThread;
        
        // 单独的回调处理器
        private java.util.function.Consumer<String> onImageSavedHandler;
        private java.util.function.Consumer<String> onErrorHandler;

        private Builder(Size imageSize) {
            this.imageSize = imageSize;
            this.backgroundThread = new HandlerThread("CaptureImageBackground");
            this.backgroundThread.start();
            this.backgroundHandler = new Handler(backgroundThread.getLooper());
        }

        public Builder setCaptureCallback(CaptureCallback captureCallback) {
            this.captureCallback = captureCallback;
            return this;
        }
        
        /**
         * 设置图像保存成功的回调
         * @param handler 处理保存路径的回调函数
         */
        public Builder onImageSaved(java.util.function.Consumer<String> handler) {
            this.onImageSavedHandler = handler;
            return this;
        }
        
        /**
         * 设置图像保存失败的回调
         * @param handler 处理错误信息的回调函数
         */
        public Builder onError(java.util.function.Consumer<String> handler) {
            this.onErrorHandler = handler;
            return this;
        }

        public Builder setImageFormat(int imageFormat) {
            this.imageFormat = imageFormat;
            return this;
        }

        public Builder setMaxImages(int maxImages) {
            this.maxImages = maxImages;
            return this;
        }

        public CaptureImageHelper build() {
            // 如果设置了单独的回调处理器，但没有设置完整的CaptureCallback
            if (captureCallback == null && (onImageSavedHandler != null || onErrorHandler != null)) {
                captureCallback = new CaptureCallback() {
                    @Override
                    public void onImageSaved(String filePath) {
                        if (onImageSavedHandler != null) {
                            onImageSavedHandler.accept(filePath);
                        }
                    }
                    
                    @Override
                    public void onError(String errorMessage) {
                        if (onErrorHandler != null) {
                            onErrorHandler.accept(errorMessage);
                        }
                    }
                };
            }
            
            return new CaptureImageHelper(this);
        }
    }

    /**
     * 获取 ImageReader 实例。
     * <p>
     * ImageReader 用于得到ImageReader的实例，在传入摄像头的输出surface会用到。
     * </p>
     *
     * @return
     *        返回 ImageReader 实例。
     */
    public ImageReader getImageReader()
    {
        return imageReader;
    }

    /**
     * 设置抓图回调。
     * <p>
     * 此方法用于设置一个回调接口，当图像保存成功或失败时会触发回调。
     * </p>
     *
     * @param callback
     *        回调接口，用于通知抓图结果。
     */
    public void setCaptureCallback(CaptureCallback callback)
    {
        this.captureCallback = callback;
    }

    /**
     * 请求抓图。
     * <p>
     * 此方法会设置抓图的目标尺寸和保存路径，并触发抓图操作。
     * </p>
     *
     * @param size
     *        抓图的目标尺寸。
     * @param outputPath
     *        抓图保存的输出路径。
     */
    public void requestCapture(Size size, String outputPath)
    {
        this.isCaptureRequested = true;
        this.requestedSize = size;
        this.outputPath = outputPath;
    }

    /**
     * 当 ImageReader 有新的图像数据时调用。
     * <p>
     * 此方法会处理新的图像数据，并根据抓图请求将图像保存为 JPEG 文件。
     * 如果没有抓图请求，则直接释放图像。
     * </p>
     *
     * @param reader
     *        ImageReader 实例。
     */
    private void onImageAvailable(ImageReader reader)
    {
        /* 获取最新的图像 */
        Image image = reader.acquireLatestImage();
        if (image == null) return;

        if (isCaptureRequested)
        {
            isCaptureRequested = false; // 重置抓图请求标志

            /* 在主线程复制图像数据并立即释放图像 */
            Size cameraSize = new Size(image.getWidth(), image.getHeight());
            ByteBuffer yuvData = copyImageToBuffer(image);
            image.close(); // 立即释放图像

            /* 在后台线程处理图像数据 */
            backgroundHandler.post(() ->
            {
                try
                {
                    /* 将 YUV 数据转换为 JPEG 格式 */
                    byte[] jpegData = convertYUVToJPEG(yuvData, cameraSize, requestedSize);

                    /* 保存图像到指定路径 */
                    saveImage(jpegData, outputPath);

                    /* 通知回调图像保存成功 */
                    if (captureCallback != null)
                    {
                        captureCallback.onImageSaved(outputPath);
                    }
                }
                catch (Exception e)
                {
                    Log.e(TAG, "Error processing image", e);

                    /* 通知回调图像保存失败 */
                    if (captureCallback != null)
                    {
                        captureCallback.onError("Failed to process image");
                    }
                }
            });
        }
        else
        {
            /* 如果没有抓图请求，立即释放图像 */
            image.close();
        }
    }

    /**
     * 将 YUV 数据转换为 JPEG 格式。
     * <p>
     * 此方法会将摄像头输出的 YUV 数据转换为 JPEG 格式，并支持缩放到目标尺寸。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param cameraSize
     *        摄像头的原始图像尺寸。
     * @param targetSize
     *        目标图像尺寸。
     * @return
     *        返回 JPEG 格式的字节数组。
     */
    private byte[] convertYUVToJPEG(ByteBuffer yuvData, Size cameraSize, Size targetSize)
    {
        /* 将 YUV 数据转换为 NV21 格式 */
        byte[] nv21 = convertYUV420ToNV21(yuvData, cameraSize.getWidth(), cameraSize.getHeight());

        /* 使用 YuvImage 将 NV21 数据转换为 JPEG */
        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, cameraSize.getWidth(), cameraSize.getHeight(), null);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, cameraSize.getWidth(), cameraSize.getHeight()), 100, out);

        /* 如果目标尺寸与原始尺寸相同，直接返回 JPEG 数据 */
        if (targetSize.equals(cameraSize))
        {
            return out.toByteArray();
        }
        else
        {
            /* 将 JPEG 数据解码为 Bitmap */
            Bitmap originalBitmap = BitmapFactory.decodeByteArray(out.toByteArray(), 0, out.size());

            /* 缩放 Bitmap 到目标尺寸 */
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, targetSize.getWidth(), targetSize.getHeight(), true);

            /* 将缩放后的 Bitmap 转换回 JPEG 数据 */
            ByteArrayOutputStream scaledOut = new ByteArrayOutputStream();
            scaledBitmap.compress(Bitmap.CompressFormat.JPEG, 100, scaledOut);
            return scaledOut.toByteArray();
        }
    }

    /**
     * 将 Image 数据复制到缓冲区。
     * <p>
     * 此方法会将 Image 对象中的 YUV 数据复制到一个 ByteBuffer 中。
     * </p>
     *
     * @param image
     *        YUV_420_888 格式的 Image。
     * @return
     *        返回包含 YUV 数据的 ByteBuffer。
     */
    private ByteBuffer copyImageToBuffer(Image image)
    {
        int bufferSize = 0;
        for (Image.Plane plane : image.getPlanes())
        {
            bufferSize += plane.getBuffer().remaining();
        }

        ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);
        for (Image.Plane plane : image.getPlanes())
        {
            ByteBuffer planeBuffer = plane.getBuffer();
            byte[] planeData = new byte[planeBuffer.remaining()];
            planeBuffer.get(planeData);
            buffer.put(planeData);
        }
        buffer.rewind();
        return buffer;
    }

    /**
     * 将 YUV_420_888 格式转换为 NV21 格式。
     * <p>
     * 此方法会将 YUV 数据转换为 NV21 格式，以便使用 YuvImage 进行处理。
     * </p>
     *
     * @param yuvData
     *        包含 YUV 数据的 ByteBuffer。
     * @param width
     *        图像宽度。
     * @param height
     *        图像高度。
     * @return
     *        返回 NV21 格式的字节数组。
     */
    private byte[] convertYUV420ToNV21(ByteBuffer yuvData, int width, int height)
    {
        int frameSize = width * height;
        int chromaSize = frameSize / 4;
        byte[] nv21 = new byte[frameSize + frameSize / 2];

        byte[] yPlane = new byte[frameSize];
        byte[] uvPlane = new byte[2 * chromaSize];

        yuvData.position(0);
        yuvData.get(yPlane, 0, frameSize);
        yuvData.get(uvPlane, 0, 2 * chromaSize);

        System.arraycopy(yPlane, 0, nv21, 0, frameSize);
        for (int i = 0; i < chromaSize; i++)
        {
            nv21[frameSize + i * 2] = uvPlane[i * 2 + 1];
            nv21[frameSize + i * 2 + 1] = uvPlane[i * 2];
        }

        return nv21;
    }

    /**
     * 保存图像到文件。
     * <p>
     * 此方法会将 JPEG 格式的字节数组保存到指定的文件路径。
     * </p>
     *
     * @param jpegData
     *        JPEG 格式的字节数组。
     * @param outputPath
     *        输出文件路径。
     * @throws IOException
     *         如果保存失败，则抛出异常。
     */
    private void saveImage(byte[] jpegData, String outputPath) throws IOException
    {
        try (FileOutputStream output = new FileOutputStream(outputPath))
        {
            output.write(jpegData);
        }
    }

    /**
     * 释放资源，停止后台线程。
     * <p>
     * 此方法会停止后台线程并释放相关资源。
     * </p>
     */
    public void release()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }
}
