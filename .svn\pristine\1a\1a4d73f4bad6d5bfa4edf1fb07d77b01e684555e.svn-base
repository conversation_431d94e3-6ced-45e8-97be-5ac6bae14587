<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 09 08:53:38 CST 2025 -->
<title>I - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-09">
<meta name="description" content="index: I">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/video/ImageDecoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">ImageDecoder</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">ImageDecoder类用于处理图片的解码和显示。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/ImageDecoder.html#%3Cinit%3E()" class="member-name-link">ImageDecoder()</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/ImageDecoder.html" title="com.android.rockchip.camera2.video中的类">ImageDecoder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/ImageDecoder.LoadCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">ImageDecoder.LoadCallback</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的接口</dt>
<dd>
<div class="block">加载回调接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/HdmiService.html#init()" class="member-name-link">init()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/HdmiService.html" title="com.android.rockchip.camera2.util中的类">HdmiService</a></dt>
<dd>
<div class="block">初始化 HDMI 状态检测。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#init(android.content.Context)" class="member-name-link">init(Context)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">初始化 TouptekIspParam 和串口通信。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.html#initialize(android.util.Size,android.view.Surface)" class="member-name-link">initialize(Size, Surface)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></dt>
<dd>
<div class="block">初始化编码器和解码器。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#initializeSerial(int)" class="member-name-link">initializeSerial(int)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">初始化串口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html#isActive()" class="member-name-link">isActive()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a></dt>
<dd>
<div class="block">检查TV预览是否处于活动状态。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#isDecoding()" class="member-name-link">isDecoding()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">检查视频是否正在解码。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html#isDisabled" class="member-name-link">isDisabled</a> - 类中的变量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#isEarlyInitialized()" class="member-name-link">isEarlyInitialized()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>
<div class="block">检查初始化状态</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#isFrameByFrame()" class="member-name-link">isFrameByFrame()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">检查视频是否处于逐帧模式。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#isFullyInitialized()" class="member-name-link">isFullyInitialized()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#isPaused()" class="member-name-link">isPaused()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">检查视频是否处于暂停状态。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#isPlaybackCompleted()" class="member-name-link">isPlaybackCompleted()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">检查视频播放是否已完成</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#isRunning()" class="member-name-link">isRunning()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlSocketService.html#isRunning()" class="member-name-link">isRunning()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlSocketService.html" title="com.android.rockchip.camera2.service中的类">TpctrlSocketService</a></dt>
<dd>
<div class="block">获取服务运行状态</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#isSerialConnected()" class="member-name-link">isSerialConnected()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">判断当前串口的连接状态。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#isSerialConnected()" class="member-name-link">isSerialConnected()</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">检查串口是否已连接</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#isStreaming()" class="member-name-link">isStreaming()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/service/TpctrlService.html#isTpctrlExists()" class="member-name-link">isTpctrlExists()</a> - 类中的方法 com.android.rockchip.camera2.service.<a href="../com/android/rockchip/camera2/service/TpctrlService.html" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
