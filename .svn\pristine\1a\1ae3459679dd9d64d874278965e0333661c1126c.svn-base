<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_margin="4dp">

    <ImageView
        android:id="@+id/media_thumbnail"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:scaleType="centerCrop"
        android:background="@android:color/darker_gray" />

    <TextView
        android:id="@+id/media_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:ellipsize="none"
        android:scrollHorizontally="false"
        android:maxLines="3"
        android:textSize="12sp"
        android:textColor="@android:color/black"
        android:layout_marginTop="4dp" />
</LinearLayout>
