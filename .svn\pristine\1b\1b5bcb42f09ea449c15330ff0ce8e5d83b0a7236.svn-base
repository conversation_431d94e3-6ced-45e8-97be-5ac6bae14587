<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Jun 19 14:36:05 CST 2025 -->
<title>类分层结构</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-19">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="com/android/rockchip/camera2/service/package-tree.html">com.android.rockchip.camera2.service</a>, </li>
<li><a href="com/android/rockchip/camera2/util/package-tree.html">com.android.rockchip.camera2.util</a>, </li>
<li><a href="com/android/rockchip/camera2/video/package-tree.html">com.android.rockchip.camera2.video</a>, </li>
<li><a href="com/android/rockchip/camera2/view/package-tree.html">com.android.rockchip.camera2.view</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/CameraManagerHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/CameraManagerHelper.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper.Builder</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/CaptureImageHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/CaptureImageHelper.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper.Builder</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/EncoderConfig.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/EncoderConfig.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/FileStorageUtils.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/HdmiService.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">HdmiService</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/ImageDecoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">ImageDecoder</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></li>
<li class="circle">com.android.rockchip.camera2.service.<a href="com/android/rockchip/camera2/service/TpctrlService.html" class="type-name-link" title="com.android.rockchip.camera2.service中的类">TpctrlService</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TransformUtils.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">TransformUtils</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/TvPreviewHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/VideoDecoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/VideoEncoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/VideoEncoder.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoEncoder.Builder</a></li>
<li class="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li class="circle">com.android.rockchip.camera2.view.<a href="com/android/rockchip/camera2/view/ROIView.html" class="type-name-link" title="com.android.rockchip.camera2.view中的类">ROIView</a> (implements com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/FileStorageUtils.StorageListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">FileStorageUtils.StorageListener</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/HdmiService.HdmiListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">HdmiService.HdmiListener</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/ImageDecoder.LoadCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">ImageDecoder.LoadCallback</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></li>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TouptekIspParam.OnSerialStateChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a></li>
<li class="circle">com.android.rockchip.camera2.service.<a href="com/android/rockchip/camera2/service/TpctrlService.HeartbeatListener.html" class="type-name-link" title="com.android.rockchip.camera2.service中的接口">TpctrlService.HeartbeatListener</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/VideoDecoder.PlaybackListener.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a></li>
<li class="circle">com.android.rockchip.camera2.video.<a href="com/android/rockchip/camera2/video/VideoEncoder.VideoDataOutputCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="java.lang中的类或接口">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.android.rockchip.camera2.util.<a href="com/android/rockchip/camera2/util/TouptekIspParam.html" class="type-name-link" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></li>
<li class="circle">com.android.rockchip.camera2.service.<a href="com/android/rockchip/camera2/service/TpctrlService.StreamType.html" class="type-name-link" title="enum class in com.android.rockchip.camera2.service">TpctrlService.StreamType</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
