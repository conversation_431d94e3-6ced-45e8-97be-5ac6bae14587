package com.android.rockchip.camera2.activity.settings
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import android.view.WindowManager
import com.android.rockchip.camera2.R

class TpSettingsDialogFragment : DialogFragment() {
    private var currentTabId = R.id.btn_format

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.testdialog_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 恢复选中状态
        savedInstanceState?.let {
            currentTabId = it.getInt("CURRENT_TAB", R.id.btn_format)
        }

        setupTabSelection(view)
        setupInitialContent()


        // 设置关闭按钮
        view.findViewById<ImageButton>(R.id.btn_close).setOnClickListener {
            dismiss()
        }
        // 禁止点击外部关闭
//        isCancelable = false
    }

    private fun setupTabSelection(view: View) {
        val tabs = listOf(
            view.findViewById<TextView>(R.id.btn_format),
            view.findViewById<TextView>(R.id.btn_video),
            view.findViewById<TextView>(R.id.btn_misc)
        )

        tabs.forEach { tab ->
            tab.setOnClickListener {
                currentTabId = tab.id
                updateTabStates(tabs)
                updateContent()
            }
        }
    }

    private fun updateTabStates(tabs: List<TextView>) {
        tabs.forEach { tab ->
            tab.isSelected = tab.id == currentTabId
        }
    }

    private fun setupInitialContent() {
        childFragmentManager.beginTransaction()
            .replace(R.id.content_container, createFragmentForTab(currentTabId))
            .commit()
    }

    private fun updateContent() {
        childFragmentManager.beginTransaction()
//            .setCustomAnimations(
//                R.anim.slide_in_right,
//                R.anim.slide_out_left
//            )
            .replace(R.id.content_container, createFragmentForTab(currentTabId))
            .commit()
    }

    private fun createFragmentForTab(tabId: Int): Fragment {
        return when (tabId) {
            R.id.btn_format -> TpFormatSettingsFragment()
//            R.id.btn_video -> VideoSettingsFragment()
//            R.id.btn_misc -> MiscSettingsFragment()
            R.id.btn_video -> TpRecordSettingsFragment()
            R.id.btn_misc -> TpMiscSettingsFragment()
            else -> TpRecordSettingsFragment()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("CURRENT_TAB", currentTabId)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {

            clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND) // 清除默认遮罩效果

//            // 获取屏幕尺寸
//            val displayMetrics = resources.displayMetrics
//            val screenWidth = displayMetrics.widthPixels
//            val screenHeight = displayMetrics.heightPixels
//
//            // 设置窗口尺寸为屏幕的一半
//            setLayout((screenWidth * 0.5).toInt(), (screenHeight * 0.5).toInt())
//
//            // 设置窗口居中
//            setGravity(Gravity.CENTER)
//
//            // 可选：设置背景透明或圆角
//            setBackgroundDrawableResource(android.R.color.transparent)


            // 窗口属性设置
            val params = attributes.apply {
                // 允许背景交互的关键设置

//                flags = flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
//                flags = flags or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL


                // 尺寸和位置设置
//                val metrics = resources.displayMetrics
//                width = (metrics.widthPixels * 0.5).toInt()
//                height = ViewGroup.LayoutParams.MATCH_PARENT
//                gravity = Gravity.END // 靠右显示


                val displayMetrics = resources.displayMetrics
                val screenWidth = displayMetrics.widthPixels
                val screenHeight = displayMetrics.heightPixels

                // 设置窗口尺寸为屏幕的一半
                setLayout((screenWidth * 0.5).toInt(), (screenHeight * 0.5).toInt())

                // 设置窗口居中
                setGravity(Gravity.CENTER)
            }

            // 设置透明背景
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            attributes = params
        }
    }
}