<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.touptek.activity.MainActivity">

    <TextView
        android:id="@+id/sample_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:textSize="16sp"
        android:text="等待串口连接..."
        app:layout_constraintBottom_toTopOf="@+id/sample_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 按钮 -->
    <Button
        android:id="@+id/sample_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送测试命令"
        app:layout_constraintTop_toBottomOf="@id/sample_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>