package com.android.rockchip.camera2.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.animation.DecelerateInterpolator;

import com.android.rockchip.camera2.util.TouptekIspParam;

/**
 * ROIView - 可拖动的感兴趣区域(ROI)选择框
 * <p>
 * 该视图提供了一个交互式的区域选择功能，用于摄像头ISP参数的ROI(Region of Interest)设置。
 * 用户可以通过触摸操作调整选择框的位置和大小，所选区域的参数会自动同步到ISP处理模块。
 * </p>
 * 
 * 主要功能：
 * <ul>
 *   <li>显示一个可拖动和调整大小的红色矩形框</li>
 *   <li>四个角落提供控制点，用于调整框的大小</li>
 *   <li>触摸矩形内部可移动整个选择框</li>
 *   <li>自动将视图坐标转换为相机坐标系统</li>
 *   <li>支持动画显示和隐藏</li>
 *   <li>自动加载和保存ROI参数</li>
 *   <li>调试模式下显示坐标信息</li>
 *   <li>自动将ROI参数变化发送到相机设备</li>
 *   <li>支持相机镜像和翻转模式</li>
 * </ul>
 * 
 * 使用方法：
 * <pre>{@code
 * // 1. 在布局XML中添加视图
 * <com.android.rockchip.camera2.view.ROIView
 *     android:id="@+id/roi_view"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent"
 *     android:visibility="invisible" />
 * 
 * // 2. 在Activity中初始化
 * ROIView roiView = findViewById(R.id.roi_view);
 * 
 * // 3. 设置相机分辨率
 * roiView.setCameraResolution(3840, 2160);
 * 
 * // 4. 显示/隐藏ROI选择框
 * roiView.setROIEnabled(true); // 显示
 * roiView.setROIEnabled(false); // 隐藏
 * }</pre>
 * 
 * <p><strong>注意：</strong>使用前需确保相机预览已经初始化完成，并且已经设置正确的预览分辨率。
 * ROI框调整或移动时，参数会自动通过TouptekIspParam发送到相机设备。</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ROIView extends View implements TouptekIspParam.OnDataChangedListener 
{
    /* 日志标签 */
    private static final String TAG = "ROIView";
    
    /* 矩形绘制配置 */
    private final Paint borderPaint = new Paint();
    private final Paint handlePaint = new Paint();
    private final Paint handleInnerPaint = new Paint();
    private final int strokeWidth = 4; /* 边框宽度，单位dp */
    
    /* 矩形位置和大小 */
    private RectF roiRect = new RectF();
    private RectF animatedRect = new RectF(); /* 用于动画 */
    private RectF originalRoiRect = new RectF(); /* 存储无镜像/翻转状态下的原始ROI位置 */
    
    /* 当前的变换矩阵 */
    private Matrix currentTransformMatrix = new Matrix();
    
    /* 默认矩形尺寸(初始大小为视图的1/3) */
    private static final float DEFAULT_SIZE_RATIO = 0.3f;
    /* 实际使用的矩形尺寸比例 */
    private float sizeRatio = DEFAULT_SIZE_RATIO;
    
    /* 触摸处理 */
    private boolean isDragging = false;
    private boolean isResizing = false;
    private float lastTouchX = 0;
    private float lastTouchY = 0;
    private float touchSlop; /* 触摸滑动阈值 */
    
    /* ROI参数缓存，用于接收外部参数变化 */
    private Integer cachedRoiLeft = null;
    private Integer cachedRoiTop = null;
    private Integer cachedRoiWidth = null;
    private Integer cachedRoiHeight = null;
    private boolean ignoreNextROIParamChanges = false;
    
    /* 镜像和翻转状态变量 */
    private boolean isMirrorEnabled = false; /* 水平翻转状态 */
    private boolean isFlipEnabled = false;   /* 垂直翻转状态 */
    
    /* 当前选中的控制点 */
    private ControlHandle activeHandle = null;
    
    /* 最小矩形尺寸，避免调整得太小 */
    private static final int MIN_RECT_SIZE = 50;
    
    /* 是否显示ROI框 */
    private boolean isROIEnabled = false;
    
    /* 相机实际分辨率，用于坐标转换 */
    private int cameraWidth = 3840; /* 默认4K */
    private int cameraHeight = 2160; /* 默认4K */
    
    /* 动画相关 */
    private ValueAnimator rectAnimator;
    private static final int ANIMATION_DURATION = 250; /* 动画持续时间（毫秒） */
    
    /* 控制点配置 */
    private static final int HANDLE_RADIUS = 10; /* 控制点半径 */
    private static final int HANDLE_INNER_RADIUS = 5; /* 控制点内圈半径 */
    private static final int HANDLE_TOUCH_RADIUS = 20; /* 控制点触摸半径（大于视觉半径，便于触摸） */
    
    /* 调试模式 - 启用后会显示额外的视觉反馈 */
    private boolean debugMode = false;

    /**
     * 控制点枚举，定义了ROI框四个角落的控制点类型
     */
    private enum ControlHandle 
    {
        /** 左上角控制点 */
        TOP_LEFT, 
        /** 右上角控制点 */
        TOP_RIGHT, 
        /** 左下角控制点 */
        BOTTOM_LEFT, 
        /** 右下角控制点 */
        BOTTOM_RIGHT, 
        /** 非控制点区域 */
        NONE
    }

    /**
     * 构造函数
     *
     * @param context 上下文环境
     */
    public ROIView(Context context) 
    {
        super(context);
        init(context);
    }
    
    /**
     * 构造函数
     *
     * @param context 上下文环境
     * @param attrs 属性集
     */
    public ROIView(Context context, AttributeSet attrs) 
    {
        super(context, attrs);
        init(context);
    }
    
    /**
     * 构造函数
     *
     * @param context 上下文环境
     * @param attrs 属性集
     * @param defStyleAttr 默认样式属性
     */
    public ROIView(Context context, AttributeSet attrs, int defStyleAttr) 
    {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    /**
     * 初始化视图
     *
     * @param context 上下文环境
     */
    private void init(Context context) 
    {
        /* 获取系统触摸滑动阈值 */
        touchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
        
        /* 设置画笔样式 - 边框 */
        borderPaint.setColor(Color.RED);
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setStrokeWidth(strokeWidth);
        borderPaint.setAntiAlias(true);
        
        /* 设置画笔样式 - 控制点外圈 */
        handlePaint.setColor(Color.WHITE);
        handlePaint.setStyle(Paint.Style.FILL);
        handlePaint.setAntiAlias(true);
        
        /* 设置画笔样式 - 控制点内圈 */
        handleInnerPaint.setColor(Color.RED);
        handleInnerPaint.setStyle(Paint.Style.FILL);
        handleInnerPaint.setAntiAlias(true);
        
        /* 默认不可见 */
        setVisibility(INVISIBLE);
        
        /* 提高绘制性能 */
        setLayerType(LAYER_TYPE_HARDWARE, null);

        /* 初始化动画 */
        initAnimator();
        
        /* 注册参数监听器 */
        TouptekIspParam.addOnDataChangedListener(this);

        /* 初始化原始矩形 */
        originalRoiRect = new RectF();
    }

    /**
     * 加载当前的镜像和翻转状态
     */
    private void loadMirrorFlipState() 
    {
        int mirrorValue = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_MIRROR);
        int flipValue = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_FLIP);

        /* 如果值为-1，表示未初始化，使用默认值0 */
        isMirrorEnabled = mirrorValue == 1;
        isFlipEnabled = flipValue == 1;

        Log.d(TAG, "加载镜像/翻转状态: 镜像=" + isMirrorEnabled + ", 翻转=" + isFlipEnabled);
    }

    /**
     * 处理参数变化事件
     * <p>
     * 当TouptekIspParam参数发生变化时调用此方法，处理镜像/翻转状态变化和ROI参数变化
     * </p>
     *
     * @param param 变化的参数
     * @param newValue 新的参数值
     */
    @Override
    public void onDataChanged(TouptekIspParam param, int newValue)
    {
        if (param == TouptekIspParam.TOUPTEK_PARAM_MIRROR) 
        {
            isMirrorEnabled = newValue == 1;
            applyMirrorFlipToROI();
            Log.d(TAG, "镜像状态变化: " + isMirrorEnabled);
        }
        else if (param == TouptekIspParam.TOUPTEK_PARAM_FLIP) 
        {
            isFlipEnabled = newValue == 1;
            applyMirrorFlipToROI();
            Log.d(TAG, "翻转状态变化: " + isFlipEnabled);
        }
        /* 处理ROI参数变化 */
        else if (param == TouptekIspParam.TOUPTEK_PARAM_ROI_LEFT || 
                param == TouptekIspParam.TOUPTEK_PARAM_ROI_TOP || 
                param == TouptekIspParam.TOUPTEK_PARAM_ROI_WIDTH || 
                param == TouptekIspParam.TOUPTEK_PARAM_ROI_HEIGHT) 
        {
            /* 如果需要忽略接下来的ROI参数变化（比如我们自己触发的更新），则直接返回 */
            if (ignoreNextROIParamChanges) 
            {
                Log.d(TAG, "忽略ROI参数变化: " + param.name() + " = " + newValue);
                return;
            }
            
            /* 缓存接收到的参数 */
            switch (param) 
            {
                case TOUPTEK_PARAM_ROI_LEFT:
                    cachedRoiLeft = newValue;
                    Log.d(TAG, "接收到ROI_LEFT: " + newValue);
                    break;
                case TOUPTEK_PARAM_ROI_TOP:
                    cachedRoiTop = newValue;
                    Log.d(TAG, "接收到ROI_TOP: " + newValue);
                    break;
                case TOUPTEK_PARAM_ROI_WIDTH:
                    cachedRoiWidth = newValue;
                    Log.d(TAG, "接收到ROI_WIDTH: " + newValue);
                    break;
                case TOUPTEK_PARAM_ROI_HEIGHT:
                    cachedRoiHeight = newValue;
                    Log.d(TAG, "接收到ROI_HEIGHT: " + newValue);
                    break;
            }
            
            /* 检查是否收到了完整的ROI参数集 */
            if (cachedRoiLeft != null && cachedRoiTop != null && 
                cachedRoiWidth != null && cachedRoiHeight != null) 
            {
                /* 使用收到的参数更新ROI框 */
                updateROIFromParams(cachedRoiLeft, cachedRoiTop, cachedRoiWidth, cachedRoiHeight);
                
                /* 清除缓存 */
                clearROIParamCache();
            }
        }
    }
    
    /**
     * 根据接收到的参数更新ROI框
     * <p>
     * 将相机坐标系参数转换为视图坐标系，并更新ROI框位置
     * </p>
     *
     * @param roiLeft 相机坐标系左边界
     * @param roiTop 相机坐标系上边界
     * @param roiWidth 相机坐标系宽度
     * @param roiHeight 相机坐标系高度
     */
    private void updateROIFromParams(int roiLeft, int roiTop, int roiWidth, int roiHeight) 
    {
        if (getWidth() == 0 || getHeight() == 0 || !isROIEnabled) 
        {
            Log.d(TAG, "视图尺寸为0或ROI未启用，无法更新ROI");
            return;
        }

        Log.d(TAG, "使用参数更新ROI框: [" + roiLeft + "," + roiTop + "," +
                roiWidth + "," + roiHeight + "]");

        /* 暂时禁用参数变化通知，避免循环触发 */
        ignoreNextROIParamChanges = true;

        /* 将相机坐标转换为视图坐标，考虑镜像和翻转 */
        float viewLeft, viewTop;

        /* 考虑镜像状态计算left */
        if (isMirrorEnabled) 
        {
            /* 如果启用了镜像，需要从右向左计算 */
            viewLeft = getWidth() - (float)(roiLeft + roiWidth) / cameraWidth * getWidth();
        } 
        else 
        {
            viewLeft = (float)roiLeft / cameraWidth * getWidth();
        }

        /* 考虑翻转状态计算top */
        if (isFlipEnabled) 
        {
            /* 如果启用了翻转，需要从底向上计算 */
            viewTop = getHeight() - (float)(roiTop + roiHeight) / cameraHeight * getHeight();
        } 
        else 
        {
            viewTop = (float)roiTop / cameraHeight * getHeight();
        }

        /* 计算宽度和高度（这些不受镜像/翻转影响） */
        float viewWidth = (float)roiWidth / cameraWidth * getWidth();
        float viewHeight = (float)roiHeight / cameraHeight * getHeight();

        /* 确保矩形不超出视图边界 */
        viewLeft = Math.max(0, Math.min(viewLeft, getWidth() - MIN_RECT_SIZE));
        viewTop = Math.max(0, Math.min(viewTop, getHeight() - MIN_RECT_SIZE));
        float viewRight = Math.min(viewLeft + viewWidth, getWidth());
        float viewBottom = Math.min(viewTop + viewHeight, getHeight());

        /* 更新ROI矩形 */
        roiRect.set(viewLeft, viewTop, viewRight, viewBottom);
        animatedRect.set(roiRect);

        /* 更新原始矩形 */
        updateOriginalRect();

        /* 刷新视图 */
        post(() -> 
        {
            invalidate();
            /* 恢复参数变化通知 */
            ignoreNextROIParamChanges = false;
        });

        Log.d(TAG, "ROI框已更新: " + roiRect);
    }

    /**
     * 清除ROI参数缓存
     */
    private void clearROIParamCache() 
    {
        cachedRoiLeft = null;
        cachedRoiTop = null;
        cachedRoiWidth = null;
        cachedRoiHeight = null;
    }

    /**
     * 处理长整型参数变化事件
     *
     * @param param 变化的参数
     * @param newValue 新的参数值
     */
    @Override
    public void onLongDataChanged(TouptekIspParam param, long newValue) 
    {
        /* 长整型数据变化不需要处理 */
    }

    /**
     * 视图从窗口分离时调用，用于清理资源
     */
    @Override
    protected void onDetachedFromWindow() 
    {
        super.onDetachedFromWindow();

        /* 取消注册监听器，避免内存泄漏 */
        TouptekIspParam.removeOnDataChangedListener(this);
    }

    /**
     * 应用镜像和翻转效果到ROI框
     * <p>
     * 根据当前的镜像和翻转状态，更新ROI框的位置
     * </p>
     */
    private void applyMirrorFlipToROI() 
    {
        if (!isROIEnabled || getWidth() == 0 || getHeight() == 0) 
        {
            return; /* 如果ROI未启用或视图尺寸为0，则不处理 */
        }

        /* 如果原始矩形未初始化或为空，先保存当前矩形为原始矩形 */
        if (originalRoiRect.width() == 0 || originalRoiRect.height() == 0) 
        {
            originalRoiRect.set(roiRect);
            Log.d(TAG, "初始化原始矩形: " + originalRoiRect);
        }

        /* 从原始矩形开始应用变换 */
        RectF newRect = new RectF(originalRoiRect);

        /* 应用水平镜像（左右翻转） */
        if (isMirrorEnabled) 
        {
            float tempLeft = getWidth() - originalRoiRect.right;
            float tempRight = getWidth() - originalRoiRect.left;
            newRect.left = tempLeft;
            newRect.right = tempRight;
        }

        /* 应用垂直翻转（上下翻转） */
        if (isFlipEnabled) 
        {
            float tempTop = getHeight() - originalRoiRect.bottom;
            float tempBottom = getHeight() - originalRoiRect.top;
            newRect.top = tempTop;
            newRect.bottom = tempBottom;
        }

        /* 更新ROI矩形 */
        roiRect.set(newRect);
        animatedRect.set(newRect);

        /* 通知ROI变化 */
        notifyROIChanged();

        /* 刷新视图 */
        invalidate();

        Log.d(TAG, "应用镜像/翻转效果 - 原始: " + originalRoiRect + " -> 新: " + roiRect +
                " (镜像:" + isMirrorEnabled + ", 翻转:" + isFlipEnabled + ")");
    }
    
    /**
     * 初始化矩形动画器
     */
    private void initAnimator() 
    {
        rectAnimator = ValueAnimator.ofFloat(0f, 1f);
        rectAnimator.setDuration(ANIMATION_DURATION);
        rectAnimator.setInterpolator(new DecelerateInterpolator());
        
        rectAnimator.addUpdateListener(animation -> 
        {
            float fraction = animation.getAnimatedFraction();
            
            /* 插值计算当前矩形 */
            animatedRect.left = roiRect.left;
            animatedRect.top = roiRect.top;
            animatedRect.right = roiRect.right;
            animatedRect.bottom = roiRect.bottom;
            
            /* 使用postInvalidateOnAnimation更新视图 */
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) 
            {
                postInvalidateOnAnimation();
            } 
            else 
            {
                invalidate();
            }
        });
    }

    /**
     * 更新原始矩形位置
     * <p>
     * 根据当前的镜像和翻转状态，计算并更新对应的原始矩形
     * </p>
     */
    private void updateOriginalRect() 
    {
        /* 根据当前的镜像和翻转状态，计算对应的原始矩形 */
        RectF tempRect = new RectF(roiRect);

        /* 如果当前启用了镜像，反向应用镜像变换 */
        if (isMirrorEnabled) 
        {
            float tempLeft = getWidth() - tempRect.right;
            float tempRight = getWidth() - tempRect.left;
            tempRect.left = tempLeft;
            tempRect.right = tempRight;
        }

        /* 如果当前启用了翻转，反向应用翻转变换 */
        if (isFlipEnabled) 
        {
            float tempTop = getHeight() - tempRect.bottom;
            float tempBottom = getHeight() - tempRect.top;
            tempRect.top = tempTop;
            tempRect.bottom = tempBottom;
        }

        /* 更新原始矩形 */
        originalRoiRect.set(tempRect);
        Log.d(TAG, "更新原始矩形: " + originalRoiRect);
    }

    /**
     * 设置相机实际分辨率
     *
     * @param width 相机宽度(像素)
     * @param height 相机高度(像素)
     */
    public void setCameraResolution(int width, int height) 
    {
        this.cameraWidth = width;
        this.cameraHeight = height;
    }
    
    /**
     * 启用或禁用ROI模式
     *
     * @param enabled true表示启用ROI框，false表示禁用
     */
    public void setROIEnabled(boolean enabled) 
    {
        if (isROIEnabled == enabled) return;

        isROIEnabled = enabled;
        setVisibility(enabled ? VISIBLE : INVISIBLE);

        if (enabled)
        {
            /* 首先加载镜像和翻转状态 */
            loadMirrorFlipState();
            
            /* 然后尝试从ISP参数加载ROI设置 */
            boolean paramLoaded = loadROIParamsFromIsp();
            
            /* 设置白平衡模式为ROI模式(2) */
            if(TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE) != 2) 
            {
                Log.d(TAG, "setROIEnabled: set TOUPTEK_PARAM_WBCHOICE ROI Mode");
                TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 2);
            }
            
            /* 如果没有加载到有效参数，才初始化到中心位置 */
            if (!paramLoaded) 
            {
                initializeROIRect();
            }

            /* 播放显示动画 */
            animateRectAppear();
        } 
        else 
        {
            /* 播放消失动画 */
            animateRectDisappear();
        }
    }
    
    /**
     * 播放矩形出现动画
     * <p>
     * 从中心点扩展到目标大小
     * </p>
     */
    private void animateRectAppear() 
    {
        /* 保存目标矩形 */
        final RectF targetRect = new RectF(roiRect);
        
        /* 设置初始矩形为中心点 */
        float centerX = (targetRect.left + targetRect.right) / 2;
        float centerY = (targetRect.top + targetRect.bottom) / 2;
        animatedRect.set(centerX, centerY, centerX, centerY);
        
        /* 创建并启动动画 */
        ValueAnimator animator = ValueAnimator.ofFloat(0f, 1f);
        animator.setDuration(ANIMATION_DURATION);
        animator.setInterpolator(new DecelerateInterpolator());
        
        animator.addUpdateListener(animation -> 
        {
            float fraction = animation.getAnimatedFraction();
            
            /* 插值计算当前矩形 */
            animatedRect.left = centerX + (targetRect.left - centerX) * fraction;
            animatedRect.top = centerY + (targetRect.top - centerY) * fraction;
            animatedRect.right = centerX + (targetRect.right - centerX) * fraction;
            animatedRect.bottom = centerY + (targetRect.bottom - centerY) * fraction;
            
            /* 使用postInvalidateOnAnimation更新视图 */
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) 
            {
                postInvalidateOnAnimation();
            } 
            else 
            {
                invalidate();
            }
        });
        
        animator.start();
        
        /* 动画结束后通知参数变化 */
        animator.addListener(new android.animation.AnimatorListenerAdapter() 
        {
            @Override
            public void onAnimationEnd(android.animation.Animator animation) 
            {
                notifyROIChanged();
            }
        });
    }
    
    /**
     * 播放矩形消失动画
     * <p>
     * 从当前大小收缩到中心点
     * </p>
     */
    private void animateRectDisappear() 
    {
        /* 保存初始矩形 */
        final RectF initialRect = new RectF(roiRect);
        
        /* 目标是矩形中心点 */
        float centerX = (initialRect.left + initialRect.right) / 2;
        float centerY = (initialRect.top + initialRect.bottom) / 2;
        
        /* 创建并启动动画 */
        ValueAnimator animator = ValueAnimator.ofFloat(0f, 1f);
        animator.setDuration(ANIMATION_DURATION);
        animator.setInterpolator(new DecelerateInterpolator());
        
        animator.addUpdateListener(animation -> 
        {
            float fraction = animation.getAnimatedFraction();
            
            /* 插值计算当前矩形 */
            animatedRect.left = initialRect.left + (centerX - initialRect.left) * fraction;
            animatedRect.top = initialRect.top + (centerY - initialRect.top) * fraction;
            animatedRect.right = initialRect.right + (centerX - initialRect.right) * fraction;
            animatedRect.bottom = initialRect.bottom + (centerY - initialRect.bottom) * fraction;
            
            /* 使用postInvalidateOnAnimation更新视图 */
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) 
            {
                postInvalidateOnAnimation();
            } 
            else 
            {
                invalidate();
            }
        });
        
        animator.start();
    }
    
    /**
     * 判断ROI模式是否启用
     *
     * @return true表示ROI框当前可见，false表示不可见
     */
    public boolean isROIEnabled() 
    {
        return isROIEnabled;
    }
    
    /**
     * 初始化ROI矩形到中心位置
     * <p>
     * 在视图中心创建一个固定大小的ROI框
     * </p>
     */
    private void initializeROIRect() 
    {
        /* 如果ROI已经有合理的尺寸，不重新初始化 */
        if (roiRect.width() > MIN_RECT_SIZE && roiRect.height() > MIN_RECT_SIZE) 
        {
            Log.d(TAG, "保留现有ROI设置: " + roiRect);
            return;
        }
        
        int viewWidth = getWidth();
        int viewHeight = getHeight();
        
        /* 使用固定的200x200尺寸，不再使用比例 */
        int rectWidth = 200;
        int rectHeight = 200;
        
        /* 计算左上角坐标，使矩形位于视图中心 */
        int left = (viewWidth - rectWidth) / 2;
        int top = (viewHeight - rectHeight) / 2;

        /* 设置ROI矩形 */
        roiRect.set(left, top, left + rectWidth, top + rectHeight);
        animatedRect.set(roiRect);

        /* 初始化原始矩形 - 当没有翻转/镜像时，原始矩形与显示矩形相同 */
        originalRoiRect.set(roiRect);
    }
    
    /**
     * 视图大小变化时调用
     *
     * @param w 新宽度
     * @param h 新高度
     * @param oldw 旧宽度
     * @param oldh 旧高度
     */
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) 
    {
        super.onSizeChanged(w, h, oldw, oldh);
        
        /* 当视图大小变化时初始化ROI矩形 */
        if (isROIEnabled) 
        {
            initializeROIRect();
            notifyROIChanged();
        }
    }
    
    /**
     * 绘制视图
     *
     * @param canvas 画布
     */
    @Override
    protected void onDraw(Canvas canvas) 
    {
        super.onDraw(canvas);
        
        if (!isROIEnabled) return;
        
        // 保存当前画布状态
        canvas.save();
        
        // 应用变换矩阵到画布，使ROI框与图像同步变换
        if (!currentTransformMatrix.isIdentity()) {
            canvas.concat(currentTransformMatrix);
        }
        
        /* 使用实际矩形绘制，而不是动画矩形 */
        RectF drawRect = roiRect;
        
        /* 只绘制边框，不绘制半透明填充 */
        canvas.drawRect(drawRect, borderPaint);
        
        /* 绘制四个角落的控制点 */
        drawControlHandles(canvas, drawRect);
        
        /* 调试模式：显示坐标信息 */
        if (debugMode) 
        {
            Paint debugTextPaint = new Paint();
            debugTextPaint.setColor(Color.WHITE);
            debugTextPaint.setTextSize(30);
            debugTextPaint.setAntiAlias(true);
            debugTextPaint.setShadowLayer(3, 1, 1, Color.BLACK);
            
            String info = String.format("View: %.0f,%.0f,%.0fx%.0f", 
                drawRect.left, drawRect.top, drawRect.width(), drawRect.height());
            canvas.drawText(info, 50, 50, debugTextPaint);
            
            /* 计算相机坐标 */
            int cameraLeft = (int)((float)drawRect.left / getWidth() * cameraWidth);
            int cameraTop = (int)((float)drawRect.top / getHeight() * cameraHeight);
            int camWidth = (int)((float)drawRect.width() / getWidth() * cameraWidth);
            int camHeight = (int)((float)drawRect.height() / getHeight() * cameraHeight);
            
            String camInfo = String.format("Cam: %d,%d,%dx%d", 
                cameraLeft, cameraTop, camWidth, camHeight);
            canvas.drawText(camInfo, 50, 90, debugTextPaint);
        }
        
        // 恢复画布状态
        canvas.restore();
    }
    
    /**
     * 处理触摸事件
     *
     * @param event 触摸事件
     * @return true表示事件已处理，false表示未处理
     */
    @Override
    public boolean onTouchEvent(MotionEvent event)
    {
        if (!isROIEnabled) 
        {
            return false;
        }
        
        // 获取原始触摸点坐标
        float originalX = event.getX();
        float originalY = event.getY();
        
        // 转换触摸点坐标以适应变换
        float[] mappedPoint = mapPoint(originalX, originalY);
        float x = mappedPoint[0];
        float y = mappedPoint[1];
        
        switch (event.getAction()) 
        {
            case MotionEvent.ACTION_DOWN:
                /* 在按下时启用硬件加速图层，提高动画流畅度 */
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) 
                {
                    setLayerType(LAYER_TYPE_HARDWARE, null);
                }
                
                /* 检查是否触摸到控制点 */
                activeHandle = getHandleAtPoint(x, y);
                
                if (activeHandle != ControlHandle.NONE) 
                {
                    /* 触摸到控制点，准备调整大小 */
                    isResizing = true;
                    isDragging = false;
                    lastTouchX = x;
                    lastTouchY = y;
                    Log.d(TAG, "开始调整大小 - 控制点: " + activeHandle);
                    return true;
                }
                /* 检查是否触摸到矩形内部用于移动 */
                else if (containsPoint(roiRect, x, y)) 
                {
                    isDragging = true;
                    isResizing = false;
                    lastTouchX = x;
                    lastTouchY = y;
                    Log.d(TAG, "开始拖动 - 起始位置: " + x + "," + y);
                    return true;
                }
                break;
                
            case MotionEvent.ACTION_MOVE:
                if (isResizing && activeHandle != ControlHandle.NONE) 
                {
                    /* 根据控制点调整矩形大小 */
                    resizeRectByHandle(x, y);
                    
                    /* 更新上次触摸位置 */
                    lastTouchX = x;
                    lastTouchY = y;
                    
                    /* 立即通知ROI变化，提高响应速度 */
                    notifyROIChanged();
                    
                    /* 立即刷新视图，不要延迟 */
                    invalidate();
                    
                    return true;
                }
                else if (isDragging) 
                {
                    /* 计算移动距离，不要使用阈值过滤，保持平滑移动 */
                    float deltaX = x - lastTouchX;
                    float deltaY = y - lastTouchY;
                    
                    /* 更新矩形位置 */
                    moveRect(deltaX, deltaY);
                    
                    /* 更新上次触摸位置 */
                    lastTouchX = x;
                    lastTouchY = y;
                    
                    /* 立即通知ROI变化，提高响应速度 */
                    notifyROIChanged();
                    
                    /* 立即刷新视图，不要延迟 */
                    invalidate();
                    
                    return true;
                }
                break;
                
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                /* 恢复视图类型 */
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) 
                {
                    setLayerType(LAYER_TYPE_NONE, null);
                }
                
                /* 结束操作时通知ROI变化 */
                if (isDragging || isResizing) 
                {
                    notifyROIChanged();
                    Log.d(TAG, "结束拖动/调整大小 - 最终位置: " + roiRect);
                }
                
                /* 重置状态 */
                isDragging = false;
                isResizing = false;
                activeHandle = ControlHandle.NONE;
                break;
        }
        
        return super.onTouchEvent(event);
    }
    
    /**
     * 获取触摸点处的控制点
     *
     * @param x 触摸点X坐标
     * @param y 触摸点Y坐标
     * @return 控制点枚举值，如果没有控制点则返回NONE
     */
    private ControlHandle getHandleAtPoint(float x, float y) 
    {
        /* 左上角 */
        if (isPointNearHandle(x, y, roiRect.left, roiRect.top)) 
        {
            return ControlHandle.TOP_LEFT;
        }
        
        /* 右上角 */
        if (isPointNearHandle(x, y, roiRect.right, roiRect.top)) 
        {
            return ControlHandle.TOP_RIGHT;
        }
        
        /* 左下角 */
        if (isPointNearHandle(x, y, roiRect.left, roiRect.bottom)) 
        {
            return ControlHandle.BOTTOM_LEFT;
        }
        
        /* 右下角 */
        if (isPointNearHandle(x, y, roiRect.right, roiRect.bottom)) 
        {
            return ControlHandle.BOTTOM_RIGHT;
        }
        
        return ControlHandle.NONE;
    }
    
    /**
     * 判断点是否在控制点附近
     *
     * @param x 点的X坐标
     * @param y 点的Y坐标
     * @param handleX 控制点X坐标
     * @param handleY 控制点Y坐标
     * @return 是否在控制点附近
     */
    private boolean isPointNearHandle(float x, float y, float handleX, float handleY) 
    {
        /* 计算点到控制点的距离 */
        float dx = x - handleX;
        float dy = y - handleY;
        float distance = (float) Math.sqrt(dx * dx + dy * dy);
        
        /* 如果距离小于控制点触摸半径，则认为点在控制点附近 */
        return distance <= HANDLE_TOUCH_RADIUS;
    }
    
    /**
     * 根据控制点调整矩形大小
     *
     * @param x 当前触摸点X坐标
     * @param y 当前触摸点Y坐标
     */
    private void resizeRectByHandle(float x, float y) 
    {
        RectF newRect = new RectF(roiRect);
        
        switch (activeHandle) 
        {
            case TOP_LEFT:
                newRect.left = Math.min(x, newRect.right - MIN_RECT_SIZE);
                newRect.top = Math.min(y, newRect.bottom - MIN_RECT_SIZE);
                break;
                
            case TOP_RIGHT:
                newRect.right = Math.max(x, newRect.left + MIN_RECT_SIZE);
                newRect.top = Math.min(y, newRect.bottom - MIN_RECT_SIZE);
                break;
                
            case BOTTOM_LEFT:
                newRect.left = Math.min(x, newRect.right - MIN_RECT_SIZE);
                newRect.bottom = Math.max(y, newRect.top + MIN_RECT_SIZE);
                break;
                
            case BOTTOM_RIGHT:
                newRect.right = Math.max(x, newRect.left + MIN_RECT_SIZE);
                newRect.bottom = Math.max(y, newRect.top + MIN_RECT_SIZE);
                break;
                
            default:
                return;
        }
        
        /* 确保矩形不超出视图边界 */
        if (newRect.left < 0) newRect.left = 0;
        if (newRect.top < 0) newRect.top = 0;
        if (newRect.right > getWidth()) newRect.right = getWidth();
        if (newRect.bottom > getHeight()) newRect.bottom = getHeight();

        /* 更新矩形 */
        roiRect.set(newRect);

        /* 也更新动画矩形，确保动画从当前位置开始 */
        animatedRect.set(roiRect);

        /* 更新原始矩形 */
        updateOriginalRect();

        Log.d(TAG, "调整大小 - 新矩形: " + roiRect);
    }
    
    /**
     * 检查点是否在矩形内
     *
     * @param rect 矩形
     * @param x 点的X坐标
     * @param y 点的Y坐标
     * @return 点是否在矩形内
     */
    private boolean containsPoint(RectF rect, float x, float y) 
    {
        return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
    }
    
    /**
     * 移动矩形，保证不超出视图边界
     *
     * @param deltaX X轴移动距离
     * @param deltaY Y轴移动距离
     */
    private void moveRect(float deltaX, float deltaY) 
    {
        /* 如果移动量太小，则忽略以避免抖动 */
        if (Math.abs(deltaX) < 0.5f && Math.abs(deltaY) < 0.5f) 
        {
            return;
        }
        
        /* 提高移动精度，减小移动阈值 */
        float newLeft = roiRect.left + deltaX;
        float newTop = roiRect.top + deltaY;
        float newRight = roiRect.right + deltaX;
        float newBottom = roiRect.bottom + deltaY;
        
        /* 检查边界 */
        if (newLeft < 0) 
        {
            /* 左边界超出，调整位置 */
            newRight = newRight - newLeft; /* 保持宽度不变 */
            newLeft = 0;
        }
        
        if (newTop < 0) 
        {
            /* 上边界超出，调整位置 */
            newBottom = newBottom - newTop; /* 保持高度不变 */
            newTop = 0;
        }
        
        if (newRight > getWidth()) 
        {
            /* 右边界超出，调整位置 */
            newLeft = newLeft - (newRight - getWidth()); /* 保持宽度不变 */
            newRight = getWidth();
        }
        
        if (newBottom > getHeight()) 
        {
            /* 下边界超出，调整位置 */
            newTop = newTop - (newBottom - getHeight()); /* 保持高度不变 */
            newBottom = getHeight();
        }

        /* 更新矩形 */
        roiRect.set(newLeft, newTop, newRight, newBottom);

        /* 也更新动画矩形，确保动画从当前位置开始 */
        animatedRect.set(roiRect);

        /* 更新原始矩形 */
        updateOriginalRect();

        Log.d(TAG, "移动矩形: " + deltaX + "," + deltaY + " -> " + roiRect);
    }

    /**
     * 通知ROI参数变化，考虑镜像和翻转状态
     * <p>
     * 将当前视图坐标系下的ROI框转换为相机坐标系，并发送到设备
     * </p>
     */
    private void notifyROIChanged() 
    {
        /* 如果设置了忽略参数变化，则直接返回 */
        if (ignoreNextROIParamChanges) 
        {
            return;
        }

        /* 暂时禁用参数变化通知 */
        ignoreNextROIParamChanges = true;

        /* 首先将当前UI上的ROI矩形转换为相机坐标 */
        float viewLeft = roiRect.left;
        float viewTop = roiRect.top;
        float viewWidth = roiRect.width();
        float viewHeight = roiRect.height();

        /* 如果启用了镜像，需要反向计算左侧位置 */
        int cameraLeft;
        if (isMirrorEnabled) 
        {
            /* 在镜像模式下，需要从右向左计算位置 */
            cameraLeft = (int)((float)(getWidth() - viewLeft - viewWidth) / getWidth() * cameraWidth);
        } 
        else 
        {
            cameraLeft = (int)((float)viewLeft / getWidth() * cameraWidth);
        }

        /* 如果启用了翻转，需要反向计算顶部位置 */
        int cameraTop;
        if (isFlipEnabled) 
        {
            /* 在翻转模式下，需要从底部向上计算位置 */
            cameraTop = (int)((float)(getHeight() - viewTop - viewHeight) / getHeight() * cameraHeight);
        } 
        else 
        {
            cameraTop = (int)((float)viewTop / getHeight() * cameraHeight);
        }

        /* 宽度和高度的计算不受镜像/翻转影响 */
        int cameraWidth = (int)((float)viewWidth / getWidth() * this.cameraWidth);
        int cameraHeight = (int)((float)viewHeight / getHeight() * this.cameraHeight);

        /* 发送转换后的参数到设备 */
        TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_ROI_LEFT, cameraLeft);
        TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_ROI_TOP, cameraTop);
        TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_ROI_WIDTH, cameraWidth);
        TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_ROI_HEIGHT, cameraHeight);

        /* 发送参数后延迟恢复通知 */
        post(() -> 
        {
            ignoreNextROIParamChanges = false;
        });

        Log.d(TAG, "ROI Changed - View: " + roiRect.toString() +
                " Camera: [" + cameraLeft + "," + cameraTop + "," +
                cameraWidth + "," + cameraHeight + "]" +
                " (镜像:" + isMirrorEnabled + ", 翻转:" + isFlipEnabled + ")");
    }
    
    /**
     * 绘制四个角落的控制点
     *
     * @param canvas 画布
     * @param rect 要绘制控制点的矩形
     */
    private void drawControlHandles(Canvas canvas, RectF rect) 
    {
        /* 绘制控制点（双圈设计：白色外圈+红色内圈） */
        drawHandleCircles(canvas, rect.left, rect.top);     /* 左上角 */
        drawHandleCircles(canvas, rect.right, rect.top);    /* 右上角 */
        drawHandleCircles(canvas, rect.left, rect.bottom);  /* 左下角 */
        drawHandleCircles(canvas, rect.right, rect.bottom); /* 右下角 */
    }
    
    /**
     * 绘制单个控制点的双圈
     *
     * @param canvas 画布
     * @param x 控制点X坐标
     * @param y 控制点Y坐标
     */
    private void drawHandleCircles(Canvas canvas, float x, float y) 
    {
        /* 绘制白色外圈 */
        canvas.drawCircle(x, y, HANDLE_RADIUS, handlePaint);
        /* 绘制红色内圈 */
        canvas.drawCircle(x, y, HANDLE_INNER_RADIUS, handleInnerPaint);
    }

    /**
     * 根据相机坐标系参数设置ROI框的位置和大小
     * 
     * @param cameraLeft 左边界坐标(相机坐标系)
     * @param cameraTop 上边界坐标(相机坐标系)
     * @param cameraWidth 宽度(相机坐标系)
     * @param cameraHeight 高度(相机坐标系)
     */
    private void setROIParams(int cameraLeft, int cameraTop, int cameraWidth, int cameraHeight)
    {
        if (getWidth() == 0 || getHeight() == 0) 
        {
            Log.w(TAG, "视图尺寸为0，无法设置ROI参数");
            return;
        }
        
        /* 将相机坐标转换为视图坐标，考虑镜像和翻转 */
        float viewLeft, viewTop;
    
        /* 考虑镜像状态计算left */
        if (isMirrorEnabled) 
        {
            /* 如果启用了镜像，需要从右向左计算 */
            viewLeft = getWidth() - (float)(cameraLeft + cameraWidth) / this.cameraWidth * getWidth();
        } 
        else 
        {
            viewLeft = (float)cameraLeft / this.cameraWidth * getWidth();
        }
    
        /* 考虑翻转状态计算top */
        if (isFlipEnabled) 
        {
            /* 如果启用了翻转，需要从底向上计算 */
            viewTop = getHeight() - (float)(cameraTop + cameraHeight) / this.cameraHeight * getHeight();
        } 
        else 
        {
            viewTop = (float)cameraTop / this.cameraHeight * getHeight();
        }
    
        /* 计算宽度和高度（这些不受镜像/翻转影响） */
        float viewWidth = (float)cameraWidth / this.cameraWidth * getWidth();
        float viewHeight = (float)cameraHeight / this.cameraHeight * getHeight();
        
        float viewRight = viewLeft + viewWidth;
        float viewBottom = viewTop + viewHeight;
    
        /* 确保矩形不超出视图边界 */
        viewLeft = Math.max(0, Math.min(viewLeft, getWidth() - MIN_RECT_SIZE));
        viewTop = Math.max(0, Math.min(viewTop, getHeight() - MIN_RECT_SIZE));
        viewRight = Math.max(viewLeft + MIN_RECT_SIZE, Math.min(viewRight, getWidth()));
        viewBottom = Math.max(viewTop + MIN_RECT_SIZE, Math.min(viewBottom, getHeight()));
    
        /* 更新ROI矩形 */
        roiRect.set(viewLeft, viewTop, viewRight, viewBottom);
        animatedRect.set(roiRect);
    
        /* 更新原始矩形 */
        updateOriginalRect();
    
        Log.d(TAG, "设置ROI参数 - 相机坐标: [" + cameraLeft + "," + cameraTop + "," +
                cameraWidth + "," + cameraHeight + "], 视图坐标: " + roiRect);
    
        /* 刷新视图 */
        invalidate();
    }

    /**
     * 从ISP参数中加载ROI设置
     * <p>
     * 如果参数存在且有效，则应用这些参数
     * </p>
     *
     * @return 是否成功加载参数
     */
    private boolean loadROIParamsFromIsp()
    {
        /* 从TouptekIspParam获取保存的ROI参数 */
        TouptekIspParam.ParamData roiLeftData = TouptekIspParam.getParamData(TouptekIspParam.TOUPTEK_PARAM_ROI_LEFT);
        TouptekIspParam.ParamData roiTopData = TouptekIspParam.getParamData(TouptekIspParam.TOUPTEK_PARAM_ROI_TOP);
        TouptekIspParam.ParamData roiWidthData = TouptekIspParam.getParamData(TouptekIspParam.TOUPTEK_PARAM_ROI_WIDTH);
        TouptekIspParam.ParamData roiHeightData = TouptekIspParam.getParamData(TouptekIspParam.TOUPTEK_PARAM_ROI_HEIGHT);
        
        /* 如果参数有效，设置ROI参数 */
        if (roiLeftData != null && roiTopData != null && 
            roiWidthData != null && roiHeightData != null) 
        {
            int left = roiLeftData.current;
            int top = roiTopData.current;
            int width = roiWidthData.current;
            int height = roiHeightData.current;
            
            /* 确保参数有效 */
            if (width > 0 && height > 0) 
            {
                Log.d(TAG, "从TouptekIspParam加载ROI参数: " + left + ", " + top + ", " + width + ", " + height);
                
                /* 直接设置参数，不使用post延迟 */
                setROIParams(left, top, width, height);
                return true;
            }
        }
        
        Log.d(TAG, "未找到有效的ROI参数");
        return false;
    }

    /**
     * 应用变换矩阵到ROI视图
     * 
     * @param matrix 要应用的变换矩阵
     */
    public void applyTransform(Matrix matrix) {
        if (matrix == null) {
            Log.e(TAG, "Cannot apply null transform matrix");
            return;
        }
        
        // 保存当前的变换矩阵
        currentTransformMatrix.set(matrix);
        
        // 强制重绘视图
        invalidate();
        
        Log.d(TAG, "Applied transform matrix to ROIView");
    }
    
    /**
     * 将视图坐标转换为变换后的坐标
     * 
     * @param x 原始X坐标
     * @param y 原始Y坐标
     * @return 包含变换后坐标的浮点数组[x, y]
     */
    private float[] mapPoint(float x, float y) {
        float[] point = new float[]{x, y};
        
        // 如果矩阵不是单位矩阵，则应用变换
        if (!currentTransformMatrix.isIdentity()) {
            Matrix inverseMatrix = new Matrix();
            if (currentTransformMatrix.invert(inverseMatrix)) {
                inverseMatrix.mapPoints(point);
            }
        }
        
        return point;
    }
    
    /**
     * 将变换后的坐标转换回原始视图坐标
     * 
     * @param x 变换后的X坐标
     * @param y 变换后的Y坐标
     * @return 包含原始视图坐标的浮点数组[x, y]
     */
    private float[] unmapPoint(float x, float y) {
        float[] point = new float[]{x, y};
        
        // 如果矩阵不是单位矩阵，则应用变换
        if (!currentTransformMatrix.isIdentity()) {
            currentTransformMatrix.mapPoints(point);
        }
        
        return point;
    }
}