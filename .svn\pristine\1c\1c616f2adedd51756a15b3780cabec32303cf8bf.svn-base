<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 18 10:12:53 CST 2025 -->
<title>M - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-18">
<meta name="description" content="index: M">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/MediaAdapter.html" class="type-name-link" title="com.android.rockchip.camera2中的类">MediaAdapter</a> - <a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a>中的类</dt>
<dd>
<div class="block">MediaAdapter 类用于在 RecyclerView 中显示媒体文件的缩略图和名称。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/MediaAdapter.html#%3Cinit%3E(java.util.List,com.android.rockchip.camera2.MediaAdapter.OnMediaClickListener)" class="member-name-link">MediaAdapter(List&lt;File&gt;, MediaAdapter.OnMediaClickListener)</a> - 类的构造器 com.android.rockchip.camera2.<a href="../com/android/rockchip/camera2/MediaAdapter.html" title="com.android.rockchip.camera2中的类">MediaAdapter</a></dt>
<dd>
<div class="block">构造函数。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/MediaAdapter.OnMediaClickListener.html" class="type-name-link" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a> - <a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a>中的接口</dt>
<dd>
<div class="block">定义媒体点击监听器接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/MediaBrowserActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">MediaBrowserActivity</a> - <a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a>中的类</dt>
<dd>
<div class="block">MediaBrowserActivity 类用于显示媒体文件的网格视图，
 并允许用户点击查看视频或图片。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/MediaBrowserActivity.html#%3Cinit%3E()" class="member-name-link">MediaBrowserActivity()</a> - 类的构造器 com.android.rockchip.camera2.<a href="../com/android/rockchip/camera2/MediaBrowserActivity.html" title="com.android.rockchip.camera2中的类">MediaBrowserActivity</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a></main>
</div>
</div>
</body>
</html>
