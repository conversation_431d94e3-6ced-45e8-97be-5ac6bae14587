package com.touptek.ui.internal;

import android.graphics.Matrix;
import android.graphics.RectF;
import android.util.Log;
import android.view.TextureView;
import android.widget.ImageView;

import com.touptek.ui.TpRoiView;

/**
 * TpViewTransform 类提供与视图变换相关的工具方法。
 * <p>
 * 包括对 TextureView 和 ImageView 的缩放和平移操作。
 */
public class TpViewTransform {
    private static final String TAG = "TpViewTransform";
    
    // 存储当前的变换矩阵，用于同步ROIView
    private static Matrix currentTransformMatrix = new Matrix();
    
    /**
     * 应用缩放变换，限制最小缩放比例为原始比例。
     * 同时将相同的变换应用到ROIView。
     *
     * @param textureView 目标 TextureView。
     * @param tpRoiView     ROI视图，如果为null则只变换TextureView。
     * @param scaleFactor 缩放比例。
     * @param focusX      手势中心点的 X 坐标。
     * @param focusY      手势中心点的 Y 坐标。
     */
    public static void applyZoom(TextureView textureView, TpRoiView tpRoiView, float scaleFactor, float focusX, float focusY) {
        if (textureView == null || scaleFactor <= 0) {
            Log.e(TAG, "Invalid parameters for applyZoom");
            return;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);
        
        // 保存原始矩阵以便在需要时恢复
        Matrix originalMatrix = new Matrix(matrix);
        
        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算当前的缩放比例
        float currentScaleX = transformedRect.width() / viewRect.width();
        float currentScaleY = transformedRect.height() / viewRect.height();
        float currentScale = Math.min(currentScaleX, currentScaleY);

        // 限制最小缩放比例为 1.0（原始比例）
        float newScale = currentScale * scaleFactor;
        if (newScale < 1.0f) {
            scaleFactor = 1.0f / currentScale;
        }

        // 以手势中心点为缩放中心进行缩放
        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
        
        // 检查缩放后的矩形是否超出边界
        RectF afterScaleRect = new RectF();
        matrix.mapRect(afterScaleRect, viewRect);
        
        // 检查并修正边界问题
        boolean needsCorrection = false;
        float translateX = 0, translateY = 0;
        
        // 检查左右边界
        if (afterScaleRect.right < viewRect.right) {
            translateX = viewRect.right - afterScaleRect.right;
            needsCorrection = true;
        } else if (afterScaleRect.left > viewRect.left) {
            translateX = viewRect.left - afterScaleRect.left;
            needsCorrection = true;
        }
        
        // 检查上下边界
        if (afterScaleRect.bottom < viewRect.bottom) {
            translateY = viewRect.bottom - afterScaleRect.bottom;
            needsCorrection = true;
        } else if (afterScaleRect.top > viewRect.top) {
            translateY = viewRect.top - afterScaleRect.top;
            needsCorrection = true;
        }
        
        // 如果需要修正，立即应用平移修正
        if (needsCorrection) {
            matrix.postTranslate(translateX, translateY);
            Log.d(TAG, "应用边界修正: X=" + translateX + ", Y=" + translateY);
        }
        
        // 保存当前矩阵
        currentTransformMatrix.set(matrix);

        // 应用变换到 TextureView
        textureView.setTransform(matrix);
        
        // 将同样的变换应用到ROIView
        if (tpRoiView != null) {
            tpRoiView.applyTransform(matrix);
        }

        // 强制刷新界面
        textureView.invalidate();

        Log.d(TAG, "Zoom applied with scaleFactor: " + scaleFactor + ", focusX: " + focusX + ", focusY: " + focusY);
    }

    /**
     * 应用平移变换，限制平移范围不超出 TextureView 的边界。
     * 同时将相同的变换应用到ROIView。
     *
     * @param textureView 目标 TextureView。
     * @param tpRoiView     ROI视图，如果为null则只变换TextureView。
     * @param deltaX      平移的 X 轴距离。
     * @param deltaY      平移的 Y 轴距离。
     */
    public static void applyPan(TextureView textureView, TpRoiView tpRoiView, float deltaX, float deltaY) {
        if (textureView == null) {
            Log.e(TAG, "Invalid parameters for applyPan");
            return;
        }

        // 忽略过小的移动
        if (Math.abs(deltaX) < 1.0f && Math.abs(deltaY) < 1.0f) {
            return;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);

        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算限制后的平移距离
        float limitedDeltaX = deltaX;
        float limitedDeltaY = deltaY;

        if (transformedRect.left + deltaX > 0) {
            limitedDeltaX = -transformedRect.left;
        } else if (transformedRect.right + deltaX < textureView.getWidth()) {
            limitedDeltaX = textureView.getWidth() - transformedRect.right;
        }

        if (transformedRect.top + deltaY > 0) {
            limitedDeltaY = -transformedRect.top;
        } else if (transformedRect.bottom + deltaY < textureView.getHeight()) {
            limitedDeltaY = textureView.getHeight() - transformedRect.bottom;
        }

        // 应用平移变换
        matrix.postTranslate(limitedDeltaX, limitedDeltaY);
        
        // 保存当前矩阵
        currentTransformMatrix.set(matrix);

        // 应用变换到 TextureView
        textureView.setTransform(matrix);
        
        // 将同样的变换应用到ROIView
        if (tpRoiView != null) {
            tpRoiView.applyTransform(matrix);
        }

        // 强制刷新界面
        textureView.invalidate();

        Log.d(TAG, "Pan applied with limitedDeltaX: " + limitedDeltaX + ", limitedDeltaY: " + limitedDeltaY);
    }

    /**
     * 获取当前的变换矩阵
     * 
     * @return 当前的变换矩阵
     */
    public static Matrix getCurrentTransformMatrix() {
        return new Matrix(currentTransformMatrix);
    }

    /**
     * 重置变换矩阵为单位矩阵
     * 
     * @param textureView 目标 TextureView
     * @param tpRoiView ROI视图，如果为null则只重置TextureView
     */
    public static void resetTransform(TextureView textureView, TpRoiView tpRoiView) {
        if (textureView == null) {
            return;
        }
        
        Matrix identity = new Matrix();
        textureView.setTransform(identity);
        currentTransformMatrix.reset();
        
        if (tpRoiView != null) {
            tpRoiView.applyTransform(identity);
        }
        
        textureView.invalidate();
    }

    /**
     * 应用缩放变换，限制最小缩放比例为原始比例。
     *
     * @param textureView 目标 TextureView。
     * @param scaleFactor 缩放比例。
     * @param focusX      手势中心点的 X 坐标。
     * @param focusY      手势中心点的 Y 坐标。
     */
    public static void applyZoom(TextureView textureView, float scaleFactor, float focusX, float focusY) {
        applyZoom(textureView, null, scaleFactor, focusX, focusY);
    }

    /**
     * 应用平移变换，限制平移范围不超出 TextureView 的边界。
     *
     * @param textureView 目标 TextureView。
     * @param deltaX      平移的 X 轴距离。
     * @param deltaY      平移的 Y 轴距离。
     */
    public static void applyPan(TextureView textureView, float deltaX, float deltaY) {
        applyPan(textureView, null, deltaX, deltaY);
    }

    /**
     * 应用缩放变换，限制最小缩放比例为原始比例。
     *
     * @param imageView   目标 ImageView。
     * @param matrix      当前的变换矩阵。
     * @param scaleFactor 缩放比例。
     * @param focusX      手势中心点的 X 坐标。
     * @param focusY      手势中心点的 Y 坐标。
     */
    public static void applyZoom(ImageView imageView, Matrix matrix, float scaleFactor, float focusX, float focusY) {
        if (imageView == null || matrix == null || scaleFactor <= 0) {
            Log.e(TAG, "Invalid parameters for applyZoom (ImageView)");
            return;
        }

        // 获取图片的原始尺寸
        android.graphics.drawable.Drawable drawable = imageView.getDrawable();
        if (drawable == null) {
            Log.e(TAG, "ImageView drawable is null");
            return;
        }

        // 获取视图边界
        RectF viewBounds = new RectF(0f, 0f, imageView.getWidth(), imageView.getHeight());

        // 使用临时Matrix进行原子化变换，避免视觉闪烁（基于TpImageView的成熟实现）
        Matrix tempMatrix = new Matrix(matrix);

        // 在临时Matrix上应用缩放变换
        tempMatrix.postScale(scaleFactor, scaleFactor, focusX, focusY);

        // 计算缩放后的图片边界
        RectF tempImageBounds = new RectF(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        tempMatrix.mapRect(tempImageBounds);

        // 计算边界修正值（使用TpImageView验证过的算法）
        float correctionX = calculateBoundaryCorrection(tempImageBounds, viewBounds, true);
        float correctionY = calculateBoundaryCorrection(tempImageBounds, viewBounds, false);

        // 应用边界修正到临时Matrix
        if (correctionX != 0 || correctionY != 0) {
            tempMatrix.postTranslate(correctionX, correctionY);
            Log.d(TAG, "ImageView边界修正: X=" + correctionX + ", Y=" + correctionY);
        }

        // 原子化应用：一次性将完整的变换应用到ImageView
        matrix.set(tempMatrix);
        imageView.setImageMatrix(matrix);

        Log.d(TAG, "ImageView缩放完成: scaleFactor=" + scaleFactor + ", focusX=" + focusX + ", focusY=" + focusY);
    }

    /**
     * 计算边界修正值（移植自TpImageView的成熟实现）
     *
     * @param imageBounds 图片边界
     * @param viewBounds 视图边界
     * @param isHorizontal 是否为水平方向
     * @return 修正值
     */
    private static float calculateBoundaryCorrection(RectF imageBounds, RectF viewBounds, boolean isHorizontal) {
        if (isHorizontal) {
            if (imageBounds.width() <= viewBounds.width()) {
                // 图片宽度小于等于视图宽度，居中显示
                return viewBounds.centerX() - imageBounds.centerX();
            } else {
                // 图片宽度大于视图宽度，检查边界
                if (imageBounds.left > viewBounds.left) {
                    return viewBounds.left - imageBounds.left;
                } else if (imageBounds.right < viewBounds.right) {
                    return viewBounds.right - imageBounds.right;
                }
            }
        } else {
            if (imageBounds.height() <= viewBounds.height()) {
                // 图片高度小于等于视图高度，居中显示
                return viewBounds.centerY() - imageBounds.centerY();
            } else {
                // 图片高度大于视图高度，检查边界
                if (imageBounds.top > viewBounds.top) {
                    return viewBounds.top - imageBounds.top;
                } else if (imageBounds.bottom < viewBounds.bottom) {
                    return viewBounds.bottom - imageBounds.bottom;
                }
            }
        }
        return 0;
    }

    /**
     * 应用平移变换，限制平移范围不超出 ImageView 的边界。
     *
     * @param imageView 目标 ImageView。
     * @param matrix    当前的变换矩阵。
     * @param deltaX    平移的 X 轴距离。
     * @param deltaY    平移的 Y 轴距离。
     */
    public static void applyPan(ImageView imageView, Matrix matrix, float deltaX, float deltaY) {
        if (imageView == null || matrix == null) {
            Log.e(TAG, "Invalid parameters for applyPan (ImageView)");
            return;
        }

        // 忽略过小的移动，减少抖动
        if (Math.abs(deltaX) < 1.0f && Math.abs(deltaY) < 1.0f) {
            return;
        }

        // 获取图片的原始尺寸
        android.graphics.drawable.Drawable drawable = imageView.getDrawable();
        if (drawable == null) {
            Log.e(TAG, "ImageView drawable is null");
            return;
        }

        // 获取视图边界和图片原始边界
        RectF viewBounds = new RectF(0f, 0f, imageView.getWidth(), imageView.getHeight());
        RectF imageBounds = new RectF(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());

        // 计算当前图片在变换后的边界
        RectF currentImageBounds = new RectF();
        matrix.mapRect(currentImageBounds, imageBounds);

        // 使用临时Matrix计算平移后的位置
        Matrix tempMatrix = new Matrix(matrix);
        tempMatrix.postTranslate(deltaX, deltaY);

        RectF afterPanBounds = new RectF();
        tempMatrix.mapRect(afterPanBounds, imageBounds);

        // 计算边界修正值
        float correctionX = calculateBoundaryCorrection(afterPanBounds, viewBounds, true);
        float correctionY = calculateBoundaryCorrection(afterPanBounds, viewBounds, false);

        // 计算最终的平移距离
        float finalDeltaX = deltaX + correctionX;
        float finalDeltaY = deltaY + correctionY;

        // 应用平移变换
        matrix.postTranslate(finalDeltaX, finalDeltaY);

        // 应用变换到 ImageView
        imageView.setImageMatrix(matrix);
        imageView.invalidate();

        Log.d(TAG, "ImageView平移: 限制后deltaX=" + finalDeltaX + ", deltaY=" + finalDeltaY);
    }

    /**
     * 获取当前的缩放比例
     *
     * @param textureView 目标 TextureView
     * @return 当前的缩放比例，如果无法获取则返回1.0（原始比例）
     */
    public static float getCurrentScale(TextureView textureView) {
        if (textureView == null) {
            Log.e(TAG, "Invalid parameter for getCurrentScale");
            return 1.0f;
        }

        // 获取当前的变换矩阵
        Matrix matrix = textureView.getTransform(null);

        // 获取 TextureView 的边界和当前变换后的矩形
        RectF viewRect = new RectF(0, 0, textureView.getWidth(), textureView.getHeight());
        RectF transformedRect = new RectF();
        matrix.mapRect(transformedRect, viewRect);

        // 计算当前的缩放比例
        float currentScaleX = transformedRect.width() / viewRect.width();
        float currentScaleY = transformedRect.height() / viewRect.height();
        
        // 返回较小的缩放比例，通常X和Y的缩放是一致的
        return Math.min(currentScaleX, currentScaleY);
    }

    /**
     * 重置ImageView的变换矩阵
     *
     * @param imageView 目标ImageView
     * @return 重置后的矩阵
     */
    public static Matrix resetImageViewTransform(ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "Cannot reset transform for null ImageView");
            return new Matrix();
        }
        
        Matrix resetMatrix = new Matrix();
        
        // 获取图像尺寸
        if (imageView.getDrawable() != null) {
            float viewWidth = imageView.getWidth();
            float viewHeight = imageView.getHeight();
            float drawableWidth = imageView.getDrawable().getIntrinsicWidth();
            float drawableHeight = imageView.getDrawable().getIntrinsicHeight();
            
            // 如果图像或视图尺寸无效，则返回单位矩阵
            if (viewWidth <= 0 || viewHeight <= 0 || drawableWidth <= 0 || drawableHeight <= 0) {
                return resetMatrix;
            }
            
            // 计算适合视图的缩放比例
            float scaleX = viewWidth / drawableWidth;
            float scaleY = viewHeight / drawableHeight;
            float scale = Math.min(scaleX, scaleY); // 取较小值以保持纵横比
            
            // 应用缩放
            resetMatrix.postScale(scale, scale);
            
            // 居中显示
            float dx = (viewWidth - drawableWidth * scale) / 2;
            float dy = (viewHeight - drawableHeight * scale) / 2;
            resetMatrix.postTranslate(dx, dy);
        }
        
        // 应用变换到 ImageView
        imageView.setImageMatrix(resetMatrix);
        imageView.invalidate();
        
        Log.d(TAG, "重置ImageView变换矩阵");
        
        return resetMatrix;
    }
}
