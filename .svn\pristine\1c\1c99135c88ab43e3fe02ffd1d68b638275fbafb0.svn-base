package com.android.rockchip.camera2.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Window;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Toast;


import com.android.rockchip.camera2.util.SMBFileUploader;
import com.android.rockchip.mediacodecnew.R;

/**
 * Samba设置对话框
 * <p>
 * 这个对话框用于配置Samba服务器连接参数和上传设置。
 * </p>
 */
public class SMBSettingsDialog extends Dialog {
    
    private CheckBox enableSambaCheckBox;
    private CheckBox enableSambaVideoCheckBox;
    private EditText serverIpEditText;
    private EditText usernameEditText;
    private EditText passwordEditText;
    private EditText shareNameEditText;
    private EditText remotePathEditText;
    private Button testConnectionButton;
    private Button saveButton;
    
    private SMBFileUploader SMBFileUploader;
    
    /**
     * 构造函数
     * 
     * @param context 上下文
     * @param SMBFileUploader Samba上传器实例
     */
    public SMBSettingsDialog(Context context, SMBFileUploader SMBFileUploader) {
        super(context);
        this.SMBFileUploader = SMBFileUploader;
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_samba_settings);
        
        // 初始化控件
        enableSambaCheckBox = findViewById(R.id.cb_enable_samba);
        enableSambaVideoCheckBox = findViewById(R.id.cb_enable_samba_video);
        serverIpEditText = findViewById(R.id.et_server_ip);
        usernameEditText = findViewById(R.id.et_username);
        passwordEditText = findViewById(R.id.et_password);
        shareNameEditText = findViewById(R.id.et_share_name);
        remotePathEditText = findViewById(R.id.et_remote_path);
        testConnectionButton = findViewById(R.id.btn_test_connection);
        saveButton = findViewById(R.id.btn_save);
        
        // 加载现有设置
        loadExistingSettings();
        
        // 设置按钮点击事件
        testConnectionButton.setOnClickListener(v -> testConnection());
        saveButton.setOnClickListener(v -> saveSettings());
    }
    
    /**
     * 加载现有Samba设置
     */
    private void loadExistingSettings() {
        enableSambaCheckBox.setChecked(SMBFileUploader.isEnabled());
        enableSambaVideoCheckBox.setChecked(SMBFileUploader.isVideoUploadEnabled());
        serverIpEditText.setText(SMBFileUploader.getServerIp());
        usernameEditText.setText(SMBFileUploader.getUsername());
        passwordEditText.setText(SMBFileUploader.getPassword());
        shareNameEditText.setText(SMBFileUploader.getShareName());
        remotePathEditText.setText(SMBFileUploader.getRemotePath());
    }
    
    /**
     * 测试Samba连接
     */
    private void testConnection() {
        // 获取输入的设置
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        String remotePath = remotePathEditText.getText().toString().trim();
        boolean enabled = enableSambaCheckBox.isChecked();
        boolean videoEnabled = enableSambaVideoCheckBox.isChecked();
        
        // 验证输入
        if (enabled) {
            if (serverIp.isEmpty()) {
                Toast.makeText(getContext(), "请输入服务器IP", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (shareName.isEmpty()) {
                Toast.makeText(getContext(), "请输入共享名称", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 临时设置参数进行测试
        SMBFileUploader.setConnectionParams(serverIp, username, password, shareName, remotePath, enabled);
        SMBFileUploader.setVideoUploadEnabled(videoEnabled);
        
        // 禁用测试按钮，显示测试中状态
        testConnectionButton.setEnabled(false);
        testConnectionButton.setText("测试中...");
        
        // 执行连接测试
        SMBFileUploader.testConnection(new SMBFileUploader.UploadCallback() {
            @Override
            public void onUploadSuccess(String remoteFilePath) {
                testConnectionButton.post(() -> {
                    testConnectionButton.setEnabled(true);
                    testConnectionButton.setText("测试连接");
                    Toast.makeText(getContext(), "连接成功! 已自动保存设置", Toast.LENGTH_SHORT).show();
                    
                    // 连接成功后自动保存设置并关闭对话框
                    saveSettings(false);
                });
            }
            
            @Override
            public void onUploadFailed(String errorMessage) {
                testConnectionButton.post(() -> {
                    testConnectionButton.setEnabled(true);
                    testConnectionButton.setText("测试连接");
                    Toast.makeText(getContext(), "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    /**
     * 保存Samba设置
     */
    private void saveSettings() {
        saveSettings(true);
    }
    
    /**
     * 保存Samba设置
     * 
     * @param showToast 是否显示Toast提示
     */
    private void saveSettings(boolean showToast) {
        // 获取输入的设置
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        String remotePath = remotePathEditText.getText().toString().trim();
        boolean enabled = enableSambaCheckBox.isChecked();
        boolean videoEnabled = enableSambaVideoCheckBox.isChecked();
        
        // 验证输入
        if (enabled) {
            if (serverIp.isEmpty()) {
                Toast.makeText(getContext(), "请输入服务器IP", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (shareName.isEmpty()) {
                Toast.makeText(getContext(), "请输入共享名称", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 保存设置
        SMBFileUploader.setConnectionParams(serverIp, username, password, shareName, remotePath, enabled);
        SMBFileUploader.setVideoUploadEnabled(videoEnabled);
        
        // 显示保存成功提示
        if (showToast) {
            Toast.makeText(getContext(), "设置已保存", Toast.LENGTH_SHORT).show();
        }
        
        // 关闭对话框
        dismiss();
    }
} 