#include "utiny/sip_transceiver_tcp.h"
#include "utiny/sip_header.h"
#include "utiny/sip_session_context.h"

sip_transceiver_tcp::sip_transceiver_tcp(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size, unsigned recv_buffer_size)
: usys_transceiver_tcp(reactor, send_retry_deque_size, recv_buffer_size)
{
}

sip_transceiver_tcp::~sip_transceiver_tcp()
{
}

usys_smartptr<sip_session_context> sip_transceiver_tcp::sip_session_ptr()
{
    return usys_smartptr<sip_session_context>::__dynamic_cast(session_ptr());
}

int sip_transceiver_tcp::get_message_len(const usys_smartptr<usys_data_block>& data_ptr, usys_smartptr_mtbase_ptr& header_ptr)
{
    if (data_ptr == 0)
        return -1;
    if (data_ptr->length() <= 7)
        return 0;
    usys_smartptr<sip_header> hptr(new sip_header);
	int ret = hptr->decode((const char*)data_ptr->rd_ptr(), data_ptr->length());
	if (ret <= 0)
		return ret;
	header_ptr = hptr;
    return hptr->msg_length();
}

int sip_transceiver_tcp::handle_accept()
{
    USYS_ASSERT(false);
    return -1;
}

int sip_transceiver_tcp::handle_connect()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    return sip_session_ptr()->handle_connect(transceiver_ptr);
}

int sip_transceiver_tcp::handle_write()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    return sip_session_ptr()->handle_write(transceiver_ptr);
}

void sip_transceiver_tcp::handle_close()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    sip_session_ptr()->handle_close(transceiver_ptr);
}

int sip_transceiver_tcp::handle_read_datablock(const usys_smartptr<usys_data_block>& data_ptr, const usys_smartptr_mtbase_ptr& header_ptr)
{
	usys_smartptr<usys_transceiver> transceiver_ptr(this);
	return sip_session_ptr()->handle_read_datablock(data_ptr, header_ptr, transceiver_ptr);
}

