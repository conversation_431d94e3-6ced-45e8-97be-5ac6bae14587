<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 22 14:59:28 CST 2025 -->
<title>S - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-22">
<meta name="description" content="index: S">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#saveAllDefaultValuesToLocal(boolean)" class="member-name-link">saveAllDefaultValuesToLocal(boolean)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">将所有参数恢复为默认值</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.StreamType.html#SCREEN" class="member-name-link">SCREEN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.StreamType.html" title="enum class in com.android.rockchip.camera2.video">TpVideoSystem.StreamType</a></dt>
<dd>
<div class="block">屏幕流</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#seekCurrentVideoRelative(long)" class="member-name-link">seekCurrentVideoRelative(long)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">当前视频相对跳转（简化版）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#seekVideoTo(long)" class="member-name-link">seekVideoTo(long)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">跳转到指定位置（简化版）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#sendCommandToSerial(int,int,int)" class="member-name-link">sendCommandToSerial(int, int, int)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">发送串口命令。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int)" class="member-name-link">sendToDevice(TouptekIspParam, int, int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">仅发送参数到设备，不保存到本地，带控制字节</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html#setBitRate(int)" class="member-name-link">setBitRate(int)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html#setBitrateMode(com.android.rockchip.camera2.video.TpVideoConfig.BitrateMode)" class="member-name-link">setBitrateMode(TpVideoConfig.BitrateMode)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html#setCodec(com.android.rockchip.camera2.video.TpVideoConfig.VideoCodec)" class="member-name-link">setCodec(TpVideoConfig.VideoCodec)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.html#setConnectionParams(com.android.rockchip.camera2.util.SMBFileUploader.SMBConfig)" class="member-name-link">setConnectionParams(SMBFileUploader.SMBConfig)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/SMBFileUploader.html" title="com.android.rockchip.camera2.util中的类">SMBFileUploader</a></dt>
<dd>
<div class="block">设置Samba连接参数（使用配置对象）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.html#setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">setConnectionParams(String, String, String, String, String, boolean)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/SMBFileUploader.html" title="com.android.rockchip.camera2.util中的类">SMBFileUploader</a></dt>
<dd>
<div class="block">设置Samba连接参数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#setCurrentVideoPlaybackSpeed(float)" class="member-name-link">setCurrentVideoPlaybackSpeed(float)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置当前视频的播放速度（简化版）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk.DeviceStateCallback)" class="member-name-link">setDeviceStateCallback(touptek_serial_rk.DeviceStateCallback)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">设置设备状态回调。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.html#setEnabled(boolean)" class="member-name-link">setEnabled(boolean)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/SMBFileUploader.html" title="com.android.rockchip.camera2.util中的类">SMBFileUploader</a></dt>
<dd>
<div class="block">设置是否启用Samba上传</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/HdmiService.html#setHdmiListener(com.android.rockchip.camera2.util.HdmiService.HdmiListener)" class="member-name-link">setHdmiListener(HdmiService.HdmiListener)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/HdmiService.html" title="com.android.rockchip.camera2.util中的类">HdmiService</a></dt>
<dd>
<div class="block">设置 HDMI 状态变化监听器。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html#setKeyFrameInterval(int)" class="member-name-link">setKeyFrameInterval(int)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#setListener(com.android.rockchip.camera2.video.TpVideoSystem.TpVideoSystemListener)" class="member-name-link">setListener(TpVideoSystem.TpVideoSystemListener)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置监听器</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnSerialStateChangedListener)" class="member-name-link">setOnSerialStateChangedListener(TouptekIspParam.OnSerialStateChangedListener)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置串口状态变化监听器。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamDefault(TouptekIspParam, int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置参数的自定义默认值</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean)" class="member-name-link">setParamDisabled(TouptekIspParam, boolean)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置参数的禁用状态</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamMaxValue(TouptekIspParam, int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置参数的自定义最大值</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">setParamMinValue(TouptekIspParam, int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置参数的自定义最小值</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int)" class="member-name-link">setParamRange(TouptekIspParam, boolean, int, int, int)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置参数的完整范围（包含禁用状态）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#setParamsRangeReceived(boolean)" class="member-name-link">setParamsRangeReceived(boolean)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">设置所有参数范围已接收标志</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#setStreamType(com.android.rockchip.camera2.video.TpVideoSystem.StreamType)" class="member-name-link">setStreamType(TpVideoSystem.StreamType)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置推流类型</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#setTvContainer(android.view.ViewGroup)" class="member-name-link">setTvContainer(ViewGroup)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置TV预览容器</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.SMBConfig.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">SMBConfig(String, String, String, String, String, boolean)</a> - 类的构造器 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/SMBFileUploader.SMBConfig.html" title="com.android.rockchip.camera2.util中的类">SMBFileUploader.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">SMBFileUploader</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">SambaUploader类用于连接Samba服务器并上传图片和视频。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.html#%3Cinit%3E(android.content.Context)" class="member-name-link">SMBFileUploader(Context)</a> - 类的构造器 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/SMBFileUploader.html" title="com.android.rockchip.camera2.util中的类">SMBFileUploader</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.DirectoryListListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">SMBFileUploader.DirectoryListListener</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">目录列表回调接口</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.SMBConfig.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">SMBFileUploader.SMBConfig</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">SMB连接配置类</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/SMBFileUploader.UploadListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">SMBFileUploader.UploadListener</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">上传结果回调接口</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#startMonitor()" class="member-name-link">startMonitor()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">开始串口设备检测。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/NetworkManager.html#startMonitoring()" class="member-name-link">startMonitoring()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/NetworkManager.html" title="com.android.rockchip.camera2.util中的类">NetworkManager</a></dt>
<dd>
<div class="block">开始监听网络状态变化</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#startRecording(java.lang.String)" class="member-name-link">startRecording(String)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">开始录制</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#startStreaming()" class="member-name-link">startStreaming()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流（使用默认配置）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#startStreaming(com.android.rockchip.camera2.video.TpVideoSystem.StreamType)" class="member-name-link">startStreaming(TpVideoSystem.StreamType)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#startStreaming(com.android.rockchip.camera2.video.TpVideoSystem.StreamType,java.lang.String)" class="member-name-link">startStreaming(TpVideoSystem.StreamType, String)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#startUsbDriveMonitor(android.content.Context,com.android.rockchip.camera2.util.FileStorageUtils.StorageListener)" class="member-name-link">startUsbDriveMonitor(Context, FileStorageUtils.StorageListener)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">开始监听U盘插拔事件。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#stepCurrentVideoFrame()" class="member-name-link">stepCurrentVideoFrame()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">当前视频逐帧播放（简化版）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/HdmiService.html#stop()" class="member-name-link">stop()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/HdmiService.html" title="com.android.rockchip.camera2.util中的类">HdmiService</a></dt>
<dd>
<div class="block">停止 HDMI 状态检测线程。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#stopMonitor()" class="member-name-link">stopMonitor()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">停止串口设备检测。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/NetworkManager.html#stopMonitoring()" class="member-name-link">stopMonitoring()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/NetworkManager.html" title="com.android.rockchip.camera2.util中的类">NetworkManager</a></dt>
<dd>
<div class="block">停止监听网络状态变化</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#stopRecording()" class="member-name-link">stopRecording()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">停止录制</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#stopStreaming()" class="member-name-link">stopStreaming()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">停止推流</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/FileStorageUtils.html#stopUsbDriveMonitor(android.content.Context)" class="member-name-link">stopUsbDriveMonitor(Context)</a> - 类中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/FileStorageUtils.html" title="com.android.rockchip.camera2.util中的类">FileStorageUtils</a></dt>
<dd>
<div class="block">停止监听U盘插拔事件。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#switchToCameraMode()" class="member-name-link">switchToCameraMode()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">切换到Camera模式</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TpVideoSystem.html#switchToTvMode()" class="member-name-link">switchToTvMode()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TpVideoSystem.html" title="com.android.rockchip.camera2.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">切换到TV模式</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#syncAllCurrentValuesToDevice()" class="member-name-link">syncAllCurrentValuesToDevice()</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">同步所有当前值到设备</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
