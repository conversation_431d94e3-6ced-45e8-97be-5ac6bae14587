#include "utiny/sip_request_context.h"
#include "utiny/sip_response_context.h"
#include "utiny/sip_session_context.h"
#include "utiny/sip_header.h"

sip_response_context::sip_response_context(const usys_smartptr<usys_transceiver>& transceiverptr, const sip_header_ptr& header)
: sip_packet_context(header, transceiverptr), stat_(E_OK), request_header_(header)
{
}

sip_request_context::sip_request_context(const usys_smartptr<usys_transceiver>& transceiverptr, const sip_header_ptr& h_header)
: sip_packet_context(h_header, transceiverptr)
{
}

void sip_response_context::start_timer(int second)
{
    usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
    if (trans_ptr)
        timer_id_ = trans_ptr->reactor_ptr()->register_timer_msec(second * 1000, false, usys_smartptr<usys_timer_doozer>(this), 0);
}

void sip_response_context::cancel_timer()
{
    if (timer_id_)
    {
        usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
        if (trans_ptr)
            trans_ptr->reactor_ptr()->unregister_timer_msec(timer_id_);
        timer_id_ = 0;
    }
}

void sip_response_context::handle_timeout(const void *arg)
{
    ACE_UNUSED_ARG(arg);
    stat_ = E_TIMEOUT;
	timer_id_ = 0;
    usys_smartptr<usys_transceiver> trans_ptr = transceiver_ptr();
    if (trans_ptr)
	{
		usys_smartptr<sip_session_context> session_ptr = usys_smartptr<sip_session_context>::__dynamic_cast(trans_ptr->session_ptr());
		if (session_ptr)
			session_ptr->proc_request_exception(usys_smartptr<sip_response_context>(this));
	}
}
