-- Merging decision tree log ---
manifest
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:2:1-26:12
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:2:1-26:12
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:2:1-26:12
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b982919163d22263b563b72744fff0a\transformed\viewbinding-8.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e6d2d091fd9c94756a231f1711bc\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c3206fd50f570076c14494b90731fcc\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ce28856fdbc95ead3abdf750c8ca887\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\68dafb83f6064bbf5abd12cabe81bc9a\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c7f2242429a0bb51635dd11972d5f83\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\ed26a788e1f6f7f19cbc570855dd2d8e\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8607cacdd01a7d3fc83d74e6c9467ace\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\958e008bfb25984cfc5cbf2fa77f6c2d\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\377e4dd1afafb8d8c361a86f27c7a58a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\abc5f2143265edb1d5181c3ff54c5aa0\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\587f2681c9e7f4778007c6d68eeac24c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9657fe4bd8f7ba61adc280541833f279\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4167248ffcd2d89bc505d50fd99e918\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b7b832a51604c372e54d91cab0043f78\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d8f182f78cdaac3e67ec22ba51b421e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\063574e95e61461f3d1a54a1a8a8821d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc2208f35bb8803c9d62b7234835a571\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4540ea9522a533fa43e04eae7096ae0a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\37625e03363ec3be93a75c61a9f47fcb\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a28c12f826c9c09eb44ca8240bc0b95\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9bebc662d3cd509c0ff6ec650df09137\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a001e2920120901caf994cd9a1d6f4c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ab0acaff76f44f57517bc8f7f706d2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\21bf10cbd0186af64bc5563382265364\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93d8e5f1f6da5147adc9101e586d30\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b293addc5801011de80fc54239a3bb4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7cd7fb8821b3cf72f15b62345bfe60d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65ac43ef1d0bcfa9983d90c79fc881c7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd85f702497d1a654844263a3119d9ca\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fefc48cd8e4b90fdf88774f11c240c55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\960ce575315f6f57ed65a680e3d46300\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e4c346d54abffe3e61c25e4896b3a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3ba16a6f4698aab29d8e1c7bf286aac\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\90fc2d05fc3c348272c219f57a107a49\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f4ddeb7943423580a695978e7d812773\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\424bd88d038ccd7f6f61257b48a02ff6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:5:5-24:19
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:5:5-24:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e6d2d091fd9c94756a231f1711bc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e6d2d091fd9c94756a231f1711bc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c3206fd50f570076c14494b90731fcc\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c3206fd50f570076c14494b90731fcc\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fefc48cd8e4b90fdf88774f11c240c55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fefc48cd8e4b90fdf88774f11c240c55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:13:9-45
	android:dataExtractionRules
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:7:9-65
activity#com.android.rockchip.camera2.activity.MainActivity
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:15:9-23:20
	android:exported
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:17:13-36
	android:name
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:16:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:18:13-22:29
action#android.intent.action.MAIN
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:19:17-69
	android:name
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:19:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:21:17-77
	android:name
		ADDED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml:21:27-74
uses-sdk
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b982919163d22263b563b72744fff0a\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b982919163d22263b563b72744fff0a\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e6d2d091fd9c94756a231f1711bc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e6d2d091fd9c94756a231f1711bc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c3206fd50f570076c14494b90731fcc\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c3206fd50f570076c14494b90731fcc\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ce28856fdbc95ead3abdf750c8ca887\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ce28856fdbc95ead3abdf750c8ca887\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\68dafb83f6064bbf5abd12cabe81bc9a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\68dafb83f6064bbf5abd12cabe81bc9a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c7f2242429a0bb51635dd11972d5f83\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c7f2242429a0bb51635dd11972d5f83\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\ed26a788e1f6f7f19cbc570855dd2d8e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\ed26a788e1f6f7f19cbc570855dd2d8e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8607cacdd01a7d3fc83d74e6c9467ace\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8607cacdd01a7d3fc83d74e6c9467ace\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\958e008bfb25984cfc5cbf2fa77f6c2d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\958e008bfb25984cfc5cbf2fa77f6c2d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\377e4dd1afafb8d8c361a86f27c7a58a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\377e4dd1afafb8d8c361a86f27c7a58a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\abc5f2143265edb1d5181c3ff54c5aa0\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\abc5f2143265edb1d5181c3ff54c5aa0\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\587f2681c9e7f4778007c6d68eeac24c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\587f2681c9e7f4778007c6d68eeac24c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9657fe4bd8f7ba61adc280541833f279\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9657fe4bd8f7ba61adc280541833f279\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4167248ffcd2d89bc505d50fd99e918\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4167248ffcd2d89bc505d50fd99e918\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b7b832a51604c372e54d91cab0043f78\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b7b832a51604c372e54d91cab0043f78\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d8f182f78cdaac3e67ec22ba51b421e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d8f182f78cdaac3e67ec22ba51b421e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\063574e95e61461f3d1a54a1a8a8821d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\063574e95e61461f3d1a54a1a8a8821d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc2208f35bb8803c9d62b7234835a571\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc2208f35bb8803c9d62b7234835a571\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4540ea9522a533fa43e04eae7096ae0a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4540ea9522a533fa43e04eae7096ae0a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\37625e03363ec3be93a75c61a9f47fcb\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\37625e03363ec3be93a75c61a9f47fcb\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a28c12f826c9c09eb44ca8240bc0b95\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a28c12f826c9c09eb44ca8240bc0b95\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9bebc662d3cd509c0ff6ec650df09137\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9bebc662d3cd509c0ff6ec650df09137\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a001e2920120901caf994cd9a1d6f4c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a001e2920120901caf994cd9a1d6f4c4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ab0acaff76f44f57517bc8f7f706d2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ab0acaff76f44f57517bc8f7f706d2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\21bf10cbd0186af64bc5563382265364\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\21bf10cbd0186af64bc5563382265364\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93d8e5f1f6da5147adc9101e586d30\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93d8e5f1f6da5147adc9101e586d30\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b293addc5801011de80fc54239a3bb4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b293addc5801011de80fc54239a3bb4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7cd7fb8821b3cf72f15b62345bfe60d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7cd7fb8821b3cf72f15b62345bfe60d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65ac43ef1d0bcfa9983d90c79fc881c7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65ac43ef1d0bcfa9983d90c79fc881c7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd85f702497d1a654844263a3119d9ca\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd85f702497d1a654844263a3119d9ca\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fefc48cd8e4b90fdf88774f11c240c55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fefc48cd8e4b90fdf88774f11c240c55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\960ce575315f6f57ed65a680e3d46300\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\960ce575315f6f57ed65a680e3d46300\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e4c346d54abffe3e61c25e4896b3a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e4c346d54abffe3e61c25e4896b3a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3ba16a6f4698aab29d8e1c7bf286aac\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3ba16a6f4698aab29d8e1c7bf286aac\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\90fc2d05fc3c348272c219f57a107a49\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\90fc2d05fc3c348272c219f57a107a49\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f4ddeb7943423580a695978e7d812773\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f4ddeb7943423580a695978e7d812773\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\424bd88d038ccd7f6f61257b48a02ff6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\424bd88d038ccd7f6f61257b48a02ff6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\RK3588\touptek_serial_rk\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ce390f6415ea36b2855b8e2a3812341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\81c0ded5ee15615213dbda2d8ba23e9a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\36b9a681db1c6e74f26419162978323f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.android.rockchip.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.android.rockchip.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\da1202ae289a2d30546cd64f22e87d3b\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\474c0bee4e3e13fc86994f44a0a09499\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
