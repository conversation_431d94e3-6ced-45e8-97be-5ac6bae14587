C/C++ Build Metadata                A           c         H                                pC:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe   "--target=i686-none-linux-android29   r--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -DSerialPort_EXPORTS   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -D_FORTIFY_SOURCE=2   -Wformat   -Werror=format-security   
-std=c++11   -O2   -DNDEBUG   -fPIC   FC:\hhx\svn\AndroidStudio\rkCamer2\app\.cxx\RelWithDebInfo\4sm1b725\x86   
SerialPort   AC:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\cpp\native-lib.cpp   *CMakeFiles\SerialPort.dir\native-lib.cpp.o                              	   
         
                  