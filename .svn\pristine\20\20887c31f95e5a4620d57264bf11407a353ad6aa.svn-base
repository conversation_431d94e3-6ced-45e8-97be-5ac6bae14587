<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 开启状态 -->
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <solid android:color="#2196F3"/> <!-- 滑轨开启时颜色（蓝色） -->
            <corners android:radius="16dp"/> <!-- 圆角半径（需与滑块半径匹配） -->
        </shape>
    </item>

    <!-- 关闭状态 -->
    <item android:state_checked="false">
        <shape android:shape="rectangle">
            <solid android:color="#BDBDBD"/> <!-- 滑轨关闭时颜色（灰色） -->
            <corners android:radius="16dp"/>
        </shape>
    </item>
</selector>