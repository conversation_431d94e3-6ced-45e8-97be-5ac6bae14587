// ***************************************************************
//  ubeda_config   version:  1.0     date: 07/31/2002
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  The Institute of System Engineering, Zhejiang University
//  -------------------------------------------------------------
//  Copyright (C) 2002 - All Rights Reserved
// ***************************************************************
// 
// ***************************************************************
#ifndef __utiny_config_h__
#define __utiny_config_h__

#ifdef _WIN32
#ifdef UTINY_EXPORTS
#define UBEDA_API __declspec(dllexport)
#elif defined(UTINY_IMPORTS)
#define UBEDA_API __declspec(dllimport)
#else
#define UBEDA_API
#endif
#define NOMINMAX
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#define UBEDA_API
#include <unistd.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <sys/poll.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <memory.h>
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string>
#ifdef __APPLE__
#include <libkern/OSAtomic.h>
#include "TargetConditionals.h"
#if TARGET_IPHONE_SIMULATOR
#elif TARGET_OS_IPHONE
#elif TARGET_OS_MAC
#include <CoreServices/CoreServices.h>
#else
#endif
#endif

#if defined(_WIN32) || defined(__osf__)
typedef int socklen_t;
#endif

#ifndef _WIN32
#define SOCKET int
#define SOCKET_ERROR -1
#define INVALID_SOCKET -1
#endif

#ifndef SHUT_RD
#define SHUT_RD 0
#endif

#ifndef SHUT_WR
#define SHUT_WR 1
#endif

#ifndef SHUT_RDWR
#define SHUT_RDWR 2
#endif

#ifndef NETDB_INTERNAL
#define NETDB_INTERNAL -1
#endif

#ifndef NETDB_SUCCESS
#define NETDB_SUCCESS 0
#endif

#ifdef ZDP
#ifndef TGUID_DEFINED
#if defined(__ANDROID__)
#elif defined(__LINUX__)
#include <tr1/array>
typedef std::tr1::array<unsigned char, 16> tguid;
#else
#include <array>
typedef std::array<unsigned char, 16> tguid;
#endif
#endif
#endif

#if (__cplusplus >= 201103L) || defined(__GXX_EXPERIMENTAL_CXX0X__) || (defined(_MSC_VER) && (_MSC_VER >= 1600))
#include <utility>
#define MOVE(x)	std::move(x)
#else
#define MOVE(x)	x
#endif

#include "usys_api.h"

#ifndef USYS_HASNOT_LOG
#ifdef _WIN32
#define USYS_DEBUG(f, ...) usys_log(__FILE__, __LINE__, f, __VA_ARGS__)
#define USYS_ERROR(f, ...) usys_log(__FILE__, __LINE__, f, __VA_ARGS__)
#else
#define USYS_DEBUG(f, ...) usys_log(__FILE__, __LINE__, f, ##__VA_ARGS__)
#define USYS_ERROR(f, ...) usys_log(__FILE__, __LINE__, f, ##__VA_ARGS__)
#endif
#define USYS_ASSERT(X) do { if(!(X)) usys_log(__FILE__, __LINE__, "assert failed for '%s'", (#X)); } while(0)
#else
#define USYS_DEBUG(f, ...)
#define USYS_ERROR(f, ...)
#define USYS_ASSERT(X)
#endif

#ifndef UCE_HASNOT_COMPRESS
enum UCE_COMPRESS_FLAG
{
    UCE_COMPRESS_NONE,
    UCE_COMPRESS_UCEBZIP2,
    UCE_COMPRESS_NATIVE,
    UCE_COMPRESS_SNAPPY,
    UCE_COMPRESS_ZIP,
};
#endif

#define UCE_MAX_MESSAGE_LENGTH		(32 * 1024 * 1024)

enum ubeda_error_e
{
    ubeda_success_base      = 0x01000000,
    ubeda_success_udb       = 0x01001000,
    ubeda_success_udb_fieldnull,
    uce_success = 0x01020000,
    uce_success_multi_response,

    ubeda_error_base    = 0x81000000, //keep for stdc error
    ubeda_error_udb     = 0x81001000,
    ubeda_error_udb_exception,
    ubeda_error_udb_connection,
    ubeda_error_udb_invalidsql,
    ubeda_error_udb_rowcol,
    ubeda_error_udb_execfailed,
    ubeda_error_udb_nonfetched,
    ubeda_error_udb_delegate,
    udeda_error_udb_sqlite = 0x81001100,
    uce_error = 0x81020000,
    uce_error_protpack,
    uce_error_protunpack,
    uce_error_unmatch_servant,
    uce_error_invalid_context,
    uce_error_invalid_response,
    uce_error_prot_unmatch,
    uce_error_syntax,
    uce_error_prot_lackmem,
    uce_error_unsupport_compress,
    uce_error_no_transceiver,
    uce_error_no_bindsession,
    uce_error_write_failed,
    uce_error_queue_oversize,
    uce_error_compress_unmatch,
    uce_error_compress_truncate,
    uce_error_compress_failed,

    uce_error_convertor = 0x81030000,
};

#if !defined(OBJECT_MONITOR) && defined(_DEBUG)
#define OBJECT_MONITOR
#endif

#ifdef OBJECT_MONITOR
#define SYS_OBJECT_DECLARE              usys_object_proxy object_proxy_
#define SYS_OBJECT_IMPLEMENT(x)         , object_proxy_(x##_object)
#define SYS_OBJECT_IMPLEMENT_EMPTY(x)   : object_proxy_(x##_object)
#define SYS_OBJECT_INSTANCE(x)          static usys_object x##_object(#x);
#else
#define SYS_OBJECT_DECLARE 
#define SYS_OBJECT_IMPLEMENT(x)
#define SYS_OBJECT_IMPLEMENT_EMPTY(x)
#define SYS_OBJECT_INSTANCE(x)
#endif

#endif // __utiny_config_h__
