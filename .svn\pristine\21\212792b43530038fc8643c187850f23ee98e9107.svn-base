package com.android.rockchip.camera2.util;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * TouptekIspParam 枚举类用于定义和管理摄像头ISP参数。
 * <p>
 * 此类提供了摄像头图像信号处理(ISP)参数的定义、存储、读取、更新和监听机制。
 * 每个枚举项对应一个ISP参数及其唯一的整数值，支持与设备串口通信，可以向设备发送参数或从设备读取参数。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>ISP参数定义与管理</li>
 *   <li>参数值的本地存储与读取</li>
 *   <li>与设备串口通信</li>
 *   <li>参数变化监听机制</li>
 *   <li>批量参数操作</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 初始化
 * TouptekIspParam.init(context);
 * 
 * // 设置参数
 * TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_BRIGHTNESS, 60);
 * 
 * // 读取参数
 * int brightness = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_BRIGHTNESS);
 * 
 * // 监听参数变化
 * TouptekIspParam.addOnDataChangedListener(new TouptekIspParam.OnDataChangedListener() {
 *     @Override
 *     public void onDataChanged(TouptekIspParam param, int newValue) {
 *         Log.d(TAG, "参数变化: " + param.name() + " = " + newValue);
 *     }
 *     
 *     @Override
 *     public void onLongDataChanged(TouptekIspParam param, long newValue) {
 *         Log.d(TAG, "长整型参数变化: " + param.name() + " = " + newValue);
 *     }
 * });
 * }</pre>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public enum TouptekIspParam 
{
    /** 版本号，只读参数 */
    TOUPTEK_PARAM_VERSION(0x0),

    /** 曝光模式选择，0:手动曝光，1:自动曝光 */
    TOUPTEK_PARAM_EXPOSURECHOICE(0x1),

    /** 曝光补偿，调整自动曝光的目标亮度 */
    TOUPTEK_PARAM_EXPOSURECOMPENSATION(0x2),

    /** 曝光时间，单位为毫秒，仅在手动曝光模式下有效 */
    TOUPTEK_PARAM_EXPOSURETIME(0x3),

    /** 曝光增益，放大传感器信号，仅在手动曝光模式下有效 */
    TOUPTEK_PARAM_EXPOSUREGAIN(0x4),

    /** 白平衡模式选择，0:手动，1:自动，2:ROI */
    TOUPTEK_PARAM_WBCHOICE(0x5),

    /** 红色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBREDGAIN(0x6),

    /** 绿色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBGREENGAIN(0x7),

    /** 蓝色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBBLUEGAIN(0x8),

    /** 锐化参数，控制图像边缘清晰度 */
    TOUPTEK_PARAM_SHARPNESS(0x9),

    /** 降噪参数，减少图像噪点 */
    TOUPTEK_PARAM_DENOISE(0xA),

    /** 镜像效果，0:关闭，1:开启 */
    TOUPTEK_PARAM_MIRROR(0xB),

    /** 翻转效果，0:关闭，1:开启 */
    TOUPTEK_PARAM_FLIP(0xC),

    /** 饱和度，控制色彩鲜艳程度 */
    TOUPTEK_PARAM_SATURATION(0xD),

    /** Gamma校正，调整图像亮度非线性分布 */
    TOUPTEK_PARAM_GAMMA(0xE),

    /** 对比度，控制图像明暗层次 */
    TOUPTEK_PARAM_CONTRAST(0xF),

    /** 频率，用于抑制电源频率引起的闪烁，0:50Hz，1:60Hz，2:禁用 */
    TOUPTEK_PARAM_HZ(0x10),

    /** 亮度，调整图像整体明亮度 */
    TOUPTEK_PARAM_BRIGHTNESS(0x11),

    /** 色调，调整图像色彩倾向 */
    TOUPTEK_PARAM_HUE(0x12),

    /** 彩色/灰度模式选择，0:彩色，1:灰度 */
    TOUPTEK_PARAM_COLORORGRAY(0x13),

    /** 带宽控制，影响图像处理的速度和质量 */
    TOUPTEK_PARAM_BANDWIDTH(0x14),

    /** 色彩色调，预设的色彩风格 */
    TOUPTEK_PARAM_COLORTONE(0x15),

    /** 彩色温度红色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTREDGAIN(0x16),

    /** 彩色温度绿色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTGREENGAIN(0x17),

    /** 彩色温度蓝色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTBLUEGAIN(0x18),

    /** 暗部增强，提升阴影区域细节 */
    TOUPTEK_PARAM_DARKENHANCE(0x19),

    /** 宽动态范围曝光比率，增强高对比度场景的细节 */
    TOUPTEK_PARAM_WDREXPRATIO(0x1A),

    /** 低动态范围对比度比率，调整低光照条件下的对比度 */
    TOUPTEK_PARAM_LDCRATIO(0x1B),

    /** ROI区域左边界坐标，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_LEFT(0xF1),

    /** ROI区域上边界坐标，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_TOP(0xF2),

    /** ROI区域宽度，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_WIDTH(0xF3),

    /** ROI区域高度，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_HEIGHT(0xF4),

    /** 场景选择参数，预设的参数组合 */
    TOUPTEK_PARAM_ISP_DEFAULT_TYPE(0xF5);


    /** 申请参数范围命令码，用于从设备获取参数范围信息 */
    private static final int REQUEST_PARAM_RANGES = 0xF0;


    /** 枚举值对应的整数值，用于串口通信 */
    private final int value;
    
    /** 用于存储参数值的SharedPreferences实例，确保应用重启后参数值不丢失 */
    private static SharedPreferences sharedPreferences;
    
    /** 数据变化监听器列表，支持多个监听器同时接收参数变化通知 */
    private static final List<OnDataChangedListener> dataChangedListeners = new ArrayList<>();
    
    /** 
     * 数据变化监听器（保持向后兼容）
     * @deprecated 推荐使用dataChangedListeners列表和addOnDataChangedListener方法
     */
    @Deprecated
    private static OnDataChangedListener dataChangedListener;
    
    /** 串口实例，用于与设备通信 */
    private static touptek_serial_rk serialInstance;
    
    /** 串口状态监听器，用于监听串口连接状态变化 */
    private static OnSerialStateChangedListener serialStateListener;

    /** 存储参数禁用状态的映射表，true表示禁用，false表示启用 */
    private static final Map<TouptekIspParam, Boolean> isDisableStates = new HashMap<>();
    /** 存储参数最小值的映射表 */
    private static final Map<TouptekIspParam, Integer> MinValues = new HashMap<>();
    private static final Map<TouptekIspParam, Integer> MaxValues = new HashMap<>();
    private static final Map<TouptekIspParam, Integer> DefaultValues = new HashMap<>();

    /** 默认参数范围接收完成标志，true表示已接收完成 */
    private static Boolean allParamsRangeReceived = false;
    /**
     * 构造函数
     * 
     * @param value 枚举项对应的整数值
     */
    TouptekIspParam(int value) 
    {
        this.value = value;
    }

    /**
     * 获取枚举值的整数值。
     *
     * @return 当前枚举项对应的整数值。
     */
    public int getValue() 
    {
        return value;
    }

    /**
     * 设置参数的自定义最小值
     * 
     * @param param 参数类型
     * @param minValue 自定义最小值
     */
    public static void setParamMinValue(TouptekIspParam param, int minValue) {
        MinValues.put(param, minValue);
    }

    /**
     * 设置参数的自定义最大值
     * 
     * @param param 参数类型
     * @param maxValue 自定义最大值
     */
    public static void setParamMaxValue(TouptekIspParam param, int maxValue) {
        MaxValues.put(param, maxValue);
    }

    /**
     * 设置参数的自定义默认值
     * 
     * @param param 参数类型
     * @param defaultValue 自定义默认值
     */
    public static void setParamDefault(TouptekIspParam param, int defaultValue) {
        DefaultValues.put(param, defaultValue);
    }

 
    /**
     * 设置参数的禁用状态
     * 
     * @param param 参数类型
     * @param isDisable true表示禁用，false表示启用
     */
    public static void setParamDisabled(TouptekIspParam param, boolean isDisable) {
        isDisableStates.put(param, isDisable);
    }

    /**
     * 设置参数的完整范围（包含禁用状态）
     * 
     * @param param 参数类型
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param defaultValue 默认值
     * @param isDisable 是否禁用
     */
    public static void setParamRange(TouptekIspParam param,boolean isDisable, int minValue, int maxValue, int defaultValue) {
        setParamMinValue(param, minValue);
        setParamMaxValue(param, maxValue);
        setParamDefault(param, defaultValue);
        setParamDisabled(param, isDisable);
    }

    /**
     * 设置所有参数范围已接收标志
     * @param ParamsRangeReceived 参数范围接收状态，true表示已接收完成，false表示未接收
     */
    public static void setParamsRangeReceived(boolean ParamsRangeReceived)
    {
        allParamsRangeReceived = ParamsRangeReceived;
    }

    /**
     * 获取所有参数范围是否已接收
     * 
     * @return true表示所有参数范围已接收，false表示未接收
     */
    public static boolean getParamsRangeReceived()
    {
        return allParamsRangeReceived;
    }


    /**
     * 参数数据结构
     * <p>
     * 包含ISP参数的完整信息，包括最小值、最大值、默认值、当前值和禁用状态。
     * 用于在UI层展示参数信息和范围，以及在业务逻辑中进行参数验证。
     * </p>
     * 
     * <p><b>字段说明：</b></p>
     * <ul>
     *   <li>min - 参数的最小允许值</li>
     *   <li>max - 参数的最大允许值</li>
     *   <li>defaultValue - 参数的默认值</li>
     *   <li>current - 参数的当前值</li>
     *   <li>isDisabled - 参数是否被禁用</li>
     * </ul>
     */
    public static class ParamData
    {
        /** 参数的最小允许值 */
        public final int min;
        
        /** 参数的最大允许值 */
        public final int max;
        
        /** 参数的默认值 */
        public final int defaultValue;
        
        /** 参数的当前值 */
        public int current;
        
        /** 参数是否被禁用，true表示禁用，false表示启用 */
        public final boolean isDisabled;

        /**
         * 构造一个新的参数数据对象
         * 
         * @param min 参数的最小允许值
         * @param max 参数的最大允许值
         * @param defaultValue 参数的默认值
         * @param current 参数的当前值
         * @param isDisabled 参数是否被禁用
         */
        public ParamData(int min, int max, int defaultValue, int current, boolean isDisabled)
        {
            this.min = min;
            this.max = max;
            this.defaultValue = defaultValue;
            this.current = current;
            this.isDisabled = isDisabled;
        }

        @Override
        public String toString()
        {
            return String.format("ParamData{min=%d, max=%d, default=%d, current=%d, disabled=%s}",
                    min, max, defaultValue, current, isDisabled);
        }
    }

    /**
     * 获取参数的最小值。
     * <p>
     * 根据不同参数类型返回其有效范围的最小值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取最小值的参数
     * @return 参数的最小值
     */
    public static int getMinValue(TouptekIspParam param) 
    {
        if (param == null) 
        {
            return 0;
        }

        // 优先使用自定义值
        if (MinValues.containsKey(param)) {
            return MinValues.get(param);
        }
        return 1111111111; // 默认最小值为0

//       switch (param)
//       {
//           case TOUPTEK_PARAM_EXPOSURECHOICE:        // ID=1
//               return 0;
//           case TOUPTEK_PARAM_EXPOSURECOMPENSATION:  // ID=2
//               return 0;
//           case TOUPTEK_PARAM_EXPOSURETIME:          // ID=3
//               return 0;
//           case TOUPTEK_PARAM_EXPOSUREGAIN:          // ID=4
//               return 0;
//           case TOUPTEK_PARAM_WBCHOICE:              // ID=5
//               return 0;
//           case TOUPTEK_PARAM_WBREDGAIN:             // ID=6
//           case TOUPTEK_PARAM_WBGREENGAIN:           // ID=7
//           case TOUPTEK_PARAM_WBBLUEGAIN:            // ID=8
//               return 1;
//           case TOUPTEK_PARAM_SHARPNESS:             // ID=9
//               return 0;
//           case TOUPTEK_PARAM_DENOISE:               // ID=10
//               return 0;
//           case TOUPTEK_PARAM_MIRROR:                // ID=11
//           case TOUPTEK_PARAM_FLIP:                  // ID=12
//               return 0;
//           case TOUPTEK_PARAM_SATURATION:            // ID=13
//               return 0;
//           case TOUPTEK_PARAM_GAMMA:                 // ID=14
//               return 1;
//           case TOUPTEK_PARAM_CONTRAST:              // ID=15
//               return 0;
//           case TOUPTEK_PARAM_HZ:                    // ID=16
//               return 0;
//           case TOUPTEK_PARAM_BRIGHTNESS:            // ID=17
//               return 0;
//           case TOUPTEK_PARAM_HUE:                   // ID=18
//               return 0;
//           case TOUPTEK_PARAM_COLORORGRAY:           // ID=19
//               return 0;
//           case TOUPTEK_PARAM_BANDWIDTH:             // ID=20
//               return 1024;
//           case TOUPTEK_PARAM_COLORTONE:             // ID=21
//               return 0;
//           case TOUPTEK_PARAM_CTREDGAIN:             // ID=22
//           case TOUPTEK_PARAM_CTGREENGAIN:           // ID=23
//           case TOUPTEK_PARAM_CTBLUEGAIN:            // ID=24
//               return 0;
//           case TOUPTEK_PARAM_DARKENHANCE:           // ID=25
//               return 0;
//           case TOUPTEK_PARAM_WDREXPRATIO:           // ID=26
//               return 1;
//           case TOUPTEK_PARAM_LDCRATIO:              // ID=27
//               return -30;
//           case TOUPTEK_PARAM_ROI_LEFT:
//               return 0;
//           case TOUPTEK_PARAM_ROI_TOP:
//               return 0;
//           case TOUPTEK_PARAM_ROI_WIDTH:
//               return 2;
//           case TOUPTEK_PARAM_ROI_HEIGHT:
//               return 2;
//           default:
//               return 0;
//       }
    }

    /**
     * 获取参数的最大值。
     * <p>
     * 根据不同参数类型返回其有效范围的最大值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取最大值的参数
     * @return 参数的最大值
     */
    public static int getMaxValue(TouptekIspParam param)
    {
        if (param == null)
        {
            return 100;
        }

        // 优先使用自定义值
        if (MaxValues.containsKey(param)) {
            return MaxValues.get(param);
        }
        return 99999999; // 默认最大值为0

//       switch (param)
//       {
//           case TOUPTEK_PARAM_EXPOSURECHOICE:        // ID=1
//               return 1;
//           case TOUPTEK_PARAM_EXPOSURECOMPENSATION:  // ID=2
//               return 25;
//           case TOUPTEK_PARAM_EXPOSURETIME:          // ID=3
//               return 1000;
//           case TOUPTEK_PARAM_EXPOSUREGAIN:          // ID=4
//               return 55;
//           case TOUPTEK_PARAM_WBCHOICE:              // ID=5
//               return 2;
//           case TOUPTEK_PARAM_WBREDGAIN:             // ID=6
//           case TOUPTEK_PARAM_WBGREENGAIN:           // ID=7
//           case TOUPTEK_PARAM_WBBLUEGAIN:            // ID=8
//               return 4095;
//           case TOUPTEK_PARAM_SHARPNESS:             // ID=9
//               return 100;
//           case TOUPTEK_PARAM_DENOISE:               // ID=10
//               return 50;
//           case TOUPTEK_PARAM_MIRROR:                // ID=11
//           case TOUPTEK_PARAM_FLIP:                  // ID=12
//               return 1;
//           case TOUPTEK_PARAM_SATURATION:            // ID=13
//               return 100;
//           case TOUPTEK_PARAM_GAMMA:                 // ID=14
//               return 20;
//           case TOUPTEK_PARAM_CONTRAST:              // ID=15
//               return 100;
//           case TOUPTEK_PARAM_HZ:                    // ID=16
//               return 2;
//           case TOUPTEK_PARAM_BRIGHTNESS:            // ID=17
//               return 100;
//           case TOUPTEK_PARAM_HUE:                   // ID=18
//               return 100;
//           case TOUPTEK_PARAM_COLORORGRAY:           // ID=19
//               return 1;
//           case TOUPTEK_PARAM_BANDWIDTH:             // ID=20
//               return 35840;
//           case TOUPTEK_PARAM_COLORTONE:             // ID=21
//               return 3;
//           case TOUPTEK_PARAM_CTREDGAIN:             // ID=22
//           case TOUPTEK_PARAM_CTGREENGAIN:           // ID=23
//           case TOUPTEK_PARAM_CTBLUEGAIN:            // ID=24
//               return 128;
//           case TOUPTEK_PARAM_DARKENHANCE:           // ID=25
//               return 100;
//           case TOUPTEK_PARAM_WDREXPRATIO:           // ID=26
//               return 16;
//           case TOUPTEK_PARAM_LDCRATIO:              // ID=27
//               return 30;
//           case TOUPTEK_PARAM_ROI_LEFT:
//           case TOUPTEK_PARAM_ROI_WIDTH:
//               return 3808;
//           case TOUPTEK_PARAM_ROI_TOP:
//           case TOUPTEK_PARAM_ROI_HEIGHT:
//               return 2128;
//           default:
//               return 100;
//       }
    }

    /**
     * 获取参数的默认值
     * <p>
     * 根据不同参数类型返回其默认值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取默认值的参数
     * @return 参数的默认值
     */
    public static int getDefaultValue(TouptekIspParam param)
    {
        if (param == null)
        {
            return 0;
        }

        // 优先使用自定义值
        if (DefaultValues.containsKey(param)) {
            return DefaultValues.get(param);
        }

        return 555555;

//       switch (param)
//       {
//           case TOUPTEK_PARAM_EXPOSURECHOICE:        // ID=1
//               return 1;
//           case TOUPTEK_PARAM_EXPOSURECOMPENSATION:  // ID=2
//               return 12;
//           case TOUPTEK_PARAM_EXPOSURETIME:          // ID=3
//               return 10;
//           case TOUPTEK_PARAM_EXPOSUREGAIN:          // ID=4
//               return 0;
//           case TOUPTEK_PARAM_WBCHOICE:              // ID=5
//               return 0;
//           case TOUPTEK_PARAM_WBREDGAIN:             // ID=6
//               return 500;
//           case TOUPTEK_PARAM_WBGREENGAIN:           // ID=7
//               return 512;
//           case TOUPTEK_PARAM_WBBLUEGAIN:            // ID=8
//               return 456;
//           case TOUPTEK_PARAM_SHARPNESS:             // ID=9
//               return 10;
//           case TOUPTEK_PARAM_DENOISE:               // ID=10
//               return 8;
//           case TOUPTEK_PARAM_MIRROR:                // ID=11
//           case TOUPTEK_PARAM_FLIP:                  // ID=12
//               return 0;
//           case TOUPTEK_PARAM_SATURATION:            // ID=13
//               return 50;
//           case TOUPTEK_PARAM_GAMMA:                 // ID=14
//               return 11;
//           case TOUPTEK_PARAM_CONTRAST:              // ID=15
//               return 50;
//           case TOUPTEK_PARAM_HZ:                    // ID=16
//               return 2;
//           case TOUPTEK_PARAM_BRIGHTNESS:            // ID=17
//               return 50;
//           case TOUPTEK_PARAM_HUE:                   // ID=18
//               return 50;
//           case TOUPTEK_PARAM_COLORORGRAY:           // ID=19
//               return 0;
//           case TOUPTEK_PARAM_BANDWIDTH:             // ID=20
//               return 15360;
//           case TOUPTEK_PARAM_COLORTONE:             // ID=21
//               return 0;
//           case TOUPTEK_PARAM_CTREDGAIN:             // ID=22
//           case TOUPTEK_PARAM_CTGREENGAIN:           // ID=23
//           case TOUPTEK_PARAM_CTBLUEGAIN:            // ID=24
//               return 0;
//           case TOUPTEK_PARAM_DARKENHANCE:           // ID=25
//               return 1;
//           case TOUPTEK_PARAM_WDREXPRATIO:           // ID=26
//               return 10;
//           case TOUPTEK_PARAM_LDCRATIO:              // ID=27
//               return 0;
//           case TOUPTEK_PARAM_ROI_LEFT:
//               return 1680;
//           case TOUPTEK_PARAM_ROI_TOP:
//               return 945;
//           case TOUPTEK_PARAM_ROI_WIDTH:
//               return 480;
//           case TOUPTEK_PARAM_ROI_HEIGHT:
//               return 270;
//           default:
//               return 50;
//       }
    }

    /**
     * 获取参数的禁用状态
     * <p>
     * 根据不同参数类型返回其使能状态。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     * @param param 参数类型
     * @return true表示禁用，false表示启用，默认为false（启用）
     */
    public static boolean getIsDisableValue(TouptekIspParam param) {
        if (param == null) {
            return false;
        }

        // 优先使用自定义设置
        if (isDisableStates.containsKey(param)) {
            return Boolean.TRUE.equals(isDisableStates.get(param));
        }
        return true;
    
//       switch (param) {
//           // 基础曝光参数 - 根据日志显示全部启用
//           case TOUPTEK_PARAM_EXPOSURECHOICE:        // ID=1, isDisable=0
//           case TOUPTEK_PARAM_EXPOSURECOMPENSATION:  // ID=2, isDisable=0
//           case TOUPTEK_PARAM_EXPOSURETIME:          // ID=3, isDisable=0
//           case TOUPTEK_PARAM_EXPOSUREGAIN:          // ID=4, isDisable=0
//               return false; // 启用
//
//           // 白平衡参数 - 根据日志显示全部启用
//           case TOUPTEK_PARAM_WBCHOICE:              // ID=5, isDisable=0
//           case TOUPTEK_PARAM_WBREDGAIN:             // ID=6, isDisable=0
//           case TOUPTEK_PARAM_WBGREENGAIN:           // ID=7, isDisable=0
//           case TOUPTEK_PARAM_WBBLUEGAIN:            // ID=8, isDisable=0
//               return false; // 启用
//
//           // 图像质量参数 - 根据日志显示全部启用
//           case TOUPTEK_PARAM_SHARPNESS:             // ID=9, isDisable=0
//           case TOUPTEK_PARAM_DENOISE:               // ID=10, isDisable=0
//           case TOUPTEK_PARAM_SATURATION:            // ID=13, isDisable=0
//           case TOUPTEK_PARAM_GAMMA:                 // ID=14, isDisable=0
//           case TOUPTEK_PARAM_CONTRAST:              // ID=15, isDisable=0
//           case TOUPTEK_PARAM_BRIGHTNESS:            // ID=17, isDisable=0
//           case TOUPTEK_PARAM_HUE:                   // ID=18, isDisable=0
//               return false; // 启用
//
//           // 基础控制参数 - 根据日志显示全部启用
//           case TOUPTEK_PARAM_MIRROR:                // ID=11, isDisable=0
//           case TOUPTEK_PARAM_FLIP:                  // ID=12, isDisable=0
//           case TOUPTEK_PARAM_HZ:                    // ID=16, isDisable=0
//           case TOUPTEK_PARAM_COLORORGRAY:           // ID=19, isDisable=0
//               return false; // 启用
//
//           // 高级功能参数 - 根据日志显示全部启用
//           case TOUPTEK_PARAM_BANDWIDTH:             // ID=20, isDisable=0
//           case TOUPTEK_PARAM_COLORTONE:             // ID=21, isDisable=0
//           case TOUPTEK_PARAM_CTREDGAIN:             // ID=22, isDisable=0
//           case TOUPTEK_PARAM_CTGREENGAIN:           // ID=23, isDisable=0
//           case TOUPTEK_PARAM_CTBLUEGAIN:            // ID=24, isDisable=0
//           case TOUPTEK_PARAM_DARKENHANCE:           // ID=25, isDisable=0
//           case TOUPTEK_PARAM_WDREXPRATIO:           // ID=26, isDisable=0
//           case TOUPTEK_PARAM_LDCRATIO:              // ID=27, isDisable=0
//               return false; // 启用
//
//           // 版本信息 - 只读，但不禁用显示
//           case TOUPTEK_PARAM_VERSION:
//               return false; // 启用显示
//
//           default:
//               return false; // 默认启用
//       }
    }



    /**
     * 获取参数的完整数据信息
     * <p>
     * 返回包含参数最小值、最大值、默认值、当前值和禁用状态的完整信息。
     * 如果参数没有存储的当前值，会自动使用默认值并保存到本地。
     * </p>
     *
     * @param param 需要获取数据的参数
     * @return 包含参数完整信息的 {@link ParamData} 对象
     */
    public static ParamData getParamData(TouptekIspParam param) 
    {
        int min = getMinValue(param);
        int max = getMaxValue(param);
        int defaultValue = getDefaultValue(param);
        int current = getData(param);
        boolean isDisabled = getIsDisableValue(param);
        
        // 如果没有存储值，使用默认值
        if (current == -1) 
        {
            current = defaultValue;
            // 保存默认值到本地
            saveToLocal(param, defaultValue);
        }
        
        return new ParamData(min, max, defaultValue, current, isDisabled);
    }

    /**
     * 获取所有参数的数据信息
     * <p>
     * 返回所有ISP参数的完整数据信息列表。
     * </p>
     *
     * @return 包含所有参数数据的列表
     */
    public static List<ParamData> getAllParamData()
    {
        List<ParamData> paramDataList = new ArrayList<>();
        for (TouptekIspParam param : TouptekIspParam.values()) 
        {
            paramDataList.add(getParamData(param));
        }
        return paramDataList;
    }

    /**
     * 发送所有已保存的参数到设备
     * <p>
     * 遍历所有参数，将本地存储的值发送到设备。根据当前的模式设置，会自动跳过某些不适用的参数：
     * <ul>
     *   <li>在自动曝光模式下跳过手动曝光参数</li>
     *   <li>在非ROI白平衡模式下跳过ROI参数</li>
     *   <li>在非手动白平衡模式下跳过色温增益参数</li>
     *   <li>始终跳过只读参数（如版本号）</li>
     * </ul>
     */
    public static void sendAllParamsToDevice() {
        android.util.Log.i("TouptekIspParam", "开始发送所有保存的参数到设备...");
        
        int sentCount = 0;
        // 获取当前模式值，用于判断是否跳过某些参数
        int exposureMode = getData(TOUPTEK_PARAM_EXPOSURECHOICE);
        int wbChoice = getData(TOUPTEK_PARAM_WBCHOICE);
        
        for (TouptekIspParam param : TouptekIspParam.values()) {
            try {
                // 1. 始终跳过版本号参数，它是只读的
                if (param == TOUPTEK_PARAM_VERSION) {
                    continue;
                }
                
                // 2. 在非ROI白平衡模式下跳过ROI参数
                if (wbChoice != 2 && (param == TOUPTEK_PARAM_ROI_LEFT || 
                                    param == TOUPTEK_PARAM_ROI_TOP || 
                                    param == TOUPTEK_PARAM_ROI_WIDTH || 
                                    param == TOUPTEK_PARAM_ROI_HEIGHT)) {
                    continue;
                }
                
                // 3. 在自动曝光模式下跳过手动曝光参数
                if (exposureMode == 1 && (param == TOUPTEK_PARAM_EXPOSURETIME || 
                                        param == TOUPTEK_PARAM_EXPOSUREGAIN)) {
                    continue;
                }
                
                // 4. 在非手动白平衡模式下跳过色温增益参数
                if (wbChoice != 0 && (param == TOUPTEK_PARAM_CTREDGAIN || 
                                    param == TOUPTEK_PARAM_CTGREENGAIN || 
                                    param == TOUPTEK_PARAM_CTBLUEGAIN)) {
                    continue;
                }
                
                // 获取已保存的参数值
                int savedValue = getData(param);
                
                // 只发送已保存过的参数（值不为-1）
                if (savedValue != -1) {
                    sendToDevice(param, savedValue);
                    sentCount++;
                    android.util.Log.d("TouptekIspParam", "发送参数: " + param.name() + " = " + savedValue);
                    
                    // 添加小延时避免过于频繁的发送
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } catch (Exception e) {
                android.util.Log.e("TouptekIspParam", "发送参数 " + param.name() + " 失败: " + e.getMessage());
            }
        }
        android.util.Log.i("TouptekIspParam", "参数发送完成，共发送 " + sentCount + " 个参数");
    }

    /**
     * 根据整数值获取枚举项。
     * <p>
     * 将设备返回的命令值转换为对应的枚举项。
     * </p>
     *
     * @param i 整数值，对应枚举项的value
     * @return 匹配的枚举项，如果没有匹配项则返回null
     */
    public static TouptekIspParam fromInt(int i) 
    {
        for (TouptekIspParam param : TouptekIspParam.values()) 
        {
            if (param.getValue() == i) 
            {
                return param;
            }
        }
        return null; // 没有匹配项
    }

    /**
     * 初始化 TouptekIspParam 和串口通信。
     * <p>
     * 此方法应在应用启动时调用，用于初始化参数存储和串口监控。
     * 初始化过程包括创建SharedPreferences实例、初始化串口通信、设置串口状态回调等。
     * </p>
     *
     * @param context 应用上下文，用于获取 SharedPreferences 实例
     */
    public static void init(Context context) 
    {
        if (sharedPreferences == null) 
        {
            sharedPreferences = context.getSharedPreferences("TouptekIspParams", Context.MODE_PRIVATE);
        }

        if (serialInstance == null) 
        {
            serialInstance = new touptek_serial_rk();
            serialInstance.setDeviceStateCallback(new touptek_serial_rk.DeviceStateCallback() 
            {
                @Override
                public void onDeviceStateChanged(boolean connected) 
                {
                    // 直接调用串口状态监听器，不再设置标志位
                    if (serialStateListener != null) 
                    {
                        serialStateListener.onSerialStateChanged(connected);
                    }
                }
            });
            serialInstance.startMonitor();
        }
    }

    /**
     * 获取串口实例
     * <p>
     * 返回用于串口通信的实例对象。
     * </p>
     *
     * @return touptek_serial_rk 实例
     */
    public static touptek_serial_rk getSerialInstance() 
    {
        return serialInstance;
    }

    /**
     * 检查串口是否已连接
     * <p>
     * 用于判断当前串口的连接状态。
     * </p>
     *
     * @return 串口连接状态，true 表示已连接，false 表示未连接
     */
    public static boolean isSerialConnected() 
    {
        return serialInstance != null && serialInstance.isSerialConnected();
    }

    /**
     * 设置串口状态变化监听器。
     * <p>
     * 当串口连接状态变化时，会触发此监听器的回调方法。
     * </p>
     *
     * @param listener 串口状态变化监听器实例。
     */
    public static void setOnSerialStateChangedListener(OnSerialStateChangedListener listener) 
    {
        serialStateListener = listener;
    }

    /**
     * 停止串口监控和相关资源。
     * <p>
     * 在应用退出或不再需要串口通信时调用。
     * 释放所有相关资源。
     * </p>
     */
    public static void release() 
    {
        if (serialInstance != null) 
        {
            serialInstance.stopMonitor();
            serialInstance.close();
            serialInstance = null;
        }
    }

    /**
     * 添加数据变化监听器
     * <p>
     * 支持多个监听器同时监听参数变化。
     * </p>
     *
     * @param listener 数据变化监听器实例
     */
    public static synchronized void addOnDataChangedListener(OnDataChangedListener listener) {
        if (listener != null && !dataChangedListeners.contains(listener)) {
            dataChangedListeners.add(listener);
        }
    }

    /**
     * 移除数据变化监听器
     * <p>
     * 从监听器列表中移除指定的监听器。
     * </p>
     *
     * @param listener 要移除的监听器实例
     */
    public static synchronized void removeOnDataChangedListener(OnDataChangedListener listener) 
    {
        dataChangedListeners.remove(listener);
    }

    /**
     * 设置数据变化监听器（保留兼容性）
     * <p>
     * 为了保持向后兼容，保留此方法。
     * 推荐使用 addOnDataChangedListener 方法。
     * </p>
     *
     * @param listener 数据变化监听器实例
     * @deprecated 推荐使用 addOnDataChangedListener
     */
    @Deprecated
    public static void setOnDataChangedListener(OnDataChangedListener listener)
    {
        // 清除旧的监听器并添加新的
        synchronized (TouptekIspParam.class) {
            dataChangedListeners.clear();
            if (listener != null) {
                dataChangedListeners.add(listener);
            }
            dataChangedListener = listener; // 保持兼容性
        }
    }

    /**
     * 通知所有监听器数据变化
     * <p>
     * 当参数值发生变化时，通知所有已注册的监听器。
     * </p>
     *
     * @param param 发生变化的参数
     * @param value 新的参数值
     */
    private static synchronized void notifyDataChanged(TouptekIspParam param, int value) 
    {
        // 通知新的监听器列表
        for (OnDataChangedListener listener : dataChangedListeners) 
        {
            if (listener != null) 
            {
                try {
                    listener.onDataChanged(param, value);
                } catch (Exception e) {
                    // 防止某个监听器出错影响其他监听器
                    System.err.println("Error in data change listener: " + e.getMessage());
                }
            }
        }
        
        // 保持向后兼容性
        if (dataChangedListener != null && !dataChangedListeners.contains(dataChangedListener)) 
        {
            try {
                dataChangedListener.onDataChanged(param, value);
            } catch (Exception e) {
                System.err.println("Error in legacy data change listener: " + e.getMessage());
            }
        }
    }

    /**
     * 通知所有监听器长整型数据变化
     * <p>
     * 当长整型参数值发生变化时，通知所有已注册的监听器。
     * </p>
     *
     * @param param 发生变化的参数
     * @param value 新的参数值
     */
    private static synchronized void notifyLongDataChanged(TouptekIspParam param, long value) 
    {
        // 通知新的监听器列表
        for (OnDataChangedListener listener : dataChangedListeners) {
            if (listener != null) {
                try {
                    listener.onLongDataChanged(param, value);
                } catch (Exception e) {
                    System.err.println("Error in long data change listener: " + e.getMessage());
                }
            }
        }
        
        // 保持向后兼容性
        if (dataChangedListener != null && !dataChangedListeners.contains(dataChangedListener)) 
        {
            try {
                dataChangedListener.onLongDataChanged(param, value);
            } catch (Exception e) {
                System.err.println("Error in legacy long data change listener: " + e.getMessage());
            }
        }
    }

    /**
     * 将所有参数恢复为默认值
     * <p>
     * 此方法会等待参数范围请求完成，然后再保存默认值
     * </p>
     *
     * @param sendToDevice 是否将默认值同时发送到设备
     * @return 成功保存的参数数量，-1表示等待超时
     */
    public static int saveAllDefaultValuesToLocal(boolean sendToDevice) {
        int savedCount = 0;
        final int MAX_WAIT_TIME = 5000; // 最大等待时间(毫秒)
        final int CHECK_INTERVAL = 100; // 检查间隔(毫秒)
        int waitTime = 0;

        // 等待参数范围接收完成
        if (!getParamsRangeReceived()) {
            System.out.println("参数范围未接收完成，等待中...");

            // 发送一次参数范围请求
//            requestAllParamRanges();

            // 循环等待参数范围接收完成
            while (!getParamsRangeReceived() && waitTime < MAX_WAIT_TIME) {
                try {
                    Thread.sleep(CHECK_INTERVAL);
                    waitTime += CHECK_INTERVAL;
                    if (waitTime % 1000 == 0) {
                        System.out.println("已等待 " + (waitTime / 1000) + " 秒...");
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.err.println("等待被中断: " + e.getMessage());
                    return -1;
                }
            }

            // 检查是否超时
            if (!getParamsRangeReceived()) {
                System.err.println("等待参数范围接收超时，无法恢复默认值");
                return -1;
            }

            System.out.println("参数范围接收完成，继续保存默认值");
        }

        System.out.println("开始保存所有参数默认值...");

        // 遍历所有参数
        for (TouptekIspParam param : TouptekIspParam.values()) {
            try {
                // 获取默认值
                int defaultValue = getDefaultValue(param);

                // 只有当默认值是有效值时才保存
                if (defaultValue != 555555) {  // 检查是否为无效的默认值标记
                    // 获取当前模式值
                    int exposureMode = getData(TOUPTEK_PARAM_EXPOSURECHOICE);
                    int wbChoice = getData(TOUPTEK_PARAM_WBCHOICE);
                    
                    // 判断是否需要跳过发送
                    boolean skipSendToDevice = false;
                    
                    // 1. 自动曝光模式下不发送手动曝光参数
                    if (exposureMode == 1 && (param == TOUPTEK_PARAM_EXPOSURETIME || param == TOUPTEK_PARAM_EXPOSUREGAIN)) {
                        skipSendToDevice = true;
                    }
                    
                    // 2. 非ROI白平衡模式下不发送ROI参数
                    else if (wbChoice != 2 && (param == TOUPTEK_PARAM_ROI_LEFT || 
                                            param == TOUPTEK_PARAM_ROI_TOP || 
                                            param == TOUPTEK_PARAM_ROI_WIDTH || 
                                            param == TOUPTEK_PARAM_ROI_HEIGHT)) {
                        skipSendToDevice = true;
                    }
                    
                    // 3. 自动白平衡或ROI模式下不发送色温增益参数
                    else if (wbChoice != 0 && (param == TOUPTEK_PARAM_CTREDGAIN || 
                                            param == TOUPTEK_PARAM_CTGREENGAIN || 
                                            param == TOUPTEK_PARAM_CTBLUEGAIN)) {
                        skipSendToDevice = true;
                    }
                    
                    // 根据skipSendToDevice和sendToDevice决定如何处理
                    if (skipSendToDevice) {
                        // 需要跳过发送，只保存到本地
                        saveToLocal(param, defaultValue);
                        System.out.println("仅保存到本地，不发送: " + param.name() + " = " + defaultValue);
                    } else if (sendToDevice) {
                        // 保存到本地并发送到设备
                        updateParam(param, defaultValue);
                    } else {
                        // 根据原始设置，只保存到本地
                        saveToLocal(param, defaultValue);
                    }

                    savedCount++;
                    System.out.println("保存参数默认值: " + param.name() + " = " + defaultValue);
                }
            } catch (Exception e) {
                System.err.println("保存参数 " + param.name() + " 默认值失败: " + e.getMessage());
            }
        }

        System.out.println("默认值保存完成，共保存 " + savedCount + " 个参数");
        return savedCount;
    }

    /**
     * 更新本地参数值，并发送到设备
     * <p>
     * 此方法执行三个操作：保存参数到本地存储、发送参数到设备、通知所有监听器参数变化。
     * 是设置ISP参数的主要方法。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要更新的参数
     * @param value 要更新的参数值
     */
    public static void updateParam(TouptekIspParam param, int value) 
    {
        // 保存到本地
        saveToLocal(param, value);
        
        // 发送到设备
        sendToDevice(param, value);

        // 添加此行来通知监听器
        notifyDataChanged(param, value);

        System.out.println("update ISP Param: " + param.name() + ": " + value);
    }
    
    /**
     * 更新本地参数值，并发送到设备，带控制字节
     * <p>
     * 此方法将执行三个操作：保存到本地、发送到设备
     * 可以指定是读取操作还是写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要更新的参数
     * @param value 要更新的 int 类型数据
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    public static void updateParam(TouptekIspParam param, int value, int ctrl) 
    {
        // 保存到本地
        saveToLocal(param, value);
        
        // 发送到设备
        sendToDevice(param, value, ctrl);

        System.out.println("update ISP Param: " + param.name() + ": " + value + " ctrl: " + (ctrl == 0x01 ? "write" : "read"));
    }
    

    /**
     * 仅保存参数到本地，不发送到设备
     * <p>
     * 使用SharedPreferences存储参数值，以便在应用重启后恢复。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数
     * @param value 要保存的 int 类型数据
     */
    private static void saveToLocal(TouptekIspParam param, int value)
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(param.name(), value);
        editor.apply();
    }
    
    /**
     * 仅保存长整型参数到本地，不发送到设备
     * <p>
     * 使用SharedPreferences存储长整型参数值，主要用于版本号等。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数
     * @param value 要保存的 long 类型数据
     */
    private static void saveToLocal(TouptekIspParam param, long value)
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(param.name(), value);
        editor.apply();
    }
    
    /**
     * 仅发送参数到设备，不保存到本地
     * <p>
     * 通过串口发送命令到设备，默认使用写入或读取控制字节。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     */
    private static void sendToDevice(TouptekIspParam param, int value)
    {
        if (param == TOUPTEK_PARAM_VERSION)
        {
            touptek_serial_rk.sendCommandToSerial(0x00, param.getValue(), value);
        } 
        else 
        {
            touptek_serial_rk.sendCommandToSerial(0x01, param.getValue(), value);
        }
    }

    /**
     * 从相机设备中获取所有默认参数
     * <p>
     * 通过串口获取设备的所有默认参数范围数据。
     * </p>
     *
     */
    public static void requestAllParamRanges()
    {
        /*  阻塞等待设备返回所有参数范围数据
        // 检查串口连接状态
        if (!isSerialConnected()) {
            System.out.println("串口未连接，无法获取设备数据");
            return;
        }

        System.out.println("开始获取所有默认参数数据...");

        // 重置接收状态标志
        setParamsRangeReceived(false);

        // 发送获取所有默认数据的命令
        touptek_serial_rk.sendCommandToSerial(0x00, REQUEST_PARAM_RANGES, 0);

        // 等待数据接收完成
        int waitCount = 0;
        final int maxWaitTime = 20; // 最大等待2秒 (50 * 100ms)

        while (!getParamsRangeReceived() && waitCount < maxWaitTime) {
            try {
                Thread.sleep(100); // 每100ms检查一次
                waitCount++;

                // 每秒打印一次进度（可选）
                if (waitCount % 10 == 0) {
                    System.out.println("等待参数数据接收中... " + (waitCount / 10) + "秒");
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("等待过程被中断");
                break;
            }
        }

        // 检查接收结果
        if (getParamsRangeReceived()) {
            System.out.println("参数数据接收完成，用时: " + (waitCount * 100) + "ms");
        } else {
            System.out.println("参数数据接收超时，可能设备响应较慢或连接异常");
        }
        */
        setParamsRangeReceived(false);
        touptek_serial_rk.sendCommandToSerial(0x00, REQUEST_PARAM_RANGES, 0);
    }

    /**
     * 仅发送参数到设备，不保存到本地，带控制字节
     * <p>
     * 通过串口发送命令到设备，可以指定控制字节。
     * 0x00表示读取操作，0x01表示写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    public static void sendToDevice(TouptekIspParam param, int value, int ctrl) 
    {
        touptek_serial_rk.sendCommandToSerial(ctrl, param.getValue(), value);
    }

    /**
     * 接收到设备数据后处理更新
     * <p>
     * 此方法用于从串口接收到数据后，更新本地参数值并触发回调。
     * 由{@link touptek_serial_rk#onSerialDataReceived(int[])}调用。
     * </p>
     * 
     * @param param 接收到的参数类型
     * @param value 接收到的参数值
     * @param isLongValue 是否为长整型值
     */
    public static void handleReceivedData(TouptekIspParam param, long value, boolean isLongValue) 
    {
        if (isLongValue) 
        {
            saveToLocal(param, value);
            notifyLongDataChanged(param, value);
        } 
        else 
        {
            saveToLocal(param, (int)value);
            notifyDataChanged(param, (int)value);
        }
    }

    /**
     * 获取存储的数据。
     * <p>
     * 从SharedPreferences中读取保存的参数值。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 int 类型数据，如果未找到则返回 -1。
     */
    public static int getData(TouptekIspParam param) 
    {
        return sharedPreferences.getInt(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取存储的 long 类型数据。
     * <p>
     * 从SharedPreferences中读取保存的长整型参数值，主要用于版本号等。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 long 类型数据，如果未找到则返回 -1。
     */
    public static long getLongData(TouptekIspParam param) 
    {
        return sharedPreferences.getLong(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取所有存储的数据。
     * <p>
     * 返回SharedPreferences中所有保存的参数值。
     * </p>
     * 
     * @return 包含所有存储键值对的 {@link Map}，键为参数名称，值为对应的存储数据。
     */
    public static Map<String, ?> getAllData() 
    {
        return sharedPreferences.getAll();
    }

    /**
     * 根据索引获取对应的枚举项。
     * <p>
     * 用于在UI中通过索引选择参数。
     * </p>
     *
     * @param index 索引值，从 0 开始。
     * @return 对应的 {@link TouptekIspParam} 枚举项，如果索引无效则返回 null。
     */
    public static TouptekIspParam getParamByIndex(int index) 
    {
        TouptekIspParam[] params = TouptekIspParam.values();
        if (index >= 0 && index < params.length) 
        {
            return params[index];
        }
        return null;  // 索引无效
    }

    /**
     * 数据变化监听器接口
     * <p>
     * 用于监听ISP参数值变化事件。应用可以实现此接口来接收参数变化通知，
     * 从而实时更新UI或执行其他操作。
     * </p>
     * 
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * TouptekIspParam.addOnDataChangedListener(new TouptekIspParam.OnDataChangedListener() {
     *     @Override
     *     public void onDataChanged(TouptekIspParam param, int newValue) {
     *         // 处理参数变化
     *         updateUI(param, newValue);
     *     }
     *     
     *     @Override
     *     public void onLongDataChanged(TouptekIspParam param, long newValue) {
     *         // 处理长整型参数变化（如版本号）
     *         if (param == TouptekIspParam.TOUPTEK_PARAM_VERSION) {
     *             showVersionInfo(newValue);
     *         }
     *     }
     * });
     * }</pre>
     */
    public interface OnDataChangedListener 
    {
        /**
         * 当 int 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 int 类型值
         */
        void onDataChanged(TouptekIspParam param, int newValue);

        /**
         * 当 long 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 long 类型值
         */
        void onLongDataChanged(TouptekIspParam param, long newValue);
    }

    /**
     * 串口状态变化监听器接口
     * <p>
     * 用于监听串口连接状态变化事件。应用可以实现此接口来接收串口连接/断开通知，
     * 从而执行相应的UI更新或业务逻辑。
     * </p>
     * 
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * TouptekIspParam.setOnSerialStateChangedListener(new TouptekIspParam.OnSerialStateChangedListener() {
     *     @Override
     *     public void onSerialStateChanged(boolean connected) {
     *         if (connected) {
     *             showConnectedStatus();
     *             // 连接后可以请求设备参数
     *             TouptekIspParam.requestAllParamRanges();
     *         } else {
     *             showDisconnectedStatus();
     *         }
     *     }
     * });
     * }</pre>
     */
    public interface OnSerialStateChangedListener 
    {
        /**
         * 当串口状态发生变化时调用。
         *
         * @param connected 串口是否已连接，true表示已连接，false表示已断开
         */
        void onSerialStateChanged(boolean connected);
    }
}
