<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 23 11:03:20 CST 2025 -->
<title>已过时的列表</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-23">
<meta name="description" content="deprecated elements">
<meta name="generator" content="javadoc/DeprecatedListWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="deprecated-list-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#deprecated">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="已过时的 API" class="title">已过时的 API</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#method">方法</a></li>
</ul>
</div>
<ul class="block-list">
<li>
<div id="method">
<div class="caption"><span>Deprecated 方法</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-summary-item-name even-row-color"><a href="com/android/rockchip/camera2/util/TouptekIspParam.html#setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)">com.android.rockchip.camera2.util.TouptekIspParam.setOnDataChangedListener<wbr>(TouptekIspParam.OnDataChangedListener)</a></div>
<div class="col-last even-row-color">
<div class="deprecation-comment">推荐使用 addOnDataChangedListener</div>
</div>
</div>
</div>
</li>
</ul>
</main>
</div>
</div>
</body>
</html>
