package com.android.rockchip.camera2.video;

import android.content.Context;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.media.ImageReader;
import android.util.Log;
import android.view.Surface;
import android.os.Handler;
import android.os.HandlerThread;
import java.util.List;

public class CameraManagerHelper
{
    private static final String TAG = "CameraManagerHelper";

    /* 摄像头设备实例 */
    private CameraDevice cameraDevice;

    /* 摄像头捕获会话实例 */
    private CameraCaptureSession captureSession;

    /* 摄像头管理器，用于管理摄像头设备 */
    private final CameraManager cameraManager;

    /* ImageReader，用于接收摄像头输出的图像数据 */
    private ImageReader imageReader;

    /* 后台线程的 Handler，用于处理摄像头操作 */
    private Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private HandlerThread backgroundThread;

    /**
     * 构造函数，初始化 CameraManager
     *
     * @param context 应用上下文，用于获取 CameraManager
     */
    public CameraManagerHelper(Context context)
    {
        cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
    }

    /**
     * 打开摄像头
     *
     * @param stateCallback 摄像头状态回调，用于处理摄像头的打开、断开和错误事件
     */
    public void openCamera(CameraDevice.StateCallback stateCallback)
    {
        try
        {
            /* 获取摄像头 ID 列表，并打开第一个摄像头 */
            String cameraId = cameraManager.getCameraIdList()[0];
            cameraManager.openCamera(cameraId, stateCallback, null);
        }
        catch (CameraAccessException e)
        {
            Log.e(TAG, "Error opening camera", e);
        }
    }

    /**
     * 启动摄像头预览
     *
     * @param cameraDevice      摄像头设备实例
     * @param previewSurface    用于显示预览的 Surface
     * @param imageReaderSurface 用于接收图像数据的 ImageReader 的 Surface
     */
    public void startCameraPreview(CameraDevice cameraDevice, Surface previewSurface, Surface imageReaderSurface)
    {
        try
        {
            Log.d(TAG, "Starting camera preview");
            this.cameraDevice = cameraDevice;

            /* 启动后台线程 */
            startBackgroundThread();

            /* 创建捕获请求 */
            CaptureRequest.Builder builder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            builder.addTarget(previewSurface); // 添加预览 Surface
            builder.addTarget(imageReaderSurface); // 添加 ImageReader 的 Surface

            /* 创建捕获会话 */
            cameraDevice.createCaptureSession(
                List.of(previewSurface, imageReaderSurface),
                new CameraCaptureSession.StateCallback()
                {
                    @Override
                    public void onConfigured(CameraCaptureSession session)
                    {
                        captureSession = session;
                        try
                        {
                            /* 设置重复请求以持续预览 */
                            captureSession.setRepeatingRequest(builder.build(), null, backgroundHandler);
                            Log.d(TAG, "Camera preview started successfully");
                        }
                        catch (CameraAccessException e)
                        {
                            Log.e(TAG, "Error starting camera preview", e);
                        }
                    }

                    @Override
                    public void onConfigureFailed(CameraCaptureSession session)
                    {
                        Log.e(TAG, "Camera configuration failed");
                    }
                },
                backgroundHandler
            );
        }
        catch (CameraAccessException e)
        {
            Log.e(TAG, "Error starting camera preview", e);
        }
    }

    /**
     * 启动后台线程
     */
    private void startBackgroundThread()
    {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }

    /**
     * 停止后台线程
     */
    private void stopBackgroundThread()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
                backgroundThread = null;
                backgroundHandler = null;
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }

    /**
     * 释放摄像头资源
     */
    public void releaseCamera()
    {
        /* 关闭捕获会话 */
        if (captureSession != null)
        {
            captureSession.close();
            captureSession = null;
        }

        /* 关闭摄像头设备 */
        if (cameraDevice != null)
        {
            cameraDevice.close();
            cameraDevice = null;
        }

        /* 关闭 ImageReader */
        if (imageReader != null)
        {
            imageReader.close();
            imageReader = null;
        }

        /* 停止后台线程 */
        stopBackgroundThread();
    }
}
