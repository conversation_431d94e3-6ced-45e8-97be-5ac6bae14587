// ***************************************************************
//  cap_pack   version:  1.0                         
//  ------------------------------------------------------------ 
//  This file was generated by vxgen.exe, don't manually modify.   
//  Hangzhou Zhidu Network Ltd.  (<EMAIL>)               
//  Copyright (C) 2012-2017 - All Rights Reserved
// *************************************************************** 

#ifndef _cap_pack_h_
#define _cap_pack_h_

#include <vector>
#include <string>
#include <utiny/usys_bytes.h>
#include <utiny/gep_header.h>
#include <utiny/gep_request_context.h>
#include <utiny/gep_response_context.h>
#include <utiny/usys_smartptr.h>
#include <utiny/ustdcpp_inc.h>
#include <utiny/usys_data_block.h>
#include "cap.h"
namespace gep
{
    int gep_pack(const IE_RESULT& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_RESULT& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_RESULT& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_RESULT& _t, uidl_uchar version);
    int gep_pack(const IE_Capture& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_Capture& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_Capture& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_Capture& _t, uidl_uchar version);
    int gep_pack(const IE_Picture& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_Picture& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_Picture& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_Picture& _t, uidl_uchar version);
    int gep_pack(const IE_String& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_String& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_String& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_String& _t, uidl_uchar version);
    int gep_pack(const IE_Uint32& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_Uint32& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_Uint32& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_Uint32& _t, uidl_uchar version);
    int gep_pack(const IE_DirItem& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_DirItem& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_DirItem& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_DirItem& _t, uidl_uchar version);
    int gep_pack(const IE_DirChange& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(IE_DirChange& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const IE_DirChange& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const IE_DirChange& _t, uidl_uchar version);
    int gep_pack(const MSG_Capture_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_Capture_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_Capture_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_Capture_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_Capture_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_Capture_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_Capture_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_Capture_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_ListWifi_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ListWifi_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ListWifi_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ListWifi_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_ListWifi_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ListWifi_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ListWifi_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ListWifi_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_ListDir_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ListDir_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ListDir_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ListDir_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_ListDir_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ListDir_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ListDir_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ListDir_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_Thumbnail_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_Thumbnail_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_Thumbnail_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_Thumbnail_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_Thumbnail_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_Thumbnail_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_Thumbnail_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_Thumbnail_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_ChangeDir_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ChangeDir_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ChangeDir_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ChangeDir_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_ChangeDir_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_ChangeDir_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_ChangeDir_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_ChangeDir_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_NotifyDir_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_NotifyDir_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_NotifyDir_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_NotifyDir_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_NotifyDir_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_NotifyDir_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_NotifyDir_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_NotifyDir_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_RecordStart_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_RecordStart_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_RecordStart_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_RecordStart_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_RecordStart_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_RecordStart_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_RecordStart_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_RecordStart_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_RecordStop_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_RecordStop_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_RecordStop_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_RecordStop_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_RecordStop_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_RecordStop_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_RecordStop_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_RecordStop_RES& _t, uidl_uchar version);
    int gep_pack(const MSG_DateTime_REQ& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_DateTime_REQ& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_DateTime_REQ& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_DateTime_REQ& _t, uidl_uchar version);
    int gep_pack(const MSG_DateTime_RES& _t, uidl_uchar* buf, int size, uidl_uchar version);
    int gep_unpack(MSG_DateTime_RES& _t, const uidl_uchar* buf, int size, uidl_uchar version);
    uidl_int32 gep_body_length(const MSG_DateTime_RES& _t, uidl_uchar version);
    uidl_int32 gep_total_length(const MSG_DateTime_RES& _t, uidl_uchar version);
    template<typename T>
    class gep_session_tpl : public gep::gep_session_context
    { 
        public:
        int proc_packet(const gep::gep_packet_context_ptr& ctx, const usys_smartptr<usys_data_block>& data_ptr)
        {
            switch (ctx->msg_type())
            {
                case E_MSG_Capture_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_Capture_REQ, true>(ctx, data_ptr);
                case E_MSG_Capture_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_Capture_RES, true>(ctx, data_ptr);
                case E_MSG_ListWifi_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ListWifi_REQ, false>(ctx, data_ptr);
                case E_MSG_ListWifi_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ListWifi_RES, true>(ctx, data_ptr);
                case E_MSG_ListDir_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ListDir_REQ, true>(ctx, data_ptr);
                case E_MSG_ListDir_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ListDir_RES, true>(ctx, data_ptr);
                case E_MSG_Thumbnail_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_Thumbnail_REQ, true>(ctx, data_ptr);
                case E_MSG_Thumbnail_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_Thumbnail_RES, true>(ctx, data_ptr);
                case E_MSG_ChangeDir_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ChangeDir_REQ, true>(ctx, data_ptr);
                case E_MSG_ChangeDir_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_ChangeDir_RES, true>(ctx, data_ptr);
                case E_MSG_NotifyDir_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_NotifyDir_REQ, true>(ctx, data_ptr);
                case E_MSG_NotifyDir_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_NotifyDir_RES, true>(ctx, data_ptr);
                case E_MSG_RecordStart_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_RecordStart_REQ, true>(ctx, data_ptr);
                case E_MSG_RecordStart_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_RecordStart_RES, true>(ctx, data_ptr);
                case E_MSG_RecordStop_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_RecordStop_REQ, false>(ctx, data_ptr);
                case E_MSG_RecordStop_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_RecordStop_RES, true>(ctx, data_ptr);
                case E_MSG_DateTime_REQ:
                return ((T*)this)->template proc_packet_tpl<T, MSG_DateTime_REQ, true>(ctx, data_ptr);
                case E_MSG_DateTime_RES:
                return ((T*)this)->template proc_packet_tpl<T, MSG_DateTime_RES, true>(ctx, data_ptr);
                default: 
                return -1;
            }
        };
    };
} //namespace gep
#endif //_cap_pack_h_
