#include <jni.h>
#include <string>
#include <fcntl.h>
#include <unistd.h>
#include <termios.h>
#include <pthread.h>
#include <android/log.h>
#include <errno.h>
#include <dirent.h>
#include <sys/inotify.h>
#include <string.h>

#define LOG_TAG "SerialJNI"
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG,__VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

/* Default baud rate */
int g_iDefBaudRate = 921600;

/* Serial port file descriptor */
int g_iSerialFd = -1;
pthread_t g_tRecvThread;
bool g_bRunning = false;
JavaVM *g_pJvm = nullptr;
jobject g_pObj = nullptr;
jmethodID g_midMethod = nullptr;
/* Buffer to wrap the command to be sent */
unsigned char g_ucSendBuffer[10];
pthread_mutex_t g_mtxSend = PTHREAD_MUTEX_INITIALIZER;

pthread_t g_tMonitorThread;
bool g_bMonitoring = true;
jmethodID g_midDeviceCallback = nullptr;
pthread_mutex_t g_mtxSerial = PTHREAD_MUTEX_INITIALIZER;

/**
 * @brief 串口数据接收线程函数
 *
 * 该线程以非阻塞模式从串口读取数据，当接收到符合特定格式（以 0xFA、0xFB 开头，0xFC、0xFD 结尾）
 * 的完整数据包时，将数据封装成 Java 整数数组，并调用 Java 回调方法进行处理。
 *
 * @param arg 线程参数，此处未使用
 * @return void* 线程返回值，通常为 nullptr
 */
void *serial_recv_thread(void *arg)
{
    JNIEnv *env;
    if (g_pJvm->AttachCurrentThread(&env, nullptr) != JNI_OK)
    {
        LOGE("Failed to attach thread to JVM");
        return nullptr;
    }

    unsigned char cBuffer[10];

    /* Set the serial port to non - blocking mode */
    int flags = fcntl(g_iSerialFd, F_GETFL, 0);
    if (flags < 0)
    {
        LOGE("Failed to get file descriptor flags");
        return nullptr;
    }
    fcntl(g_iSerialFd, F_SETFL, flags | O_NONBLOCK);

    while (g_bRunning)
    {
        int bytes_read = read(g_iSerialFd, cBuffer, sizeof(cBuffer));
        if (bytes_read < 0)
        {
            usleep(10000);  /* If no data, sleep for 100ms to avoid high CPU usage */
            continue;
        }

        /* Log the number of received bytes */
        // LOGI("Received %d bytes", bytes_read);

        if (bytes_read >= 10 && cBuffer[0] == 0xFA && cBuffer[1] == 0xFB &&cBuffer[8] == 0xFC && cBuffer[9] == 0xFD)
        {

            jint data[6];
            data[0] = (jint)cBuffer[2];
            for (int i = 0; i < 5; i++)
            {
                data[i + 1] = (jint)cBuffer[3 + i];
                /* Log the data element */
                // LOGI("data[%d] = %x", i, data[i]);
            }

            jintArray result = env->NewIntArray(6);
            if (result != nullptr)
            {
                env->SetIntArrayRegion(result, 0, 6, data);
                env->CallVoidMethod(g_pObj, g_midMethod, result);
                env->DeleteLocalRef(result);
            }
        }
        usleep(10000);  /* Sleep for 100ms per loop to avoid high CPU usage */
    }
    g_pJvm->DetachCurrentThread();
    return nullptr;
}

/**
 * @brief 配置串口的属性
 *
 * 该函数用于设置串口的波特率、数据位、停止位、奇偶校验等属性。
 *
 * @param fd 串口文件描述符
 * @param baudRate 要设置的波特率
 * @return int 配置成功返回 0，失败返回 -1
 */
int configure_serial_port(int fd, int baudRate)
{
    struct termios options;
    if (tcgetattr(fd, &options) < 0)
    {
        LOGE("Failed to get serial port attributes");
        return -1;
    }

    cfsetispeed(&options, baudRate);
    cfsetospeed(&options, baudRate);
    options.c_cflag &= ~PARENB;
    options.c_cflag &= ~CSTOPB;
    options.c_cflag &= ~CSIZE;
    options.c_cflag |= CS8;
    options.c_cflag |= CLOCAL | CREAD;
    options.c_iflag &= ~(IXON | IXOFF | IXANY);
    options.c_oflag &= ~OPOST;
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

    //解决0x0D无法被识别问题
    options.c_iflag &=~(INLCR|IGNCR|ICRNL);
    options.c_oflag &=~(ONLCR|OCRNL);

    if (tcsetattr(fd, TCSANOW, &options) < 0)
    {
        LOGE("Failed to set serial port attributes");
        return -1;
    }
    return 0;
}

/**
 * @brief 设备监控线程函数
 *
 * 该线程负责监控串口设备的插拔事件。启动时会检查是否已有 ttyACM 设备，
 * 若有则尝试打开并配置串口；之后使用 inotify 机制监听 /dev 目录下的设备创建和删除事件，
 * 根据事件结果进行相应的处理，如打开或关闭串口，并调用 Java 回调方法通知设备状态变化。
 *
 * @param arg 线程参数，此处未使用
 * @return void* 线程返回值，通常为 nullptr
 */
void *device_monitor_thread(void *arg)
{
    JNIEnv *env;
    if (g_pJvm->AttachCurrentThread(&env, nullptr) != JNI_OK)
    {
        LOGE("Failed to attach thread to JVM");
        return nullptr;
    }

    /* 1. Check if there is a ttyACM device when the thread starts */
    DIR *pDir = opendir("/dev/");
    if (pDir != nullptr)
    {
        struct dirent *stuEntry;
        while ((stuEntry = readdir(pDir)) != nullptr)
        {
            if (strncmp(stuEntry->d_name, "ttyACM", 6) == 0)
            {
                LOGI("Detected existing device: %s", stuEntry->d_name);

                pthread_mutex_lock(&g_mtxSerial);
                if (g_iSerialFd < 0)
                {
                    char cDevicePath[256];
                    snprintf(cDevicePath, sizeof(cDevicePath), "/dev/%s", stuEntry->d_name);
                    g_iSerialFd = open(cDevicePath, O_RDWR | O_NOCTTY);
                    if (g_iSerialFd >= 0)
                    {
                        LOGI("Successfully opened serial port device: %s", cDevicePath);
                        if (configure_serial_port(g_iSerialFd, g_iDefBaudRate) == 0)
                        {
                            g_bRunning = true;
                            pthread_create(&g_tRecvThread, nullptr, serial_recv_thread, nullptr);
                            env->CallVoidMethod(g_pObj, g_midDeviceCallback, JNI_TRUE);
                        }
                        else
                        {
                            close(g_iSerialFd);
                            g_iSerialFd = -1;
                        }
                    }
                }
                pthread_mutex_unlock(&g_mtxSerial);
                break;
            }
        }
        closedir(pDir);
    }

    /* 2. Continue the inotify monitoring logic */
    int iInotify_fd = inotify_init();
    if (iInotify_fd < 0)
    {
        LOGE("Unable to initialize inotify");
        g_pJvm->DetachCurrentThread();
        return nullptr;
    }

    int watch_fd = inotify_add_watch(iInotify_fd, "/dev/", IN_CREATE | IN_DELETE);
    if (watch_fd < 0)
    {
        LOGE("Unable to listen to the /dev directory");
        close(iInotify_fd);
        g_pJvm->DetachCurrentThread();
        return nullptr;
    }

    char cBuffer[1024];
    while (g_bMonitoring)
    {
        int length = read(iInotify_fd, cBuffer, sizeof(cBuffer));
        if (length < 0)
        {
            usleep(50000);
            continue;
        }

        int i = 0;
        while (i < length)
        {
            struct inotify_event *pEvent = (struct inotify_event *)&cBuffer[i];
            if (pEvent->len > 0 && strncmp(pEvent->name, "ttyACM", 6) == 0)
            {
                if (pEvent->mask & IN_CREATE)
                {
                    LOGI("Device insertion detected: %s", pEvent->name);

                    pthread_mutex_lock(&g_mtxSerial);
                    if (g_iSerialFd < 0)
                    {
                        char device_path[256];
                        snprintf(device_path, sizeof(device_path), "/dev/%s", pEvent->name);
                        g_iSerialFd = open(device_path, O_RDWR | O_NOCTTY);
                        if (g_iSerialFd >= 0)
                        {
                            LOGI("Serial port device is successfully enabled: %s", device_path);
                            if (configure_serial_port(g_iSerialFd, g_iDefBaudRate) == 0)
                            {
                                g_bRunning = true;
                                pthread_create(&g_tRecvThread, nullptr, serial_recv_thread, nullptr);
                                env->CallVoidMethod(g_pObj, g_midDeviceCallback, JNI_TRUE);
                            }
                            else
                            {
                                close(g_iSerialFd);
                                g_iSerialFd = -1;
                            }
                        }
                    }
                    pthread_mutex_unlock(&g_mtxSerial);
                }
                else if (pEvent->mask & IN_DELETE)
                {
                    LOGI("Device is detected unplugged: %s", pEvent->name);
                    pthread_mutex_lock(&g_mtxSerial);
                    if (g_iSerialFd >= 0)
                    {
                        g_bRunning = false;
                        pthread_join(g_tRecvThread, nullptr);
                        close(g_iSerialFd);
                        g_iSerialFd = -1;
                        env->CallVoidMethod(g_pObj, g_midDeviceCallback, JNI_FALSE);
                    }
                    pthread_mutex_unlock(&g_mtxSerial);
                }
            }
            i += sizeof(struct inotify_event) + pEvent->len;
        }
    }

    inotify_rm_watch(iInotify_fd, watch_fd);
    close(iInotify_fd);
    g_pJvm->DetachCurrentThread();
    return nullptr;
}

/**
 * @brief 启动设备监控线程
 *
 * 该函数用于初始化 JNI 相关资源，查找 Java 回调方法，然后启动设备监控线程。
 *
 * @param env JNI 环境指针
 * @param obj Java 对象引用
 * @return int 启动成功返回 JNI_TRUE，失败返回 JNI_FALSE
 */
extern "C" JNIEXPORT int JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_startMonitor(JNIEnv *env, jobject obj)
{
    /* Get the JavaVM and other necessary object references */
    env->GetJavaVM(&g_pJvm);
    g_pObj = env->NewGlobalRef(obj);

    jclass clazz = env->GetObjectClass(obj);
    g_midMethod = env->GetMethodID(clazz, "onSerialDataReceived", "([I)V");
    if (g_midMethod == nullptr)
    {
        LOGE("Failed to find Java callback method");
        return JNI_FALSE;
    }

    g_midDeviceCallback = env->GetMethodID(clazz, "onDeviceStateChanged", "(Z)V");
    if (!g_midDeviceCallback)
    {
        LOGE("Failed to find the Java device state callback method");
        return JNI_FALSE;
    }

    g_bMonitoring = true;
    if (pthread_create(&g_tMonitorThread, nullptr, device_monitor_thread, nullptr) != 0)
    {
        LOGE("Failed to create the device monitoring thread");
        return JNI_FALSE;
    }

    LOGI("Device monitoring thread has been started");
    return JNI_TRUE;
}

/**
 * @brief 停止设备监控线程
 *
 * 该函数用于停止设备监控线程，关闭串口连接，并释放相关的 JNI 资源。
 *
 * @param env JNI 环境指针
 * @param obj Java 对象引用
 */
extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_stopMonitor(JNIEnv *env, jobject obj)
{
    g_bMonitoring = false;
    pthread_join(g_tMonitorThread, nullptr);

    if (g_iSerialFd >= 0)
    {
        g_bRunning = false;
        pthread_join(g_tRecvThread, nullptr);
        close(g_iSerialFd);
        g_iSerialFd = -1;
    }

    if (g_pObj)
    {
        env->DeleteGlobalRef(g_pObj);
        g_pObj = nullptr;
    }
    LOGI("Device monitoring thread has been stopped");
}

/**
 * @brief 初始化串口
 *
 * 该函数用于查找可用的 ttyACM 串口设备，打开并配置串口，然后启动数据接收线程。
 *
 * @param env JNI 环境指针
 * @param obj Java 对象引用
 * @param baudRate 要设置的波特率
 * @return int 初始化成功返回 JNI_TRUE，失败返回 JNI_FALSE
 */
extern "C" JNIEXPORT int JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_initSerial(JNIEnv *env, jobject obj, jint baudRate)
{
    /* Get the JavaVM and other necessary object references */
    env->GetJavaVM(&g_pJvm);
    g_pObj = env->NewGlobalRef(obj);

    jclass clazz = env->GetObjectClass(obj);
    g_midMethod = env->GetMethodID(clazz, "onSerialDataReceived", "([I)V");
    if (g_midMethod == nullptr)
    {
        LOGE("Failed to find Java callback method");
        return JNI_FALSE;
    }

    /* Open the /dev directory to find all ttyACM devices */
    DIR *pDir = opendir("/dev/");
    if (pDir == nullptr)
    {
        LOGE("Failed to open /dev/ directory");
        return JNI_FALSE;
    }

    struct dirent *stuEntry;
    bool bFoundSerial = false;

    while ((stuEntry = readdir(pDir)) != nullptr)
    {
        /* Find devices starting with "ttyACM" */
        if (strncmp(stuEntry->d_name, "ttyACM", 6) == 0)
        {
            /* Construct the full path of the device file */
            char device_path[256];
            snprintf(device_path, sizeof(device_path), "/dev/%s", stuEntry->d_name);

            /* Try to open this serial port device */
            g_iSerialFd = open(device_path, O_RDWR | O_NOCTTY);
            if (g_iSerialFd >= 0)
            {
                LOGI("Successfully opened serial port: %s", device_path);
                bFoundSerial = true;
                break;
            }
        }
    }

    closedir(pDir);

    /* If no available serial port device is found */
    if (!bFoundSerial)
    {
        LOGE("Failed to open any serial port");
        return JNI_FALSE;
    }

    /* Configure the serial port attributes */
    if (configure_serial_port(g_iSerialFd, baudRate) != 0)
    {
        LOGE("Failed to configure serial port");
        close(g_iSerialFd);
        return JNI_FALSE;
    }
    g_iDefBaudRate = baudRate;

    g_bRunning = true;
    if (pthread_create(&g_tRecvThread, nullptr, serial_recv_thread, nullptr) != 0)
    {
        LOGE("Failed to create receive thread");
        return JNI_FALSE;
    }
    LOGI("Serial port initialized successfully");
    return JNI_TRUE;
}

/**
 * @brief 向串口发送命令
 *
 * 该函数用于向已打开且正在运行的串口发送命令。
 * 命令数据会被封装成特定格式的数据包后发送。
 *
 * @param env JNI 环境指针
 * @param obj Java 对象引用
 * @param ctrl 控制字节
 * @param command 命令字节
 * @param data 包含命令数据的 Java 整数数组
 */
extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_sendCommand(JNIEnv *env, jobject obj, jint ctrl, jint command, jintArray data)
{
    /* Check if the serial port is open and running */
    if ((g_iSerialFd < 0) || (g_bRunning == false))
    {
        LOGE("Serial port not open");
        return;
    }

    /* Get the elements of the Java int array */
    jint *dataArr = env->GetIntArrayElements(data, nullptr);
    if (dataArr == nullptr)
    {
        LOGE("Failed to get data array");
        return;
    }

    /* Construct the packet to be sent */
    g_ucSendBuffer[0] = 0xfa;
    g_ucSendBuffer[1] = 0xfb;
    g_ucSendBuffer[2] = (unsigned char)ctrl;
    g_ucSendBuffer[3] = (unsigned char)command;

    for (int i = 0; i < 4; i++)
    {
        g_ucSendBuffer[4 + i] = (unsigned char)(dataArr[i] & 0xFF);
    }
    g_ucSendBuffer[8] = 0xfc;
    g_ucSendBuffer[9] = 0xfd;

    /* Lock the send mutex */
    pthread_mutex_lock(&g_mtxSend);

    /* Write the packet to the serial port */
    ssize_t bytes_written = write(g_iSerialFd, g_ucSendBuffer, 10);
    if (bytes_written != sizeof(g_ucSendBuffer))
    {
        LOGE("Failed to send data, error=%s", strerror(errno));
    }
    else
    {
        LOGI("%s: Sent param:%d = %d", __func__,
            g_ucSendBuffer[3],
            (g_ucSendBuffer[4]<<24 | g_ucSendBuffer[5]<<16 | g_ucSendBuffer[6]<<8 | g_ucSendBuffer[7]));
    }

    /* Wait for the send buffer to clear */
    // tcdrain(g_iSerialFd);
    usleep(10000);

    /* Unlock the send mutex */
    pthread_mutex_unlock(&g_mtxSend);

    /* Release the Java int array elements */
    env->ReleaseIntArrayElements(data, dataArr, 0);
}

/**
 * @brief 检查串口是否连接
 *
 * 该函数用于检查是否存在可用的 ttyACM 串口设备。
 *
 * @param env JNI 环境指针
 * @param thiz Java 对象引用
 * @return jboolean 若找到串口设备返回 JNI_TRUE，否则返回 JNI_FALSE
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_isSerialConnected(JNIEnv *env, jobject thiz)
{
    /* Open the /dev directory */
    DIR *pDir = opendir("/dev/");
    if (pDir == nullptr)
    {
        LOGI("Failed to open /dev directory");
        return JNI_FALSE; /* Return false if the directory cannot be opened */
    }

    struct dirent *stuEntry;
    jboolean result = JNI_FALSE; /* Assume the serial port is not connected by default */

    while ((stuEntry = readdir(pDir)) != nullptr)
    {
        if (strncmp(stuEntry->d_name, "ttyACM", 6) == 0)
        { /* Find a ttyACM device */
            LOGI("Serial port alive: %s", stuEntry->d_name);
            result = JNI_TRUE;
            break; /* Break the loop after finding the first matching device */
        }
    }

    closedir(pDir); /* Close the directory to prevent resource leaks */
    return result;
}

/**
 * @brief 关闭串口
 *
 * 该函数用于停止数据接收线程，关闭串口连接，并释放相关的 JNI 资源。
 *
 * @param env JNI 环境指针
 * @param obj Java 对象引用
 */
extern "C" JNIEXPORT void JNICALL
Java_com_android_rockchip_camera2_util_touptek_1serial_1rk_closeSerial(JNIEnv *env, jobject obj)
{
    g_bRunning = false;
    pthread_join(g_tRecvThread, nullptr);

    if (g_iSerialFd >= 0)
    {
        close(g_iSerialFd);
        g_iSerialFd = -1;
    }
    if (g_pObj != nullptr)
    {
        env->DeleteGlobalRef(g_pObj);
        g_pObj = nullptr;
    }
    LOGI("Serial port closed");
}
