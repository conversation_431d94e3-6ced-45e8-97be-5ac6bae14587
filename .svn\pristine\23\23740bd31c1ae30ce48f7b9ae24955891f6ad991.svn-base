<?xml version="1.0" encoding="utf-8"?>
<!-- 设置按钮圆形样式 -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#40FFFFFF">
    
    <item android:id="@android:id/background">
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <shape android:shape="oval">
                    <solid android:color="#FF333333" />
                    <stroke android:width="1dp" android:color="#66FFFFFF" />
                </shape>
            </item>
            
            <!-- 禁用状态 -->
            <item android:state_enabled="false">
                <shape android:shape="oval">
                    <solid android:color="#FF1A1A1A" />
                    <stroke android:width="1dp" android:color="#33FFFFFF" />
                </shape>
            </item>
            
            <!-- 正常状态 -->
            <item>
                <shape android:shape="oval">
                    <solid android:color="#FF2A2A2A" />
                    <stroke android:width="1dp" android:color="#4DFFFFFF" />
                </shape>
            </item>
        </selector>
    </item>
    
</ripple>
