/* ***************************************************************
//  usys_network   version:  1.0  date: 6/5/2009
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __usys_network_h__
#define __usys_network_h__
#include <cstdarg>
#include <string>
#include <vector>
#include <utiny/utiny_config.h>

class UBEDA_API usys_network
{
public:
    static std::vector<sockaddr_storage> get_local_addresses(int af = AF_INET);
    static bool is_wildcard(const char* host);
    static int get_interface_index(const std::string& name);
    static in_addr get_interface_address(const std::string& name);

    static bool interrupted();
    static bool accept_interrupted();
    static bool would_block();
    static bool connect_failed();
    static bool connection_refused();
    static bool connect_in_progress();
    static bool connection_lost();
    static bool not_connected();
    static bool check_connection(SOCKET s);

    static SOCKET create_socket(bool, int);
    static void set_tcp_no_delay(SOCKET fd);
    static void close_socket(SOCKET);
    static void close_socket_no_throw(SOCKET);

    static void set_block(SOCKET, bool);
    static void set_keep_alive(SOCKET);
    static void set_send_buffer_size(SOCKET, int);
    static int get_send_buffer_size(SOCKET);
    static void set_recv_buffer_size(SOCKET, int);
    static int get_recv_buffer_size(SOCKET);
    static void set_reuse_address(SOCKET, bool);

    static unsigned short do_bind(SOCKET, sockaddr_storage&);
    static bool do_listen(SOCKET, int);
    static bool do_connect(SOCKET, sockaddr_storage&);
    static void do_finish_connect(SOCKET);
    static SOCKET do_accept(SOCKET);

    static int get_address_for_server(const char*, int, sockaddr_storage&);
    static int get_address(const char*, int, sockaddr_storage&);

    static int compare_address(const sockaddr_storage&, const sockaddr_storage&);

    static void create_pipe(SOCKET fds[2]);

    static std::string fd_to_string(SOCKET);
    static std::string addresses_to_string(const sockaddr_storage&, const sockaddr_storage&, bool);
    static void fd_to_local_address(SOCKET, sockaddr_storage&);
    static bool fd_to_remote_address(SOCKET, sockaddr_storage&);
    static std::string inet_addr_to_string(const sockaddr_storage&);
    static std::string addr_to_string(const sockaddr_storage&);
    static bool is_multicast(const sockaddr_storage&);
    static int get_port(const sockaddr_storage&);

    static void set_tcp_buf_size(SOCKET, int recv_size, int send_size);

    static int get_socket_errno();
};

#endif // __usys_network_h__
