package com.android.rockchip.camera2.video;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Size;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ImageSocketService - 处理图像socket通信服务
 * 
 * <p>此服务监听socket连接，接收图像请求，
 * 使用CaptureImageHelper捕获图像并发送响应。</p>
 * 
 */
public class ImageSocketService 
{
    /* 日志标签 */
    private static final String TAG = "ImageSocketService";
    
    /* 服务端口号 */
    private static final int PORT = 12345;
    
    /* 请求图像命令码 */
    private static final int CMD_REQUEST_IMAGE = 1;
    
    /* 缩略图命令码 */
    private static final int CMD_THUMBNAIL = 1;
    
    /* 全尺寸图像命令码 */
    private static final int CMD_FULLSIZE = 0;
    
    /* 缩略图宽度 */
    private static final int THUMBNAIL_WIDTH = 352;
    
    /* 缩略图高度 */
    private static final int THUMBNAIL_HEIGHT = 288;
    
    /* 服务运行状态标志 */
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    /* 服务器Socket */
    private ServerSocket serverSocket = null;
    
    /* 服务器线程 */
    private Thread serverThread = null;
    
    /* 主线程Handler */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /* 图像捕获助手实例 */
    private CaptureImageHelper captureHelper;
    
    /* 临时图像文件路径 */
    private String tempImagePath;
    
    /**
     * 日志监听接口，用于外部接收服务日志
     */
    public interface LogListener 
    {
        /**
         * 当有新的日志消息时调用
         * 
         * @param message 日志消息
         */
        void onLogMessage(String message);
    }
    
    /* 日志监听器 */
    private LogListener logListener;
    
    /* 线程池，用于处理客户端连接 */
    private final ExecutorService executor = Executors.newCachedThreadPool();
    
    /**
     * 构造函数
     * 
     * @param captureHelper 图像捕获助手实例
     * @param tempImagePath 临时图像保存路径
     */
    public ImageSocketService(CaptureImageHelper captureHelper, String tempImagePath) 
    {
        this.captureHelper = captureHelper;
        this.tempImagePath = tempImagePath;
    }
    
    /**
     * 设置日志监听器
     * 
     * @param listener 日志监听器实例
     */
    public void setLogListener(LogListener listener) 
    {
        this.logListener = listener;
    }
    
    /**
     * 获取服务运行状态
     * 
     * @return 如果服务正在运行返回true，否则返回false
     */
    public boolean isRunning() 
    {
        return isRunning.get();
    }
    
    /**
     * 启动socket服务
     * <p>
     * 创建一个新线程来监听指定端口上的连接请求。
     * 如果服务已经在运行，此方法不会执行任何操作。
     * </p>
     */
    public void start() 
    {
        if (isRunning.get()) 
        {
            Log.e(TAG, "Service is already running, will not start again");
            return;
        }
        
        isRunning.set(true);
        serverThread = new Thread(this::runServer);
        serverThread.start();
        Log.e(TAG, "Image Socket Service started, listening on port: " + PORT);
    }
    
    /**
     * 停止socket服务
     * <p>
     * 关闭服务器socket和线程，清理资源。
     * 如果服务未运行，此方法不会执行任何操作。
     * </p>
     */
    public void stop() 
    {
        if (!isRunning.get()) return;
        
        isRunning.set(false);
        
        if (serverSocket != null) 
        {
            try 
            {
                serverSocket.close();
                Log.d(TAG, "Server socket closed");
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing server socket: " + e.getMessage());
            }
            serverSocket = null;
        }
        
        if (serverThread != null) 
        {
            serverThread.interrupt();
            serverThread = null;
            Log.d(TAG, "Server thread terminated");
        }
        
        /* 关闭线程池 */
        executor.shutdown();
    }
    
    /**
     * 服务器主线程
     * <p>
     * 持续监听连接请求，并为每个客户端创建一个处理线程。
     * </p>
     */
    private void runServer() 
    {
        try 
        {
            serverSocket = new ServerSocket(PORT);
            Log.e(TAG, "Socket server started on port: " + PORT);
            
            while (isRunning.get()) 
            {
                try 
                {
                    Log.d(TAG, "Waiting for client connection...");
                    Socket clientSocket = serverSocket.accept();
                    Log.d(TAG, "Client connected: " + clientSocket.getInetAddress().getHostAddress());
                    
                    /* 在新线程中处理客户端请求 */
                    executor.execute(() -> handleClient(clientSocket));
                } 
                catch (IOException e) 
                {
                    if (isRunning.get()) 
                    {
                        Log.e(TAG, "Error accepting client connection: " + e.getMessage());
                    }
                }
            }
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error creating server socket: " + e.getMessage());
        } 
        finally 
        {
            if (serverSocket != null && !serverSocket.isClosed()) 
            {
                try 
                {
                    serverSocket.close();
                } 
                catch (IOException e) 
                {
                    Log.e(TAG, "Error closing server socket: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 处理客户端连接
     * <p>
     * 读取客户端命令并执行相应操作。
     * </p>
     * 
     * @param clientSocket 客户端socket连接
     */
    private void handleClient(Socket clientSocket) 
    {
        try 
        {
            clientSocket.setSoTimeout(10000); /* 10秒超时 */
            
            InputStream inputStream = clientSocket.getInputStream();
            OutputStream outputStream = clientSocket.getOutputStream();
            
            /* 读取命令 */
            byte[] cmdBuffer = new byte[2];
            int bytesRead = inputStream.read(cmdBuffer);
            
            if (bytesRead != 2) 
            {
                Log.e(TAG, "Invalid command length: " + bytesRead);
                return;
            }
            
            int cmd = cmdBuffer[0];
            int type = cmdBuffer[1]; /* thumbnail or fullsize */
            
            Log.d(TAG, "Received command: " + cmd + ", type: " + (type == CMD_THUMBNAIL ? "thumbnail" : "fullsize"));
            
            if (cmd == CMD_REQUEST_IMAGE) 
            {
                /* 捕获并发送图像 */
                captureAndSendImage(outputStream, type == CMD_THUMBNAIL);
            } 
            else 
            {
                Log.w(TAG, "Unknown command: " + cmd);
            }
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error handling client: " + e.getMessage());
        } 
        finally 
        {
            try 
            {
                clientSocket.close();
                Log.d(TAG, "Client connection closed");
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing client socket: " + e.getMessage());
            }
        }
    }
    
    /**
     * 捕获并发送图像
     * <p>
     * 使用CaptureImageHelper捕获图像，处理并通过输出流发送。
     * </p>
     * 
     * @param outputStream 客户端输出流
     * @param isThumbnail 是否为缩略图请求
     * @throws IOException 如果发生I/O错误
     */
    private void captureAndSendImage(OutputStream outputStream, boolean isThumbnail) throws IOException 
    {
        final File imageFile = new File(tempImagePath);
        final ByteArrayOutputStream jpegStream = new ByteArrayOutputStream();
        
        try 
        {
            /* 捕获图像 */
            Size captureSize = EncoderConfig.createDefault4K().getSize();
            
            /* 检查临时目录是否存在，不存在则创建 */
            File directory = imageFile.getParentFile();
            if (directory != null && !directory.exists()) 
            {
                directory.mkdirs();
            }
            
            /* 创建一个标志来追踪捕获是否成功 */
            final AtomicBoolean captureDone = new AtomicBoolean(false);
            final AtomicBoolean captureSuccess = new AtomicBoolean(false);
            
            /* 通过CaptureImageHelper拍照 */
            mainHandler.post(() -> 
            {
                try 
                {
                    captureHelper.requestCapture(captureSize, tempImagePath);
                    /* 设置回调，当图像保存完成后通知 */
                    captureHelper.setCaptureCallback(new CaptureImageHelper.CaptureCallback() 
                    {
                        @Override
                        public void onImageSaved(String filePath) 
                        {
                            Log.d(TAG, "Image saved to: " + filePath);
                            captureSuccess.set(true);
                            captureDone.set(true);
                        }
                        
                        @Override
                        public void onError(String errorMessage) 
                        {
                            Log.e(TAG, "Error capturing image: " + errorMessage);
                            captureDone.set(true);
                        }
                    });
                } 
                catch (Exception e) 
                {
                    Log.e(TAG, "Error requesting capture: " + e.getMessage());
                    captureDone.set(true);
                }
            });
            
            /* 等待捕获完成 */
            int timeoutMs = 5000; /* 5秒超时 */
            long startTime = System.currentTimeMillis();
            while (!captureDone.get() && System.currentTimeMillis() - startTime < timeoutMs) 
            {
                try 
                {
                    Thread.sleep(100);
                } 
                catch (InterruptedException e) 
                {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            /* 检查捕获结果 */
            if (!captureSuccess.get()) 
            {
                /* 如果捕获失败，创建一个红色的默认图像 */
                Log.w(TAG, "Capture failed or timeout, sending default red image");
                sendRedImage(outputStream, isThumbnail);
                return;
            }
            
            /* 读取捕获的图像 */
            Bitmap bitmap = BitmapFactory.decodeFile(tempImagePath);
            if (bitmap == null) 
            {
                Log.e(TAG, "Cannot decode captured image file");
                sendRedImage(outputStream, isThumbnail);
                return;
            }
            
            /* 如果需要缩略图，调整大小 */
            if (isThumbnail) 
            {
                Bitmap thumbnailBitmap = Bitmap.createScaledBitmap(
                        bitmap, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, true);
                bitmap.recycle();
                bitmap = thumbnailBitmap;
            }
            
            /* 转换为JPEG */
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, jpegStream);
            byte[] jpegData = jpegStream.toByteArray();
            Log.d(TAG, "JPEG created, size: " + jpegData.length + " bytes");
            
            /* 发送图像数据 */
            sendJpegData(outputStream, bitmap.getWidth(), bitmap.getHeight(), jpegData);
            
            /* 清理 */
            bitmap.recycle();
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error processing image: " + e.getMessage());
            /* 出错时发送默认红色图像 */
            sendRedImage(outputStream, isThumbnail);
        } 
        finally 
        {
            try 
            {
                jpegStream.close();
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing JPEG stream: " + e.getMessage());
            }
        }
    }
    
    /**
     * 发送JPEG图像数据
     * <p>
     * 按协议格式发送图像元数据和JPEG数据。
     * </p>
     * 
     * @param outputStream 客户端输出流
     * @param width 图像宽度
     * @param height 图像高度
     * @param jpegData JPEG编码的图像数据
     * @throws IOException 如果发生I/O错误
     */
    private void sendJpegData(OutputStream outputStream, int width, int height, byte[] jpegData) throws IOException 
    {
        /* 发送图像元数据 */
        ByteBuffer buffer = ByteBuffer.allocate(4);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        
        /* 宽度 */
        buffer.putInt(0, width);
        outputStream.write(buffer.array());
        Log.d(TAG, "Sent width: " + width);
        
        /* 高度 */
        buffer.putInt(0, height);
        outputStream.write(buffer.array());
        Log.d(TAG, "Sent height: " + height);
        
        /* 格式 (JPEG 格式的 FourCC 码) */
        int fourcc = ('J' & 0xFF) | (('P' & 0xFF) << 8) | (('E' & 0xFF) << 16) | (('G' & 0xFF) << 24);
        buffer.putInt(0, fourcc);
        outputStream.write(buffer.array());
        Log.d(TAG, "Sent format: JPEG");
        
        /* 数据长度 */
        buffer.putInt(0, jpegData.length);
        outputStream.write(buffer.array());
        Log.d(TAG, "Sent data length: " + jpegData.length);
        
        /* 发送JPEG数据 */
        outputStream.write(jpegData);
        outputStream.flush();
        
        Log.d(TAG, "Image successfully sent: " + width + "x" + height + ", " + jpegData.length + " bytes");
    }
    
    /**
     * 发送红色图像（备选方案，当捕获失败时使用）
     * <p>
     * 创建一个纯红色的图像并发送。
     * </p>
     * 
     * @param outputStream 客户端输出流
     * @param isThumbnail 是否为缩略图请求
     * @throws IOException 如果发生I/O错误
     */
    private void sendRedImage(OutputStream outputStream, boolean isThumbnail) throws IOException 
    {
        /* 创建全红色位图 */
        int width = isThumbnail ? THUMBNAIL_WIDTH : 1920;
        int height = isThumbnail ? THUMBNAIL_HEIGHT : 1080;
        Log.d(TAG, "Creating default red image of " + width + "x" + height);
        
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawColor(Color.RED);
        
        /* 转换为JPEG */
        ByteArrayOutputStream jpegStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, jpegStream);
        byte[] jpegData = jpegStream.toByteArray();
        
        /* 发送图像数据 */
        sendJpegData(outputStream, width, height, jpegData);
        
        /* 清理 */
        bitmap.recycle();
        jpegStream.close();
    }
}
