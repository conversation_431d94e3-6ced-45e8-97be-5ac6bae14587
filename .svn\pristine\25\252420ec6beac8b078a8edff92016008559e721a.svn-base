<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <TextureView
        android:id="@+id/textureView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/controlPanel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/captureButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="拍照"
            android:layout_margin="4dp" />

        <Button
            android:id="@+id/recordButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="录像"
            android:layout_margin="4dp" />

        <Button
            android:id="@+id/streamButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="RTSP推流"
            android:layout_margin="4dp" />

        <Button
            android:id="@+id/streamTypeButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="摄像头流"
            android:layout_margin="4dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 