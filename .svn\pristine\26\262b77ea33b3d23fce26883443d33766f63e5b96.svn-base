1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.android.rockchip.camera2"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="5002"
6    android:versionName="14" >
7
8    <uses-sdk
8-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:6:5-7:40
9        android:minSdkVersion="29"
9-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:6:15-41
10        android:targetSdkVersion="29" />
10-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:9-38
11
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:9-69
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:26-66
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:9-85
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:26-82
14
15    <!-- Screen Capturing -->
16    <uses-permission android:name="android.permission.MANAGE_MEDIA_PROJECTION" />
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:13:9-86
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:13:26-83
17
18    <!-- Screen Recording -->
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:16:9-81
19-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:16:26-78
20    <uses-permission android:name="android.permission.RECORD_AUDIO" />
20-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:17:9-75
20-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:17:26-72
21    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
21-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:18:9-82
21-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:18:26-80
22    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
22-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:19:9-82
22-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:19:26-80
23    <uses-permission android:name="android.permission.ACCESS_USB" />
23-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:21:9-72
23-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:21:26-70
24    <uses-permission android:name="android.permission.SERIAL_PORT" />
24-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:22:9-73
24-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:22:26-71
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:23:9-84
25-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:23:26-81
26    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
26-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:24:9-86
26-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:24:26-83
27
28    <permission
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- 适用于Android 11及以上 -->
32-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
33    <application
33-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:29:5-96:19
34        android:allowBackup="true"
34-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:30:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\0477c8fa77aacd50d53a8ceb7b83f8ec\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:icon="@mipmap/ic_launcher"
38-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:31:9-43
39        android:label="@string/app_name"
39-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:32:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:33:9-54
41        android:supportsRtl="true"
41-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:34:9-35
42        android:testOnly="true"
43        android:theme="@style/BaseTheme" >
43-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:35:9-41
44        <activity
44-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:36:9-44:20
45            android:name="com.android.rockchip.camera2.RockchipCamera2"
45-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:38:13-44
46            android:exported="true" >
46-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:37:10-33
47
48            <!-- <intent-filter> -->
49            <!-- <action android:name="android.intent.action.MAIN" /> -->
50
51
52            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
53            <!-- </intent-filter> -->
54        </activity>
55        <activity
55-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:9-59:20
56            android:name="com.android.rockchip.camera2.activity.WelcomeActivity"
56-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:47:13-53
57            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
57-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:48:13-115
58            android:exported="true"
58-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:49:13-36
59            android:launchMode="singleTask"
59-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:50:13-44
60            android:screenOrientation="unspecified"
60-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:51:13-52
61            android:supportsPictureInPicture="false" >
61-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:52:13-53
62            <intent-filter>
62-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:53:13-58:29
63                <action android:name="android.intent.action.MAIN" />
63-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:54:17-69
63-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:54:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:56:17-77
65-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:56:27-74
66                <category android:name="android.intent.category.DEFAULT" />
66-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:17-76
66-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:27-73
67            </intent-filter>
68        </activity>
69        <activity
69-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:61:9-68:54
70            android:name="com.android.rockchip.camera2.activity.MainActivity"
70-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:62:13-50
71            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
71-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:63:13-115
72            android:exported="true"
72-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:64:13-36
73            android:launchMode="singleTask"
73-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:65:13-44
74            android:resizeableActivity="true"
74-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:66:13-46
75            android:screenOrientation="unspecified"
75-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:67:13-52
76            android:supportsPictureInPicture="true" />
76-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:68:13-52
77
78        <service
78-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:70:9-73:62
79            android:name="com.android.rockchip.camera2.ScreenRecordService"
79-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:72:13-48
80            android:exported="true"
80-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:71:13-36
81            android:foregroundServiceType="mediaProjection" />
81-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:73:13-60
82        <service
82-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:75:9-83:19
83            android:name="com.android.rockchip.camera2.HdmiService"
83-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:77:13-40
84            android:exported="true" >
84-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:76:10-33
85            <intent-filter>
85-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:78:13-82:29
86                <action android:name="com.android.rockchip.camera2.HdmiService" />
86-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:79:17-83
86-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:79:25-80
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:17-76
88-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:27-73
89            </intent-filter>
90        </service>
91
92        <!-- 注册 BootBroadcastReceiver -->
93        <receiver
93-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:86:9-94:20
94            android:name="com.android.rockchip.camera2.BootBroadcastReceiver"
94-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:86:19-56
95            android:enabled="true"
95-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:87:17-39
96            android:exported="false" >
96-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:88:17-41
97            <intent-filter>
97-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:89:13-93:29
98                <action android:name="android.intent.action.BOOT_COMPLETED" />
98-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:90:17-79
98-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:90:25-76
99                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
99-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:91:17-82
99-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:91:25-79
100
101                <category android:name="android.intent.category.DEFAULT" />
101-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:17-76
101-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:57:27-73
102            </intent-filter>
103        </receiver>
104        <!-- 注册 BootBroadcastReceiver -->
105        <provider
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
106            android:name="androidx.startup.InitializationProvider"
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
107            android:authorities="com.android.rockchip.camera2.androidx-startup"
107-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
108            android:exported="false" >
108-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
109            <meta-data
109-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.emoji2.text.EmojiCompatInitializer"
110-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
111                android:value="androidx.startup" />
111-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a757549a45d92e7a214815e852f79147\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
112            <meta-data
112-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3d344ed39b808cd01f3006d1e2e43619\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
113-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3d344ed39b808cd01f3006d1e2e43619\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
114                android:value="androidx.startup" />
114-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3d344ed39b808cd01f3006d1e2e43619\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
117                android:value="androidx.startup" />
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
118        </provider>
119
120        <receiver
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
121            android:name="androidx.profileinstaller.ProfileInstallReceiver"
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
122            android:directBootAware="false"
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
123            android:enabled="true"
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
124            android:exported="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
125            android:permission="android.permission.DUMP" >
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
127                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
128            </intent-filter>
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
130                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
131            </intent-filter>
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
133                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
136                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a8f4298247018018565563f4226a214\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
137            </intent-filter>
138        </receiver>
139    </application>
140
141</manifest>
