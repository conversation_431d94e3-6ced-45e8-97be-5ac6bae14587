<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Mon Jun 09 16:54:21 CST 2025 -->
<title>ImageDecoder</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-09">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.video, class: ImageDecoder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.video</a></div>
<h1 title="类 ImageDecoder" class="title">类 ImageDecoder</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.video.ImageDecoder</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ImageDecoder</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">ImageDecoder类用于处理图片的解码和显示。
 <p>
 此类提供了图片解码、缩放和加载到ImageView的功能。
 支持同步和异步加载，以及内存优化的采样加载方式。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ImageDecoder.LoadCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">ImageDecoder.LoadCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">加载回调接口。</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ImageDecoder</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static android.graphics.Bitmap</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#decodeSampledBitmapFromFile(java.lang.String,int,int)" class="member-name-link">decodeSampledBitmapFromFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 int&nbsp;reqWidth,
 int&nbsp;reqHeight)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">解码图片文件，使用内存优化的采样方式。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#loadImage(java.lang.String,android.widget.ImageView)" class="member-name-link">loadImage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">加载图片到ImageView。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#loadImageAsync(java.lang.String,android.widget.ImageView)" class="member-name-link">loadImageAsync</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">简化版的异步加载方法，不需要回调。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#loadImageAsync(java.lang.String,android.widget.ImageView,com.android.rockchip.camera2.video.ImageDecoder.LoadCallback)" class="member-name-link">loadImageAsync</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView,
 <a href="ImageDecoder.LoadCallback.html" title="com.android.rockchip.camera2.video中的接口">ImageDecoder.LoadCallback</a>&nbsp;callback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">异步加载图片到ImageView。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static android.graphics.Bitmap</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#rotateBitmap(android.graphics.Bitmap,float)" class="member-name-link">rotateBitmap</a><wbr>(android.graphics.Bitmap&nbsp;bitmap,
 float&nbsp;degrees)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">旋转图片到指定角度。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ImageDecoder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ImageDecoder</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="loadImage(java.lang.String,android.widget.ImageView)">
<h3>loadImage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">loadImage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</span></div>
<div class="block">加载图片到ImageView。
 <p>
 此方法会将指定路径的图片加载到ImageView中，并自动进行内存优化。
 注意：此方法在调用线程中同步执行，不应在主线程中调用。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图片文件路径</dd>
<dd><code>imageView</code> - 目标ImageView</dd>
<dt>返回:</dt>
<dd>如果加载成功返回true，否则返回false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadImageAsync(java.lang.String,android.widget.ImageView,com.android.rockchip.camera2.video.ImageDecoder.LoadCallback)">
<h3>loadImageAsync</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">loadImageAsync</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView,
 <a href="ImageDecoder.LoadCallback.html" title="com.android.rockchip.camera2.video中的接口">ImageDecoder.LoadCallback</a>&nbsp;callback)</span></div>
<div class="block">异步加载图片到ImageView。
 <p>
 此方法会在后台线程加载图片，完成后在主线程更新ImageView。
 适合在主线程中调用，不会阻塞UI。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图片文件路径</dd>
<dd><code>imageView</code> - 目标ImageView</dd>
<dd><code>callback</code> - 加载回调（可为null）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decodeSampledBitmapFromFile(java.lang.String,int,int)">
<h3>decodeSampledBitmapFromFile</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">android.graphics.Bitmap</span>&nbsp;<span class="element-name">decodeSampledBitmapFromFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 int&nbsp;reqWidth,
 int&nbsp;reqHeight)</span></div>
<div class="block">解码图片文件，使用内存优化的采样方式。
 <p>
 此方法会根据目标尺寸计算合适的采样率，以减少内存占用。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图片文件路径</dd>
<dd><code>reqWidth</code> - 请求的宽度</dd>
<dd><code>reqHeight</code> - 请求的高度</dd>
<dt>返回:</dt>
<dd>解码后的Bitmap，如果解码失败返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="rotateBitmap(android.graphics.Bitmap,float)">
<h3>rotateBitmap</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">android.graphics.Bitmap</span>&nbsp;<span class="element-name">rotateBitmap</span><wbr><span class="parameters">(android.graphics.Bitmap&nbsp;bitmap,
 float&nbsp;degrees)</span></div>
<div class="block">旋转图片到指定角度。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>bitmap</code> - 原始Bitmap</dd>
<dd><code>degrees</code> - 旋转角度</dd>
<dt>返回:</dt>
<dd>旋转后的Bitmap</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadImageAsync(java.lang.String,android.widget.ImageView)">
<h3>loadImageAsync</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">loadImageAsync</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</span></div>
<div class="block">简化版的异步加载方法，不需要回调。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图片文件路径</dd>
<dd><code>imageView</code> - 目标ImageView</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
