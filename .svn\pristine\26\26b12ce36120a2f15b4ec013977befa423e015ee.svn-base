<!-- res/layout/fragment_network_settings.xml -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:divider="@drawable/divider"
        android:showDividers="middle">

        <!-- 标题区域 -->

        <!-- 有线网络设置区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="有线网络 (以太网)"
                android:textSize="22sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="连接状态:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvEthernetStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="未连接"
                    android:textSize="20sp"
                    android:textColor="@color/red"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="IP地址:"
                    android:textSize="18sp"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvEthernetIp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0.0.0.0"
                    android:textSize="18sp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btnEthernetConfig"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="网络配置"
                    android:layout_marginEnd="8dp"/>

                <Button
                    android:id="@+id/btnDisconnectEthernet"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="断开连接"
                    android:enabled="false"
                    android:layout_marginStart="8dp"/>
            </LinearLayout>
        </LinearLayout>

        <!-- 无线网络设置区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="无线网络 (WiFi)"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="STA模式："
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"
                android:textColor="?android:attr/textColorPrimary"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/tvStaLinkStatusTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="连接状态:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvSTAStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="未连接"
                    android:textSize="20sp"
                    android:textColor="@color/red"/>
            </LinearLayout>

            <!-- 当前网络和状态行 - 修改为单行显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"> <!-- 关键修改：gravity改为start -->

                <!-- 当前网络标签和值 -->
                <TextView
                    android:id="@+id/staCurrentNetTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="当前网络:"
                    android:textSize="20sp"/>
                <TextView
                    android:id="@+id/tvCurrentWifi"
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    android:text="无"
                    android:textSize="20sp"
                    android:layout_marginEnd="16dp"/>

            </LinearLayout>

            <!-- 连接按钮 -->
            <Button
                android:id="@+id/btnToggleWifi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="连接WiFi"
                android:layout_gravity="start"
                android:minWidth="120dp"
                android:layout_marginStart="-7dp"/>

            <Space
                android:layout_width="match_parent"
                android:layout_height="16dp"
                android:layout_marginTop="8dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="AP模式："
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"
                android:textColor="?android:attr/textColorPrimary"/>

            <!-- AP 模式部分 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:id="@+id/ApStatusTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                    android:text="状态:"
                    android:textSize="20sp"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:id="@+id/tvApStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="已关闭"
                        android:textColor="@color/red"
                        android:textSize="18sp"/>

                    <TextView
                        android:id="@+id/tvApSsidTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                    android:text="SSID:"
                    android:textSize="20sp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvApSsid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MyHotspot"
                    android:textSize="20sp"/>
                </LinearLayout>

                <Button
                    android:id="@+id/btnToggleAp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="开启热点"
                    android:layout_gravity="start"
                    android:minWidth="120dp"/>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="RTSP推流"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp"/>

            <!-- 推流状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="推流状态:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvRtspStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="未启动"
                    android:textSize="20sp"
                    android:textColor="@color/red"/>
            </LinearLayout>

            <!-- RTSP URI -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="RTSP URI:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tvRtspUri"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="rtsp://*************:554/stream"
                    android:textSize="20sp"
                    android:ellipsize="end"
                    android:singleLine="true"/>
            </LinearLayout>

            <!-- 网络接口选择 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="网络接口:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <Spinner
                    android:id="@+id/spinnerNetworkInterface"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
            </LinearLayout>

            <!-- 推流类型选择 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="推流类型:"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp"/>

                <RadioGroup
                    android:id="@+id/rgStreamType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <RadioButton
                        android:id="@+id/rbCameraStream"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="摄像头"
                        android:checked="true"
                        android:gravity="center_vertical"
                        android:layout_marginTop="-3dp"/>

                    <RadioButton
                        android:id="@+id/rbScreenStream"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="屏幕"
                        android:layout_marginStart="5dp"
                        android:layout_marginTop="-3dp"/>
                </RadioGroup>
            </LinearLayout>

            <!-- 功能按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="6">

                <Button
                    android:id="@+id/btnStartStream"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="启动推流"
                    android:layout_marginEnd="4dp"/>

                <Button
                    android:id="@+id/btnStopStream"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="停止推流"
                    android:enabled="false"
                    android:layout_marginHorizontal="4dp"/>

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</ScrollView>