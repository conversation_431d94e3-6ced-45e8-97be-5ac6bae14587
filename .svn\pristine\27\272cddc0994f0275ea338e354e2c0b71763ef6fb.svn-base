#ifndef _RTSP_REQUEST_CONTEXT_H
#define _RTSP_REQUEST_CONTEXT_H

#pragma once
#include <utiny/usys_smartptr.h>
#include <utiny/rtsp_header.h>
#include <utiny/rtsp_packet_context.h>

class rtsp_session_context;
class usys_transceiver;

class UBEDA_API rtsp_request_context : public rtsp_packet_context
{
    rtsp_header_ptr response_user_;
	int cseq_;
public:
    rtsp_request_context(const usys_smartptr<usys_transceiver>& transceiverptr, const rtsp_header_ptr& header);

    inline const rtsp_header_ptr& response_context_header() const { return response_user_; }
	inline void response_context_header(const rtsp_header_ptr& response_user) { response_user_ = response_user; }
	void cseq(int v) { cseq_ = v; }
	int cseq()const { return cseq_; }
};

typedef usys_smartptr<rtsp_request_context> rtsp_request_context_ptr;

#endif
