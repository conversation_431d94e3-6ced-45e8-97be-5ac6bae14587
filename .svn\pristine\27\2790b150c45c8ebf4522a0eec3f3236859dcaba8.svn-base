<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res"><file name="button_background" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_selector" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\button_selector.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_about" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_about.png" qualifiers="" type="drawable"/><file name="ic_close" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_close.png" qualifiers="" type="drawable"/><file name="ic_color_adjustment" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_color_adjustment.png" qualifiers="" type="drawable"/><file name="ic_draw" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_draw.png" qualifiers="" type="drawable"/><file name="ic_exposure" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_exposure.png" qualifiers="" type="drawable"/><file name="ic_flip" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_flip.png" qualifiers="" type="drawable"/><file name="ic_folder" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_folder.png" qualifiers="" type="drawable"/><file name="ic_image_processing" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_image_processing.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_menu.png" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_pause.png" qualifiers="" type="drawable"/><file name="ic_pitch_on" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_pitch_on.png" qualifiers="" type="drawable"/><file name="ic_power_frequency" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_power_frequency.png" qualifiers="" type="drawable"/><file name="ic_record_shutter" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_record_shutter.png" qualifiers="" type="drawable"/><file name="ic_record_start" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_record_start.png" qualifiers="" type="drawable"/><file name="ic_record_video" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_record_video.png" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_settings.png" qualifiers="" type="drawable"/><file name="ic_storage" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_storage.png" qualifiers="" type="drawable"/><file name="ic_take_photo" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_take_photo.png" qualifiers="" type="drawable"/><file name="ic_take_photo_ing" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_take_photo_ing.png" qualifiers="" type="drawable"/><file name="ic_white_balance" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_white_balance.png" qualifiers="" type="drawable"/><file name="ic_zoom_in" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_zoom_in.png" qualifiers="" type="drawable"/><file name="ic_zoom_out" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\ic_zoom_out.png" qualifiers="" type="drawable"/><file name="rounded_border" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\rounded_border.xml" qualifiers="" type="drawable"/><file name="seekbar_thumb" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable\seekbar_thumb.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_main" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_rockchip_camera2" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_rockchip_camera2.xml" qualifiers="" type="layout"/><file name="activity_touptek" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_touptek.xml" qualifiers="" type="layout"/><file name="activity_touptek_btn" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_touptek_btn.xml" qualifiers="" type="layout"/><file name="activity_touptek_settings" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_touptek_settings.xml" qualifiers="" type="layout"/><file name="activity_welcome" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\activity_welcome.xml" qualifiers="" type="layout"/><file name="layout_input_info_item" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\layout_input_info_item.xml" qualifiers="" type="layout"/><file name="layout_main_pop_settings" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\layout_main_pop_settings.xml" qualifiers="" type="layout"/><file name="misc_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\misc_layout.xml" qualifiers="" type="layout"/><file name="popup_menu_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\popup_menu_layout.xml" qualifiers="" type="layout"/><file name="setting_file_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_file_layout.xml" qualifiers="" type="layout"/><file name="setting_image_format_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_image_format_layout.xml" qualifiers="" type="layout"/><file name="setting_language_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_language_layout.xml" qualifiers="" type="layout"/><file name="setting_storage_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_storage_layout.xml" qualifiers="" type="layout"/><file name="setting_time_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_time_layout.xml" qualifiers="" type="layout"/><file name="setting_video_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_video_layout.xml" qualifiers="" type="layout"/><file name="setting_zoom_rate_layout" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\layout\setting_zoom_rate_layout.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="scene_options">
        <item>生物</item>
        <item>体视</item>
    </string-array></file><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="RoundelMenu">
        <attr format="color|reference" name="round_menu_roundColor"/>
        <attr format="color|reference" name="round_menu_centerColor"/>
        <attr format="dimension" name="round_menu_expandedRadius"/>
        <attr format="dimension" name="round_menu_collapsedRadius"/>
        <attr format="integer" name="round_menu_duration"/>
        <attr format="integer" name="round_menu_item_anim_delay"/>
        <attr format="dimension" name="round_menu_item_width"/>
    </declare-styleable></file><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="purple_200">#FFBB86FC</color><color name="purple_700">#FF3700B3</color><color name="black">#FF000000</color><color name="teal_200">#FF03DAC5</color><color name="gray">#808080</color><color name="light_gray_background">#F5F5F5</color><color name="light_gray_sidebar">#E0E0E0</color><color name="white_background">#FFFFFF</color><color name="button_text_color">#333333</color><color name="text_color">#666666</color><color name="divider_color">#CCCCCC</color><color name="border_color">#999999</color><color name="radio_button_tint_color">#000000</color><color name="current_color">#FF0000</color><color name="button_normal_color">#E0E0E0</color><color name="button_pressed_color">#BDBDBD</color><color name="edit_text_background_color">#F5F5F5</color></file><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">HdmiIn</string><string name="take_picture">Take picture</string><string name="back_key_warn">Back Key disable</string><string name="err_not_channel">Not found ChannelUri</string><string name="btn_switch">Switch</string><string name="btn_record">record</string><string name="btn_record_start">start record</string><string name="btn_record_stop">end record</string><string name="btn_pq">pq</string><string name="btn_pq_start">start pq</string><string name="btn_pq_stop">end pq</string><string name="btn_calc_luma">luma</string><string name="btn_calc_luma_start">start luma</string><string name="btn_calc_luma_stop">end luma</string><string name="btn_lf_range">assign range</string><string name="btn_lf_range_start">start assign range</string><string name="btn_lf_range_stop">end assign range</string><string name="btn_screenshot">screenshot</string><string name="btn_pip">PIP</string><string name="notification_screen_record">screen record</string><string name="save_failed">save failed</string><string name="screenshot_failed">screenshot failed</string><string name="menu"/><string name="draw"/><string name="about"/><string name="settings"/><string name="zoom_out"/><string name="zoom_in"/><string name="folder"/><string name="pause"/><string name="record_video"/><string name="take_photo"/></file><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="android:style/Theme.Holo.Light.DarkActionBar">
        
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:colorAccent">@color/colorAccent</item>
    </style><style name="BaseTheme" parent="@android:style/Theme.Material.NoActionBar.Fullscreen">
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@android:color/black</item>
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.RkCamer2" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\XCamViewDemo\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="RoundelMenu">
        <attr format="color|reference" name="round_menu_roundColor"/>
        <attr format="color|reference" name="round_menu_centerColor"/>
        <attr format="dimension" name="round_menu_expandedRadius"/>
        <attr format="dimension" name="round_menu_collapsedRadius"/>
        <attr format="integer" name="round_menu_duration"/>
        <attr format="integer" name="round_menu_item_anim_delay"/>
        <attr format="dimension" name="round_menu_item_width"/>
    </declare-styleable></configuration></mergedItems></merger>