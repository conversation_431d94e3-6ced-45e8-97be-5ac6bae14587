#ifndef _RTSP_RESPONSE_CONTEXT_H
#define _RTSP_RESPONSE_CONTEXT_H

#pragma once
#include <utiny/usys_smartptr.h>
#include <utiny/usys_handler.h>
#include <utiny/rtsp_header.h>

class rtsp_session_context;
class UBEDA_API rtsp_response_context : public rtsp_packet_context, public usys_timer_doozer
{
public:
    enum e_stat
    {
        E_OK,
        E_TIMEOUT,
        E_EXCEPTION,
        E_USERCANEL,
    };
private:
    e_stat stat_;
    usys_smartptr_mtbase_ptr response_stat_;
    usys_smartptr_mtbase_ptr timer_id_;
	rtsp_header_ptr request_header_;
	int request_session_id_;
public:
    rtsp_response_context(const usys_smartptr<usys_transceiver>& transceiverptr, const rtsp_header_ptr& header);
	const rtsp_header_ptr& request_context_header()const { return request_header_; }
    const usys_smartptr_mtbase_ptr& response_stat() { return response_stat_; }
    void response_stat(const usys_smartptr_mtbase_ptr& stat) { response_stat_ = stat;}
    void start_timer(int second);
    void cancel_timer();
    virtual void handle_timeout(const void *arg);
    inline e_stat stat() { return stat_; }
    inline void stat(e_stat e) { stat_ = e; }
	int request_session_id()const { return request_session_id_; }
	void request_session_id(int id) { request_session_id_ = id; }
};

typedef usys_smartptr<rtsp_response_context> rtsp_response_context_ptr;

#endif
