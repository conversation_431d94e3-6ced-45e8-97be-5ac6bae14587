<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 18 10:12:53 CST 2025 -->
<title>com.android.rockchip.camera2.video</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-18">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.video">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#package">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>程序包：</p>
<ul>
<li>说明</li>
<li><a href="#related-package-summary">相关程序包</a></li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>程序包：&nbsp;</li>
<li>说明&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">相关程序包</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.android.rockchip.camera2.video" class="title">程序包 com.android.rockchip.camera2.video</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.android.rockchip.camera2.video</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>相关程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.android.rockchip.camera2</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../util/package-summary.html">com.android.rockchip.camera2.util</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">所有类和接口</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">CameraManagerHelper 类用于管理摄像头的打开、预览和资源释放。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CaptureImageHelper.html" title="com.android.rockchip.camera2.video中的类">CaptureImageHelper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">CaptureImageHelper 类用于处理摄像头图像的抓取和保存。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CaptureImageHelper.CaptureCallback.html" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">回调接口，用于通知抓图结果</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TvPreviewHelper.html" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">TvPreviewHelper 类用于提供基于TvView的低延迟预览功能。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="VideoDecoder.PlaybackListener.html" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">播放状态回调接口</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">VideoEncoder 类负责视频编码、存储监控以及文件大小限制的处理。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
