<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 预览布局修改为占据整个屏幕 -->
    <FrameLayout
        android:id="@+id/preview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        
        <!-- 相机预览区域 - 修改ID为texture_view以匹配代码中的引用 -->
        <TextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- ROI选择框叠加层 - 这个仍然需要 -->
        <com.android.rockchip.camera2.view.TpRoiView
            android:id="@+id/roi_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />
            
        <!-- 设置菜单面板 - 默认隐藏 -->
        <include
            android:id="@+id/settings_panel"
            layout="@layout/popup_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|start"
            android:layout_margin="20dp"
            android:visibility="gone" />
        
        <!-- 缩放比例显示 - 右上角 -->
        <TextView
            android:id="@+id/tv_zoom_scale"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|end"
            android:layout_margin="16dp"
            android:background="#80000000"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:text="1.0x"
            android:visibility="visible" />
        
    </FrameLayout>

    <!-- 控制面板 - 修改为悬浮在预览窗口上方 -->
    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#00000000"
        android:elevation="8dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp">

        <!-- 录制按钮等现有控件... -->
        <Button
            android:id="@+id/btn_record"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录制"
            android:layout_marginEnd="10dp"/>

        <!-- 抓图按钮 -->
        <Button
            android:id="@+id/btn_capture"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="抓图"
            android:layout_marginStart="10dp"/>

        <!-- 浏览媒体按钮 -->
        <Button
            android:id="@+id/btn_browse_media"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="浏览媒体"
            android:layout_marginStart="10dp"/>

        <!-- 清除缓存按钮 -->
        <Button
            android:id="@+id/btn_clear_cache"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="清除缓存"
            android:layout_marginStart="10dp"/>

    </LinearLayout>
</RelativeLayout>