<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Apr 22 11:23:35 CST 2025 -->
<title>MediaAdapter</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-22">
<meta name="description" content="declaration: package: com.android.rockchip.camera2, class: MediaAdapter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2</a></div>
<h1 title="类 MediaAdapter" class="title">类 MediaAdapter</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">androidx.recyclerview.widget.RecyclerView.Adapter&lt;com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&gt;
<div class="inheritance">com.android.rockchip.camera2.MediaAdapter</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">MediaAdapter</span>
<span class="extends-implements">extends androidx.recyclerview.widget.RecyclerView.Adapter&lt;com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&gt;</span></div>
<div class="block">MediaAdapter 类用于在 RecyclerView 中显示媒体文件的缩略图和名称。</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="MediaAdapter.OnMediaClickListener.html" class="type-name-link" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">定义媒体点击监听器接口。</div>
</div>
</div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-androidx.recyclerview.widget.RecyclerView.Adapter">从类继承的嵌套类/接口&nbsp;androidx.recyclerview.widget.RecyclerView.Adapter</h2>
<code>androidx.recyclerview.widget.RecyclerView.Adapter.StateRestorationPolicy</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.util.List,com.android.rockchip.camera2.MediaAdapter.OnMediaClickListener)" class="member-name-link">MediaAdapter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/File.html" title="java.io中的类或接口" class="external-link">File</a>&gt;&nbsp;mediaFiles,
 <a href="MediaAdapter.OnMediaClickListener.html" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数。</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getItemCount()" class="member-name-link">getItemCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取媒体文件的数量。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onBindViewHolder(com.android.rockchip.camera2.MediaAdapter.MediaViewHolder,int)" class="member-name-link">onBindViewHolder</a><wbr>(com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&nbsp;holder,
 int&nbsp;position)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">绑定 ViewHolder。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.android.rockchip.camera2.MediaAdapter.MediaViewHolder</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onCreateViewHolder(android.view.ViewGroup,int)" class="member-name-link">onCreateViewHolder</a><wbr>(android.view.ViewGroup&nbsp;parent,
 int&nbsp;viewType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">创建 ViewHolder。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-androidx.recyclerview.widget.RecyclerView.Adapter">从类继承的方法&nbsp;androidx.recyclerview.widget.RecyclerView.Adapter</h3>
<code>bindViewHolder, createViewHolder, findRelativeAdapterPositionIn, getItemId, getItemViewType, getStateRestorationPolicy, hasObservers, hasStableIds, notifyDataSetChanged, notifyItemChanged, notifyItemChanged, notifyItemInserted, notifyItemMoved, notifyItemRangeChanged, notifyItemRangeChanged, notifyItemRangeInserted, notifyItemRangeRemoved, notifyItemRemoved, onAttachedToRecyclerView, onBindViewHolder, onDetachedFromRecyclerView, onFailedToRecycleView, onViewAttachedToWindow, onViewDetachedFromWindow, onViewRecycled, registerAdapterDataObserver, setHasStableIds, setStateRestorationPolicy, unregisterAdapterDataObserver</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.util.List,com.android.rockchip.camera2.MediaAdapter.OnMediaClickListener)">
<h3>MediaAdapter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MediaAdapter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/File.html" title="java.io中的类或接口" class="external-link">File</a>&gt;&nbsp;mediaFiles,
 <a href="MediaAdapter.OnMediaClickListener.html" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a>&nbsp;listener)</span></div>
<div class="block">构造函数。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>mediaFiles</code> - 媒体文件列表</dd>
<dd><code>listener</code> - 媒体点击监听器</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onCreateViewHolder(android.view.ViewGroup,int)">
<h3>onCreateViewHolder</h3>
<div class="member-signature"><span class="annotations">@NonNull
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">com.android.rockchip.camera2.MediaAdapter.MediaViewHolder</span>&nbsp;<span class="element-name">onCreateViewHolder</span><wbr><span class="parameters">(@NonNull
 android.view.ViewGroup&nbsp;parent,
 int&nbsp;viewType)</span></div>
<div class="block">创建 ViewHolder。</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>onCreateViewHolder</code>&nbsp;在类中&nbsp;<code>androidx.recyclerview.widget.RecyclerView.Adapter&lt;com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&gt;</code></dd>
<dt>参数:</dt>
<dd><code>parent</code> - 父视图组</dd>
<dd><code>viewType</code> - 视图类型</dd>
<dt>返回:</dt>
<dd>创建的 MediaViewHolder</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onBindViewHolder(com.android.rockchip.camera2.MediaAdapter.MediaViewHolder,int)">
<h3>onBindViewHolder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onBindViewHolder</span><wbr><span class="parameters">(@NonNull
 com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&nbsp;holder,
 int&nbsp;position)</span></div>
<div class="block">绑定 ViewHolder。</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>onBindViewHolder</code>&nbsp;在类中&nbsp;<code>androidx.recyclerview.widget.RecyclerView.Adapter&lt;com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&gt;</code></dd>
<dt>参数:</dt>
<dd><code>holder</code> - MediaViewHolder 实例</dd>
<dd><code>position</code> - 当前项的位置</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getItemCount()">
<h3>getItemCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getItemCount</span>()</div>
<div class="block">获取媒体文件的数量。</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>getItemCount</code>&nbsp;在类中&nbsp;<code>androidx.recyclerview.widget.RecyclerView.Adapter&lt;com.android.rockchip.camera2.MediaAdapter.MediaViewHolder&gt;</code></dd>
<dt>返回:</dt>
<dd>媒体文件数量</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
