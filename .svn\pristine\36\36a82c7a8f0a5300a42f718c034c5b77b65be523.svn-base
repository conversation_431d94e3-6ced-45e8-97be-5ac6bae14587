/* ***************************************************************
//  usys_transceiver_tcp   version:  1.0  date: 24/24/2009
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#include "utiny/utiny_config.h"
#include "utiny/usys_transceiver_tcp.h"
#include "utiny/usys_network.h"
#include <time.h>

#define MESSAGE_MAX_LENGTH	(1024 * 1024 * 8)

usys_transceiver_tcp::usys_transceiver_tcp(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size, unsigned recv_buffer_size)
: usys_transceiver(reactor, send_retry_deque_size, recv_buffer_size), estatus_(e_zero)
{
}

usys_transceiver_tcp::~usys_transceiver_tcp() 
{
}

bool usys_transceiver_tcp::init_socket(int af)
{
    SOCKET result = socket(af, SOCK_STREAM, 0);
    if (result != INVALID_SOCKET)
    {
        estatus_ = e_socket;
        handle_ = result;
        usys_network::set_block(handle_, false);
    }
    usys_transceiver::reactor_ptr()->register_handler(usys_smartptr<usys_handler>(this));
    return (result != -1);
}

void usys_transceiver_tcp::set_socket(SOCKET fd, bool is_connected)
{
    handle_ = fd;
    if (is_connected)
        estatus_ = e_connected;
    else
        estatus_ = e_socket;
    usys_transceiver::reactor_ptr()->register_handler(usys_smartptr<usys_handler>(this));
}

int usys_transceiver_tcp::listen(unsigned n)
{
    if (usys_network::do_listen(handle_, n))
    {
        estatus_ = e_accepting;
        return true;
    }
    return false;
}

SOCKET usys_transceiver_tcp::accept(sockaddr* addr, int* length_ptr)
{
    return ::accept(handle_, addr, (socklen_t*)length_ptr);
}

int usys_transceiver_tcp::connect(sockaddr* addr, int length)
{
    int result = ::connect(handle_, addr, length);
    if (result == 0) 
    {
        result = this->handle_connect();
        if (result < 0)
            return result;
        estatus_ = e_connected;
    }

    if (usys_network::connect_in_progress())
    {
        estatus_ = e_connecting;
        return 0;
    }
    else 
    {
        // some other error condition
        return -1;
    }
}

int usys_transceiver_tcp::connect(const char* remote_addr, unsigned short port)
{
    if (remote_addr == NULL || remote_addr[0] == 0 || port == 0)
        return -1;
    sockaddr_storage addr;
    int nLen = usys_network::get_address(remote_addr, port, addr);
    if (nLen <= 0)
        return -1;

    return this->connect((sockaddr*)&addr, nLen);
}

void usys_transceiver_tcp::close()
{
    if (estatus_ <= e_closed)
        return;

    {
        usys_guard<usys_mutex> g(send_retry_deque_lock_);
        send_retry_deque_.clear();
    }

    if (handle_ >= 0)
    {
        usys_network::close_socket_no_throw(handle_);
        handle_ = -1;
    }

    usys_transceiver::reactor_ptr()->unregister_handler(usys_smartptr<usys_handler>(this));

    // flag this socket as closed, so it will be removed
    // from the socket map.

    estatus_ = e_closed;

    handle_close();
}

int usys_transceiver_tcp::get_remote_addr(sockaddr_storage* addr)
{
    int len = sizeof(sockaddr_storage);
    return getpeername(handle_, (sockaddr*)addr, (socklen_t*)&len);
}

void usys_transceiver_tcp::handle_read_event()
{
    int ret = 0;
    if (estatus_ == e_accepting)
        ret = this->handle_accept();
    else if (estatus_ == e_connected)
        ret = this->handle_read();
    if (ret < 0)
        close();
}

bool usys_transceiver_tcp::readable()
{
    return ((estatus_ == e_connected) || (estatus_ == e_accepting));
}

bool usys_transceiver_tcp::writable()
{
    if (estatus_ == e_connecting)
        return true;
    if (estatus_ == e_connected)
    {
        usys_guard<usys_mutex> g(send_retry_deque_lock_);
        return (!send_retry_deque_.empty());
    }
    return false;
}

bool usys_transceiver_tcp::exceptable()
{
    return (estatus_ == e_connecting);
}

void usys_transceiver_tcp::handle_write_event()
{
    if (estatus_ == e_connecting)
    {
#ifndef _WIN32
        sockaddr_storage addr;
        socklen_t len = sizeof(sockaddr_storage);
        if (getpeername(handle_, (sockaddr*)&addr, &len) < 0)
        {
            handle_except_event();
            return;
        }
#endif
        if (this->handle_connect() < 0)
        {
            close();
            return;
        }
        
        estatus_ = e_connected;
        this->handle_read();
    }

    if (estatus_ != e_connected)
        return;	//nothing to do

    handle_write();

    while (1)
    {
        usys_smartptr<usys_data_block_i> datablock;
        {
            usys_guard<usys_mutex> g(send_retry_deque_lock_);
            if (send_retry_deque_.empty())
                break;
            datablock = send_retry_deque_.front();
        }

        if (datablock == 0)
        {
            close();
            break;
        }

        iovec iov[IOV_MAX];
        unsigned n = datablock->rd_iov(iov);
        int ret = (1 == n) ? this->send((const char*)iov[0].iov_base, iov[0].iov_len) : this->sendv(iov, n);
        if (ret == (int)datablock->length())
        {
            usys_guard<usys_mutex> g(send_retry_deque_lock_);
            if (!send_retry_deque_.empty()) /* maybe cleared by close */
            {
                handle_write_datablock(send_retry_deque_.front());
                send_retry_deque_.pop_front();
            }
        }
        else if (ret <= 0)
        {
            if (usys_network::would_block())
                break;

            close();
            break;
        }
        else if (ret > 0)
        {
            datablock->rd_ptr(ret);
            break;
        }
    }
}

void usys_transceiver_tcp::handle_except_event()
{
    close();
}

int usys_transceiver_tcp::handle_read()
{
    int ret = this->recv(recv_datablock_ptr_->wr_ptr(), recv_datablock_ptr_->space(), 0);
    if (ret < 0)
        return -1;
    if (ret == 0) //handle_read will be called after connect success, don't know why
        return 0;
    recv_datablock_ptr_->wr_ptr(ret);
    last_recv_time(usys_time());
    do
    {
        usys_smartptr_mtbase_ptr header_ptr;
        int message_len = get_message_len(recv_datablock_ptr_, header_ptr);
        if (message_len < 0 || message_len > MESSAGE_MAX_LENGTH)
        {
            USYS_ASSERT(false);
            return -1;
        }
        else if (0 == message_len)
        {
            if ((recv_datablock_ptr_->capacity() < MESSAGE_MAX_LENGTH) && (recv_datablock_ptr_->space() <= 0))
            {
                int newlength = recv_datablock_ptr_->capacity() + recv_buffer_size_;
                if (newlength > MESSAGE_MAX_LENGTH)
                    newlength = MESSAGE_MAX_LENGTH;
                recv_datablock_ptr_->size(newlength);
            }
            
            return 0;
        }

        int total_len = (int)recv_datablock_ptr_->length();
        if (total_len < message_len)
        {
            if (recv_datablock_ptr_->capacity() < (size_t)message_len)
                recv_datablock_ptr_->size(message_len);
            return 0;
        }
        else if (total_len >= message_len)
        {
            ret = handle_read_datablock(recv_datablock_ptr_, header_ptr);
            if (ret < 0)
                return ret;
        }

        if (recv_datablock_ptr_->__get_ref_count() > 1)
        {
            usys_smartptr<usys_data_block> new_recv_datablock_ptr_(new usys_data_block(recv_datablock_ptr_->capacity()));
            if (total_len > message_len)
                new_recv_datablock_ptr_->copy(&recv_datablock_ptr_->base_ptr()[message_len], total_len - message_len);
            recv_datablock_ptr_ = new_recv_datablock_ptr_;
        }
        else
        {
            recv_datablock_ptr_->rd_ptr(message_len);
            recv_datablock_ptr_->crunch();
        }
    } while (recv_datablock_ptr_->length() > 0);

    return ret;
}
