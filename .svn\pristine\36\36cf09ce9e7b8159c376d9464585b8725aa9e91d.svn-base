package com.android.rockchip.camera2.activity

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.video.CameraManagerHelper
import com.android.rockchip.camera2.video.VideoEncoder

class MainMenu : DialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = inflater.inflate(R.layout.activity_touptek_btn, container, false)
        setupButtonClickListeners(view!!)

        return view
    }

    override fun onStart() {
        super.onStart()

        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                // 设置对话框背景为透明，去除屏幕变暗效果
//                window.setBackgroundDrawableResource(android.R.color.transparent);
                window.setBackgroundDrawableResource(R.color.white_background)
                // 设置对话框的宽度和高度为包裹内容
                val params = window.attributes
                params.width = WindowManager.LayoutParams.WRAP_CONTENT // 让宽度自适应内容
                params.height = WindowManager.LayoutParams.MATCH_PARENT // 让高度占满屏幕

                //                 使对话框贴左边
                params.gravity = Gravity.START or Gravity.TOP // 使对话框紧贴左上角

                // 将对话框的宽度设置为屏幕宽度的一半，调整高度和位置
                window.attributes = params
            }
        }
    }

    private fun setupButtonClickListeners(view: View) {
        // 获取各个按钮并设置点击事件
        val btnTakePhoto = view.findViewById<ImageButton>(R.id.btn_take_photo)
        btnTakePhoto.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Take Photo"
            )
        }

        val btnRecordVideo = view.findViewById<ImageButton>(R.id.btn_record_video)
        btnRecordVideo.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Record Video"
            )
        }

        val btnPause = view.findViewById<ImageButton>(R.id.btn_pause)
        btnPause.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Pause"
            )
        }

        val btnFolder = view.findViewById<ImageButton>(R.id.btn_folder)
        btnFolder.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Folder"
            )
        }

        val btnStorage = view.findViewById<ImageButton>(R.id.btn_storage)
        btnStorage.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "storage"
            )
        }

        val btnZoomIn = view.findViewById<ImageButton>(R.id.btn_zoom_in)
        btnZoomIn.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Zoom In"
            )
        }

        val btnZoomOut = view.findViewById<ImageButton>(R.id.btn_zoom_out)
        btnZoomOut.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Zoom Out"
            )
        }

        val btnSettings = view.findViewById<ImageButton>(R.id.btn_settings)
        btnSettings.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Settings"
            )
        }

        val btnAbout = view.findViewById<ImageButton>(R.id.btn_about)
        btnAbout.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "About"
            )
        }

        val btnDraw = view.findViewById<ImageButton>(R.id.btn_draw)
        btnDraw.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Draw"
            )
        }

        val btnMenu = view.findViewById<ImageButton>(R.id.btn_menu)
        btnMenu.setOnClickListener { v: View ->
            handleButtonClick(
                v,
                "Menu"
            )
        }
    }

    private fun handleButtonClick(button: View, buttonName: String) {
        // 执行每个按钮的具体操作
        when (buttonName) {
//            "Take Photo" -> handleTakePhoto(button)
            "Record Video" -> handleRecordVideo(button)
//            "Pause" -> handlePause(button)
            "Folder" -> handleFolder(button)
//            "storage" -> handleStorage(button)
//            "Zoom In" -> handleZoomIn(button)
//            "Zoom Out" -> handleZoomOut(button)
            "Settings" -> handleSettings(button)
//            "About" -> handleAbout(button)
//            "Draw" -> handleDraw(button)
            "Menu" -> handleMenu(button)
            else -> Toast.makeText(activity, "$buttonName clicked", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleRecordVideo(button: View) {
        menuClickRecordBtn()
    }

    private fun handleFolder(button: View) {
        menuClickBrowseBtn()
    }

    private fun handleSettings(button: View) {
    }

    private fun handleMenu(button: View) {
        // 获取按钮的位置
        val location = IntArray(2)
        button.getLocationOnScreen(location)
        val buttonX = location[0]
        val buttonY = location[1]
        val buttonWidth = button.width // 获取按钮的宽度

        // 显示新的菜单弹出窗口
        val menuPopupDialogFragment: MainMenu.MenuPopupDialogFragment =
            MainMenu.MenuPopupDialogFragment()

        // 传递位置和宽度参数给弹出窗口
        val args = Bundle()
        args.putInt("x", buttonX)
        args.putInt("y", buttonY)
        args.putInt("width", buttonWidth)
        menuPopupDialogFragment.setArguments(args)
        menuPopupDialogFragment.show(
            childFragmentManager as androidx.fragment.app.FragmentManager,
            "MenuPopupDialogFragment"
        )
    }

    //    static
    class MenuPopupDialogFragment : DialogFragment() {
        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle? // 关键修正点：Bundle 改为 Bundle?
        ): View? {
            val view = inflater.inflate(R.layout.popup_menu_layout, container, false)
            val popupButtonAE = view.findViewById<ImageButton>(R.id.btn_exposure)
            popupButtonAE.setOnClickListener {
                // 处理点击事件（示例）

                val AEDialog = TpAEDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 30)
                        putInt("width", popupButtonAE.width)
                    }
                }

                // 关键：使用父 FragmentManager
                AEDialog.show(parentFragmentManager, "AEDialog")
            }

            val popupButtonWB = view.findViewById<ImageButton>(R.id.btn_white_balance)
            popupButtonWB.setOnClickListener {
                // 处理点击事件（示例）

                val WBDialog = TpWBDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 30)
                        putInt("width", popupButtonWB.width)
                    }
                }
//
//                // 关键：使用父 FragmentManager
                WBDialog.show(parentFragmentManager, "WBDialog")
            }


            val popupButtonImageProcess = view.findViewById<ImageButton>(R.id.btn_color_adjustment)
            popupButtonImageProcess.setOnClickListener {
                // 处理点击事件（示例）

                val ImageProcessDialog = TpImageProcessDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                ImageProcessDialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonImageProcess2 = view.findViewById<ImageButton>(R.id.btn_image_processing)
            popupButtonImageProcess2.setOnClickListener {
                // 处理点击事件（示例）

                val ImageProcess2Dialog = TpImageProcess2DialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                ImageProcess2Dialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonFlip = view.findViewById<ImageButton>(R.id.btn_flip)
            popupButtonFlip.setOnClickListener {
                // 处理点击事件（示例）

                val FlipDialog = TpFlipDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                FlipDialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonHz = view.findViewById<ImageButton>(R.id.btn_power_frequency)
            popupButtonHz.setOnClickListener {
                // 处理点击事件（示例）

                val HzDialog = TpHzDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                HzDialog.show(parentFragmentManager, "WBDialog")
            }

            
            return view
        }


        override fun onStart() {
            super.onStart()
            val dialog = dialog
            if (dialog != null) {
                val window = dialog.window
                if (window != null) {
                    window.setDimAmount(0f)
                    //                    window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                    window.setBackgroundDrawableResource(android.R.color.transparent)
                    val params = window.attributes

                    // 获取传递过来的位置和宽度参数
                    val args = arguments
                    if (args != null) {
                        val buttonX = args.getInt("x", 0)
                        val buttonY = args.getInt("y", 0)
                        val buttonWidth = args.getInt("width", 0)

                        // 设置对话框的位置，使其显示在按钮的右边
                        params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                        params.y = buttonY // 同按钮的Y轴位置
                    }

                    params.width = WindowManager.LayoutParams.WRAP_CONTENT
                    params.height = WindowManager.LayoutParams.WRAP_CONTENT
                    params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                    window.attributes = params
                }
            }
        }
    }


//    class AEDialogFragment : DialogFragment() {
//        override fun onCreateView(
//            inflater: LayoutInflater,
//            container: ViewGroup?,
//            savedInstanceState: Bundle?
//        ): View? {
//            val view = inflater.inflate(R.layout.autoae_layout, container, false)
//            // 这里可以为弹出窗口的图片按钮设置点击事件
//            return view
//        }
//
//
//        override fun onStart() {
//            super.onStart()
//            val dialog = dialog
//            if (dialog != null) {
//                val window = dialog.window
//                if (window != null) {
//                    window.setDimAmount(0f)
//                    //                    window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//                    window.setBackgroundDrawableResource(android.R.color.transparent)
//                    val params = window.attributes
//
//                    // 获取传递过来的位置和宽度参数
//                    val args = arguments
//                    if (args != null) {
//                        val buttonX = args.getInt("x", 0)
//                        val buttonY = args.getInt("y", 0)
//                        val buttonWidth = args.getInt("width", 0)
//
//                        // 设置对话框的位置，使其显示在按钮的右边
//                        params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
//                        params.y = buttonY - 315 // 同按钮的Y轴位置
//                    }
//
//                    params.width = WindowManager.LayoutParams.WRAP_CONTENT
//                    params.height = WindowManager.LayoutParams.WRAP_CONTENT
//                    params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
//                    window.attributes = params
//                }
//            }
//        }
//    }


    private fun menuClickRecordBtn() {
        val mainActivity = activity as? MainActivity
        mainActivity?.showVideoEncoderActivity()
    }

    private fun menuClickBrowseBtn() {
        val mainActivity = activity as? MainActivity
        mainActivity?.showBrowseActivity()
    }
}