<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <com.android.rockchip.camera2.view.TpImageView
        android:id="@+id/image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="matrix" />

    <!-- 可选：添加操作提示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:layout_marginTop="16dp"
        android:text="双指缩放 • 拖拽移动 • 双击返回"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:background="#80000000"
        android:padding="8dp"
        android:alpha="0.8" />

</FrameLayout>
