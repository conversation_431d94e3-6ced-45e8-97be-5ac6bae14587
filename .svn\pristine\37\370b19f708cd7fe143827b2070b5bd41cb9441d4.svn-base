<resources>
    <string name="app_name">HdmiIn</string>
    <string name="take_picture">Take picture</string>

    <string name="back_key_warn">Back Key disable</string>
    <string name="err_not_channel">Not found ChannelUri</string>
    <string name="btn_switch">Switch</string>
    <string name="btn_record">record</string>
    <string name="btn_record_start">start record</string>
    <string name="btn_record_stop">end record</string>
    <string name="btn_pq">pq</string>
    <string name="btn_pq_start">start pq</string>
    <string name="btn_pq_stop">end pq</string>
    <string name="btn_calc_luma">luma</string>
    <string name="btn_calc_luma_start">start luma</string>
    <string name="btn_calc_luma_stop">end luma</string>
    <string name="btn_lf_range">assign range</string>
    <string name="btn_lf_range_start">start assign range</string>
    <string name="btn_lf_range_stop">end assign range</string>
    <string name="btn_screenshot">screenshot</string>
    <string name="btn_pip">PIP</string>
    <string name="notification_screen_record">screen record</string>
    <string name="save_failed">save failed</string>
    <string name="screenshot_failed">screenshot failed</string>
    <string name="menu" />
    <string name="draw" />
    <string name="about" />
    <string name="settings" />
    <string name="zoom_out" />
    <string name="zoom_in" />
    <string name="folder" />
    <string name="pause" />
    <string name="record_video" />
    <string name="take_photo" />

    <string name="btn_add">+</string>
    <string name="btn_reduce" >-</string>
    <string name="btn_auto_exposure" >Auto Exposure</string>
    <string name="btn_exposure_compensation_colon" >Exposure Compensation：</string>
    <string name="btn_exposure_time_colon" >Exposure Time：</string>
    <string name="btn_exposure_gain_colon" >Gain：</string>
    <string name="btn_radio_auto_wb" >Auto</string>
    <string name="btn_radio_manual_wb" >Manual</string>
    <string name="btn_radio_roi" >ROI</string>
    <string name="title_wb_red_text" >Red：</string>
    <string name="title_wb_green_text" >Green：</string>
    <string name="title_wb_blue_text" >Blue：</string>

    <string name="title_saturation_text" >Saturation：</string>
    <string name="title_gamma_text" >Gamma：</string>
    <string name="title_contrast_text" >Contrast：</string>
    <string name="title_brightness_text" >Brightness：</string>
    <string name="title_sharpness_text" >Sharpness：</string>
    <string name="title_denoise_text" >Denoise：</string>

</resources>
