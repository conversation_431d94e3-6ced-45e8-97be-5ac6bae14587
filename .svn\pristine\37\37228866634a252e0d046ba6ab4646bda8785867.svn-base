package com.android.rockchip.camera2.video;

import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。
 */
public class VideoDecoder
{
    private static final String TAG = "VideoDecoder";
    private final String videoPath;
    private final Surface surface;

    private MediaCodec mediaCodec;
    private MediaExtractor mediaExtractor;
    private Thread decodingThread;

    private volatile boolean isDecoding = false;
    private volatile boolean isPaused = false;
    private volatile boolean isFrameByFrame = false;

    private long videoDuration;

    /**
     * 构造函数。
     *
     * @param videoPath 视频文件路径
     * @param surface   用于渲染视频的 Surface
     */
    public VideoDecoder(String videoPath, Surface surface)
    {
        this.videoPath = videoPath;
        this.surface = surface;

        /* 打印日志 */
        Log.d(TAG, "VideoDecoder");
    }

    /**
     * 开始视频解码过程。
     */
    public synchronized void startDecoding()
    {
        try
        {
            /* 创建媒体提取器实例 */
            mediaExtractor = new MediaExtractor();
            /* 设置视频文件数据源 */
            mediaExtractor.setDataSource(videoPath);

            /* 选择视频轨道索引 */
            int trackIndex = selectVideoTrack(mediaExtractor);
            if (trackIndex == -1)
            {
                Log.e(TAG, "未找到视频轨道");
                return;
            }

            /* 获取视频轨道索引的媒体格式 */
            MediaFormat format = mediaExtractor.getTrackFormat(trackIndex);
            /* 获取视频帧的总时长(us) */
            videoDuration = format.getLong(MediaFormat.KEY_DURATION);
            /* 获取视频编码格式（如：video/avc） */
            String mime = format.getString(MediaFormat.KEY_MIME);
            /* 根据 mime 类型创建对应的解码器 */
            mediaCodec = MediaCodec.createDecoderByType(mime);
            /* 配置解码器（输出到指定 surface） */
            mediaCodec.configure(format, surface, null, 0);
            /* 启动解码器 */
            mediaCodec.start();

            /* 启动解码线程 */
            isDecoding = true;
            /* 创建并启动解码器线程 */
            decodingThread = new Thread(this::decodingTask);
            decodingThread.start();
        }
        catch (IOException e)
        {
            Log.e(TAG, "解码器初始化失败", e);
        }

        /* 打印日志 */
        Log.d(TAG, "startDecoding");
    }

    /**
     * 选择媒体文件中的视频轨道。
     *
     * @param extractor 媒体提取器实例，用于访问媒体轨道
     * @return 视频轨道索引（成功找到返回非负整数，未找到返回 -1）
     */
    private int selectVideoTrack(MediaExtractor extractor)
    {
        /* 遍历所有可用媒体轨道 */
        for (int i = 0; i < extractor.getTrackCount(); i++)
        {
            /* 获取当前轨道的媒体格式 */
            MediaFormat format = extractor.getTrackFormat(i);

            /* 检测是否为视频轨道（MIME 类型以 "video/" 开头） */
            if (format.getString(MediaFormat.KEY_MIME).startsWith("video/"))
            {
                /* 选择该轨道进行后续操作 */
                extractor.selectTrack(i);
                /* 打印日志 */
                Log.d(TAG, "selectVideoTrack");
                return i; /* 返回找到的视频轨道索引 */
            }
        }

        /* 打印日志 */
        Log.d(TAG, "selectVideoTrack");
        return -1; /* 未找到符合条件的视频轨道 */
    }

    /**
     * 在单独线程中运行的主要解码任务。
     */
    private void decodingTask()
    {
        /* 创建输出缓冲区信息对象 */
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        /* 主解码循环：持续运行直到停止解码 */
        while (isDecoding)
        {
            /* 处理暂停/逐帧模式：短暂休眠避免 CPU 空转 */
            if (isPaused || isFrameByFrame)
            {
                safeSleep(10);
                continue;
            }

            /* 从解码器获取可用输入缓冲区 */
            int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
            if (inputIndex >= 0)
            {
                /* 获取输入缓冲区字节缓冲 */
                ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);
                /* 读取媒体数据到缓冲区 */
                int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);

                if (sampleSize < 0)
                {
                    /* 小于0表示视频流结束,发送流结束标志 */
                    mediaCodec.queueInputBuffer(inputIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                }
                else
                {
                    /* 提交有效数据到解码器 */
                    mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize, mediaExtractor.getSampleTime(), 0);
                    /* 推进到下一帧数据 */
                    mediaExtractor.advance();
                }
            }

            /* 从解码器获取输出缓冲区 */
            int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
            if (outputIndex >= 0)
            {
                /* 渲染解码完成的帧到 Surface */
                mediaCodec.releaseOutputBuffer(outputIndex, true);
            }
            else if (outputIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED)
            {
                /* 处理输出格式变化事件 */
                Log.d(TAG, "输出格式已更改");
            }
        }

        /* 打印日志 */
        Log.d(TAG, "decodingTask");
    }

    /**
     * 跳转到视频的指定位置。
     *
     * @param position 要跳转到的位置，以微秒为单位
     */
    public synchronized void seekTo(long position)
    {
        if (!isDecoding)
        {
            return;
        }

        boolean wasPaused = isPaused;
        isPaused = true;

        safeSleep(50);

        try
        {
            /* 使用Math.max(0, position)确保跳转位置不小于0,MediaExtractor.SEEK_TO_CLOSEST_SYNC表示跳转到最近的关键帧 */
            mediaExtractor.seekTo(Math.max(0, position), MediaExtractor.SEEK_TO_CLOSEST_SYNC);
            /* 清空解码器的输入和输出缓冲区 */
            mediaCodec.flush();
        }
        catch (IllegalStateException e)
        {
            Log.e(TAG, "跳转失败", e);
        }
        /* 恢复到跳转前的解码状态 */
        isPaused = wasPaused;

        /* 打印日志 */
        Log.d(TAG, "seekTo");
    }

    /**
     * 切换播放和暂停状态。
     */
    public void togglePlayPause()
    {
        isPaused = !isPaused;

        /* 打印日志 */
        Log.d(TAG, "togglePlayPause");
    }

    /**
     * 逐帧播放视频。
     */
    public void stepFrame()
    {
        isFrameByFrame = true;
        new Thread(() ->
        {
            decodeSingleFrame();
            isFrameByFrame = false;
        }).start();

        /* 打印日志 */
        Log.d(TAG, "stepFrame");
    }

    /**
     * 解码视频的单个帧。
     */
    private void decodeSingleFrame()
    {
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
        if (inputIndex >= 0)
        {
            /*从解码器中请求一个空闲的输入缓冲区 */
            ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);

            /* 将视频数据从mediaExtractor读取到输入缓冲区中 */
            int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);
            if (sampleSize >= 0)
            {
                /* 将填充好的输入缓冲区提交给解码器 */
                mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize, mediaExtractor.getSampleTime(), 0);
                mediaExtractor.advance();
            }
        }

        /* 从解码器请求一个输出缓冲区 */
        int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
        if (outputIndex >= 0)
        {
            /* 将解码后的帧渲染到界面上 */
            mediaCodec.releaseOutputBuffer(outputIndex, true);
        }

        /* 打印日志 */
        Log.d(TAG, "decodeSingleFrame");
    }

    /**
     * 相对跳转控制方法。
     * 基于当前播放位置进行时间偏移跳转。
     *
     * @param deltaMs 时间偏移量（单位：毫秒，正数前进/负数后退）
     */
    public void seekRelative(long deltaMs)
    {
        try
        {
            long currentPos = mediaExtractor.getSampleTime();
            long newPos = Math.max(0, Math.min(videoDuration, currentPos + deltaMs * 1000));
            seekTo(newPos);
        }
        catch (IllegalStateException e)
        {
            Log.e(TAG, "跳转失败", e);
        }

        /* 打印日志 */
        Log.d(TAG, "seekRelative");
    }

    /**
     * 停止视频解码过程。
     */
    public synchronized void stopDecoding()
    {
        isDecoding = false;

        try
        {
            if (decodingThread != null)
            {
                decodingThread.join(500);
            }
        }
        catch (InterruptedException e)
        {
            Log.w(TAG, "线程中断", e);
        }

        if (mediaCodec != null)
        {
            mediaCodec.stop();
            mediaCodec.release();
            mediaCodec = null;
        }

        if (mediaExtractor != null)
        {
            mediaExtractor.release();
            mediaExtractor = null;
        }

        /* 打印日志 */
        Log.d(TAG, "stopDecoding");
    }

    /**
     * 检查视频是否正在解码。
     *
     * @return 如果正在解码则返回 true，否则返回 false
     */
    public boolean isDecoding()
    {
        /* 打印日志 */
        Log.d(TAG, "isDecoding");
        return isDecoding;
    }

    /**
     * 检查视频是否处于暂停状态。
     *
     * @return 如果暂停则返回 true，否则返回 false
     */
    public boolean isPaused()
    {
        /* 打印日志 */
        Log.d(TAG, "isPaused");
        return isPaused;
    }

    /**
     * 检查视频是否处于逐帧模式。
     *
     * @return 如果处于逐帧模式则返回 true，否则返回 false
     */
    public boolean isFrameByFrame()
    {
        /* 打印日志 */
        Log.d(TAG, "isFrameByFrame");
        return isFrameByFrame;
    }

    /**
     * 获取视频的持续时间。
     *
     * @return 视频持续时间，以微秒为单位
     */
    public long getVideoDuration()
    {
        /* 打印日志 */
        Log.d(TAG, "getVideoDuration");
        return videoDuration;
    }

    /**
     * 获取视频的当前位置。
     *
     * @return 当前位置，以微秒为单位
     */
    public long getCurrentPosition()
    {
        /* 打印日志 */
        Log.d(TAG, "getCurrentPosition");
        return mediaExtractor.getSampleTime();
    }

    /**
     * 安全地睡眠指定时间，处理中断。
     *
     * @param millis 要睡眠的时间，以毫秒为单位
     */
    private void safeSleep(long millis)
    {
        try
        {
            Thread.sleep(millis);
        }
        catch (InterruptedException e)
        {
            Log.d(TAG, "线程睡眠被中断");
        }

        /* 打印日志 */
//        Log.d(TAG, "safeSleep");
    }
}