package com.android.rockchip.camera2.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

/**
 * 自定义进度条 - 优化的动画效果和视觉设计
 * 
 * 特点：
 * 1. 圆圈可以自由超出轨道边界
 * 2. 流畅的动画效果
 * 3. 优美的视觉反馈
 */
public class TpCustomProgressBar extends View {
    private static final String TAG = "TpCustomProgressBar";
    
    // 绘制相关
    private Paint mTrackPaint;          // 轨道画笔
    private Paint mProgressPaint;       // 进度画笔
    private Paint mThumbPaint;          // 圆圈画笔
    private Paint mThumbShadowPaint;    // 圆圈阴影画笔
    
    // 尺寸配置
    private float mTrackHeight = 16f;   // 轨道高度
    private float mThumbRadius = 18f;   // 圆圈半径（正常状态）
    private float mThumbRadiusPressed = 24f; // 圆圈半径（按下状态）
    private float mCurrentThumbRadius = mThumbRadius;
    
    // 动画相关
    private ValueAnimator mThumbAnimator;
    private float mThumbScale = 1.0f;   // 圆圈缩放比例
    private float mThumbAlpha = 1.0f;   // 圆圈透明度
    
    // 进度相关
    private int mMax = 100;
    private int mProgress = 0;
    private int mSecondaryProgress = 0;
    
    // 交互状态
    private boolean mIsDragging = false;
    private boolean mIsPressed = false;
    
    // 监听器
    private OnProgressChangeListener mProgressChangeListener;
    
    public interface OnProgressChangeListener {
        void onProgressChanged(TpCustomProgressBar progressBar, int progress, boolean fromUser);
        void onStartTrackingTouch(TpCustomProgressBar progressBar);
        void onStopTrackingTouch(TpCustomProgressBar progressBar);
    }
    
    public TpCustomProgressBar(Context context) {
        super(context);
        init();
    }
    
    public TpCustomProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public TpCustomProgressBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 转换dp到px
        float density = getResources().getDisplayMetrics().density;
        mTrackHeight *= density;
        mThumbRadius *= density;
        mThumbRadiusPressed *= density;
        mCurrentThumbRadius = mThumbRadius;
        
        // 初始化画笔
        mTrackPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTrackPaint.setColor(0x33FFFFFF); // 半透明白色
        
        mProgressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mProgressPaint.setColor(0xFFFFFFFF); // 纯白色
        
        mThumbPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mThumbPaint.setColor(0xFFFFFFFF); // 纯白色
        
        mThumbShadowPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mThumbShadowPaint.setColor(0x40000000); // 阴影色
        
        setClickable(true);
        setFocusable(true);
    }
    
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int width = MeasureSpec.getSize(widthMeasureSpec);
        // 高度需要容纳最大的圆圈加阴影
        int height = (int) (mThumbRadiusPressed * 2.5f + getPaddingTop() + getPaddingBottom());
        setMeasuredDimension(width, height);
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        int width = getWidth() - getPaddingLeft() - getPaddingRight();
        int height = getHeight() - getPaddingTop() - getPaddingBottom();
        
        float centerY = getPaddingTop() + height / 2f;
        float trackLeft = getPaddingLeft() + mCurrentThumbRadius;
        float trackRight = getWidth() - getPaddingRight() - mCurrentThumbRadius;
        float trackWidth = trackRight - trackLeft;
        
        // 绘制背景轨道
        RectF trackRect = new RectF(
            trackLeft, 
            centerY - mTrackHeight / 2,
            trackRight, 
            centerY + mTrackHeight / 2
        );
        canvas.drawRoundRect(trackRect, mTrackHeight / 2, mTrackHeight / 2, mTrackPaint);
        
        // 绘制进度轨道
        if (mProgress > 0) {
            float progressWidth = (trackWidth * mProgress) / mMax;
            RectF progressRect = new RectF(
                trackLeft,
                centerY - mTrackHeight / 2,
                trackLeft + progressWidth,
                centerY + mTrackHeight / 2
            );
            canvas.drawRoundRect(progressRect, mTrackHeight / 2, mTrackHeight / 2, mProgressPaint);
        }
        
        // 计算圆圈位置
        float thumbX = trackLeft + (trackWidth * mProgress) / mMax;
        float currentRadius = mCurrentThumbRadius * mThumbScale;
        
        // 绘制圆圈阴影
        if (mIsPressed) {
            canvas.drawCircle(thumbX + 2, centerY + 2, currentRadius, mThumbShadowPaint);
        }
        
        // 设置圆圈透明度
        mThumbPaint.setAlpha((int) (255 * mThumbAlpha));
        
        // 绘制圆圈
        canvas.drawCircle(thumbX, centerY, currentRadius, mThumbPaint);
        
        // 恢复透明度
        mThumbPaint.setAlpha(255);
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isEnabled()) {
            return false;
        }
        
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mIsPressed = true;
                mIsDragging = true;
                animateThumbPress(true);
                
                if (mProgressChangeListener != null) {
                    mProgressChangeListener.onStartTrackingTouch(this);
                }
                
                updateProgressFromTouch(event);
                break;
                
            case MotionEvent.ACTION_MOVE:
                if (mIsDragging) {
                    updateProgressFromTouch(event);
                }
                break;
                
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mIsPressed = false;
                mIsDragging = false;
                animateThumbPress(false);
                
                if (mProgressChangeListener != null) {
                    mProgressChangeListener.onStopTrackingTouch(this);
                }
                break;
        }
        
        return true;
    }
    
    private void updateProgressFromTouch(MotionEvent event) {
        float trackLeft = getPaddingLeft() + mCurrentThumbRadius;
        float trackRight = getWidth() - getPaddingRight() - mCurrentThumbRadius;
        float trackWidth = trackRight - trackLeft;
        
        float touchX = event.getX();
        float progress = (touchX - trackLeft) / trackWidth;
        progress = Math.max(0, Math.min(1, progress)); // 限制在0-1之间
        
        int newProgress = (int) (progress * mMax);
        if (newProgress != mProgress) {
            setProgress(newProgress, true);
        }
    }
    
    private void animateThumbPress(boolean pressed) {
        // 取消之前的动画
        if (mThumbAnimator != null) {
            mThumbAnimator.cancel();
        }
        
        float targetRadius = pressed ? mThumbRadiusPressed : mThumbRadius;
        float targetScale = pressed ? 1.1f : 1.0f;
        float targetAlpha = pressed ? 0.9f : 1.0f;
        
        mThumbAnimator = ValueAnimator.ofFloat(0f, 1f);
        mThumbAnimator.setDuration(200);
        mThumbAnimator.setInterpolator(new DecelerateInterpolator());
        
        final float startRadius = mCurrentThumbRadius;
        final float startScale = mThumbScale;
        final float startAlpha = mThumbAlpha;
        
        mThumbAnimator.addUpdateListener(animation -> {
            float fraction = animation.getAnimatedFraction();
            
            // 圆圈大小动画
            mCurrentThumbRadius = startRadius + (targetRadius - startRadius) * fraction;
            
            // 缩放动画
            mThumbScale = startScale + (targetScale - startScale) * fraction;
            
            // 透明度动画
            mThumbAlpha = startAlpha + (targetAlpha - startAlpha) * fraction;
            
            invalidate();
        });
        
        mThumbAnimator.start();
    }
    
    // 公共API方法
    public void setProgress(int progress) {
        setProgress(progress, false);
    }
    
    public void setProgress(int progress, boolean fromUser) {
        progress = Math.max(0, Math.min(mMax, progress));
        if (progress != mProgress) {
            mProgress = progress;
            invalidate();
            
            if (mProgressChangeListener != null) {
                mProgressChangeListener.onProgressChanged(this, mProgress, fromUser);
            }
        }
    }
    
    public int getProgress() {
        return mProgress;
    }
    
    public void setMax(int max) {
        if (max > 0) {
            mMax = max;
            if (mProgress > max) {
                mProgress = max;
            }
            invalidate();
        }
    }
    
    public int getMax() {
        return mMax;
    }
    
    public void setSecondaryProgress(int secondaryProgress) {
        secondaryProgress = Math.max(0, Math.min(mMax, secondaryProgress));
        if (secondaryProgress != mSecondaryProgress) {
            mSecondaryProgress = secondaryProgress;
            invalidate();
        }
    }
    
    public int getSecondaryProgress() {
        return mSecondaryProgress;
    }
    
    public void setOnProgressChangeListener(OnProgressChangeListener listener) {
        mProgressChangeListener = listener;
    }
}
