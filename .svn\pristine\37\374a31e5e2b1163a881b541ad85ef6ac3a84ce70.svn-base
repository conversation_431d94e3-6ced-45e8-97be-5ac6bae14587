<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 12:51:44 CST 2025 -->
<title>com.touptek.utils</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.utils">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.touptek.utils" class="title">程序包 com.touptek.utils</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.touptek.utils</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">TpFileManager 类提供文件存储相关的工具方法。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">存储设备变化监听接口。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">TpHdmiMonitor 类用于检测和管理HDMI输入状态。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TpHdmiMonitor.HdmiListener.html" title="com.touptek.utils中的接口">TpHdmiMonitor.HdmiListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">定义 HDMI 状态变化监听接口。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">TpNetworkMonitor 类用于管理WiFi、以太网和热点相关操作。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpNetworkMonitor.HotspotInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">热点信息类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">网络接口信息类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TpNetworkMonitor.NetworkStateListener.html" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">网络状态监听器接口</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpNetworkMonitor.WifiConnectionInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">WiFi连接信息类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">SambaUploader类用于连接Samba服务器并上传图片和视频。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">目录列表回调接口</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">SMB连接配置类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">上传结果回调接口</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
