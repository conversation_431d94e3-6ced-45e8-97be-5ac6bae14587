package com.android.rockchip.camera2.activity.ispdialogfragment
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.util.TouptekIspParam
import com.android.rockchip.camera2.util.touptek_serial_rk

class TpImageProcess2DialogFragment : DialogFragment(), touptek_serial_rk.DeviceStateCallback {
    private var touptek_serial: touptek_serial_rk? = null
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.image_parameter_2_layout, container, false)
        touptek_serial = touptek_serial_rk()
        touptek_serial?.startMonitor()
        touptek_serial?.setDeviceStateCallback(this)
        return view
    }


    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                window.setBackgroundDrawableResource(android.R.color.transparent)
                val params = window.attributes

                // 获取传递过来的位置和宽度参数
                val args = arguments
                if (args != null) {
                    val buttonX = args.getInt("x", 0)
                    val buttonY = args.getInt("y", 0)
                    val buttonWidth = args.getInt("width", 0)

                    // 设置对话框的位置，使其显示在按钮的右边
                    params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                    params.y = buttonY - 170 // 同按钮的Y轴位置
                }

                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                window.attributes = params
            }
        }
    }


    private fun sendProgressToSerial(param: TouptekIspParam, ctrl: Int, progress: Int) {
        //

        val data = IntArray(4)

        // 将进度值拆解为4个字节
        data[3] = (progress and 0xFF).toByte().toInt() // 获取最低字节
        data[2] = ((progress shr 8) and 0xFF).toByte().toInt() // 获取第二个字节
        data[1] = ((progress shr 16) and 0xFF).toByte().toInt() // 获取第三个字节
        data[0] = ((progress shr 24) and 0xFF).toByte().toInt() // 获取最高字节

        //         发送拆解后的数据到串口
        touptek_serial?.sendCommandToSerial(ctrl, param.value, data)
    }

    private fun setupSeekBar(view: View, seekBarId: Int, textViewId: Int, param: TouptekIspParam) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        val textView = view.findViewById<TextView>(textViewId)

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                // 只处理用户滑动触发的变化
                if (fromUser) {
                    // 更新 TextView
                    textView.text = progress.toString()
                    // 保存当前滑块值
                    TouptekIspParam.saveData(TouptekIspParam.valueOf(param.name), progress)
                    // 发送数据到串口
                    sendProgressToSerial(param, 0x01, progress)

                    println("set!")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸开始时的逻辑
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸结束时的逻辑
            }
        })
    }

    override fun onDeviceStateChanged(connected: Boolean) {
        val message = if (connected) "设备已连接" else "设备已断开"
        Log.d("SerialMonitor", message)

        // 切换到主线程更新 UI
        if (activity != null) {
//            activity!!.runOnUiThread {
//                val textSerialStatus =
//                    view!!.findViewById<TextView>(R.id.text_serial_status)
//                if (textSerialStatus != null) {
//                    textSerialStatus.text = "串口状态：" + (if (connected) "已连接" else "已断开")
//                }
//                // 也可以弹出 Toast 提示
//                Toast.makeText(context, message, Toast.LENGTH_SHORT)
//                    .show()
//            }
        }
    }


    private fun handleWBButtonClick() {
        // 在这里添加捕获按钮点击时的操作，例如开始捕获图像
        Toast.makeText(activity, "恢复白平衡默认状态", Toast.LENGTH_SHORT).show()
    }
}