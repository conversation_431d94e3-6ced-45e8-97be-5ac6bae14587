package com.android.rockchip.camera2.rtsp;

import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.android.rockchip.camera2.rtsp.config.RTSPConfig;
import com.android.rockchip.camera2.rtsp.service.ProjectionData;
import com.android.rockchip.camera2.rtsp.service.RTSPStreamer;
import com.android.rockchip.camera2.video.VideoEncoder;

import java.util.function.Consumer;

/**
 * RTSP推流统一管理器
 * 
 * 这是RTSP推流功能的唯一访问点，负责协调所有推流相关操作。
 * 应用程序应该只与此类交互，而不直接使用底层实现类。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class RTSPManager 
{
    private static final String TAG = "RTSPManager";

    /** 单例实例 */
    private static RTSPManager instance;
    
    /** 应用程序上下文 */
    private Context context;
    
    /** RTSP配置信息 */
    private RTSPConfig config;
    
    /** RTSP流媒体推送器 */
    private RTSPStreamer streamer; // 将RTSPStreamer设为私有成员
    
    /** 视频编码器 */
    private VideoEncoder videoEncoder;
    
    /** 当前推流类型 */
    private StreamType currentStreamType = StreamType.CAMERA;
    
    /** 屏幕录制权限数据 */
    private ProjectionData projectionData;
    
    /** 是否已初始化 */
    private boolean initialized = false;
    
    /** 是否正在推流 */
    private boolean streaming = false;
    
    /** 屏幕捕获启动器 */
    private ActivityResultLauncher<Intent> screenCaptureLauncher;
    
    /** 主线程Handler，用于回调操作 */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /** 流开始回调处理器 */
    private Consumer<String> onStreamStartedHandler;
    
    /** 流停止回调处理器 */
    private Runnable onStreamStoppedHandler;
    
    /** 流错误回调处理器 */
    private Consumer<String> onStreamErrorHandler;
    
    /** 权限授权回调处理器 */
    private Consumer<String> onPermissionGrantedHandler;
    
    /**
     * 私有的StreamStateListener实现，用于转发到回调处理器
     */
    private final StreamStateListener streamListener = new StreamStateListener() 
    {
        @Override
        public void onStreamStarted(String url) 
        {
            if (onStreamStartedHandler != null) 
            {
                mainHandler.post(() -> onStreamStartedHandler.accept(url));
            }
        }

        @Override
        public void onStreamStopped() 
        {
            if (onStreamStoppedHandler != null) 
            {
                mainHandler.post(() -> onStreamStoppedHandler.run());
            }
        }

        @Override
        public void onStreamError(String errorMessage) 
        {
            if (onStreamErrorHandler != null) 
            {
                mainHandler.post(() -> onStreamErrorHandler.accept(errorMessage));
            }
        }
        
        @Override
        public void onPermissionGranted(String message) 
        {
            if (onPermissionGrantedHandler != null) 
            {
                mainHandler.post(() -> onPermissionGrantedHandler.accept(message));
            }
        }
    };

    /**
     * 私有构造函数，防止外部实例化
     */
    private RTSPManager() 
    {
    }

    /**
     * 获取RTSPManager单例实例
     * 
     * @return RTSPManager单例实例
     */
    public static synchronized RTSPManager getInstance() 
    {
        if (instance == null) 
        {
            instance = new RTSPManager();
        }
        return instance;
    }

    /**
     * 初始化RTSPManager
     * 必须在使用其他方法前调用此方法
     * 
     * @param activity 关联的Activity实例，用于权限请求
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager initialize(AppCompatActivity activity) 
    {
        if (initialized) return this;

        this.context = activity.getApplicationContext();
        this.config = RTSPConfig.createDefaultConfig();

        screenCaptureLauncher = activity.registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> handleScreenCaptureResult(result)
        );

        initialized = true;
        return this;
    }

    /**
     * 设置RTSP配置
     * 
     * @param config RTSP配置对象
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager setConfig(RTSPConfig config) 
    {
        this.config = config;
        return this;
    }

    /**
     * 设置视频编码器
     * 当推流类型为CAMERA时必须设置
     * 
     * @param videoEncoder 视频编码器实例
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager setVideoEncoder(VideoEncoder videoEncoder) 
    {
        this.videoEncoder = videoEncoder;
        return this;
    }
    
    /**
     * 设置流开始的回调处理器
     * 
     * @param handler 处理流URL的回调，参数为完整RTSP URL
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager onStreamStarted(Consumer<String> handler) 
    {
        this.onStreamStartedHandler = handler;
        return this;
    }
    
    /**
     * 设置流停止的回调处理器
     * 
     * @param handler 处理流停止的回调
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager onStreamStopped(Runnable handler) 
    {
        this.onStreamStoppedHandler = handler;
        return this;
    }
    
    /**
     * 设置流错误的回调处理器
     * 
     * @param handler 处理错误消息的回调，参数为错误信息
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager onStreamError(Consumer<String> handler) 
    {
        this.onStreamErrorHandler = handler;
        return this;
    }
    
    /**
     * 设置权限获取成功的回调处理器
     * 
     * @param handler 处理权限消息的回调，参数为成功消息
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager onPermissionGranted(Consumer<String> handler) 
    {
        this.onPermissionGrantedHandler = handler;
        return this;
    }

    /**
     * 设置推流类型
     * 只切换类型，不自动请求权限或开始推流
     * 
     * @param type 推流类型，SCREEN或CAMERA
     * @return RTSPManager实例，用于链式调用
     */
    public RTSPManager setStreamType(StreamType type) 
    {
        if (currentStreamType == type) return this;

        if (streaming) stopStreaming();
        currentStreamType = type;
        
        // 如果是屏幕推流模式但没有权限，请求权限但不自动开始推流
        if (type == StreamType.SCREEN && (projectionData == null || !projectionData.isValid())) 
        {
            requestScreenCapturePermission();
        }

        return this;
    }

    /**
     * 开始推流
     * 如果是屏幕推流但没有权限，会先请求权限
     * 
     * @return 是否成功开始推流或请求权限
     */
    public boolean startStreaming() 
    {
        if (!initialized) 
        {
            Log.e(TAG, "RTSPManager not initialized");
            return false;
        }

        if (streaming) 
        {
            Log.w(TAG, "Already streaming");
            return true;
        }

        if (currentStreamType == StreamType.CAMERA) 
        {
            if (videoEncoder == null) 
            {
                Log.e(TAG, "VideoEncoder not set for camera streaming");
                return false;
            }
            initStreamer();
        } 
        else if (currentStreamType == StreamType.SCREEN) 
        {
            if (projectionData == null || !projectionData.isValid()) 
            {
                requestScreenCapturePermission();
                return true;
            }
            initStreamer();
        }

        if (streamer != null) 
        {
            streaming = streamer.startStream();
            return streaming;
        }

        return false;
    }

    /**
     * 停止推流
     */
    public void stopStreaming() 
    {
        if (!streaming || streamer == null) return;

        streamer.stopStream();
        streaming = false;
    }

    /**
     * 请求屏幕捕获权限
     * 该方法会弹出系统权限对话框
     */
    private void requestScreenCapturePermission() 
    {
        MediaProjectionManager projectionManager =
            (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        Intent captureIntent = projectionManager.createScreenCaptureIntent();
        screenCaptureLauncher.launch(captureIntent);
    }

    /**
     * 处理屏幕捕获结果
     * 
     * @param result 屏幕捕获权限结果
     */
    private void handleScreenCaptureResult(ActivityResult result) 
    {
        if (result.getResultCode() == AppCompatActivity.RESULT_OK) 
        {
            projectionData = new ProjectionData(result.getResultCode(), result.getData());
            
            // 使用专门的权限获取成功回调，而不是推流开始回调
            if (streamListener != null) 
            {
                streamListener.onPermissionGranted("屏幕捕获权限已获取，可以开始推流");
            }
        } 
        else 
        {
            if (streamListener != null) 
            {
                streamListener.onStreamError("屏幕捕获权限被拒绝");
            }
        }
    }

    /**
     * 初始化推流器
     */
    private void initStreamer() 
    {
        streamer = new RTSPStreamer(
            context,
            config,
            currentStreamType,
            projectionData,
            videoEncoder,
            streamListener
        );
    }

    /**
     * 获取当前推流URL
     * 
     * @return 推流URL，如果未推流则返回null
     */
    public String getStreamUrl() 
    {
        return streamer != null && streamer.isStreaming() ? streamer.getRtspUrl() : null;
    }

    /**
     * 获取当前推流类型
     * 
     * @return 当前推流类型
     */
    public StreamType getCurrentStreamType() 
    {
        return currentStreamType;
    }

    /**
     * 判断是否正在推流
     * 
     * @return 是否正在推流
     */
    public boolean isStreaming() 
    {
        return streaming;
    }

    /**
     * 释放所有资源
     * 在不再需要推流功能时调用
     */
    public void release() 
    {
        if (streaming) stopStreaming();
        streamer = null;
        initialized = false;
    }

    /**
     * 推流类型枚举
     */
    public enum StreamType 
    {
        /** 屏幕录制推流 */
        SCREEN, 
        /** 摄像头推流 */
        CAMERA
    }

    /**
     * 推流状态监听接口
     * 仅供RTSPManager内部使用
     */
    public interface StreamStateListener 
    {
        /**
         * 推流开始回调
         * 
         * @param url 推流URL
         */
        void onStreamStarted(String url);
        
        /**
         * 推流停止回调
         */
        void onStreamStopped();
        
        /**
         * 推流错误回调
         * 
         * @param errorMessage 错误信息
         */
        void onStreamError(String errorMessage);
        
        /**
         * 权限获取成功回调
         * 
         * @param message 成功信息
         */
        void onPermissionGranted(String message);
    }
}