package com.android.rockchip.camera2.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.Toast;

import com.android.rockchip.camera2.util.SMBFileUploader;
import com.android.rockchip.mediacodecnew.R;

import java.util.ArrayList;
import java.util.List;

/**
 * SMB设置对话框
 * <p>
 * 这个对话框用于配置SMB服务器连接参数和上传设置。
 * </p>
 */
public class SMBSettingsDialog extends Dialog {
    
    // 默认IP地址
    private static final String DEFAULT_IP = "*************";
    
    private CheckBox enableSmbCheckBox;
    private CheckBox enableSmbVideoCheckBox;
    private EditText serverIpEditText;
    private EditText usernameEditText;
    private EditText passwordEditText;
    private EditText shareNameEditText;
    
    // 远程路径UI
    private Spinner remotePathSpinner;
    private ProgressBar loadingPathsProgressBar;
    private Button refreshPathsButton;
    
    private Button testConnectionButton;
    private Button saveButton;
    
    private SMBFileUploader SMBFileUploader;
    
    private List<String> remotePaths = new ArrayList<>();
    private ArrayAdapter<String> remotePathsAdapter;
    
    private String selectedRemotePath = "/";
    
    /**
     * 构造函数
     * 
     * @param context 上下文
     * @param SMBFileUploader SMB上传器实例
     */
    public SMBSettingsDialog(Context context, SMBFileUploader SMBFileUploader) {
        super(context);
        this.SMBFileUploader = SMBFileUploader;
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_smb_settings);
        
        // 初始化控件
        initViews();
        
        // 初始化适配器
        initAdapters();
        
        // 加载现有设置
        loadExistingSettings();
        
        // 设置事件监听器
        setupListeners();
    }
    
    /**
     * 初始化所有视图控件
     */
    private void initViews() {
        enableSmbCheckBox = findViewById(R.id.cb_enable_smb);
        enableSmbVideoCheckBox = findViewById(R.id.cb_enable_smb_video);
        
        // 服务器信息控件
        serverIpEditText = findViewById(R.id.et_server_ip);
        usernameEditText = findViewById(R.id.et_username);
        passwordEditText = findViewById(R.id.et_password);
        shareNameEditText = findViewById(R.id.et_share_name);
        
        // 远程路径控件
        remotePathSpinner = findViewById(R.id.spinner_remote_path);
        loadingPathsProgressBar = findViewById(R.id.progress_loading_paths);
        refreshPathsButton = findViewById(R.id.btn_refresh_paths);
        
        // 底部按钮
        testConnectionButton = findViewById(R.id.btn_test_connection);
        saveButton = findViewById(R.id.btn_save);
    }
    
    /**
     * 初始化所有适配器
     */
    private void initAdapters() {
        // 远程路径适配器
        remotePaths.add("/");
        // 使用自定义布局提高视觉清晰度
        remotePathsAdapter = new ArrayAdapter<>(getContext(), 
                R.layout.spinner_item, remotePaths);
        remotePathsAdapter.setDropDownViewResource(R.layout.spinner_item);
        remotePathSpinner.setAdapter(remotePathsAdapter);
    }
    
    /**
     * 设置所有事件监听器
     */
    private void setupListeners() {
        // 刷新路径按钮
        refreshPathsButton.setOnClickListener(v -> refreshRemoteDirectories());
        
        // 远程路径选择事件
        remotePathSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedRemotePath = remotePaths.get(position);
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 不做任何处理
            }
        });
        
        // 底部按钮
        testConnectionButton.setOnClickListener(v -> testConnection());
        saveButton.setOnClickListener(v -> saveSettings());
    }
    
    /**
     * 加载现有SMB设置
     */
    private void loadExistingSettings() {
        enableSmbCheckBox.setChecked(SMBFileUploader.isEnabled());
        enableSmbVideoCheckBox.setChecked(SMBFileUploader.isVideoUploadEnabled());
        
        // 设置默认IP或加载保存的IP
        String savedIp = SMBFileUploader.getServerIp();
        serverIpEditText.setText(savedIp.isEmpty() ? DEFAULT_IP : savedIp);
        
        usernameEditText.setText(SMBFileUploader.getUsername());
        passwordEditText.setText(SMBFileUploader.getPassword());
        shareNameEditText.setText(SMBFileUploader.getShareName());
        
        // 保存当前设置的远程路径
        selectedRemotePath = SMBFileUploader.getRemotePath();
        
        // 如果已经有远程路径，添加到列表
        if (!selectedRemotePath.isEmpty()) {
            remotePaths.clear();
            remotePaths.add(selectedRemotePath);
            remotePathsAdapter.notifyDataSetChanged();
        }
    }
    
    /**
     * 刷新远程目录列表
     */
    private void refreshRemoteDirectories() {
        // 检查参数
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        
        // 验证必要的服务器信息
        if (serverIp.isEmpty()) {
            serverIp = DEFAULT_IP;
            serverIpEditText.setText(serverIp);
        }
        
        if (shareName.isEmpty()) {
            Toast.makeText(getContext(), "请输入共享名称", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 显示进度条
        loadingPathsProgressBar.setVisibility(View.VISIBLE);
        refreshPathsButton.setEnabled(false);
        
        // 清空路径列表
        remotePaths.clear();
        remotePaths.add("正在加载...");
        remotePathsAdapter.notifyDataSetChanged();
        
        // 临时设置参数以获取目录
        SMBFileUploader.setConnectionParams(serverIp, username, password, shareName, selectedRemotePath, true);
        
        // 请求远程目录
        SMBFileUploader.getRemoteDirectories(new SMBFileUploader.DirectoryListCallback() {
            @Override
            public void onDirectoriesLoaded(List<String> directories) {
                loadingPathsProgressBar.post(() -> {
                    // 更新UI
                    loadingPathsProgressBar.setVisibility(View.GONE);
                    refreshPathsButton.setEnabled(true);
                    
                    // 更新目录列表
                    remotePaths.clear();
                    remotePaths.addAll(directories);
                    remotePathsAdapter.notifyDataSetChanged();
                    
                    // 选择之前配置的路径
                    selectPath(selectedRemotePath);
                    
                    Toast.makeText(getContext(), "已获取 " + directories.size() + " 个远程目录", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onDirectoryLoadFailed(String errorMessage) {
                loadingPathsProgressBar.post(() -> {
                    // 更新UI
                    loadingPathsProgressBar.setVisibility(View.GONE);
                    refreshPathsButton.setEnabled(true);
                    
                    // 确保至少有根目录
                    remotePaths.clear();
                    remotePaths.add("/");
                    remotePathsAdapter.notifyDataSetChanged();
                    
                    Toast.makeText(getContext(), "获取远程目录失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    /**
     * 选择指定的路径
     * @param path 要选择的路径
     */
    private void selectPath(String path) {
        // 查找路径在列表中的位置
        int position = remotePaths.indexOf(path);
        
        // 如果找到了路径，则选择它
        if (position >= 0) {
            remotePathSpinner.setSelection(position);
        } else if (!remotePaths.isEmpty()) {
            // 如果未找到，默认选择第一个（通常是根路径）
            remotePathSpinner.setSelection(0);
            selectedRemotePath = remotePaths.get(0);
        }
    }
    
    /**
     * 测试SMB连接
     */
    private void testConnection() {
        // 获取输入的设置
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        String remotePath = selectedRemotePath;
        boolean enabled = enableSmbCheckBox.isChecked();
        boolean videoEnabled = enableSmbVideoCheckBox.isChecked();
        
        // 验证输入
        if (enabled) {
            if (serverIp.isEmpty()) {
                serverIp = DEFAULT_IP;
                serverIpEditText.setText(serverIp);
            }
            
            if (shareName.isEmpty()) {
                Toast.makeText(getContext(), "请输入共享名称", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 临时设置参数进行测试
        SMBFileUploader.setConnectionParams(serverIp, username, password, shareName, remotePath, enabled);
        SMBFileUploader.setVideoUploadEnabled(videoEnabled);
        
        // 禁用测试按钮，显示测试中状态
        testConnectionButton.setEnabled(false);
        testConnectionButton.setText("测试中...");
        
        // 执行连接测试
        SMBFileUploader.testConnection(new SMBFileUploader.UploadCallback() {
            @Override
            public void onUploadSuccess(String remoteFilePath) {
                testConnectionButton.post(() -> {
                    testConnectionButton.setEnabled(true);
                    testConnectionButton.setText("测试连接");
                    Toast.makeText(getContext(), "连接成功! 已自动保存设置", Toast.LENGTH_SHORT).show();
                    
                    // 连接成功后自动保存设置并关闭对话框
                    saveSettings(false);
                });
            }
            
            @Override
            public void onUploadFailed(String errorMessage) {
                testConnectionButton.post(() -> {
                    testConnectionButton.setEnabled(true);
                    testConnectionButton.setText("测试连接");
                    Toast.makeText(getContext(), "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    /**
     * 保存SMB设置
     */
    private void saveSettings() {
        saveSettings(true);
    }
    
    /**
     * 保存SMB设置
     * 
     * @param showToast 是否显示Toast提示
     */
    private void saveSettings(boolean showToast) {
        // 获取输入的设置
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        String remotePath = selectedRemotePath;
        boolean enabled = enableSmbCheckBox.isChecked();
        boolean videoEnabled = enableSmbVideoCheckBox.isChecked();
        
        // 验证输入
        if (enabled) {
            if (serverIp.isEmpty()) {
                serverIp = DEFAULT_IP;
                serverIpEditText.setText(serverIp);
            }
            
            if (shareName.isEmpty()) {
                Toast.makeText(getContext(), "请输入共享名称", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 保存设置
        SMBFileUploader.setConnectionParams(serverIp, username, password, shareName, remotePath, enabled);
        SMBFileUploader.setVideoUploadEnabled(videoEnabled);
        
        // 显示保存成功提示
        if (showToast) {
            Toast.makeText(getContext(), "设置已保存", Toast.LENGTH_SHORT).show();
        }
        
        // 关闭对话框
        dismiss();
    }
} 