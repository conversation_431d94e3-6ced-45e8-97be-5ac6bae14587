# rk3588运行yolov5流程：

## 1.细胞检测模型训练------>得到yolov5s.pt

## 2.细胞检测模型导出------>得到yolov5s.onnx

## 3.细胞检测模型转化------>得到yolov5s.rknn,选择量化参数（int8、fp16）

## 4.安卓部分开发------>1.界面开发 2.c++推理代码编写 3.安卓app内JNI调用c++的方法



## 1、细胞检测模型训练

### Yolov5s模型训练代码：https://github.com/airockchip/yolov5

#### 备注：内部relu函数替换为silu函数。精度略微下降，运行速度提升许多。训练教程见：

https://blog.csdn.net/qq_45945548/article/details/121701492?ops_request_misc=%257B%2522request%255Fid%2522%253A%25224773ab25964883ed6150b948df3cfe85%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=4773ab25964883ed6150b948df3cfe85&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~top_positive~default-1-121701492-null-null.142

#### 环境配置：Anaconda安装见:

https://blog.csdn.net/qq_44000789/article/details/142214660?ops_request_misc=%257B%2522request%255Fid%2522%253A%2522b9d08bfbe0900034d4d33bbce321dc2f%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=b9d08bfbe0900034d4d33bbce321dc2f&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~top_positive~default-1-142214660-null-null.142

#### RTX4060已配置训练环境。环境名：yolov5。



## 2、细胞检测模型导出

#### Yolov5s模型导出为onnx：https://github.com/airockchip/yolov5 ，见其中export.py说明。



## 3、细胞检测模型转换

### 需要用到[rknn-toolkit](https://github.com/airockchip/rknn-toolkit2)的工具链,目前只支持在ubuntu系统上使用，windows下无法使用。

### 解决方案：windows下使用虚拟机。虚拟机内安装docker镜像即可，参考教程见《教程》文件夹。

### 模型转换教程参考：

https://blog.csdn.net/qq_50695483/article/details/146264778?ops_request_misc=%257B%2522request%255Fid%2522%253A%2522307e35603c015139f953170cea813113%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=307e35603c015139f953170cea813113&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~sobaiduend~default-2-146264778-null-null.142

### docker运行命令：

docker run -t -i --privileged -v /dev/bus/usb:/dev/bus/usb -v /home/<USER>/rk_model_zoo/rknn_model_zoo-main:/rknn_model_zoo rknn-toolkit2:2.1.0-cp38 /bin/bash

### 转换命令：

python convert.py ../model/best.onnx rk3588（参考model_zoo里面yolov5/python/convert.py）



### 4、安卓部分开发

#### 开发流程：

#### [c++推理代码](https://github.com/airockchip/rknn_model_zoo/tree/main/examples/yolov5/cpp)开发、[JNI接口函数](https://github.com/maxenergy/rknn_yolov5_android)编写、调用、显示。











