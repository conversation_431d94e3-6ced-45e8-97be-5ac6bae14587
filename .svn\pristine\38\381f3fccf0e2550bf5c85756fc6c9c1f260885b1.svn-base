<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jun 17 14:25:18 CST 2025 -->
<title>EncoderConfig.Builder</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-17">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.video, class: EncoderConfig, class: Builder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.video</a></div>
<h1 title="类 EncoderConfig.Builder" class="title">类 EncoderConfig.Builder</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.video.EncoderConfig.Builder</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>封闭类:</dt>
<dd><a href="EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">EncoderConfig.Builder</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">EncoderConfig构建器类
 <p>
 使用Builder模式构建EncoderConfig实例，支持链式调用设置各项参数。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(android.util.Size)" class="member-name-link">Builder</a><wbr>(android.util.Size&nbsp;size)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">构建EncoderConfig实例</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBitRate(int)" class="member-name-link">setBitRate</a><wbr>(int&nbsp;bitRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置比特率</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBitrateMode(int)" class="member-name-link">setBitrateMode</a><wbr>(int&nbsp;bitrateMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置比特率模式</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setColorFormat(int)" class="member-name-link">setColorFormat</a><wbr>(int&nbsp;colorFormat)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置颜色格式</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setColorRange(int)" class="member-name-link">setColorRange</a><wbr>(int&nbsp;colorRange)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置颜色范围</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setColorStandard(int)" class="member-name-link">setColorStandard</a><wbr>(int&nbsp;colorStandard)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置颜色标准</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFrameRate(int)" class="member-name-link">setFrameRate</a><wbr>(int&nbsp;frameRate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置帧率</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIFrameInterval(int)" class="member-name-link">setIFrameInterval</a><wbr>(int&nbsp;iFrameInterval)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置关键帧间隔</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMimeType(java.lang.String)" class="member-name-link">setMimeType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;mimeType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置MIME类型</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProfileLevel(int)" class="member-name-link">setProfileLevel</a><wbr>(int&nbsp;profileLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置编码器配置文件级别</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(android.util.Size)">
<h3>Builder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Builder</span><wbr><span class="parameters">(android.util.Size&nbsp;size)</span></div>
<div class="block">构造函数</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>size</code> - 视频尺寸</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></code> - 如果尺寸为null</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMimeType(java.lang.String)">
<h3>setMimeType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setMimeType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;mimeType)</span></div>
<div class="block">设置MIME类型</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>mimeType</code> - 视频MIME类型，如"video/avc"</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBitRate(int)">
<h3>setBitRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setBitRate</span><wbr><span class="parameters">(int&nbsp;bitRate)</span></div>
<div class="block">设置比特率</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>bitRate</code> - 比特率，单位bps</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFrameRate(int)">
<h3>setFrameRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setFrameRate</span><wbr><span class="parameters">(int&nbsp;frameRate)</span></div>
<div class="block">设置帧率</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>frameRate</code> - 帧率，单位fps</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setColorFormat(int)">
<h3>setColorFormat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setColorFormat</span><wbr><span class="parameters">(int&nbsp;colorFormat)</span></div>
<div class="block">设置颜色格式</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>colorFormat</code> - 颜色格式，参见MediaCodecInfo.CodecCapabilities</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIFrameInterval(int)">
<h3>setIFrameInterval</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setIFrameInterval</span><wbr><span class="parameters">(int&nbsp;iFrameInterval)</span></div>
<div class="block">设置关键帧间隔</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>iFrameInterval</code> - 关键帧间隔，单位秒</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBitrateMode(int)">
<h3>setBitrateMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setBitrateMode</span><wbr><span class="parameters">(int&nbsp;bitrateMode)</span></div>
<div class="block">设置比特率模式</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>bitrateMode</code> - 比特率模式，参见MediaCodecInfo.EncoderCapabilities</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setColorRange(int)">
<h3>setColorRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setColorRange</span><wbr><span class="parameters">(int&nbsp;colorRange)</span></div>
<div class="block">设置颜色范围</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>colorRange</code> - 颜色范围，参见MediaFormat</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setColorStandard(int)">
<h3>setColorStandard</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setColorStandard</span><wbr><span class="parameters">(int&nbsp;colorStandard)</span></div>
<div class="block">设置颜色标准</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>colorStandard</code> - 颜色标准，参见MediaFormat</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProfileLevel(int)">
<h3>setProfileLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.Builder.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig.Builder</a></span>&nbsp;<span class="element-name">setProfileLevel</span><wbr><span class="parameters">(int&nbsp;profileLevel)</span></div>
<div class="block">设置编码器配置文件级别</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>profileLevel</code> - 配置文件级别，参见MediaCodecInfo.CodecProfileLevel</dd>
<dt>返回:</dt>
<dd>构建器实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="build()">
<h3>build</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EncoderConfig.html" title="com.android.rockchip.camera2.video中的类">EncoderConfig</a></span>&nbsp;<span class="element-name">build</span>()</div>
<div class="block">构建EncoderConfig实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>根据当前配置创建的EncoderConfig实例</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
