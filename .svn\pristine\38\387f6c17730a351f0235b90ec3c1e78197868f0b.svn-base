/* ***************************************************************
//  usys_service   version:  1.0  date: 6/5/2009
//  -------------------------------------------------------------
//  <PERSON><PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __usys_service_h__
#define __usys_service_h__

#include <vector>
#include <string>
#include <utiny/utiny_config.h>

class UBEDA_API usys_service
{
public:

	usys_service();
	virtual ~usys_service();

	//
	// Shutdown the service.
	//
	virtual bool shutdown();

	//
	// Notify the service about a signal interrupt. The default
	// implementation invokes shutdown().
	//
	virtual void interrupt();

	//
	// The primary entry point for services. This function examines
	// the given argument vector for reserved options and takes the
	// appropriate action. The reserved options are shown below.
	//
	// Win32:
	//
	// --install NAME [--display DISP] [--executable EXEC]
	// --uninstall NAME
	// --start NAME [args ...]
	// --stop NAME
	// --service NAME
	//
	// Unix:
	//
	// --daemon [--nochdir] [--noclose]
	//
	// If --service or --daemon are specified, the program runs as
	// a service, otherwise the program runs as a regular foreground
	// process.
	//
	// The return value is an exit status code: EXIT_FAILURE or
	// EXIT_SUCCESS.
	//
	int main(int&, char*[]);

	//
	// Returns the usys_service singleton.
	//
	static usys_service* instance();

	//
	// Indicates whether the program is running as a Win32 service or
	// Unix daemon.
	//
	bool service() const;

	//
	// Returns the program name. If the program is running as a Win32
	// service, the return value is the service name. Otherwise the
	// return value is the executable name (i.e., argv[0]).
	//
	std::string name() const;

	//
	// Alternative entry point for services that use their own
	// command-line options. Instead of invoking main(), the
	// program processes its command-line options and invokes
	// run(). To run as a Win32 service or Unix daemon, the
	// program must first invoke configure_service() or
	// configure_daemon(), respectively.
	//
	// The return value is an exit status code: EXIT_FAILURE or
	// EXIT_SUCCESS.
	//
	int run(int&, char*[]);

#ifdef _WIN32

	//
	// Configures the program to run as a Win32 service with the
	// given name.
	//
	void configure_service(const std::string&);

	//
	// Installs a Win32 service.
	//
	int install_service(bool, const std::string&, const std::string&, const std::string&,
		const std::vector<std::string>&);

	//
	// Uninstalls a Win32 service.
	//
	int uninstall_service(bool, const std::string&);

	//
	// Starts a Win32 service. The argument vector is passed to the
	// service at startup.
	//
	int start_service(const std::string&, const std::vector<std::string>&);

	//
	// Stops a running Win32 service.
	//
	int stop_service(const std::string&);

	static void set_module_handle(HMODULE);

#else

	//
	// Configures the program to run as a Unix daemon. The first
	// argument indicates whether the daemon should change its
	// working directory to the root directory. The second
	// argument indicates whether extraneous file descriptors are
	// closed. If the value of the last argument is not an empty
	// string, the daemon writes its process ID to the given
	// filename.
	//
	void configure_daemon(bool, bool, const std::string&);

#endif

	//
	// Invoked by the CtrlCHandler.
	//
	virtual void handle_interrupt(int);

protected:

	//
	// Prepare a service for execution, including the creation and
	// activation of object adapters and servants.
	//
	virtual bool start(int, char*[]) = 0;

	//
	// Blocks until the service shuts down.
	//
	virtual void wait_for_shutdown();

	//
	// Clean up resources after shutting down.
	//
	virtual bool stop();

	//
	// Log an error.
	//
	virtual void error(const std::string&);

	//
	// Log a warning.
	//
	virtual void warning(const std::string&);

	//
	// Log trace information.
	//
	virtual void trace(const std::string&);

	//
	// Log a literal message.
	//
	virtual void print(const std::string&);

	//
	// Enable the CtrlCHandler to invoke interrupt() when a signal occurs.
	//
	void enable_interrupt();

	//
	// Ignore signals.
	//
	void disable_interrupt();

private:

	bool nohup_;
	bool service_;
	std::string name_;

	static usys_service* instance_;

#ifdef _WIN32

	int run_service(int, char*[]);
	void terminate_service(DWORD);
	bool wait_for_service_state(SC_HANDLE, DWORD, SERVICE_STATUS&);
	void show_service_status(const std::string&, SERVICE_STATUS&);

	SERVICE_STATUS_HANDLE status_handle_;
	std::vector<std::string> service_args_;

public:

	void service_main(int, char*[]);
	void control(int);

#else

	int run_daemon(int, char*[]);

	bool change_directory_;
	bool close_files_;
	std::string pid_file_;

#endif
};

#endif // __usys_service_h__
