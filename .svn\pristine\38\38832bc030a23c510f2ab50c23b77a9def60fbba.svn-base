#Thu Feb 06 10:05:11 CST 2025
base.0=D\:\\RK3588\\touptek_serial_rk\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\RK3588\\touptek_serial_rk\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=D\:\\RK3588\\touptek_serial_rk\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.3=D\:\\RK3588\\touptek_serial_rk\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.4=D\:\\RK3588\\touptek_serial_rk\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=13/classes.dex
path.3=11/classes.dex
path.4=14/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
