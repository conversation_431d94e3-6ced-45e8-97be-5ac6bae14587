<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jun 17 14:25:18 CST 2025 -->
<title>TouptekIspParam.OnDataChangedListener</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-17">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, enum: TouptekIspParam, interface: OnDataChangedListener">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="接口 TouptekIspParam.OnDataChangedListener" class="title">接口 TouptekIspParam.OnDataChangedListener</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已知实现类:</dt>
<dd><code><a href="../view/ROIView.html" title="com.android.rockchip.camera2.view中的类">ROIView</a></code></dd>
</dl>
<dl class="notes">
<dt>封闭类:</dt>
<dd><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static interface </span><span class="element-name type-name-label">TouptekIspParam.OnDataChangedListener</span></div>
<div class="block">定义数据变化监听器接口。
 <p>
 用于监听数据变化事件。
 应用可以实现此接口来接收参数值变化的通知。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">抽象方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">onDataChanged</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;newValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">当 int 类型数据发生变化时调用。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)" class="member-name-link">onLongDataChanged</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;newValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">当 long 类型数据发生变化时调用。</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>onDataChanged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">onDataChanged</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;newValue)</span></div>
<div class="block">当 int 类型数据发生变化时调用。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 发生变化的参数 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a></dd>
<dd><code>newValue</code> - 新的 int 类型值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)">
<h3>onLongDataChanged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">onLongDataChanged</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;newValue)</span></div>
<div class="block">当 long 类型数据发生变化时调用。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 发生变化的参数 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a></dd>
<dd><code>newValue</code> - 新的 long 类型值</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
