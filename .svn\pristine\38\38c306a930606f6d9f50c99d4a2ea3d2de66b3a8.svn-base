<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_main_pop_settings" modulePackage="com.android.rockchip.camera2" filePath="app\src\main\res\layout\layout_main_pop_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_main_pop_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="165" endOffset="14"/></Target><Target id="@+id/txt_staus" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="23" endOffset="54"/></Target><Target id="@+id/btn_edid" view="Button"><Expressions/><location startLine="25" startOffset="8" endLine="35" endOffset="33"/></Target><Target id="@+id/btn_record" view="Button"><Expressions/><location startLine="37" startOffset="8" endLine="48" endOffset="44"/></Target><Target id="@+id/btn_screenshot" view="Button"><Expressions/><location startLine="50" startOffset="8" endLine="60" endOffset="51"/></Target><Target id="@+id/btn_pip" view="Button"><Expressions/><location startLine="62" startOffset="8" endLine="72" endOffset="44"/></Target><Target id="@+id/btn_pq" view="Button"><Expressions/><location startLine="74" startOffset="8" endLine="85" endOffset="39"/></Target><Target id="@+id/btn_lf_range" view="Button"><Expressions/><location startLine="87" startOffset="8" endLine="98" endOffset="39"/></Target><Target id="@+id/btn_calc_luma" view="Button"><Expressions/><location startLine="100" startOffset="8" endLine="111" endOffset="39"/></Target><Target id="@+id/btn_test2" view="Button"><Expressions/><location startLine="113" startOffset="8" endLine="123" endOffset="42"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="126" startOffset="8" endLine="134" endOffset="45"/></Target><Target id="@+id/rm_pop_settings" view="com.android.rockchip.camera2.widget.RoundMenu"><Expressions/><location startLine="138" startOffset="4" endLine="163" endOffset="51"/></Target><Target id="@+id/txt_hdmirx_edid_1" view="TextView"><Expressions/><location startLine="144" startOffset="8" endLine="148" endOffset="33"/></Target><Target id="@+id/txt_hdmirx_edid_3" view="TextView"><Expressions/><location startLine="150" startOffset="8" endLine="155" endOffset="33"/></Target><Target id="@+id/txt_hdmirx_edid_2" view="TextView"><Expressions/><location startLine="157" startOffset="8" endLine="161" endOffset="33"/></Target></Targets></Layout>