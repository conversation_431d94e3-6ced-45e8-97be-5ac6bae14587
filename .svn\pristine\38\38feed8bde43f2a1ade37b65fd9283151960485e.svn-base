#ifndef __usys_mutex_h__
#define __usys_mutex_h__

#include <assert.h>
#include <utiny/utiny_config.h>
#ifndef _WIN32
#include <pthread.h>
#include <errno.h>
#ifdef __APPLE__
#include <dispatch/dispatch.h>
#else
#include <semaphore.h>
#endif
#endif

class UBEDA_API usys_semaphore
{
#ifdef _WIN32
    HANDLE sem_;
#elif defined(__APPLE__)
	dispatch_semaphore_t	sem_;
#else
    sem_t sem_;
#endif
public:
    usys_semaphore(int initcount);
    ~usys_semaphore();
    int acquire(int ms = -1);
    int release();
};

class UBEDA_API usys_mutex_null
{
public:
    inline usys_mutex_null() {};
    ~usys_mutex_null() {};

    void acquire() const {};
    void release() const {};

private:
    usys_mutex_null(const usys_mutex_null&) {};// noncopyable
    void operator = (const usys_mutex_null&) {};
};

class UBEDA_API usys_mutex
{
public:
    inline usys_mutex();
    ~usys_mutex();

    void acquire() const;
    void release() const;

private:
    usys_mutex(const usys_mutex&);// noncopyable
    void operator = (const usys_mutex&);

#ifdef _WIN32
    mutable CRITICAL_SECTION mutex_;
#else
    mutable pthread_mutex_t mutex_;
#endif
};

class UBEDA_API usys_null_mutex
{
public:
    usys_null_mutex() {}
    ~usys_null_mutex() {}

    void acquire() {}
    void release() {}
};

template<typename T>
class UBEDA_API usys_guard
{
    T& lock_;
public:
    // = Initialization and termination methods.
    usys_guard(T& l):lock_(l)
    {
        lock_.acquire();
    };
    ~usys_guard()
    {
        lock_.release();
    };
};


#ifdef _WIN32

inline usys_semaphore::usys_semaphore(int initcount)
{
    sem_ = CreateSemaphore(NULL, initcount, 0x7fffffff, NULL);
}

inline usys_semaphore::~usys_semaphore()
{
    if (sem_)
        CloseHandle(sem_);
}

inline int usys_semaphore::acquire(int ms)
{
    if (WaitForSingleObject(sem_, ms) == WAIT_OBJECT_0)
        return 0;
    return -1;
}

inline int usys_semaphore::release()
{
    if (ReleaseSemaphore(sem_, 1, NULL) == 0)
        return -1;
    return 0;
}

inline usys_mutex::usys_mutex()
{
    InitializeCriticalSection(&mutex_);
}

inline usys_mutex::~usys_mutex()
{
    DeleteCriticalSection(&mutex_);
}

inline void usys_mutex::acquire() const
{
    EnterCriticalSection(&mutex_);
    assert(mutex_.RecursionCount == 1);
}

inline void usys_mutex::release() const
{
    assert(mutex_.RecursionCount == 1);
    LeaveCriticalSection(&mutex_);
}

#else

inline usys_semaphore::usys_semaphore(int initcount)
{
#ifdef __APPLE__
	sem_ = dispatch_semaphore_create(initcount);
#else
    sem_init(&sem_, 0, initcount);
#endif
}

inline usys_semaphore::~usys_semaphore()
{
#ifdef __APPLE__
	dispatch_release(sem_);
#else
    sem_destroy(&sem_);
#endif
}

inline int usys_semaphore::acquire(int timeout)
{
#ifdef __APPLE__
	return (int)dispatch_semaphore_wait(sem_, DISPATCH_TIME_FOREVER);
#else
    return sem_wait(&sem_);
#endif
}

inline int usys_semaphore::release()
{
#ifdef __APPLE__
	return (int)dispatch_semaphore_signal(sem_);
#else
    return sem_post(&sem_);
#endif
}

inline usys_mutex::usys_mutex()
{
    pthread_mutexattr_t attr;
    pthread_mutexattr_init(&attr);
    pthread_mutexattr_settype(&attr, PTHREAD_MUTEX_RECURSIVE);
    pthread_mutex_init(&mutex_, &attr);
    pthread_mutexattr_destroy(&attr);
}

inline usys_mutex::~usys_mutex()
{
    pthread_mutex_destroy(&mutex_);
}

inline void usys_mutex::acquire() const
{
    pthread_mutex_lock(&mutex_);
}

inline void usys_mutex::release() const
{
    pthread_mutex_unlock(&mutex_);
}

#endif

#endif // __usys_mutex_h__
