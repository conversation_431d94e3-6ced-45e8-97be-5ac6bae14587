package com.android.rockchip.camera2.rtspserver;

import android.Manifest;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.media.projection.MediaProjectionManager;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.OnLifecycleEvent;

import com.android.rockchip.camera2.video.VideoEncoder;
import com.pedro.common.ConnectChecker;
import com.pedro.rtspserver.server.RtspServer;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * RTSP屏幕录制器封装类
 * <p>
 * 提供启动和停止RTSP屏幕录制功能
 * 处理权限申请和屏幕录制逻辑
 * </p>
 */
public class RtspScreenRecorder implements ConnectChecker {
    /* 日志标签 */
    private static final String TAG = "RtspScreenRecorder";
    
    /* 流源类型常量 */
    public static final int SOURCE_TYPE_SCREEN = 0;   /* 录屏流 */
    public static final int SOURCE_TYPE_CAMERA = 1;   /* 相机流 */
    
    /* 默认RTSP端口 */
    private static final int DEFAULT_PORT = 8554;
    
    /* 默认视频参数 */
    private static final int DEFAULT_WIDTH = 1920;
    private static final int DEFAULT_HEIGHT = 1080;
    private static final int DEFAULT_BITRATE = 6000000; /* 6Mbps */
    private static final int DEFAULT_FRAMERATE = 30;
    
    /* 需要申请的权限（Android 10以下需要） */
    private static final String[] PERMISSIONS = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.RECORD_AUDIO
    };
    
    /* 上下文 */
    private final FragmentActivity mActivity;
    
    /* RTSP端口 */
    private int mPort = DEFAULT_PORT;
    
    /* 自定义IP地址 */
    private String mIpAddress = null;
    
    /* 是否正在录制 */
    private boolean mIsRecording = false;
    
    /* 是否曾在网络断开前在推流 */
    private boolean mWasStreamingBeforeDisconnect = false;
    
    /* 新的回调系统 */
    private RtspCallbacks mCallbacks;

    /* 媒体投影管理器 */
    private MediaProjectionManager mMediaProjectionManager;
    
    /* 媒体投影结果启动器 */
    private ActivityResultLauncher<Intent> mMediaProjectionLauncher;
    
    /* 权限请求启动器 */
    private ActivityResultLauncher<String[]> mPermissionLauncher;
    
    /* 状态广播接收器 */
    private BroadcastReceiver mStatusReceiver;
    
    /* 视频参数 */
    private int mVideoWidth = DEFAULT_WIDTH;
    private int mVideoHeight = DEFAULT_HEIGHT;
    private int mVideoBitRate = DEFAULT_BITRATE;
    private int mVideoFrameRate = DEFAULT_FRAMERATE;

    /* 目标Activity类名 */
    private String targetActivityClassName;
    
    /* 流源类型，默认为录屏 */
    private int mSourceType = SOURCE_TYPE_SCREEN;
    
    /* RTSP服务器相关变量 */
    private RtspServer mRtspServer;
    private String mRtspUrl;
    private boolean mRtspInitialized = false;
    private boolean mSpsPpsSetted = false;
    
    /* SPS和PPS缓存 - H.264编码参数 */
    private byte[] mSps;
    private byte[] mPps;
    
    /* 视频编码器引用 - 用于相机模式 */
    private VideoEncoder videoEncoder;
    
    /* 生命周期观察者 */
    private final LifecycleObserver lifecycleObserver = new LifecycleObserver() {
        @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
        public void onPause() {
            // 当应用进入后台时的处理
            if (mIsRecording) {

            }
        }
        
        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        public void onDestroy() {
            // 确保资源释放
            release();
        }
    };
    
    /* 重连计数 */
    private int reconnectAttempts = 0;
    
    /* 重连最大次数 */
    private int maxReconnectAttempts = 3;
    
    /* 重连延迟(毫秒) */
    private int reconnectDelay = 5000;
    
    /* 自动重连标志 */
    private boolean autoReconnect = true;
    
    /* 自适应码率标志 */
    private boolean adaptiveBitrate = false;
    
    /* 客户端连接数量 */
    private int connectedClients = 0;
    
    /* 重连Handler */
    private final Handler reconnectHandler = new Handler(Looper.getMainLooper());
    
    /* 重连任务 */
    private final Runnable reconnectRunnable = new Runnable() {
        @Override
        public void run() {
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                mCallbacks.notifyReconnecting(reconnectAttempts, maxReconnectAttempts);
                Log.d(TAG, "尝试重连 RTSP 服务器: " + reconnectAttempts + "/" + maxReconnectAttempts);
                
                // 根据源类型选择合适的重连方法
                if (mSourceType == SOURCE_TYPE_CAMERA) {
                    startRtspServer();
                } else {
                    // 屏幕录制模式
                    requestScreenCapture();
                }
            } else {
                Log.e(TAG, "达到最大重连次数，放弃重连");
                mCallbacks.notifyError(RtspConfiguration.ErrorType.CONNECTION_ERROR, 
                                 "达到最大重连次数(" + maxReconnectAttempts + ")，无法恢复连接");
            }
        }
    };

    /**
     * 构造函数 - 创建RTSP屏幕录制器实例
     * 
     * @param activity FragmentActivity上下文，用于启动媒体投影和权限请求
     * @param callbacks 回调系统，接收RTSP流状态变更通知
     */
    public RtspScreenRecorder(FragmentActivity activity, RtspCallbacks callbacks) {
        this.mActivity = activity;
        this.mCallbacks = callbacks != null ? callbacks : RtspCallbacks.builder().build();
        
        // 初始化媒体投影管理器
        mMediaProjectionManager = (MediaProjectionManager) 
                activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        
        // 初始化媒体投影启动器 - 处理屏幕录制权限请求结果
        mMediaProjectionLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                        // 权限获取成功，开始屏幕录制
                        startScreenRecording(result.getResultCode(), result.getData());
                    } else {
                        // 用户拒绝屏幕录制权限
                        mCallbacks.notifyError(RtspConfiguration.ErrorType.PERMISSION_DENIED, "未获得屏幕录制权限");
                    }
                });
        
        // 初始化权限请求启动器 - 处理存储和音频权限请求结果
        mPermissionLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(),
                permissions -> {
                    boolean allGranted = true;
                    for (Boolean granted : permissions.values()) {
                        if (!granted) {
                            allGranted = false;
                            break;
                        }
                    }
                    
                    if (allGranted) {
                        // 权限获取成功，请求屏幕录制
                        requestScreenCapture();
                    } else {
                        // 权限请求被拒绝
                        mCallbacks.notifyError(RtspConfiguration.ErrorType.PERMISSION_DENIED, "需要存储和音频权限才能继续");
                    }
                });
        
        // 初始化状态广播接收器
        initStatusReceiver();
        
        // 注册生命周期观察者
        if (activity instanceof LifecycleOwner) {
            ((LifecycleOwner)activity).getLifecycle().addObserver(lifecycleObserver);
        }
        
        // 初始化网络监控
        initNetworkMonitor();
    }

    /**
     * 设置回调系统
     * 
     * @param callbacks 新的回调系统
     * @return this用于链式调用
     */
    public RtspScreenRecorder setCallbacks(RtspCallbacks callbacks) {
        this.mCallbacks = callbacks != null ? callbacks : RtspCallbacks.builder().build();
        return this;
    }

    /**
     * 配置RTSP流参数
     *
     * @param config RTSP配置对象
     * @return 该RtspScreenRecorder实例，用于链式调用
     */
    public RtspScreenRecorder configure(RtspConfiguration config) {
        if (config != null) {
            this.mVideoWidth = config.width;
            this.mVideoHeight = config.height;
            this.mVideoBitRate = config.bitRate;
            this.mVideoFrameRate = config.frameRate;
            
            if (config.targetActivityClass != null) {
                this.targetActivityClassName = config.targetActivityClass.getName();
            }
            
            if (config.port > 0) {
                this.mPort = config.port;
            }
            
            this.mIpAddress = config.ipAddress;
            
            // 新增配置参数
            this.autoReconnect = config.autoReconnect;
            this.reconnectDelay = config.reconnectDelay;
            this.maxReconnectAttempts = config.reconnectMaxAttempts;
            this.adaptiveBitrate = config.adaptiveBitrate;
        }
        return this;
    }
    
    /**
     * 设置视频编码器
     * 显式设置编码器实例
     * 
     * @param encoder 视频编码器实例
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setVideoEncoder(VideoEncoder encoder) {
        this.videoEncoder = encoder;
        return this;
    }

    /**
     * 初始化网络监控
     */
    private void initNetworkMonitor() {
        ConnectivityManager cm = (ConnectivityManager) mActivity.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkRequest request = new NetworkRequest.Builder().build();
        cm.registerNetworkCallback(request, new ConnectivityManager.NetworkCallback() {
            @Override
            public void onLost(Network network) {
                // 网络断开时处理
                if (mIsRecording) {
                    mWasStreamingBeforeDisconnect = true;
                    Log.w(TAG, "网络连接断开，暂停RTSP流");
                    
                    // 通知监听器
                    mCallbacks.notifyError(RtspConfiguration.ErrorType.NETWORK_ERROR, "网络连接断开");
                }
            }
            
            @Override
            public void onAvailable(Network network) {
                // 网络恢复时处理
                if (mWasStreamingBeforeDisconnect && autoReconnect) {
                    mWasStreamingBeforeDisconnect = false;
                    Log.d(TAG, "网络连接恢复，尝试重连RTSP");
                    
                    // 重置重连计数
                    reconnectAttempts = 0;
                    
                    // 延迟一段时间后尝试重连
                    reconnectHandler.removeCallbacks(reconnectRunnable);
                    reconnectHandler.postDelayed(reconnectRunnable, 2000);
                }
            }
        });
    }
    
    /**
     * 初始化状态广播接收器
     * 用于接收RTSP服务的状态变更广播
     */
    private void initStatusReceiver() {
        mStatusReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if ("com.android.touptek.rtspserver.ACTION_RTSP_STARTED".equals(intent.getAction())) {
                    // RTSP服务已启动
                    mIsRecording = true;
                    String rtspUrl = intent.getStringExtra("rtspUrl");
                    if (rtspUrl != null) {
                        mCallbacks.notifyRtspStarted(rtspUrl);
                    }
                } else if ("com.android.touptek.rtspserver.ACTION_RTSP_STOPPED".equals(intent.getAction())) {
                    // RTSP服务已停止
                    mIsRecording = false;
                    mCallbacks.notifyRtspStopped();
                }
            }
        };
        
        // 注册广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction("com.android.touptek.rtspserver.ACTION_RTSP_STARTED");
        filter.addAction("com.android.touptek.rtspserver.ACTION_RTSP_STOPPED");
        
        // 根据Android版本选择适当的注册方式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            mActivity.registerReceiver(mStatusReceiver, filter, Context.RECEIVER_EXPORTED);
        } else {
            mActivity.registerReceiver(mStatusReceiver, filter);
        }
    }

    /**
     * 启动RTSP流
     * 根据设置的源类型执行不同的操作：
     * - 相机源: 直接初始化并启动RTSP服务器
     * - 屏幕源: 请求必要权限并启动屏幕录制
     */
    public void start() {
        if (mIsRecording) {
            return;
        }

        try {
            // 重置重连计数
            reconnectAttempts = 0;
            
            // 根据源类型执行不同的启动流程
            if (mSourceType == SOURCE_TYPE_CAMERA) {
                startCameraStream();
            } else {
                startScreenStream();
            }
        } catch (Exception e) {
            Log.e(TAG, "启动RTSP流失败", e);
        }
    }
    
    /**
     * 启动相机流
     */
    private void startCameraStream() {
        // 检查是否设置了编码器
        if (videoEncoder == null) {
            return;
        }

        // 初始化RTSP服务器
        initRtspServer();

        // 从VideoEncoder获取SPS和PPS
        MediaFormat currentFormat = videoEncoder.getEncoderOutputFormat();
        if (currentFormat != null) {
            ByteBuffer spsBuffer = currentFormat.getByteBuffer("csd-0");
            ByteBuffer ppsBuffer = currentFormat.getByteBuffer("csd-1");

            if (spsBuffer != null && ppsBuffer != null) {
                byte[] sps = new byte[spsBuffer.remaining()];
                byte[] pps = new byte[ppsBuffer.remaining()];
                spsBuffer.get(sps);
                ppsBuffer.get(pps);

                // 缓存SPS和PPS
                this.mSps = sps;
                this.mPps = pps;
            }
        }

        // 启动RTSP服务器
        startRtspServer();

        // 启用视频编码器的RTSP发送功能
        videoEncoder.setOutputCallback(createVideoOutputCallback());

        // 请求关键帧
        videoEncoder.requestKeyFrame();
        
        // 如果启用了自适应码率
        if (adaptiveBitrate) {
            startAdaptiveBitrateMonitoring();
        }
    }
    
    /**
     * 启动屏幕流
     */
    private void startScreenStream() {
        // 检查权限并请求屏幕录制
        if (checkPermissions()) {
            requestScreenCapture();
        } else {
            requestPermissions();
        }
    }

    /**
     * 停止RTSP流
     * 根据流源类型执行不同的停止操作
     */
    public void stop() {
        if (!mIsRecording) {
            return;
        }

        try {
            if (mSourceType == SOURCE_TYPE_SCREEN) {
                // 对于屏幕录制，停止包内可见的ScreenRecorderService服务
                ScreenRecorderService.stopRtspService(mActivity);
            } else {
                // 对于相机流，禁用编码器输出并停止服务器
                if (videoEncoder != null) {
                    videoEncoder.setOutputCallback(null);
                }
                stopRtspServer();
            }
        } catch (Exception e) {
            Log.e(TAG, "停止RTSP流失败", e);
        }
    }
    
    /**
     * 无缝切换流源类型
     * 如果当前正在推流，会自动停止当前流并启动新源的流
     *
     * @param newSourceType 新的流源类型
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder switchSource(int newSourceType) {
        if (this.mSourceType == newSourceType) {
            return this; // 源类型相同，不需要切换
        }
        
        boolean wasStreaming = mIsRecording;
        
        // 如果正在推流，先停止
        if (wasStreaming) {
            stop();
        }
        
        // 切换源类型
        this.mSourceType = newSourceType;
        
        // 如果之前在推流，切换后自动重新开始推流
        if (wasStreaming) {
            start();
        }
        
        return this;
    }
    
    /**
     * 启用/禁用自动重连
     * 
     * @param enable 是否启用自动重连
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder enableAutoReconnect(boolean enable) {
        this.autoReconnect = enable;
        return this;
    }
    
    /**
     * 启用/禁用自适应码率
     * 
     * @param enable 是否启用自适应码率
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder enableAdaptiveBitrate(boolean enable) {
        this.adaptiveBitrate = enable;
        if (enable && mIsRecording) {
            startAdaptiveBitrateMonitoring();
        }
        return this;
    }
    
    /**
     * 创建视频数据输出回调
     * 用于连接VideoEncoder与RTSP流
     *
     * @return 视频数据输出回调实例
     */
    public VideoEncoder.VideoDataOutputCallback createVideoOutputCallback() {
        return new VideoEncoder.VideoDataOutputCallback() {
            @Override
            public void onVideoFormatChanged(MediaFormat format, ByteBuffer spsBuffer, ByteBuffer ppsBuffer) {
                if (spsBuffer != null && ppsBuffer != null) {
                    byte[] sps = new byte[spsBuffer.remaining()];
                    byte[] pps = new byte[ppsBuffer.remaining()];
                    spsBuffer.get(sps);
                    ppsBuffer.get(pps);
                    setSpsPps(sps, pps);
                }
            }

            //当相机的数据可用的时候通过RTSP发送H264的data
            @Override
            public void onVideoDataAvailable(ByteBuffer encodedData, MediaCodec.BufferInfo bufferInfo) {
                sendVideoData(encodedData, bufferInfo);
            }
        };
    }

    /**
     * 确保SPS/PPS设置成功
     */
    private void ensureSpsPpsSet() {
        if (!mSpsPpsSetted && mSps != null && mPps != null && mRtspServer != null && mRtspServer.isRunning()) {
            try {
                ByteBuffer spsBuff = ByteBuffer.wrap(mSps);
                ByteBuffer ppsBuff = ByteBuffer.wrap(mPps);
                mRtspServer.setVideoInfo(spsBuff, ppsBuff, null);
                mSpsPpsSetted = true;
                Log.d(TAG, "成功设置SPS和PPS到RTSP服务器");
            } catch (Exception e) {
                Log.e(TAG, "设置SPS和PPS失败，将在5秒后重试", e);
                // 添加重试机制
                new Handler(Looper.getMainLooper()).postDelayed(this::ensureSpsPpsSet, 5000);
            }
        }
    }
    

    
    /**
     * 启动RTSP服务器
     * 如尚未初始化，先进行初始化
     */
    private void startRtspServer() {
        if (!mRtspInitialized) {
            initRtspServer();
        }
        
        try {
            if (mRtspServer != null && !mRtspServer.isRunning()) {
                // 启动尚未运行的服务器
                Log.d(TAG, "启动RTSP服务器: " + mRtspUrl);
                mRtspServer.startServer();
                mIsRecording = true;
                
                // 如果已经有SPS和PPS，设置到服务器
                if (mSps != null && mPps != null) {
                    Log.d(TAG, "服务器启动后设置之前缓存的SPS和PPS");
                    ByteBuffer spsBuff = ByteBuffer.wrap(mSps);
                    ByteBuffer ppsBuff = ByteBuffer.wrap(mPps);
                    mRtspServer.setVideoInfo(spsBuff, ppsBuff, null);
                    mSpsPpsSetted = true;
                }
                
                // 通知回调
                mCallbacks.notifyRtspStarted(mRtspUrl);
            } else if (mRtspServer != null && mRtspServer.isRunning()) {
                // 服务器已在运行，记录状态并执行必要的设置
                Log.d(TAG, "RTSP服务器已经在运行: " + mRtspUrl);
                mIsRecording = true;
                
                // 如果已经在运行但还没设置SPS/PPS，尝试设置
                ensureSpsPpsSet();
                
                // 通知回调
                mCallbacks.notifyRtspStarted(mRtspUrl);
            } else {
                // 服务器未初始化或出现问题
                Log.e(TAG, "RTSP服务器未初始化，无法启动");
                mCallbacks.notifyError(RtspConfiguration.ErrorType.SERVER_ERROR, "RTSP服务器未初始化，无法启动");
            }
        } catch (Exception e) {
            Log.e(TAG, "启动RTSP服务器失败", e);
            mCallbacks.notifyError(RtspConfiguration.ErrorType.SERVER_ERROR, "启动RTSP服务器失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止RTSP服务器
     * 如服务器正在运行，停止它并通知监听器
     */
    private void stopRtspServer() {
        try {
            if (mRtspServer != null && mRtspServer.isRunning()) {
                mRtspServer.stopServer();
                mIsRecording = false;
                
                // 通知回调
                mCallbacks.notifyRtspStopped();
            }
        } catch (Exception e) {
            Log.e(TAG, "停止RTSP服务器失败", e);
            mCallbacks.notifyError(RtspConfiguration.ErrorType.SERVER_ERROR, "停止RTSP服务器失败: " + e.getMessage());
        }
    }

    /**
     * 初始化RTSP服务器
     * 创建RtspServer实例并设置监听器
     */
    private void initRtspServer() {
        try {
            if (mRtspServer == null) {
                // 创建新的RTSP服务器实例
                mRtspServer = new RtspServer(this, mPort);
                mRtspServer.setLogs(false); // 禁用日志以减少输出

                // 如果提供了自定义IP地址，则使用它，否则使用服务器检测到的IP
                if (mIpAddress != null && !mIpAddress.isEmpty()) {
                    mRtspUrl = "rtsp://" + mIpAddress + ":" + mPort;
                } else {
                    mRtspUrl = "rtsp://" + mRtspServer.getServerIp() + ":" + mPort;
                }

                Log.i(TAG, "RTSP服务地址: " + mRtspUrl);
                mRtspInitialized = true;
            }
        } catch (Exception e) {
            Log.e(TAG, "初始化RTSP服务器失败", e);

            // 尝试使用本地回环地址作为备选
            if (mIpAddress != null && !mIpAddress.isEmpty()) {
                mRtspUrl = "rtsp://" + mIpAddress + ":" + mPort;
            } else {
                mRtspUrl = "rtsp://127.0.0.1:" + mPort;
            }
        }
    }
    
    /**
     * 启动屏幕录制（内部方法）
     * 在获得屏幕录制权限后调用，启动前台服务
     * 注意：内部使用包级别访问权限的ScreenRecorderService类
     * 
     * @param resultCode 媒体投影结果码
     * @param resultData 媒体投影结果数据
     */
    private void startScreenRecording(int resultCode, Intent resultData) {
        try {
            // 使用ScreenRecorderService启动RTSP服务，传递视频参数
            ScreenRecorderService.startRtspService(
                    mActivity, resultCode, resultData, mPort, mIpAddress,
                    mVideoWidth, mVideoHeight, mVideoBitRate, mVideoFrameRate, targetActivityClassName);
        } catch (Exception e) {
            Log.e(TAG, "启动RTSP服务失败", e);
        }
    }
    

    
    /**
     * 发送视频数据到RTSP服务器
     * 此方法由VideoEncoder调用，用于发送相机编码数据
     * 
     * @param encodedData 编码后的H.264数据
     * @param bufferInfo 媒体编码缓冲区信息
     * @return 是否发送成功
     */
    public boolean sendVideoData(ByteBuffer encodedData, MediaCodec.BufferInfo bufferInfo) {
        if (mRtspServer == null || !mRtspServer.isRunning()) {
            Log.w(TAG, "发送视频数据失败：RTSP服务器未运行");
            return false;
        }
        
        if (!mSpsPpsSetted) {
            Log.w(TAG, "发送视频数据失败：SPS/PPS未设置");
            return false;
        }
        
        try {
            // 检查是否是关键帧并打印日志
            boolean isKeyFrame = (bufferInfo.flags & MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0;
            if (isKeyFrame) {
                Log.d(TAG, "发送关键帧到RTSP服务器：" + bufferInfo.size + " 字节");
            }
            
            // 发送到RTSP服务器
            mRtspServer.sendVideo(encodedData, bufferInfo);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "发送视频数据失败", e);
            return false;
        }
    }



    /**
     * 设置流源类型
     * 可选屏幕录制或相机捕获两种类型
     *
     * @param sourceType 流源类型: SOURCE_TYPE_SCREEN 或 SOURCE_TYPE_CAMERA
     * @return 该RtspScreenRecorder实例，用于链式调用
     */
    public RtspScreenRecorder setSourceType(int sourceType) {
        this.mSourceType = sourceType;
        return this;
    }

    /**
     * 获取当前流源类型
     *
     * @return 当前流源类型 (SOURCE_TYPE_SCREEN或SOURCE_TYPE_CAMERA)
     */
    public int getSourceType() {
        return mSourceType;
    }



    /**
     * 性能监控和自适应码率调整
     */
    private void startPerformanceMonitoring() {
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mIsRecording) {
                    // TODO: 实现性能监控和自适应码率调整
                    // 继续监控
                    new Handler(Looper.getMainLooper()).postDelayed(this, 5000);
                }
            }
        }, 5000);
    }
    
    /**
     * 设置SPS和PPS数据
     * 这些是H.264编码所需的参数集，用于正确解码视频流
     * 
     * @param sps 序列参数集(Sequence Parameter Set)数据
     * @param pps 图像参数集(Picture Parameter Set)数据
     */
    public void setSpsPps(byte[] sps, byte[] pps) {
        Log.d(TAG, "接收到SPS和PPS数据：SPS长度=" + (sps != null ? sps.length : 0) + 
              ", PPS长度=" + (pps != null ? pps.length : 0));
              
        if (sps == null || pps == null || sps.length == 0 || pps.length == 0) {
            Log.e(TAG, "SPS或PPS数据无效");
            return;
        }
        
        this.mSps = sps;
        this.mPps = pps;
        
        ensureSpsPpsSet();
    }

    /**
     * 设置RTSP端口
     *
     * @param port RTSP端口号，默认为8554
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setPort(int port) {
        this.mPort = port;
        return this;
    }

    /**
     * 设置自定义IP地址
     * 如不设置，将使用设备IP地址
     *
     * @param ipAddress 要使用的IP地址
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setIpAddress(String ipAddress) {
        this.mIpAddress = ipAddress;
        return this;
    }

    /**
     * 设置视频分辨率
     *
     * @param width 视频宽度（像素）
     * @param height 视频高度（像素）
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setResolution(int width, int height) {
        this.mVideoWidth = width;
        this.mVideoHeight = height;
        return this;
    }

    /**
     * 获取当前设置的视频宽度
     *
     * @return 视频宽度（像素）
     */
    public int getVideoWidth() {
        return mVideoWidth;
    }

    /**
     * 获取当前设置的视频高度
     *
     * @return 视频高度（像素）
     */
    public int getVideoHeight() {
        return mVideoHeight;
    }

    /**
     * 设置视频码率（比特率）
     *
     * @param bitRate 码率，单位为bps，默认6000000 (6Mbps)
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setBitRate(int bitRate) {
        this.mVideoBitRate = bitRate;
        return this;
    }

    /**
     * 获取当前设置的视频码率
     *
     * @return 视频码率，单位为bps
     */
    public int getBitRate() {
        return mVideoBitRate;
    }

    /**
     * 设置视频帧率
     *
     * @param frameRate 帧率，单位为fps，默认30fps
     * @return this对象，用于链式调用
     */
    public RtspScreenRecorder setFrameRate(int frameRate) {
        this.mVideoFrameRate = frameRate;
        return this;
    }

    /**
     * 获取当前设置的视频帧率
     *
     * @return 视频帧率，单位为fps
     */
    public int getFrameRate() {
        return mVideoFrameRate;
    }

    /**
     * 设置RTSP通知的目标Activity
     * 当用户点击通知时将跳转到指定Activity
     *
     * @param targetActivityClassName 目标Activity的完整类名
     * @return 该RtspScreenRecorder实例，用于链式调用
     */
    public RtspScreenRecorder setTargetActivityClassName(String targetActivityClassName) {
        this.targetActivityClassName = targetActivityClassName;
        return this;
    }
    
    /**
     * 释放资源
     * 停止录制，注销广播接收器，释放RTSP服务器
     * 在Activity销毁时必须调用此方法
     */
    public void release() {
        if (mIsRecording) {
            stop();
        }
        
        try {
            if (mStatusReceiver != null) {
                mActivity.unregisterReceiver(mStatusReceiver);
                mStatusReceiver = null;
            }
            
            if (mRtspServer != null) {
                if (mRtspServer.isRunning()) {
                    mRtspServer.stopServer();
                }
                mRtspServer = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "释放资源失败", e);
        }
    }

    /* ConnectChecker接口实现 - 处理RTSP连接状态回调 */
    
    @Override
    public void onConnectionStarted(String url) {
        Log.i(TAG, "RTSP连接开始: " + url);
    }

    @Override
    public void onConnectionSuccess() {
        Log.i(TAG, "RTSP连接成功");
        
        // 如果是重连成功，通知监听器
        if (reconnectAttempts > 0) {
            reconnectAttempts = 0;
            mCallbacks.notifyReconnected();
        }
    }

    @Override
    public void onConnectionFailed(String reason) {
        Log.e(TAG, "RTSP连接失败: " + reason);
        
        if (autoReconnect && reconnectAttempts < maxReconnectAttempts) {
            // 尝试重连
            reconnectHandler.removeCallbacks(reconnectRunnable);
            reconnectHandler.postDelayed(reconnectRunnable, reconnectDelay);
        } else {
            mCallbacks.notifyError(RtspConfiguration.ErrorType.CONNECTION_ERROR, "RTSP连接失败: " + reason);
        }
    }

    @Override
    public void onDisconnect() {
        Log.i(TAG, "RTSP断开连接");
    }

    @Override
    public void onAuthError() {
        Log.e(TAG, "RTSP认证错误");
        mCallbacks.notifyError(RtspConfiguration.ErrorType.SERVER_ERROR, "RTSP认证错误");
    }

    @Override
    public void onAuthSuccess() {
        Log.i(TAG, "RTSP认证成功");
    }
    
    /**
     * 检查所需权限是否已授予
     * Android 10及以上版本不需要存储权限检查
     * 
     * @return 如果所有权限都已授予，返回true
     */
    private boolean checkPermissions() {
        // Android 10 (API 29)及以上版本不需要外部存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return true;
        }
        
        // 检查所有需要的权限
        List<String> permissionsNeeded = new ArrayList<>();
        for (String permission : PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(mActivity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(permission);
            }
        }
        return permissionsNeeded.isEmpty();
    }
    
    /**
     * 请求所需权限
     * 对于Android 10以下版本，请求存储和录音权限
     */
    private void requestPermissions() {
        // Android 10 (API 29)及以上版本不需要外部存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            requestScreenCapture();
            return;
        }
        
        // 收集需要申请的权限
        List<String> permissionsNeeded = new ArrayList<>();
        for (String permission : PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(mActivity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(permission);
            }
        }
        
        if (!permissionsNeeded.isEmpty()) {
            // 申请权限
            mPermissionLauncher.launch(permissionsNeeded.toArray(new String[0]));
        } else {
            // 已有权限，直接请求屏幕录制
            requestScreenCapture();
        }
    }
    
    /**
     * 请求屏幕录制权限
     * 启动系统屏幕捕获意图
     */
    private void requestScreenCapture() {
        try {
            Intent captureIntent = mMediaProjectionManager.createScreenCaptureIntent();
            mMediaProjectionLauncher.launch(captureIntent);
        } catch (Exception e) {
            Log.e(TAG, "请求屏幕录制失败", e);
            mCallbacks.notifyError(RtspConfiguration.ErrorType.PERMISSION_DENIED, "请求屏幕录制失败: " + e.getMessage());
        }
    }
    
    /**
     * 开始自适应码率监控
     * 根据网络状况动态调整视频码率
     */
    private void startAdaptiveBitrateMonitoring() {
        if (!adaptiveBitrate) return;
        
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mIsRecording && adaptiveBitrate) {
                    // 获取网络类型和信号强度
                    int networkType = getNetworkType();
                    
                    // 根据网络类型调整码率
                    int newBitRate = calculateOptimalBitRate(networkType);
                    if (newBitRate != mVideoBitRate) {
                        Log.d(TAG, "自适应码率调整: " + mVideoBitRate + " -> " + newBitRate);
                        mVideoBitRate = newBitRate;
                        
                        // 应用新码率到编码器
                        applyNewBitRate(newBitRate);
                    }
                    
                    // 继续监控
                    new Handler(Looper.getMainLooper()).postDelayed(this, 10000); // 每10秒检查一次
                }
            }
        }, 10000);
    }
    
    /**
     * 获取当前网络类型
     */
    private int getNetworkType() {
        ConnectivityManager cm = (ConnectivityManager) mActivity.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        if (activeNetwork != null) {
            return activeNetwork.getType();
        }
        return -1;
    }
    
    /**
     * 计算最佳码率
     */
    private int calculateOptimalBitRate(int networkType) {
        // 根据网络类型和条件计算最佳码率
        if (networkType == ConnectivityManager.TYPE_WIFI) {
            return Math.min(6000000, mVideoBitRate); // WiFi，最高6Mbps
        } else if (networkType == ConnectivityManager.TYPE_MOBILE) {
            return Math.min(2000000, mVideoBitRate); // 移动网络，最高2Mbps
        } else {
            return Math.min(1000000, mVideoBitRate); // 其他网络，最高1Mbps
        }
    }
    
    /**
     * 应用新码率到编码器
     */
    private void applyNewBitRate(int newBitRate) {
        if (videoEncoder != null && mSourceType == SOURCE_TYPE_CAMERA) {
            // TODO: 实现动态调整VideoEncoder码率的方法
        }
    }
    
    /**
     * 客户端连接数量变化处理
     */
    public void updateClientCount(int count) {
        if (count != connectedClients) {
            boolean isIncrease = count > connectedClients;
            connectedClients = count;
            
            if (isIncrease) {
                mCallbacks.notifyClientConnected(count);
            } else {
                mCallbacks.notifyClientDisconnected(count);
            }
        }
    }
}