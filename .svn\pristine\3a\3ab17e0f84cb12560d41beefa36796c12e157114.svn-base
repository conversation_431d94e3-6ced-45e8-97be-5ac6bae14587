<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Apr 15 14:46:38 CST 2025 -->
<title>V - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-15">
<meta name="description" content="index: V">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#%3Cinit%3E(java.lang.String,android.view.Surface)" class="member-name-link">VideoDecoder(String, Surface)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">构造函数。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/VideoDecoderActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">VideoDecoderActivity</a> - <a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a>中的类</dt>
<dd>
<div class="block">VideoDecoderActivity 类负责视频解码和播放控制。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/VideoDecoderActivity.html#%3Cinit%3E()" class="member-name-link">VideoDecoderActivity()</a> - 类的构造器 com.android.rockchip.camera2.<a href="../com/android/rockchip/camera2/VideoDecoderActivity.html" title="com.android.rockchip.camera2中的类">VideoDecoderActivity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">VideoEncoder 类负责视频编码、存储监控以及文件大小限制的处理。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.html#%3Cinit%3E(com.android.rockchip.camera2.video.VideoEncoder.Callback)" class="member-name-link">VideoEncoder(VideoEncoder.Callback)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a></dt>
<dd>
<div class="block">构造函数。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/VideoEncoderActivity.html" class="type-name-link" title="com.android.rockchip.camera2中的类">VideoEncoderActivity</a> - <a href="../com/android/rockchip/camera2/package-summary.html">com.android.rockchip.camera2</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/VideoEncoderActivity.html#%3Cinit%3E()" class="member-name-link">VideoEncoderActivity()</a> - 类的构造器 com.android.rockchip.camera2.<a href="../com/android/rockchip/camera2/VideoEncoderActivity.html" title="com.android.rockchip.camera2中的类">VideoEncoderActivity</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">E</a>&nbsp;<a href="index-4.html">F</a>&nbsp;<a href="index-5.html">G</a>&nbsp;<a href="index-6.html">H</a>&nbsp;<a href="index-7.html">I</a>&nbsp;<a href="index-8.html">M</a>&nbsp;<a href="index-9.html">O</a>&nbsp;<a href="index-10.html">R</a>&nbsp;<a href="index-11.html">S</a>&nbsp;<a href="index-12.html">T</a>&nbsp;<a href="index-13.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
