<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Jun 05 09:11:19 CST 2025 -->
<title>T - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-05">
<meta name="description" content="index: T">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.html#togglePlayPause()" class="member-name-link">togglePlayPause()</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.html" title="com.android.rockchip.camera2.video中的类">VideoDecoder</a></dt>
<dd>
<div class="block">切换播放和暂停状态。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html#toString()" class="member-name-link">toString()</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_BANDWIDTH" class="member-name-link">TOUPTEK_PARAM_BANDWIDTH</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">带宽控制（影响图像处理的速度和质量）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_BRIGHTNESS" class="member-name-link">TOUPTEK_PARAM_BRIGHTNESS</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">亮度</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_COLORORGRAY" class="member-name-link">TOUPTEK_PARAM_COLORORGRAY</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">彩色/灰度模式选择</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_COLORTONE" class="member-name-link">TOUPTEK_PARAM_COLORTONE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">色彩色调</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_CONTRAST" class="member-name-link">TOUPTEK_PARAM_CONTRAST</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">对比度</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_CTBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_CTBLUEGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">彩色温度蓝色通道增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_CTGREENGAIN" class="member-name-link">TOUPTEK_PARAM_CTGREENGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">彩色温度绿色通道增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_CTREDGAIN" class="member-name-link">TOUPTEK_PARAM_CTREDGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">彩色温度红色通道增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_DARKENHANCE" class="member-name-link">TOUPTEK_PARAM_DARKENHANCE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">暗部增强</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_DENOISE" class="member-name-link">TOUPTEK_PARAM_DENOISE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">降噪参数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_EXPOSURECHOICE" class="member-name-link">TOUPTEK_PARAM_EXPOSURECHOICE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">曝光模式选择</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_EXPOSURECOMPENSATION" class="member-name-link">TOUPTEK_PARAM_EXPOSURECOMPENSATION</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">曝光补偿</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_EXPOSUREGAIN" class="member-name-link">TOUPTEK_PARAM_EXPOSUREGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">曝光增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_EXPOSURETIME" class="member-name-link">TOUPTEK_PARAM_EXPOSURETIME</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">曝光时间</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_FLIP" class="member-name-link">TOUPTEK_PARAM_FLIP</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">翻转效果（水平/垂直翻转）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_GAMMA" class="member-name-link">TOUPTEK_PARAM_GAMMA</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">Gamma 校正</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_HUE" class="member-name-link">TOUPTEK_PARAM_HUE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">色调</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_HZ" class="member-name-link">TOUPTEK_PARAM_HZ</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">频率</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_LDCRATIO" class="member-name-link">TOUPTEK_PARAM_LDCRATIO</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">低动态范围对比度比率</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_MIRROR" class="member-name-link">TOUPTEK_PARAM_MIRROR</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">镜像效果（水平/垂直镜像）</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_SATURATION" class="member-name-link">TOUPTEK_PARAM_SATURATION</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">饱和度</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_SHARPNESS" class="member-name-link">TOUPTEK_PARAM_SHARPNESS</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">锐化参数</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_VERSION" class="member-name-link">TOUPTEK_PARAM_VERSION</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">版本号</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_WBBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_WBBLUEGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">蓝色通道的白平衡增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_WBCHOICE" class="member-name-link">TOUPTEK_PARAM_WBCHOICE</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">白平衡模式选择</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_WBGREENGAIN" class="member-name-link">TOUPTEK_PARAM_WBGREENGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">绿色通道的白平衡增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_WBREDGAIN" class="member-name-link">TOUPTEK_PARAM_WBREDGAIN</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">红色通道的白平衡增益</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html#TOUPTEK_PARAM_WDREXPRATIO" class="member-name-link">TOUPTEK_PARAM_WDREXPRATIO</a> - enum class 中的枚举常量 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></dt>
<dd>
<div class="block">宽动态范围曝光比率</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">touptek_serial_rk 类用于管理串口通信。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#%3Cinit%3E()" class="member-name-link">touptek_serial_rk()</a> - 类的构造器 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.DeviceStateCallback.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">touptek_serial_rk.DeviceStateCallback</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">定义设备状态回调接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.html" class="type-name-link" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的Enum Class</dt>
<dd>
<div class="block">枚举类 TouptekIspParam 定义了摄像头 ISP 参数的键值对。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">定义数据变化监听器接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnSerialStateChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的接口</dt>
<dd>
<div class="block">定义串口状态变化监听器接口。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.ParamData.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">TouptekIspParam.ParamData</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">参数数据结构</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TransformUtils.html" class="type-name-link" title="com.android.rockchip.camera2.util中的类">TransformUtils</a> - <a href="../com/android/rockchip/camera2/util/package-summary.html">com.android.rockchip.camera2.util</a>中的类</dt>
<dd>
<div class="block">TransformUtils 类提供与视图变换相关的工具方法。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TransformUtils.html#%3Cinit%3E()" class="member-name-link">TransformUtils()</a> - 类的构造器 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TransformUtils.html" title="com.android.rockchip.camera2.util中的类">TransformUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a> - <a href="../com/android/rockchip/camera2/video/package-summary.html">com.android.rockchip.camera2.video</a>中的类</dt>
<dd>
<div class="block">TvPreviewHelper 类用于提供基于TvView的低延迟预览功能。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html#%3Cinit%3E(android.content.Context,android.view.ViewGroup)" class="member-name-link">TvPreviewHelper(Context, ViewGroup)</a> - 类的构造器 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/TvPreviewHelper.html" title="com.android.rockchip.camera2.video中的类">TvPreviewHelper</a></dt>
<dd>
<div class="block">构造函数，初始化TV预览助手。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
