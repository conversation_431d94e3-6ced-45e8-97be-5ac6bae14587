<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.rockchip.camera2"
    android:sharedUserId="android.uid.system"
    android:versionCode="5002"
    android:versionName="14" >

    <uses-sdk
        android:minSdkVersion="29"
        android:targetSdkVersion="29" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Screen Capturing -->
    <uses-permission android:name="android.permission.MANAGE_MEDIA_PROJECTION" />

    <!-- Screen Recording -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.ACCESS_USB" />
    <uses-permission android:name="android.permission.SERIAL_PORT" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <permission
        android:name="com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.android.rockchip.camera2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- 适用于Android 11及以上 -->
    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/BaseTheme" >
        <activity
            android:name="com.android.rockchip.camera2.RockchipCamera2"
            android:exported="true" >

            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.MAIN" /> -->


            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name="com.android.rockchip.camera2.activity.WelcomeActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="unspecified"
            android:supportsPictureInPicture="false" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.android.rockchip.camera2.activity.MainActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:screenOrientation="unspecified"
            android:supportsPictureInPicture="true" />

        <service
            android:name="com.android.rockchip.camera2.ScreenRecordService"
            android:exported="true"
            android:foregroundServiceType="mediaProjection" />
        <service
            android:name="com.android.rockchip.camera2.HdmiService"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.android.rockchip.camera2.HdmiService" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <!-- 注册 BootBroadcastReceiver -->
        <receiver
            android:name="com.android.rockchip.camera2.BootBroadcastReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <!-- 注册 BootBroadcastReceiver -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.android.rockchip.camera2.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>