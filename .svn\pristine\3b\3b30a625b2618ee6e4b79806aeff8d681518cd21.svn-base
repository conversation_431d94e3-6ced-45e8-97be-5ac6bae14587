package com.android.rockchip.camera2.video;

import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。
 */
public class VideoDecoder {
    private static final String TAG = "VideoDecoder";
    private final String videoPath;
    private final Surface surface;

    private MediaCodec mediaCodec;
    private MediaExtractor mediaExtractor;
    private Thread decodingThread;

    private volatile boolean isDecoding = false;
    private volatile boolean isPaused = false;
    private volatile boolean isFrameByFrame = false;

    private long videoDuration;

    /**
     * 构造函数。
     *
     * @param videoPath 视频文件路径
     * @param surface   用于渲染视频的 Surface
     */
    public VideoDecoder(String videoPath, Surface surface) {
        this.videoPath = videoPath;
        this.surface = surface;
    }

    /**
     * 开始视频解码过程。
     */
    public synchronized void startDecoding() {
        try {
            mediaExtractor = new MediaExtractor();
            mediaExtractor.setDataSource(videoPath);

            int trackIndex = selectVideoTrack(mediaExtractor);
            if (trackIndex == -1) {
                Log.e(TAG, "未找到视频轨道");
                return;
            }

            MediaFormat format = mediaExtractor.getTrackFormat(trackIndex);
            videoDuration = format.getLong(MediaFormat.KEY_DURATION);
            String mime = format.getString(MediaFormat.KEY_MIME);

            mediaCodec = MediaCodec.createDecoderByType(mime);
            mediaCodec.configure(format, surface, null, 0);
            mediaCodec.start();

            isDecoding = true;
            decodingThread = new Thread(this::decodingTask);
            decodingThread.start();
        } catch (IOException e) {
            Log.e(TAG, "解码器初始化失败", e);
        }
    }

    /**
     * 从媒体提取器中选择视频轨道。
     *
     * @param extractor MediaExtractor 实例
     * @return 视频轨道的索引，如果未找到视频轨道则返回 -1
     */
    private int selectVideoTrack(MediaExtractor extractor) {
        for (int i = 0; i < extractor.getTrackCount(); i++) {
            MediaFormat format = extractor.getTrackFormat(i);
            if (format.getString(MediaFormat.KEY_MIME).startsWith("video/")) {
                extractor.selectTrack(i);
                return i;
            }
        }
        return -1;
    }

    /**
     * 在单独线程中运行的主要解码任务。
     */
    private void decodingTask() {
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        while (isDecoding) {
            if (isPaused || isFrameByFrame) {
                safeSleep(10);
                continue;
            }

            int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
            if (inputIndex >= 0) {
                ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);
                int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);

                if (sampleSize < 0) {
                    mediaCodec.queueInputBuffer(inputIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                } else {
                    mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize, mediaExtractor.getSampleTime(), 0);
                    mediaExtractor.advance();
                }
            }

            int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
            if (outputIndex >= 0) {
                mediaCodec.releaseOutputBuffer(outputIndex, true);
            } else if (outputIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                Log.d(TAG, "输出格式已更改");
            }
        }
    }

    /**
     * 跳转到视频的指定位置。
     *
     * @param position 要跳转到的位置，以微秒为单位
     */
    public synchronized void seekTo(long position) {
        if (!isDecoding) return;

        boolean wasPaused = isPaused;
        isPaused = true;

        safeSleep(50);

        try {
            mediaExtractor.seekTo(Math.max(0, position), MediaExtractor.SEEK_TO_CLOSEST_SYNC);
            mediaCodec.flush();
        } catch (IllegalStateException e) {
            Log.e(TAG, "跳转失败", e);
        }

        isPaused = wasPaused;
    }

    /**
     * 切换播放和暂停状态。
     */
    public void togglePlayPause() {
        isPaused = !isPaused;
    }

    /**
     * 逐帧播放视频。
     */
    public void stepFrame() {
        isFrameByFrame = true;
        new Thread(() -> {
            decodeSingleFrame();
            isFrameByFrame = false;
        }).start();
    }

    /**
     * 解码视频的单个帧。
     */
    private void decodeSingleFrame() {
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

        int inputIndex = mediaCodec.dequeueInputBuffer(10_000);
        if (inputIndex >= 0) {
            ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputIndex);
            int sampleSize = mediaExtractor.readSampleData(inputBuffer, 0);

            if (sampleSize >= 0) {
                mediaCodec.queueInputBuffer(inputIndex, 0, sampleSize,
                        mediaExtractor.getSampleTime(), 0);
                mediaExtractor.advance();
            }
        }

        int outputIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10_000);
        if (outputIndex >= 0) {
            mediaCodec.releaseOutputBuffer(outputIndex, true);
        }
    }

    /**
     * 相对于当前位置进行跳转。
     *
     * @param deltaMs 要跳转的时间，以毫秒为单位
     */
    public void seekRelative(long deltaMs) {
        try {
            long currentPos = mediaExtractor.getSampleTime();
            long newPos = Math.max(0, Math.min(videoDuration, currentPos + deltaMs * 1000));
            seekTo(newPos);
        } catch (IllegalStateException e) {
            Log.e(TAG, "跳转失败", e);
        }
    }

    /**
     * 停止视频解码过程。
     */
    public synchronized void stopDecoding() {
        isDecoding = false;

        try {
            if (decodingThread != null) {
                decodingThread.join(500);
            }
        } catch (InterruptedException e) {
            Log.w(TAG, "线程中断", e);
        }

        if (mediaCodec != null) {
            mediaCodec.stop();
            mediaCodec.release();
            mediaCodec = null;
        }

        if (mediaExtractor != null) {
            mediaExtractor.release();
            mediaExtractor = null;
        }
    }

    /**
     * 检查视频是否正在解码。
     *
     * @return 如果正在解码则返回 true，否则返回 false
     */
    public boolean isDecoding() {
        return isDecoding;
    }

    /**
     * 检查视频是否处于暂停状态。
     *
     * @return 如果暂停则返回 true，否则返回 false
     */
    public boolean isPaused() {
        return isPaused;
    }

    /**
     * 检查视频是否处于逐帧模式。
     *
     * @return 如果处于逐帧模式则返回 true，否则返回 false
     */
    public boolean isFrameByFrame() {
        return isFrameByFrame;
    }

    /**
     * 获取视频的持续时间。
     *
     * @return 视频持续时间，以微秒为单位
     */
    public long getVideoDuration() {
        return videoDuration;
    }

    /**
     * 获取视频的当前位置。
     *
     * @return 当前位置，以微秒为单位
     */
    public long getCurrentPosition() {
        return mediaExtractor.getSampleTime();
    }

    /**
     * 安全地睡眠指定时间，处理中断。
     *
     * @param millis 要睡眠的时间，以毫秒为单位
     */
    private void safeSleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Log.d(TAG, "线程睡眠被中断");
        }
    }
}