#include "utiny/http_transceiver.h"
#include "utiny/http_header.h"
#include "utiny/http_session_context.h"
#include <stdlib.h>
#include <ctype.h>
#ifdef _WIN32
#define snprintf	_snprintf
#define strcasecmp	stricmp
#endif

http_transceiver::http_transceiver(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size, unsigned recv_buffer_size)
: usys_transceiver_tcp(reactor, send_retry_deque_size, recv_buffer_size)
{
}

http_transceiver::~http_transceiver()
{
}

usys_smartptr<http_session_context> http_transceiver::http_session_ptr()
{
    return usys_smartptr<http_session_context>::__dynamic_cast(session_ptr());
}

static int http_parse_chunked_length(const char* ptr, int length)
{
	int totollength = 0;
	const char *p = ptr;
	while (p - ptr < length)
	{
		const char* q = p;
		while (p - ptr < length)
		{
			if (('\r' == *p) && ('\n' == *(p + 1)))
			{
				if (p - q == 0)
					return -1;
				char xlen[64], *endptr;
				memcpy(xlen, q, p - q);
				xlen[p - q] = '\0';
				long x = strtol(xlen, &endptr, 16);
				if (endptr && ('\0' != *endptr))
					return -1;
				totollength += p - q + 4 + x;
				if (0 == x)
					return totollength;
				if (totollength >= length)
					return 0;				
				p += x + 4;
				break;
			}
			else if (!isxdigit(*p))
				return -1;
			else if (p - q > 32)
				return -1;
			++p;
		}
	}

	return 0;
}

int http_transceiver::get_message_len(const usys_smartptr<usys_data_block>& data_ptr, usys_smartptr_mtbase_ptr& header_ptr)
{
    if (data_ptr == 0)
        return -1;
    if (data_ptr->length() <= 7)
        return 0;
    usys_smartptr<http_header> hptr(new http_header);
	int ret = hptr->unpack((const char*)data_ptr->rd_ptr(), data_ptr->length());
	if (ret <= 0)
		return ret;
	if (strcasecmp(hptr->exparam("Transfer-Encoding").c_str(), "chunked") == 0)
	{
		ret = http_parse_chunked_length((const char*)data_ptr->rd_ptr() + ret, data_ptr->length() - ret);
		if (ret <= 0)
			return ret;
		hptr->body_length(ret);
	}

	header_ptr = hptr;
    return hptr->header_length() + hptr->body_length();
}

int http_transceiver::handle_accept()
{
    USYS_ASSERT(false);
    return -1;
}

int http_transceiver::handle_connect()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    return http_session_ptr()->handle_connect(transceiver_ptr);
}

int http_transceiver::handle_write()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    return http_session_ptr()->handle_write(transceiver_ptr);
}

void http_transceiver::handle_close()
{
    usys_smartptr<usys_transceiver> transceiver_ptr(this);
    http_session_ptr()->handle_close(transceiver_ptr);
}

int http_transceiver::handle_read_datablock(const usys_smartptr<usys_data_block>& data_ptr, const usys_smartptr_mtbase_ptr& header_ptr)
{
	usys_smartptr<usys_transceiver> transceiver_ptr(this);
	return http_session_ptr()->handle_read_datablock(data_ptr, header_ptr, transceiver_ptr);
}
