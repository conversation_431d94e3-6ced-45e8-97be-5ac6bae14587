                        -HC:\hhx\svn\AndroidStudio\rkCamer2\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON>AN<PERSON>OID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++11
-DCMA<PERSON>_LIBRARY_OUTPUT_DIRECTORY=C:\hhx\svn\AndroidStudio\rkCamer2\app\build\intermediates\cxx\Debug\2l5s7n3p\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\hhx\svn\AndroidStudio\rkCamer2\app\build\intermediates\cxx\Debug\2l5s7n3p\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BC:\hhx\svn\AndroidStudio\rkCamer2\app\.cxx\Debug\2l5s7n3p\x86
-GNinja
                        Build command args: []
                        Version: 2