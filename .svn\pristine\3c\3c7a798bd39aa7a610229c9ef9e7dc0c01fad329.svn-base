memberSearchIndex = [{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"addOnDataChangedListener(TouptekIspParam.OnDataChangedListener)","u":"addOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)"},{"p":"com.android.rockchip.camera2.util","c":"TransformUtils","l":"applyPan(ImageView, Matrix, float, float)","u":"applyPan(android.widget.ImageView,android.graphics.Matrix,float,float)"},{"p":"com.android.rockchip.camera2.util","c":"TransformUtils","l":"applyPan(TextureView, float, float)","u":"applyPan(android.view.TextureView,float,float)"},{"p":"com.android.rockchip.camera2.util","c":"TransformUtils","l":"applyZoom(ImageView, Matrix, float, float, float)","u":"applyZoom(android.widget.ImageView,android.graphics.Matrix,float,float,float)"},{"p":"com.android.rockchip.camera2.util","c":"TransformUtils","l":"applyZoom(TextureView, float, float, float)","u":"applyZoom(android.view.TextureView,float,float,float)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"areServicesStarted()"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper.Builder","l":"build()"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"build()"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"build()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"build()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"builder()"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"builder(Context)","u":"builder(android.content.Context)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"builder(Size)","u":"builder(android.util.Size)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"Builder(Size)","u":"%3Cinit%3E(android.util.Size)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.StreamType","l":"CAMERA"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"CameraManagerHelper(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"CaptureImageHelper(Size)","u":"%3Cinit%3E(android.util.Size)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"close()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"completeInitialization(VideoEncoder, CaptureImageHelper)","u":"completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"completeInitialization(VideoEncoder, CaptureImageHelper, TpctrlService.StreamType)","u":"completeInitialization(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.CaptureImageHelper,com.android.rockchip.camera2.service.TpctrlService.StreamType)"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"configCameraOutputs(CameraDevice, Surface, Surface)","u":"configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"createDefault1080P()"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"createDefault4K()"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"createDefault720P()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"createEarlyInstance(AppCompatActivity, TpctrlService.HeartbeatListener)","u":"createEarlyInstance(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.TpctrlService.HeartbeatListener)"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"createImagePath(Context)","u":"createImagePath(android.content.Context)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"createMediaFormat()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"createVideoPath(Context)","u":"createVideoPath(android.content.Context)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"current"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"decodeSampledBitmapFromFile(String, int, int)","u":"decodeSampledBitmapFromFile(java.lang.String,int,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"defaultValue"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"FileStorageUtils()","u":"%3Cinit%3E()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"fromInt(int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getAllData()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getAllParamData()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"getAvailableStorageSpace(String)","u":"getAvailableStorageSpace(java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"getCurrentPosition()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getCurrentStreamType()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getData(TouptekIspParam)","u":"getData(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getDefaultValue(TouptekIspParam)","u":"getDefaultValue(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"getEncoderOutputFormat()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"getExternalStoragePath(Context)","u":"getExternalStoragePath(android.content.Context)"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"getFileSystemType(String)","u":"getFileSystemType(java.lang.String)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getHeartbeatTimeout()"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"getImageReader()"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService","l":"getInstance()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"getInternalStoragePath(Context)","u":"getInternalStoragePath(android.content.Context)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getIsDisableValue(TouptekIspParam)","u":"getIsDisableValue(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getIspPort()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getLastHeartbeatTime()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getLongData(TouptekIspParam)","u":"getLongData(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getMaxValue(TouptekIspParam)","u":"getMaxValue(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"getMimeType()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getMinValue(TouptekIspParam)","u":"getMinValue(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getParamByIndex(int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getParamData(TouptekIspParam)","u":"getParamData(com.android.rockchip.camera2.util.TouptekIspParam)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getParamsRangeReceived()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"getPlaybackSpeed()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getSerialInstance()"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig","l":"getSize()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getSocketPort()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"getStreamUrl()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"getValue()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"getVideoDuration()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"handleReceivedData(TouptekIspParam, long, boolean)","u":"handleReceivedData(com.android.rockchip.camera2.util.TouptekIspParam,long,boolean)"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService","l":"HdmiService()","u":"%3Cinit%3E()"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"ImageDecoder()","u":"%3Cinit%3E()"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService","l":"init()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"init(Context)","u":"init(android.content.Context)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"initialize(Size, Surface)","u":"initialize(android.util.Size,android.view.Surface)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"initializeSerial(int)"},{"p":"com.android.rockchip.camera2.video","c":"TvPreviewHelper","l":"isActive()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"isDecoding()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"isDisabled"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"isEarlyInitialized()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"isFrameByFrame()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"isFullyInitialized()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"isPaused()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"isPlaybackCompleted()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"isRunning()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"isRunning()"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"isSerialConnected()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"isSerialConnected()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"isStreaming()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"isTpctrlExists()"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"loadImage(String, ImageView)","u":"loadImage(java.lang.String,android.widget.ImageView)"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"loadImageAsync(String, ImageView)","u":"loadImageAsync(java.lang.String,android.widget.ImageView)"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"loadImageAsync(String, ImageView, ImageDecoder.LoadCallback)","u":"loadImageAsync(java.lang.String,android.widget.ImageView,com.android.rockchip.camera2.video.ImageDecoder.LoadCallback)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"max"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"min"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"onBind(Intent)","u":"onBind(android.content.Intent)"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper.Builder","l":"onCameraDisconnected(Consumer<CameraDevice>)","u":"onCameraDisconnected(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper.Builder","l":"onCameraError(BiConsumer<CameraDevice, Integer>)","u":"onCameraError(java.util.function.BiConsumer)"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper.Builder","l":"onCameraOpened(Consumer<CameraDevice>)","u":"onCameraOpened(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"onDataChanged(TouptekIspParam, int)","u":"onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.OnDataChangedListener","l":"onDataChanged(TouptekIspParam, int)","u":"onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk.DeviceStateCallback","l":"onDeviceStateChanged(boolean)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"onDeviceStateChanged(boolean)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"onError(BiConsumer<String, Exception>)","u":"onError(java.util.function.BiConsumer)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"onError(Consumer<String>)","u":"onError(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.CaptureCallback","l":"onError(String)","u":"onError(java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder.LoadCallback","l":"onError(String)","u":"onError(java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"onFileSizeLimitReached(Runnable)","u":"onFileSizeLimitReached(java.lang.Runnable)"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService.HdmiListener","l":"onHdmiStatusChanged(boolean)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"onImageSaved(Consumer<String>)","u":"onImageSaved(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.CaptureCallback","l":"onImageSaved(String)","u":"onImageSaved(java.lang.String)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService.LogListener","l":"onLogMessage(String)","u":"onLogMessage(java.lang.String)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"onLongDataChanged(TouptekIspParam, long)","u":"onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.OnDataChangedListener","l":"onLongDataChanged(TouptekIspParam, long)","u":"onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder.PlaybackListener","l":"onPlaybackCompleted()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"onSaveComplete(Consumer<String>)","u":"onSaveComplete(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"onSerialDataReceived(int[])"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.OnSerialStateChangedListener","l":"onSerialStateChanged(boolean)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onServicesStarted()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onServicesStopped()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"onStorageFull(Runnable)","u":"onStorageFull(java.lang.Runnable)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onStreamError(String)","u":"onStreamError(java.lang.String)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onStreamStatusChanged(boolean, String)","u":"onStreamStatusChanged(boolean,java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder.LoadCallback","l":"onSuccess(Bitmap)","u":"onSuccess(android.graphics.Bitmap)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"onSurfaceAvailable(Consumer<Surface>)","u":"onSurfaceAvailable(java.util.function.Consumer)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onTpctrlDetected()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.HeartbeatListener","l":"onTpctrlLost()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils.StorageListener","l":"onUsbDriveConnected(String)","u":"onUsbDriveConnected(java.lang.String)"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils.StorageListener","l":"onUsbDriveDisconnected(String)","u":"onUsbDriveDisconnected(java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.VideoDataOutputCallback","l":"onVideoDataAvailable(ByteBuffer, MediaCodec.BufferInfo)","u":"onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec.BufferInfo)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.VideoDataOutputCallback","l":"onVideoFormatChanged(MediaFormat, ByteBuffer, ByteBuffer)","u":"onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"openCamera()"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"openCamera(CameraDevice.StateCallback)","u":"openCamera(android.hardware.camera2.CameraDevice.StateCallback)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"ParamData(int, int, int, int, boolean)","u":"%3Cinit%3E(int,int,int,int,boolean)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"release()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"release()"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"release()"},{"p":"com.android.rockchip.camera2.video","c":"TvPreviewHelper","l":"release()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"release()"},{"p":"com.android.rockchip.camera2.video","c":"CameraManagerHelper","l":"releaseCamera()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"removeOnDataChangedListener(TouptekIspParam.OnDataChangedListener)","u":"removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"requestAllParamRanges()"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"requestCapture(Size, String)","u":"requestCapture(android.util.Size,java.lang.String)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"requestKeyFrame()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"resetToStart()"},{"p":"com.android.rockchip.camera2.video","c":"ImageDecoder","l":"rotateBitmap(Bitmap, float)","u":"rotateBitmap(android.graphics.Bitmap,float)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"saveToLocal(TouptekIspParam, int)","u":"saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"saveToLocal(TouptekIspParam, long)","u":"saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,long)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.StreamType","l":"SCREEN"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"seekRelative(long)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"seekTo(long)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"sendCommandToSerial(int, int, int)","u":"sendCommandToSerial(int,int,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"sendToDevice(TouptekIspParam, int)","u":"sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"sendToDevice(TouptekIspParam, int, int)","u":"sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setBitRate(int)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setBitrateMode(int)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"setCaptureCallback(CaptureImageHelper.CaptureCallback)","u":"setCaptureCallback(com.android.rockchip.camera2.video.CaptureImageHelper.CaptureCallback)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper","l":"setCaptureCallback(CaptureImageHelper.CaptureCallback)","u":"setCaptureCallback(com.android.rockchip.camera2.video.CaptureImageHelper.CaptureCallback)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setColorFormat(int)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setColorRange(int)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setColorStandard(int)"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"setDeviceStateCallback(touptek_serial_rk.DeviceStateCallback)","u":"setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk.DeviceStateCallback)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"setEncoderConfig(EncoderConfig)","u":"setEncoderConfig(com.android.rockchip.camera2.video.EncoderConfig)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setFrameRate(int)"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService","l":"setHdmiListener(HdmiService.HdmiListener)","u":"setHdmiListener(com.android.rockchip.camera2.util.HdmiService.HdmiListener)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setIFrameInterval(int)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"setImageFormat(int)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"setLogListener(TpctrlSocketService.LogListener)","u":"setLogListener(com.android.rockchip.camera2.service.TpctrlSocketService.LogListener)"},{"p":"com.android.rockchip.camera2.video","c":"CaptureImageHelper.Builder","l":"setMaxImages(int)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setMimeType(String)","u":"setMimeType(java.lang.String)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setOnDataChangedListener(TouptekIspParam.OnDataChangedListener)","u":"setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setOnSerialStateChangedListener(TouptekIspParam.OnSerialStateChangedListener)","u":"setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnSerialStateChangedListener)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"setOutputCallback(VideoEncoder.VideoDataOutputCallback)","u":"setOutputCallback(com.android.rockchip.camera2.video.VideoEncoder.VideoDataOutputCallback)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamDefault(TouptekIspParam, int)","u":"setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamDisabled(TouptekIspParam, boolean)","u":"setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamMaxValue(TouptekIspParam, int)","u":"setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamMinValue(TouptekIspParam, int)","u":"setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamRange(TouptekIspParam, boolean, int, int, int)","u":"setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"setParamsRangeReceived(boolean)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"setPlaybackListener(VideoDecoder.PlaybackListener)","u":"setPlaybackListener(com.android.rockchip.camera2.video.VideoDecoder.PlaybackListener)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"setPlaybackSpeed(float)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"setPreviewSurface(Surface)","u":"setPreviewSurface(android.view.Surface)"},{"p":"com.android.rockchip.camera2.video","c":"EncoderConfig.Builder","l":"setProfileLevel(int)"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder.Builder","l":"setSize(Size)","u":"setSize(android.util.Size)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"setStreamType(TpctrlService.StreamType)","u":"setStreamType(com.android.rockchip.camera2.service.TpctrlService.StreamType)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"start()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"start()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"startDecoding()"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"startMonitor()"},{"p":"com.android.rockchip.camera2.video","c":"TvPreviewHelper","l":"startPreview()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"startRecording(String)","u":"startRecording(java.lang.String)"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"startUsbDriveMonitor(Context, FileStorageUtils.StorageListener)","u":"startUsbDriveMonitor(android.content.Context,com.android.rockchip.camera2.util.FileStorageUtils.StorageListener)"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"stepFrame()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"stop()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"stop()"},{"p":"com.android.rockchip.camera2.util","c":"HdmiService","l":"stop()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"stopDecoding()"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"stopMonitor()"},{"p":"com.android.rockchip.camera2.video","c":"TvPreviewHelper","l":"stopPreview()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"stopProcessMonitoring()"},{"p":"com.android.rockchip.camera2.video","c":"VideoEncoder","l":"stopRecording()"},{"p":"com.android.rockchip.camera2.util","c":"FileStorageUtils","l":"stopUsbDriveMonitor(Context)","u":"stopUsbDriveMonitor(android.content.Context)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService","l":"switchStreamType()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"togglePlayPause()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam.ParamData","l":"toString()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_BANDWIDTH"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_BRIGHTNESS"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_COLORORGRAY"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_COLORTONE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_CONTRAST"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_CTBLUEGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_CTGREENGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_CTREDGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_DARKENHANCE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_DENOISE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_EXPOSURECHOICE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_EXPOSURECOMPENSATION"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_EXPOSUREGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_EXPOSURETIME"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_FLIP"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_GAMMA"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_HUE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_HZ"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_LDCRATIO"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_MIRROR"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_SATURATION"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_SHARPNESS"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_VERSION"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_WBBLUEGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_WBCHOICE"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_WBGREENGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_WBREDGAIN"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"TOUPTEK_PARAM_WDREXPRATIO"},{"p":"com.android.rockchip.camera2.util","c":"touptek_serial_rk","l":"touptek_serial_rk()","u":"%3Cinit%3E()"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlSocketService","l":"TpctrlSocketService(CaptureImageHelper, String)","u":"%3Cinit%3E(com.android.rockchip.camera2.video.CaptureImageHelper,java.lang.String)"},{"p":"com.android.rockchip.camera2.util","c":"TransformUtils","l":"TransformUtils()","u":"%3Cinit%3E()"},{"p":"com.android.rockchip.camera2.video","c":"TvPreviewHelper","l":"TvPreviewHelper(Context, ViewGroup)","u":"%3Cinit%3E(android.content.Context,android.view.ViewGroup)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"updateParam(TouptekIspParam, int)","u":"updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"updateParam(TouptekIspParam, int, int)","u":"updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int,int)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.StreamType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.android.rockchip.camera2.service","c":"TpctrlService.StreamType","l":"values()"},{"p":"com.android.rockchip.camera2.util","c":"TouptekIspParam","l":"values()"},{"p":"com.android.rockchip.camera2.video","c":"VideoDecoder","l":"VideoDecoder(String, Surface)","u":"%3Cinit%3E(java.lang.String,android.view.Surface)"}];updateSearchResults();