<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Apr 29 14:54:29 CST 2025 -->
<title>VideoDecoder</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-29">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.video, class: VideoDecoder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.video</a></div>
<h1 title="类 VideoDecoder" class="title">类 VideoDecoder</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.video.VideoDecoder</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">VideoDecoder</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">VideoDecoder 类负责解码视频文件并将其渲染到给定的 Surface 上。
 <p>
 此类提供了视频解码的初始化、播放控制（播放、暂停、逐帧播放）、跳转以及资源释放等功能。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="VideoDecoder.PlaybackListener.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">播放状态回调接口</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,android.view.Surface)" class="member-name-link">VideoDecoder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;videoPath,
 android.view.Surface&nbsp;surface)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数。</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentPosition()" class="member-name-link">getCurrentPosition</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取视频的当前位置。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlaybackSpeed()" class="member-name-link">getPlaybackSpeed</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前播放速度</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVideoDuration()" class="member-name-link">getVideoDuration</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取视频的持续时间。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDecoding()" class="member-name-link">isDecoding</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查视频是否正在解码。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFrameByFrame()" class="member-name-link">isFrameByFrame</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查视频是否处于逐帧模式。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isPaused()" class="member-name-link">isPaused</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查视频是否处于暂停状态。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isPlaybackCompleted()" class="member-name-link">isPlaybackCompleted</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查视频播放是否已完成</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resetToStart()" class="member-name-link">resetToStart</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">重置播放到开始位置</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#seekRelative(long)" class="member-name-link">seekRelative</a><wbr>(long&nbsp;deltaMs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">相对跳转控制方法。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#seekTo(long)" class="member-name-link">seekTo</a><wbr>(long&nbsp;position)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">跳转到视频的指定位置。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPlaybackListener(com.android.rockchip.camera2.video.VideoDecoder.PlaybackListener)" class="member-name-link">setPlaybackListener</a><wbr>(<a href="VideoDecoder.PlaybackListener.html" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置播放监听器</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPlaybackSpeed(float)" class="member-name-link">setPlaybackSpeed</a><wbr>(float&nbsp;speed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置播放速度</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startDecoding()" class="member-name-link">startDecoding</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始视频解码过程。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stepFrame()" class="member-name-link">stepFrame</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">逐帧播放视频。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopDecoding()" class="member-name-link">stopDecoding</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止视频解码过程。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#togglePlayPause()" class="member-name-link">togglePlayPause</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换播放和暂停状态。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,android.view.Surface)">
<h3>VideoDecoder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VideoDecoder</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;videoPath,
 android.view.Surface&nbsp;surface)</span></div>
<div class="block">构造函数。
 <p>
 初始化 VideoDecoder 实例并设置视频文件路径和渲染 Surface。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoPath</code> - 视频文件的路径。</dd>
<dd><code>surface</code> - 用于渲染视频的 Surface。</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="setPlaybackListener(com.android.rockchip.camera2.video.VideoDecoder.PlaybackListener)">
<h3>setPlaybackListener</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPlaybackListener</span><wbr><span class="parameters">(<a href="VideoDecoder.PlaybackListener.html" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a>&nbsp;listener)</span></div>
<div class="block">设置播放监听器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 播放监听器</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startDecoding()">
<h3>startDecoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startDecoding</span>()</div>
<div class="block">开始视频解码过程。
 <p>
 此方法会初始化 MediaExtractor 和 MediaCodec，并启动解码线程。
 解码后的视频帧会被渲染到指定的 Surface 上。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="seekTo(long)">
<h3>seekTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">seekTo</span><wbr><span class="parameters">(long&nbsp;position)</span></div>
<div class="block">跳转到视频的指定位置。
 <p>
 此方法会将解码器和提取器的状态重置到指定的时间位置。
 跳转位置会自动对齐到最近的关键帧。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>position</code> - 要跳转到的位置，以微秒为单位。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="togglePlayPause()">
<h3>togglePlayPause</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">togglePlayPause</span>()</div>
<div class="block">切换播放和暂停状态。
 <p>
 如果当前处于播放状态，则暂停播放；如果当前处于暂停状态，则恢复播放。
 如果播放已完成，则重置到开始位置并开始播放。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="stepFrame()">
<h3>stepFrame</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stepFrame</span>()</div>
<div class="block">逐帧播放视频。
 <p>
 此方法会解码并渲染视频的单个帧，然后暂停解码。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="seekRelative(long)">
<h3>seekRelative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">seekRelative</span><wbr><span class="parameters">(long&nbsp;deltaMs)</span></div>
<div class="block">相对跳转控制方法。
 <p>
 此方法会基于当前播放位置进行时间偏移跳转。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>deltaMs</code> - 时间偏移量（单位：毫秒，正数表示前进，负数表示后退）。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopDecoding()">
<h3>stopDecoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopDecoding</span>()</div>
<div class="block">停止视频解码过程。
 <p>
 此方法会停止解码线程并释放解码器和提取器的资源。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="isDecoding()">
<h3>isDecoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDecoding</span>()</div>
<div class="block">检查视频是否正在解码。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果正在解码则返回 true，否则返回 false。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isPaused()">
<h3>isPaused</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isPaused</span>()</div>
<div class="block">检查视频是否处于暂停状态。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果暂停则返回 true，否则返回 false。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFrameByFrame()">
<h3>isFrameByFrame</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFrameByFrame</span>()</div>
<div class="block">检查视频是否处于逐帧模式。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果处于逐帧模式则返回 true，否则返回 false。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVideoDuration()">
<h3>getVideoDuration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getVideoDuration</span>()</div>
<div class="block">获取视频的持续时间。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>视频的持续时间，以微秒为单位。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentPosition()">
<h3>getCurrentPosition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getCurrentPosition</span>()</div>
<div class="block">获取视频的当前位置。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>视频的当前位置，以微秒为单位。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPlaybackSpeed(float)">
<h3>setPlaybackSpeed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPlaybackSpeed</span><wbr><span class="parameters">(float&nbsp;speed)</span></div>
<div class="block">设置播放速度</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>speed</code> - 播放速度倍率，1.0表示正常速度</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlaybackSpeed()">
<h3>getPlaybackSpeed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getPlaybackSpeed</span>()</div>
<div class="block">获取当前播放速度</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前播放速度倍率</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resetToStart()">
<h3>resetToStart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resetToStart</span>()</div>
<div class="block">重置播放到开始位置</div>
</section>
</li>
<li>
<section class="detail" id="isPlaybackCompleted()">
<h3>isPlaybackCompleted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isPlaybackCompleted</span>()</div>
<div class="block">检查视频播放是否已完成</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果播放完成返回true，否则返回false</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
