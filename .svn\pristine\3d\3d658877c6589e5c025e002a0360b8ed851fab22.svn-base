package com.android.rockchip.camera2.util;

import android.util.Log;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

/**
 * HdmiService 类用于检测 HDMI 输入状态的变化。
 */
public class HdmiService {
    private static final String TAG = "HdmiService";
    /* HDMI IN 状态文件路径（需注意权限配置） */
    private final String mHdmiRxDevicePath = "/sys/kernel/debug/hdmirx/status";
    private File mHdmiRxFile;
    /* 保存上一次状态，防止重复触发 */
    private String lastStatus = "";
    /* 控制线程终止 */
    private boolean threadStatus = false;

    /* HDMI 状态变化监听器 */
    private HdmiListener listener;

    /**
     * 定义 HDMI 状态变化监听接口。
     */
    public interface HdmiListener {
        /**
         * 当 HDMI 状态发生变化时调用。
         *
         * @param status 当前 HDMI 状态
         */
        void onHdmiStatusChanged(String status);
    }

    /**
     * 设置 HDMI 状态变化监听器。
     *
     * @param listener 监听器实例
     */
    public void setHdmiListener(HdmiListener listener) {
        this.listener = listener;
    }

    /* 单例实例 */
    private static HdmiService instance = new HdmiService();

    /**
     * 获取 HdmiService 的单例实例。
     *
     * @return HdmiService 实例
     */
    public static HdmiService getInstance() {
        return instance;
    }

    /**
     * 初始化 HDMI 状态检测。
     */
    public void init() {
        Log.d(TAG, "init: 开始读取 HDMI 状态");
        mHdmiRxFile = new File(mHdmiRxDevicePath);
        threadStatus = false;
        new ReadThread().start();
    }

    /**
     * 用于读取 HDMI 状态的线程类。
     */
    private class ReadThread extends Thread {
        @Override
        public void run() {
            while (!threadStatus) {
                try {
                    /* 每 300 毫秒检测一次 */
                    Thread.sleep(300);
                    FileReader reader = new FileReader(mHdmiRxFile);
                    BufferedReader bufReader = new BufferedReader(reader);
                    String currentStatus = bufReader.readLine();
                    bufReader.close();

                    /* 如果状态有变化则触发回调 */
                    if (currentStatus != null && !currentStatus.equals(lastStatus)) {
                        lastStatus = currentStatus;
                        /* HDMI 插入状态：包含 "plugin" */
                        if (currentStatus.contains("plugin")) {
                            Log.d(TAG, "检测到 HDMI 插入，延时 2 秒等待驱动稳定");
                            Thread.sleep(2500);
                        }
                        /* HDMI 拔出状态：包含 "plugout"（拔出时不需要延时） */
                        if (listener != null) {
                            listener.onHdmiStatusChanged(currentStatus);
                        }
                    }
                } catch (IOException e) {
                    Log.d(TAG, "读取 HDMI 状态异常：" + e.toString());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 停止 HDMI 状态检测线程。
     */
    public void stop() {
        threadStatus = true;
    }
}
