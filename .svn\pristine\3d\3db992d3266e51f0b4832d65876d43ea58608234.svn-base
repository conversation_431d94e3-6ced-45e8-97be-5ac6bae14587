[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: x86", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86\\android_gradle_build.json due to:", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a hard configure file changed, will remove stale configuration folder", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86\\metadata_generation_command.txt (LAST_MODIFIED_CHANGED)", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86'", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86'", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=29\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++11\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\54t2sa2v\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\54t2sa2v\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\.cxx\\\\Debug\\\\54t2sa2v\\\\x86\" ^\n  -GNinja\n", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=29\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++11\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\54t2sa2v\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\54t2sa2v\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamViewDemo\\\\app\\\\.cxx\\\\Debug\\\\54t2sa2v\\\\x86\" ^\n  -GNinja\n", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86\\compile_commands.json.bin normally", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\Debug\\54t2sa2v\\x86\\compile_commands.json to C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\.cxx\\tools\\debug\\x86\\compile_commands.json", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamViewDemo\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]