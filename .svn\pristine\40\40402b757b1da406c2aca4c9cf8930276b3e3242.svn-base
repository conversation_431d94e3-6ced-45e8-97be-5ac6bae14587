package com.android.rockchip.camera2.integrated.settings.fragments;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.touptek.utils.TpSambaClient;
import com.android.rockchip.mediacodecnew.R;

import java.util.ArrayList;
import java.util.List;

/**
 * ToupTek SMB网络共享设置Fragment
 * 迁移自SMBSettingsDialog的所有功能
 */
public class TpSmbSettingsFragment extends Fragment {
    
    private static final String TAG = "TpSmbSettingsFragment";
    private static final String DEFAULT_IP = "*************";
    
    // UI组件
    private CheckBox enableSmbCheckBox;
    private CheckBox enableSmbVideoCheckBox;
    private EditText serverIpEditText;
    private EditText shareNameEditText;
    private EditText usernameEditText;
    private EditText passwordEditText;
    private Spinner remotePathSpinner;
    private Button browsePathButton;
    private TextView connectionStatusText;
    private Button testConnectionButton;
    private Button saveSettingsButton;
    private ProgressBar progressBar;
    
    // 数据
    private TpSambaClient tpSambaClient;
    private List<String> remotePaths = new ArrayList<>();
    private ArrayAdapter<String> remotePathsAdapter;
    private String selectedRemotePath = "";
    
    // UI更新Handler
    private final Handler uiHandler = new Handler(Looper.getMainLooper());
    
    /**
     * 创建Fragment实例的工厂方法
     */
    public static TpSmbSettingsFragment newInstance(TpSambaClient tpSambaClient) {
        TpSmbSettingsFragment fragment = new TpSmbSettingsFragment();
        fragment.tpSambaClient = tpSambaClient;
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "TpSmbSettingsFragment onCreate");
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_tp_smb_settings, container, false);
        
        // 初始化视图
        initViews(view);
        
        // 初始化适配器
        initAdapters();
        
        // 加载现有设置
        loadExistingSettings();
        
        // 设置事件监听器
        setupEventListeners();
        
        Log.d(TAG, "TpSmbSettingsFragment onCreateView 完成");
        return view;
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        enableSmbCheckBox = view.findViewById(R.id.cb_enable_smb);
        enableSmbVideoCheckBox = view.findViewById(R.id.cb_enable_smb_video);
        serverIpEditText = view.findViewById(R.id.et_server_ip);
        shareNameEditText = view.findViewById(R.id.et_share_name);
        usernameEditText = view.findViewById(R.id.et_username);
        passwordEditText = view.findViewById(R.id.et_password);
        remotePathSpinner = view.findViewById(R.id.spinner_remote_path);
        browsePathButton = view.findViewById(R.id.btn_browse_path);
        connectionStatusText = view.findViewById(R.id.tv_connection_status);
        testConnectionButton = view.findViewById(R.id.btn_test_connection);
        saveSettingsButton = view.findViewById(R.id.btn_save_settings);
        progressBar = view.findViewById(R.id.progress_bar);
    }
    
    /**
     * 初始化适配器
     */
    private void initAdapters() {
        if (getContext() != null) {
            remotePathsAdapter = new ArrayAdapter<>(
                getContext(),
                android.R.layout.simple_spinner_item,
                remotePaths
            );
            remotePathsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            remotePathSpinner.setAdapter(remotePathsAdapter);
        }
    }
    
    /**
     * 加载现有SMB设置
     */
    private void loadExistingSettings() {
        if (tpSambaClient == null) {
            Log.w(TAG, "SMBFileUploader为null，无法加载设置");
            return;
        }
        
        enableSmbCheckBox.setChecked(tpSambaClient.isEnabled());
        // 视频上传开关已移除，默认启用SMB时就支持所有文件类型上传
        enableSmbVideoCheckBox.setChecked(tpSambaClient.isEnabled());
        
        // 使用新的配置API加载设置
        TpSambaClient.SMBConfig config = tpSambaClient.getConnectionParams();

        // 设置默认IP或加载保存的IP
        String savedIp = config.getServerIp();
        serverIpEditText.setText(savedIp.isEmpty() ? DEFAULT_IP : savedIp);

        usernameEditText.setText(config.getUsername());
        passwordEditText.setText(""); // 出于安全考虑，不显示密码
        shareNameEditText.setText(config.getShareName());

        // 保存当前设置的远程路径
        selectedRemotePath = config.getRemotePath();
        
        // 如果已经有远程路径，添加到列表
        if (!selectedRemotePath.isEmpty()) {
            remotePaths.clear();
            remotePaths.add(selectedRemotePath);
            remotePathsAdapter.notifyDataSetChanged();
        }
        
        updateConnectionStatus();
    }
    
    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // 远程路径选择事件
        remotePathSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < remotePaths.size()) {
                    selectedRemotePath = remotePaths.get(position);
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 不处理
            }
        });
        
        // 浏览路径按钮
        browsePathButton.setOnClickListener(v -> browseRemotePaths());
        
        // 测试连接按钮
        testConnectionButton.setOnClickListener(v -> testConnection());
        
        // 保存设置按钮
        saveSettingsButton.setOnClickListener(v -> saveSettings());
    }
    
    /**
     * 浏览远程路径
     */
    private void browseRemotePaths() {
        if (tpSambaClient == null) {
            showToast("SMB上传器未初始化");
            return;
        }
        
        showProgress(true);
        connectionStatusText.setText("连接状态：正在浏览路径...");
        
        // 在后台线程执行
        new Thread(() -> {
            try {
                // 先保存当前设置
                saveCurrentSettings();
                
                // 获取远程目录列表
                tpSambaClient.getRemoteDirectories(new TpSambaClient.DirectoryListListener() {
                    @Override
                    public void onDirectoriesLoaded(List<String> directories) {
                        uiHandler.post(() -> {
                            showProgress(false);
                            if (directories != null && !directories.isEmpty()) {
                                remotePaths.clear();
                                remotePaths.addAll(directories);
                                remotePathsAdapter.notifyDataSetChanged();
                                connectionStatusText.setText("连接状态：路径浏览成功");
                                showToast("找到 " + directories.size() + " 个路径");
                            } else {
                                connectionStatusText.setText("连接状态：未找到路径");
                                showToast("未找到可用路径");
                            }
                        });
                    }

                    @Override
                    public void onDirectoryLoadFailed(String errorMessage) {
                        uiHandler.post(() -> {
                            showProgress(false);
                            connectionStatusText.setText("连接状态：浏览失败");
                            showToast("浏览路径失败: " + errorMessage);
                        });
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "浏览路径失败", e);
                uiHandler.post(() -> {
                    showProgress(false);
                    connectionStatusText.setText("连接状态：浏览失败");
                    showToast("浏览路径失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    /**
     * 测试连接
     */
    private void testConnection() {
        if (tpSambaClient == null) {
            showToast("SMB上传器未初始化");
            return;
        }
        
        showProgress(true);
        connectionStatusText.setText("连接状态：正在测试连接...");
        
        // 在后台线程执行
        new Thread(() -> {
            try {
                // 先保存当前设置
                saveCurrentSettings();
                
                // 测试连接
                tpSambaClient.testConnection(new TpSambaClient.UploadListener() {
                    @Override
                    public void onUploadSuccess(String remoteFilePath) {
                        uiHandler.post(() -> {
                            showProgress(false);
                            connectionStatusText.setText("连接状态：连接成功");
                            showToast("SMB连接测试成功");
                        });
                    }
                    
                    @Override
                    public void onUploadFailed(String errorMessage) {
                        uiHandler.post(() -> {
                            showProgress(false);
                            connectionStatusText.setText("连接状态：连接失败");
                            showToast("连接失败: " + errorMessage);
                        });
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "测试连接失败", e);
                uiHandler.post(() -> {
                    showProgress(false);
                    connectionStatusText.setText("连接状态：测试失败");
                    showToast("测试连接失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    /**
     * 保存设置
     */
    private void saveSettings() {
        if (tpSambaClient == null) {
            showToast("SMB上传器未初始化");
            return;
        }
        
        try {
            saveCurrentSettings();
            showToast("设置已保存");
            updateConnectionStatus();
        } catch (Exception e) {
            Log.e(TAG, "保存设置失败", e);
            showToast("保存设置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存当前设置到SMBFileUploader
     */
    private void saveCurrentSettings() {
        if (tpSambaClient == null) return;
        
        String serverIp = serverIpEditText.getText().toString().trim();
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String shareName = shareNameEditText.getText().toString().trim();
        boolean enableSmb = enableSmbCheckBox.isChecked();
        boolean enableSmbVideo = enableSmbVideoCheckBox.isChecked();
        
        // 设置连接参数
        tpSambaClient.setConnectionParams(
            serverIp.isEmpty() ? DEFAULT_IP : serverIp,
            username,
            password,
            shareName.isEmpty() ? "share" : shareName,
            selectedRemotePath,
            enableSmb
        );
        
        // 视频上传开关已移除，现在统一通过SMB总开关控制所有文件类型上传
        // enableSmbVideo参数现在被忽略，因为启用SMB就支持所有文件类型
    }
    
    /**
     * 更新连接状态显示
     */
    private void updateConnectionStatus() {
        if (tpSambaClient == null) {
            connectionStatusText.setText("连接状态：SMB服务未初始化");
            return;
        }
        
        if (tpSambaClient.isEnabled()) {
            connectionStatusText.setText("连接状态：SMB服务已启用");
        } else {
            connectionStatusText.setText("连接状态：SMB服务已禁用");
        }
    }
    
    /**
     * 显示/隐藏进度条
     */
    private void showProgress(boolean show) {
        if (progressBar != null) {
            progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        }
        
        // 禁用/启用按钮
        if (testConnectionButton != null) {
            testConnectionButton.setEnabled(!show);
        }
        if (browsePathButton != null) {
            browsePathButton.setEnabled(!show);
        }
        if (saveSettingsButton != null) {
            saveSettingsButton.setEnabled(!show);
        }
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 更新SMBFileUploader实例（供外部调用）
     */
    public void updateSmbFileUploader(TpSambaClient tpSambaClient) {
        this.tpSambaClient = tpSambaClient;
        if (getView() != null) {
            loadExistingSettings();
        }
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TpSmbSettingsFragment onDestroy");
    }
}
