package com.android.rockchip.camera2;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;
import android.widget.Button;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

// StreamingService现在通过TpVideoSystem管理，无需直接导入
import com.android.rockchip.camera2.settings.TpSettingsDialog;
import com.android.rockchip.camera2.util.HdmiService;
import com.android.rockchip.camera2.util.TouptekIspParam;
import com.android.rockchip.camera2.video.TpVideoSystem;
import com.android.rockchip.camera2.video.TpVideoConfig;
import com.android.rockchip.mediacodecnew.R;

/**
 * MainActivity - 使用TpVideoSystem统一入口的示例
 * <p>
 * 此Activity演示如何使用TpVideoSystem类来简化相机和视频操作，
 * 功能包括视频预览、录制和图像捕获。基于VideoEncoderActivity的成功实现，确保1080P 60fps性能。
 * 支持TpVideoConfig配置系统，提供灵活的视频参数配置。
 * </p>
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    // UI组件
    private TextureView mTextureView;
    private Button mCaptureButton;
    private Button mRecordButton;
    private Button mSettingsButton;

    // 视频系统
    private TpVideoSystem videoSystem;
    
    // HDMI服务
    private HdmiService hdmiService;
    
    // 延迟处理器
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    // HDMI状态
    private boolean isHdmiConnected = false;

    // WiFi设置启动器
    private ActivityResultLauncher<Intent> wifiPanelLauncher;

    // 流媒体状态现在通过TpVideoSystem管理，无需单独维护
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化TouptekIspParam，确保SharedPreferences被正确初始化
        TouptekIspParam.init(this);

        // 初始化WiFi设置启动器
        initWifiPanelLauncher();

        // 初始化UI组件
        mTextureView = findViewById(R.id.textureView);
        mCaptureButton = findViewById(R.id.captureButton);
        mRecordButton = findViewById(R.id.recordButton);
        mSettingsButton = findViewById(R.id.settingsButton);
        
        // 设置按钮点击事件
        setupButtonListeners();
        
        // 初始化HDMI服务
        initHdmiService();

        // ✅ 重要：在onCreate中创建TpVideoSystem以支持推流功能
        // 这是因为RTSP推流需要注册ActivityResultLauncher，必须在Activity进入STARTED状态前完成
        initVideoSystemEarly();
        
        // 当TextureView准备好后，初始化TpVideoSystem
        mTextureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surfaceTexture, int width, int height) {
                // 创建用于相机预览的Surface
                Surface previewSurface = new Surface(surfaceTexture);

                // 完成TpVideoSystem初始化（TpVideoSystem已在onCreate中创建）
                completeVideoSystemInit(previewSurface);

                // 检查相机权限
                if (checkCameraPermission()) {
                    // 权限已有，直接初始化
                    videoSystem.initialize(previewSurface);
                } else {
                    requestCameraPermission();
                }
            }

            @Override
            public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surfaceTexture, int width, int height) {
                // 处理Surface尺寸变化
            }

            @Override
            public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surfaceTexture) {
                // 释放资源
                if (videoSystem != null) {
                    videoSystem.release();
                }
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surfaceTexture) {
                // 当纹理更新时
            }
        });
    }

    /**
     * 初始化WiFi设置启动器
     */
    private void initWifiPanelLauncher() {
        wifiPanelLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                Log.d(TAG, "WiFi设置返回，结果码: " + result.getResultCode());
                // WiFi设置返回后的处理逻辑可以在这里添加
            }
        );
    }

    /**
     * 第一步：在onCreate中提前创建TpVideoSystem
     * 这是为了满足RTSP推流功能的ActivityResultLauncher注册要求
     */
    private void initVideoSystemEarly() {
        try {
            Log.d(TAG, "在onCreate中创建TpVideoSystem...");

            // 创建TpVideoSystem实例 - 必须在onCreate中完成以支持推流功能
            TpVideoConfig config = TpVideoConfig.createDefault1080P();
            videoSystem = new TpVideoSystem(this, config);

            Log.d(TAG, "TpVideoSystem创建完成，等待Surface准备完成初始化");
        } catch (Exception e) {
            Log.e(TAG, "TpVideoSystem创建失败", e);
            Toast.makeText(this, "视频系统初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * 初始化HDMI服务
     */
    private void initHdmiService() {
        hdmiService = HdmiService.getInstance();
        hdmiService.setHdmiListener(this::handleHdmiStatusChanged);
        hdmiService.init();
        Log.d(TAG, "HDMI服务已初始化");
    }
    
    /**
     * 处理HDMI状态变化
     * 
     * @param isConnected HDMI连接状态
     */
    private void handleHdmiStatusChanged(boolean isConnected) {
        Log.d(TAG, "HDMI状态变化: " + (isConnected ? "已连接" : "已断开"));
        
        // 如果状态没有变化，不处理
        if (isHdmiConnected == isConnected) {
            return;
        }
        
        isHdmiConnected = isConnected;
        
        // 使用延迟处理，确保状态稳定
        mHandler.postDelayed(() -> {
            if (isConnected) {
                handleHdmiConnected();
            } else {
                handleHdmiDisconnected();
            }
        }, 500); // 延迟500毫秒处理
    }
    
    /**
     * 处理HDMI连接事件
     */
    private void handleHdmiConnected() {
        Log.d(TAG, "处理HDMI连接");
        runOnUiThread(() -> {
            Toast.makeText(this, "HDMI已连接", Toast.LENGTH_SHORT).show();
            
            // 如果TextureView可用且视频系统已初始化，重新启动预览
            if (mTextureView.isAvailable() && videoSystem != null) {
                if (!videoSystem.isCameraStarted()) {
                    android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                    if (surfaceTexture != null) {
                        Surface previewSurface = new Surface(surfaceTexture);
                        videoSystem.initialize(previewSurface);
                    }
                }
            }
        });
    }
    
    /**
     * 处理HDMI断开事件
     */
    private void handleHdmiDisconnected() {
        Log.d(TAG, "处理HDMI断开");
        runOnUiThread(() -> {
            Toast.makeText(this, "HDMI已断开", Toast.LENGTH_SHORT).show();
            
            // 如果正在录制，停止录制
            if (videoSystem != null && videoSystem.isRecording()) {
                videoSystem.stopRecording();
                updateRecordButtonState(false);
            }
            
            // HDMI断开时，推流会自动停止，无需手动处理
            
            // 停止预览
            if (videoSystem != null) {
                videoSystem.release();
            }
        });
    }
    
    /**
     * 第二步：完成TpVideoSystem初始化
     * 在TextureView准备好后调用，完成视频系统的完整初始化
     *
     * @param previewSurface 用于预览的Surface
     */
    private void completeVideoSystemInit(Surface previewSurface) {
        if (videoSystem == null) {
            Log.e(TAG, "TpVideoSystem未在onCreate中创建，无法完成初始化");
            Toast.makeText(this, "视频系统未正确初始化", Toast.LENGTH_LONG).show();
            return;
        }

        // 使用适配器模式，只重写需要的方法
        videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
            @Override
            public void onCameraStarted() {
                Log.d(TAG, "相机已启动");
                runOnUiThread(() -> {
                    // 相机启动完成，可以进行其他操作
                    Toast.makeText(MainActivity.this, "相机已启动", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onRecordingStarted(String outputPath) {
                Log.d(TAG, "录制已开始: " + outputPath);
                runOnUiThread(() -> {
                    updateRecordButtonState(true);
                    Toast.makeText(MainActivity.this, "开始录制", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onRecordingStopped(String outputPath) {
                Log.d(TAG, "录制已停止: " + outputPath);
                runOnUiThread(() -> {
                    updateRecordButtonState(false);
                    Toast.makeText(MainActivity.this,
                            "录制完成: " + outputPath, Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onImageCaptured(String imagePath) {
//                Log.d(TAG, "图片已捕获: " + imagePath);
//                runOnUiThread(() ->
//                    Toast.makeText(MainActivity.this,
//                            "图片已保存: " + imagePath, Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onStreamingStatusChanged(boolean isStreaming, String rtspUrl) {
                Log.d(TAG, "推流状态变化: " + isStreaming + ", URL: " + rtspUrl);
                // 推流状态变化现在由设置界面处理，主界面不再显示推流相关信息
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "错误: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this,
                            "错误: " + errorMessage, Toast.LENGTH_LONG).show();
                    // 如果是录制错误，更新按钮状态
                    if (videoSystem.isRecording()) {
                        updateRecordButtonState(false);
                    }
                    // 错误处理，推流相关错误现在由设置界面处理
                });
            }
        });
    }
    

    
    /**
     * 设置按钮点击事件
     */
    private void setupButtonListeners() {
        // 拍照按钮
        mCaptureButton.setOnClickListener(v -> {
            if (videoSystem != null && videoSystem.isCameraStarted()) {
                videoSystem.captureImage(null); // 使用默认路径
//                Toast.makeText(this, "拍照中...", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "相机未启动", Toast.LENGTH_SHORT).show();
            }
        });

        // 录像按钮
        mRecordButton.setOnClickListener(v -> {
            if (videoSystem == null || !videoSystem.isCameraStarted()) {
                Toast.makeText(this, "相机未启动", Toast.LENGTH_SHORT).show();
                return;
            }

            // 检查SimpleVideoSystem中的状态
            boolean isRecording = videoSystem.isRecording();

            if (isRecording) {
                // 停止录制
                videoSystem.stopRecording();
            } else {
                // 开始录制（使用默认路径）
                videoSystem.startRecording(null);
            }
        });

        // 设置按钮
        mSettingsButton.setOnClickListener(v -> {
            openSettings();
        });
    }
    
    /**
     * 打开设置对话框
     */
    private void openSettings() {
        if (videoSystem == null) {
            Toast.makeText(this, "视频系统未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        // 获取StreamingService实例（通过TpVideoSystem）
        TpSettingsDialog settingsDialog = TpSettingsDialog.newInstance(
            videoSystem,
            videoSystem.getStreamingService(), // 从TpVideoSystem获取StreamingService
            wifiPanelLauncher
        );

        settingsDialog.show(getSupportFragmentManager(), "TpSettingsDialog");
        Log.d(TAG, "设置对话框已显示");
    }
    
    /**
     * 更新录制按钮状态
     */
    private void updateRecordButtonState(boolean isRecording) {
        mRecordButton.setText(isRecording ? "停止录制" : "开始录制");
    }


    

    
    /**
     * 启动相机预览（SimpleVideoSystem中已自动处理）
     */
    private void startPreview() {
        // SimpleVideoSystem在initialize时会自动启动相机
        Log.d(TAG, "相机预览将在initialize时自动启动");
    }
    
    /**
     * 检查相机权限
     */
    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.CAMERA}, 
                REQUEST_CAMERA_PERMISSION);
    }
    
    /**
     * 权限请求结果处理
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限获取成功，初始化视频系统
                if (mTextureView.isAvailable() && videoSystem != null) {
                    android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                    if (surfaceTexture != null) {
                        Surface previewSurface = new Surface(surfaceTexture);
                        videoSystem.initialize(previewSurface);
                    }
                }
            } else {
                Toast.makeText(this, "需要相机权限才能使用此功能", Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 恢复预览
        if (mTextureView.isAvailable() && videoSystem != null && !videoSystem.isCameraStarted()) {
            android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
            if (surfaceTexture != null) {
                Surface previewSurface = new Surface(surfaceTexture);
                videoSystem.initialize(previewSurface);
            }
        }
        
        // 重新启动HDMI服务
        if (hdmiService != null) {
            hdmiService.init();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 停止录制
        if (videoSystem != null && videoSystem.isRecording()) {
            videoSystem.stopRecording();
            updateRecordButtonState(false);
        }
        
        // 推流状态现在由设置界面管理，主界面不直接控制
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放资源
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
        }
        
        // 停止HDMI服务
        if (hdmiService != null) {
            hdmiService.stop();
        }
        
        // StreamingService资源现在通过TpVideoSystem自动管理
    }
}
