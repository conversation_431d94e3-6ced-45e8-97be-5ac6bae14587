                        -HD:\RK3588\touptek_serial_rk\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++11
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\RK3588\touptek_serial_rk\app\build\intermediates\cxx\Debug\6o6h6d6d\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\RK3588\touptek_serial_rk\app\build\intermediates\cxx\Debug\6o6h6d6d\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\RK3588\touptek_serial_rk\app\.cxx\Debug\6o6h6d6d\x86
-GNinja
                        Build command args: []
                        Version: 2