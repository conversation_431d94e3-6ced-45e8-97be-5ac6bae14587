/* ***************************************************************
//  usys_reactor   version:  1.0  date: 23/23/2009
//  -------------------------------------------------------------
//  <PERSON><PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#include "utiny/usys_reactor.h"
#include "utiny/utiny_config.h"
#include "utiny/usys_handler.h"
#include "utiny/usys_api.h"
#include "utiny/usys_network.h"
#include "utiny/usys_task.h"
#include <algorithm>
#include <set>
#include <list>
#include <deque>
#include <vector>
#ifndef _WIN32
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#endif
#include "usys_reactor_select.h"
#include "usys_reactor_epoll.h"

usys_reactor::usys_reactor()
: terminated_(false), loop_thread_id_(0), timer_queue_(new reactor_timer_queue), trigger_ptr_(new reactor_trigger)
{
}

usys_reactor::~usys_reactor()
{
    USYS_DEBUG("~usys_reactor");
}

usys_reactor* usys_reactor::create_reactor(REACTOR_TYPE t)
{
    switch (t)
    {
    case REACTOR_SELECT:
        return new usys_reactor_select();
#if defined(__linux__) && !defined(UCE_HASNOT_EPOLL)     
    case REACTOR_EPOLL:
        return new usys_reactor_epoll();
#endif		
    default:
        return 0;
    }
}

int usys_reactor::loop()
{
    unsigned long long cur_ms;
    timeval tv_wait;
    timeval* p_tv_wati;
    loop_thread_id_ = usys_get_thread_id();
    do {
        cur_ms = usys_get_tick_count();
        timer_queue_->check_timer(cur_ms);

        if (!trigger_task())
            continue;

        tv_wait.tv_sec = tv_wait.tv_usec = 0;
        p_tv_wati = timer_queue_->get_tv_wait(cur_ms, &tv_wait);

        int ret = work(p_tv_wati);
        if (ret < 0)
            break;
    } while (!terminated_);
    return 0;
}

class reactor_terminate_task : public usys_task
{
    usys_reactor* parent_ptr_;
public:
    reactor_terminate_task(usys_reactor* parent)
        : parent_ptr_(parent)
    {
    };
    virtual void call()
    {
        if (parent_ptr_)
            parent_ptr_->do_terminate();
    };
};

void usys_reactor::terminate()
{
    usys_smartptr<usys_task> task_terminate_ptr(new reactor_terminate_task(this));
    delegate_task(task_terminate_ptr, true);
}

void usys_reactor::do_terminate()
{
    terminated_ = true;
    timer_queue_->clear();
}

usys_smartptr_mtbase_ptr usys_reactor::register_timer_msec(unsigned msec, bool repeat, const usys_smartptr<usys_timer_doozer>& doozer_ptr, const void* arg, bool precise)
{
    usys_smartptr_mtbase_ptr timer_id = timer_queue_->insert_doozer(msec, repeat, doozer_ptr, arg, precise);
    trigger_ptr_->trigger();
    return timer_id;
}

int usys_reactor::unregister_timer_msec(const usys_smartptr_mtbase_ptr& timer_id)
{
    return timer_queue_->remove_doozer(timer_id);
}

int usys_reactor::delegate_task(const usys_smartptr<usys_task>& task_ptr, bool quickly)
{
    USYS_ASSERT(task_ptr != 0);
    if (task_ptr == 0)
        return -1;
    if (quickly && usys_get_thread_id() == loop_thread_id_)
    {
        task_ptr->call();
        return 0;
    }

    bool btrigger;
    {
        usys_guard<usys_mutex> g(task_deque_lock_);
        btrigger = task_deque_.empty();
        task_deque_.push_back(task_ptr);
    }
    if (btrigger)
        trigger_ptr_->trigger();
    return 0;
}

bool usys_reactor::trigger_task()
{
    std::deque<usys_smartptr<usys_task> > task_deque;
    {
        usys_guard<usys_mutex> g(task_deque_lock_);
        task_deque.swap(task_deque_);
    }

    bool bempty = task_deque.empty();
    while (!task_deque.empty())
    {
        usys_smartptr<usys_task> t = task_deque.front();
        task_deque.pop_front();
#ifdef DEBUG
        try
        {
            t->call();
        }
        catch (...)
        {
            USYS_ASSERT(false);
        }
#else
        t->call();
#endif
    }
    return bempty;
}

void usys_reactor::trigger()
{
    if (trigger_ptr_ != 0)
        trigger_ptr_->trigger();
}
