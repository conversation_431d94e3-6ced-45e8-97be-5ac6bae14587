package com.android.rockchip.camera2.util;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.Map;

/**
 * 枚举类 TouptekIspParam 定义了摄像头 ISP 参数的键值对。
 * <p>
 * 每个枚举项对应一个 ISP 参数及其唯一的整数值。
 * 此类同时提供了参数的存储、读取、更新和监听机制。
 * 支持与设备串口通信，可以向设备发送参数或从设备读取参数。
 * </p>
 */
public enum TouptekIspParam 
{
    /** 版本号 */
    TOUPTEK_PARAM_VERSION(0x0),

    /** 曝光模式选择 */
    TOUPTEK_PARAM_EXPOSURECHOICE(0x1),

    /** 曝光补偿 */
    TOUPTEK_PARAM_EXPOSURECOMPENSATION(0x2),

    /** 曝光时间 */
    TOUPTEK_PARAM_EXPOSURETIME(0x3),

    /** 曝光增益 */
    TOUPTEK_PARAM_EXPOSUREGAIN(0x4),

    /** 白平衡模式选择 */
    TOUPTEK_PARAM_WBCHOICE(0x5),

    /** 红色通道的白平衡增益 */
    TOUPTEK_PARAM_WBREDGAIN(0x6),

    /** 绿色通道的白平衡增益 */
    TOUPTEK_PARAM_WBGREENGAIN(0x7),

    /** 蓝色通道的白平衡增益 */
    TOUPTEK_PARAM_WBBLUEGAIN(0x8),

    /** 锐化参数 */
    TOUPTEK_PARAM_SHARPNESS(0x9),

    /** 降噪参数 */
    TOUPTEK_PARAM_DENOISE(0xA),

    /** 镜像效果（水平/垂直镜像） */
    TOUPTEK_PARAM_MIRROR(0xB),

    /** 翻转效果（水平/垂直翻转） */
    TOUPTEK_PARAM_FLIP(0xC),

    /** 饱和度 */
    TOUPTEK_PARAM_SATURATION(0xD),

    /** Gamma 校正 */
    TOUPTEK_PARAM_GAMMA(0xE),

    /** 对比度 */
    TOUPTEK_PARAM_CONTRAST(0xF),

    /** 水平分辨率/频率（具体含义取决于硬件） */
    TOUPTEK_PARAM_HZ(0x10),

    /** 亮度 */
    TOUPTEK_PARAM_BRIGHTNESS(0x11),

    /** 色调 */
    TOUPTEK_PARAM_HUE(0x12),

    /** 彩色/灰度模式选择 */
    TOUPTEK_PARAM_COLORORGRAY(0x13),

    /** 带宽控制（影响图像处理的速度和质量） */
    TOUPTEK_PARAM_BANDWIDTH(0x14),

    /** 色彩色调 */
    TOUPTEK_PARAM_COLORTONE(0x15),

    /** 彩色温度红色通道增益 */
    TOUPTEK_PARAM_CTREDGAIN(0x16),

    /** 彩色温度绿色通道增益 */
    TOUPTEK_PARAM_CTGREENGAIN(0x17),

    /** 彩色温度蓝色通道增益 */
    TOUPTEK_PARAM_CTBLUEGAIN(0x18),

    /** 暗部增强 */
    TOUPTEK_PARAM_DARKENHANCE(0x19),

    /** 宽动态范围曝光比率 */
    TOUPTEK_PARAM_WDREXPRATIO(0x1A),

    /** 低动态范围对比度比率 */
    TOUPTEK_PARAM_LDCRATIO(0x1B);

    /** 枚举值对应的整数值 */
    private final int value;
    
    /** 用于存储参数值的SharedPreferences实例 */
    private static SharedPreferences sharedPreferences;
    
    /** 数据变化监听器 */
    private static OnDataChangedListener dataChangedListener;
    
    /** 串口实例 */
    private static touptek_serial_rk serialInstance;
    
    /** 串口状态监听器 */
    private static OnSerialStateChangedListener serialStateListener;

    /**
     * 构造函数
     * 
     * @param value 枚举项对应的整数值
     */
    TouptekIspParam(int value) 
    {
        this.value = value;
    }

    /**
     * 获取枚举值的整数值。
     *
     * @return 当前枚举项对应的整数值。
     */
    public int getValue() 
    {
        return value;
    }

    /**
     * 获取参数的最小值。
     * <p>
     * 根据不同参数类型返回其有效范围的最小值。
     * </p>
     *
     * @param param 需要获取最小值的参数
     * @return 参数的最小值
     * <p>
     * 曝光时间最小值返回0，实际下位机相机设置的值为0.019ms
     */
    public static int getMinValue(TouptekIspParam param) 
    {
        if (param == null) 
        {
            return 0;
        }
        
        switch (param) 
        {
            case TOUPTEK_PARAM_EXPOSURECOMPENSATION:
                return 0;
            case TOUPTEK_PARAM_EXPOSURETIME:
                return 0; 
            case TOUPTEK_PARAM_EXPOSUREGAIN:
                return 0;
            case TOUPTEK_PARAM_WBREDGAIN:
            case TOUPTEK_PARAM_WBGREENGAIN:
            case TOUPTEK_PARAM_WBBLUEGAIN:
                return 1;
            case TOUPTEK_PARAM_SHARPNESS:
                return 0;
            case TOUPTEK_PARAM_DENOISE:
                return 0;
            case TOUPTEK_PARAM_SATURATION:
                return 0;
            case TOUPTEK_PARAM_GAMMA:
                return 1;
            case TOUPTEK_PARAM_CONTRAST:
                return 0;
            case TOUPTEK_PARAM_BRIGHTNESS:
                return 0;
            case TOUPTEK_PARAM_HUE:
                return 0;
            default:
                return -1;
        }
    }

    /**
     * 获取参数的最大值。
     * <p>
     * 根据不同参数类型返回其有效范围的最大值。
     * </p>
     *
     * @param param 需要获取最大值的参数
     * @return 参数的最大值
     */
    public static int getMaxValue(TouptekIspParam param) 
    {
        if (param == null) 
        {
            return 100;
        }
        
        switch (param) 
        {
            case TOUPTEK_PARAM_EXPOSURECOMPENSATION:
                return 25;
            case TOUPTEK_PARAM_EXPOSURETIME:
                return 1000; 
            case TOUPTEK_PARAM_EXPOSUREGAIN:
                return 55;
            case TOUPTEK_PARAM_WBREDGAIN:
            case TOUPTEK_PARAM_WBGREENGAIN:
            case TOUPTEK_PARAM_WBBLUEGAIN:
                return 4095;
            case TOUPTEK_PARAM_SHARPNESS:
                return 100;
            case TOUPTEK_PARAM_DENOISE:
                return 50;
            case TOUPTEK_PARAM_SATURATION:
                return 100;
            case TOUPTEK_PARAM_GAMMA:
                return 20;
            case TOUPTEK_PARAM_CONTRAST:
                return 100;
            case TOUPTEK_PARAM_BRIGHTNESS:
                return 100;
            case TOUPTEK_PARAM_HUE:
                return 100;
            default:
                return -1;
        }
    }

    /**
     * 根据整数值获取枚举项。
     * <p>
     * 将设备返回的命令值转换为对应的枚举项。
     * </p>
     *
     * @param i 整数值，对应枚举项的value
     * @return 匹配的枚举项，如果没有匹配项则返回null
     */
    public static TouptekIspParam fromInt(int i) 
    {
        for (TouptekIspParam param : TouptekIspParam.values()) 
        {
            if (param.getValue() == i) 
            {
                return param;
            }
        }
        return null; // 没有匹配项
    }

    /**
     * 初始化 TouptekIspParam 和串口通信。
     * <p>
     * 此方法应在应用启动时调用，用于初始化参数存储和串口监控。
     * </p>
     *
     * @param context 应用上下文，用于获取 SharedPreferences 实例。
     */
    public static void init(Context context) 
    {
        if (sharedPreferences == null) 
        {
            sharedPreferences = context.getSharedPreferences("TouptekIspParams", Context.MODE_PRIVATE);
        }

        if (serialInstance == null) 
        {
            serialInstance = new touptek_serial_rk();
            serialInstance.setDeviceStateCallback(new touptek_serial_rk.DeviceStateCallback() 
            {
                @Override
                public void onDeviceStateChanged(boolean connected) 
                {
                    // 直接调用串口状态监听器，不再设置标志位
                    if (serialStateListener != null) 
                    {
                        serialStateListener.onSerialStateChanged(connected);
                    }
                }
            });
            serialInstance.startMonitor();
        }
    }

    /**
     * 获取串口实例
     * <p>
     * 返回用于串口通信的实例对象。
     * </p>
     *
     * @return touptek_serial_rk 实例
     */
    public static touptek_serial_rk getSerialInstance() 
    {
        return serialInstance;
    }

    /**
     * 检查串口是否已连接
     * <p>
     * 用于判断当前串口的连接状态。
     * </p>
     *
     * @return 串口连接状态，true 表示已连接，false 表示未连接
     */
    public static boolean isSerialConnected() 
    {
        return serialInstance != null && serialInstance.isSerialConnected();
    }

    /**
     * 设置串口状态变化监听器。
     * <p>
     * 当串口连接状态变化时，会触发此监听器的回调方法。
     * </p>
     *
     * @param listener 串口状态变化监听器实例。
     */
    public static void setOnSerialStateChangedListener(OnSerialStateChangedListener listener) 
    {
        serialStateListener = listener;
    }

    /**
     * 停止串口监控和相关资源。
     * <p>
     * 在应用退出或不再需要串口通信时调用。
     * 释放所有相关资源。
     * </p>
     */
    public static void release() 
    {
        if (serialInstance != null) 
        {
            serialInstance.stopMonitor();
            serialInstance.close();
            serialInstance = null;
        }
    }

    /**
     * 设置数据变化监听器。
     * <p>
     * 当参数值发生变化时，会触发此监听器的回调方法。
     * </p>
     *
     * @param listener 数据变化监听器实例。
     */
    public static void setOnDataChangedListener(OnDataChangedListener listener) 
    {
        dataChangedListener = listener;
    }

    /**
     * 更新参数值 - 完整流程
     * <p>
     * 此方法将执行三个操作：保存到本地、发送到设备
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要更新的参数
     * @param value 要更新的 int 类型数据
     */
    public static void updateParam(TouptekIspParam param, int value) 
    {
        // 保存到本地
        saveToLocal(param, value);
        
        // 发送到设备
        sendToDevice(param, value);

        System.out.println("update ISP Param: " + param.name() + ": " + value);
    }
    
    /**
     * 更新参数值 - 完整流程，带控制字节
     * <p>
     * 此方法将执行三个操作：保存到本地、发送到设备、触发回调
     * 可以指定是读取操作还是写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要更新的参数
     * @param value 要更新的 int 类型数据
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    public static void updateParam(TouptekIspParam param, int value, int ctrl) 
    {
        // 保存到本地
        saveToLocal(param, value);
        
        // 发送到设备
        sendToDevice(param, value, ctrl);

        System.out.println("update ISP Param: " + param.name() + ": " + value + " ctrl: " + (ctrl == 0x01 ? "write" : "read"));
    }
    

    /**
     * 仅保存参数到本地，不发送到设备
     * <p>
     * 使用SharedPreferences存储参数值，以便在应用重启后恢复。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数
     * @param value 要保存的 int 类型数据
     */
    public static void saveToLocal(TouptekIspParam param, int value) 
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(param.name(), value);
        editor.apply();
    }
    
    /**
     * 仅保存长整型参数到本地，不发送到设备
     * <p>
     * 使用SharedPreferences存储长整型参数值，主要用于版本号等。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要保存的参数
     * @param value 要保存的 long 类型数据
     */
    public static void saveToLocal(TouptekIspParam param, long value) 
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(param.name(), value);
        editor.apply();
    }
    
    /**
     * 仅发送参数到设备，不保存到本地
     * <p>
     * 通过串口发送命令到设备，默认使用写入或读取控制字节。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     */
    public static void sendToDevice(TouptekIspParam param, int value) 
    {
        if (param == TOUPTEK_PARAM_VERSION) 
        {
            touptek_serial_rk.sendCommandToSerial(0x00, param.getValue(), value);
        } 
        else 
        {
            touptek_serial_rk.sendCommandToSerial(0x01, param.getValue(), value);
        }
    }
    
    /**
     * 仅发送参数到设备，不保存到本地，带控制字节
     * <p>
     * 通过串口发送命令到设备，可以指定控制字节。
     * 0x00表示读取操作，0x01表示写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    public static void sendToDevice(TouptekIspParam param, int value, int ctrl) 
    {
        touptek_serial_rk.sendCommandToSerial(ctrl, param.getValue(), value);
    }

    /**
     * 接收到设备数据后处理更新
     * <p>
     * 此方法用于从串口接收到数据后，更新本地参数值并触发回调。
     * 由{@link touptek_serial_rk#onSerialDataReceived(int[])}调用。
     * </p>
     * 
     * @param param 接收到的参数类型
     * @param value 接收到的参数值
     * @param isLongValue 是否为长整型值
     */
    public static void handleReceivedData(TouptekIspParam param, long value, boolean isLongValue) 
    {
        if (isLongValue) 
        {
            saveToLocal(param, value);
            if (dataChangedListener != null) 
            {
                dataChangedListener.onLongDataChanged(param, value);
            }
        } 
        else 
        {
            saveToLocal(param, (int)value);
            if (dataChangedListener != null) 
            {
                dataChangedListener.onDataChanged(param, (int)value);
            }
        }
    }

    /**
     * 获取存储的数据。
     * <p>
     * 从SharedPreferences中读取保存的参数值。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 int 类型数据，如果未找到则返回 -1。
     */
    public static int getData(TouptekIspParam param) 
    {
        return sharedPreferences.getInt(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取存储的 long 类型数据。
     * <p>
     * 从SharedPreferences中读取保存的长整型参数值，主要用于版本号等。
     * </p>
     *
     * @param param 枚举类型 {@link TouptekIspParam}，表示需要获取的参数。
     * @return 存储的 long 类型数据，如果未找到则返回 -1。
     */
    public static long getLongData(TouptekIspParam param) 
    {
        return sharedPreferences.getLong(param.name(), -1); // -1 为默认值，如果没有找到
    }

    /**
     * 获取所有存储的数据。
     * <p>
     * 返回SharedPreferences中所有保存的参数值。
     * </p>
     * 
     * @return 包含所有存储键值对的 {@link Map}，键为参数名称，值为对应的存储数据。
     */
    public static Map<String, ?> getAllData() 
    {
        return sharedPreferences.getAll();
    }

    /**
     * 根据索引获取对应的枚举项。
     * <p>
     * 用于在UI中通过索引选择参数。
     * </p>
     *
     * @param index 索引值，从 0 开始。
     * @return 对应的 {@link TouptekIspParam} 枚举项，如果索引无效则返回 null。
     */
    public static TouptekIspParam getParamByIndex(int index) 
    {
        TouptekIspParam[] params = TouptekIspParam.values();
        if (index >= 0 && index < params.length) 
        {
            return params[index];
        }
        return null;  // 索引无效
    }

    /**
     * 定义数据变化监听器接口。
     * <p>
     * 用于监听数据变化事件。
     * 应用可以实现此接口来接收参数值变化的通知。
     * </p>
     */
    public interface OnDataChangedListener 
    {
        /**
         * 当 int 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 int 类型值
         */
        void onDataChanged(TouptekIspParam param, int newValue);

        /**
         * 当 long 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TouptekIspParam}
         * @param newValue 新的 long 类型值
         */
        void onLongDataChanged(TouptekIspParam param, long newValue);
    }

    /**
     * 定义串口状态变化监听器接口。
     * <p>
     * 用于监听串口连接状态变化事件。
     * 应用可以实现此接口来接收串口连接状态的变化通知。
     * </p>
     */
    public interface OnSerialStateChangedListener 
    {
        /**
         * 当串口状态发生变化时调用。
         *
         * @param connected 串口是否已连接，true表示已连接，false表示已断开
         */
        void onSerialStateChanged(boolean connected);
    }
}
