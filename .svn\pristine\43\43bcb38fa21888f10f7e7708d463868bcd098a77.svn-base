/* ***************************************************************
//  gep_transceiver_tcp   version:  1.0  date: 27/27/2009
//  -------------------------------------------------------------
//  Yongming <PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __zdp_transceiver_tcp_h__
#define __zdp_transceiver_tcp_h__

#include <utiny/usys_transceiver_tcp.h>

namespace gep
{
    class gep_session_context;
    class UBEDA_API gep_transceiver_tcp : public usys_transceiver_tcp
    {
    public:
        gep_transceiver_tcp(const usys_smartptr<usys_reactor>& reactor, unsigned send_retry_deque_size, unsigned recv_buffer_size);
        virtual ~gep_transceiver_tcp();
        usys_smartptr<gep_session_context> gep_session_ptr();
        virtual int get_message_len(const usys_smartptr<usys_data_block>& data_ptr, usys_smartptr_mtbase_ptr& /*header_ptr*/);
        virtual int handle_accept();
        virtual int handle_connect();
        virtual int handle_write();
        virtual void handle_close();
        virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& data_ptr, const usys_smartptr_mtbase_ptr& /*header_ptr*/);
    };
}

#endif
