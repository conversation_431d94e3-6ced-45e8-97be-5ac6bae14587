[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: arm64-v8a", "file_": "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\.cxx\\Debug\\54t2sa2v\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\hhx\\svn\\AndroidStudio\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]