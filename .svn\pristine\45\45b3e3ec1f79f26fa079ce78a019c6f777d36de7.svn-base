package com.android.rockchip.camera2.integrated.browser;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.touptek.ui.TpVideoPlayerView;
import com.android.rockchip.mediacodecnew.R;

/**
 * TpVideoPlayerActivity - 使用TpVideoPlayerView的视频播放Activity
 * <p>
 * 此Activity使用TpVideoPlayerView组件进行视频播放，
 * TpVideoPlayerView内置了完整的播放控制界面，无需额外的控制组件。
 * </p>
 */
public class TpVideoPlayerActivity extends AppCompatActivity {
    private static final String TAG = "TpVideoPlayerActivity";
    private static final String EXTRA_VIDEO_PATH = "video_path";

    /* UI组件 */
    private TpVideoPlayerView mVideoPlayerView;

    /* 视频路径 */
    private String mVideoPath;

    /**
     * 启动TpVideoPlayerActivity的静态方法
     *
     * @param context   上下文
     * @param videoPath 要播放的视频文件路径
     */
    public static void start(Context context, String videoPath) {
        Intent intent = new Intent(context, TpVideoPlayerActivity.class);
        intent.putExtra(EXTRA_VIDEO_PATH, videoPath);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        /* 设置全屏显示 */
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.activity_tp_video_player_new);

        /* 获取视频路径 */
        mVideoPath = getIntent().getStringExtra(EXTRA_VIDEO_PATH);

        if (mVideoPath == null || mVideoPath.isEmpty()) {
            Toast.makeText(this, "视频路径无效", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        /* 初始化UI */
        initViews();

        /* 设置视频路径并开始播放 */
        setupVideoPlayer();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        mVideoPlayerView = findViewById(R.id.tp_video_player_view);

    }

    /**
     * 设置视频播放器
     */
    private void setupVideoPlayer() {
        if (mVideoPlayerView != null && mVideoPath != null) {
            /* 设置视频路径并自动开始播放 */
            mVideoPlayerView.setVideoPath(mVideoPath);
            mVideoPlayerView.play();
        }
    }



    @Override
    protected void onPause() {
        super.onPause();
        /* 暂停时自动暂停视频播放 */
        if (mVideoPlayerView != null && mVideoPlayerView.isPlaying()) {
            mVideoPlayerView.pause();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        /* 销毁时停止视频播放 */
        if (mVideoPlayerView != null) {
            mVideoPlayerView.stop();
        }
    }
}
