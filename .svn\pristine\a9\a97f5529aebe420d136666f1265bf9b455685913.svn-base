package com.android.rockchip.camera2.util;

import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.RectF;
import android.view.TextureView;

/**
 * TransformUtils 类提供了用于配置 TextureView 变换矩阵、计算数字变焦裁剪区域、限制缩放因子以及处理滚动事件的工具方法。
 */
public class TransformUtils
{
    /* 最小缩放因子 */
    private static final float MIN_ZOOM = 1.0f;
    /* 最大缩放因子 */
    private static final float MAX_ZOOM = 10.0f;

    /*
     * 配置 TextureView 的变换矩阵，用于旋转、缩放和平移。
     *
     * @param textureView TextureView 实例
     * @param viewWidth   TextureView 的宽度
     * @param viewHeight  TextureView 的高度
     * @param previewWidth 预览宽度
     * @param previewHeight 预览高度
     * @param scaleFactor 缩放因子
     * @param focalX      焦点 X 坐标
     * @param focalY      焦点 Y 坐标
     * @param rotation    旋转角度（顺时针方向，单位：度）
     */
    public static void configureTransform(TextureView textureView, int viewWidth, int viewHeight,
                                          int previewWidth, int previewHeight, float scaleFactor,
                                          float focalX, float focalY, int rotation)
    {
        /* 如果 TextureView 为空，直接返回 */
        if (textureView == null)
            return;

        /* 创建一个新的 Matrix 对象 */
        Matrix matrix = new Matrix();

        /* 定义视图矩形和缓冲区矩形 */
        RectF viewRect = new RectF(0, 0, viewWidth, viewHeight);
        /* 注意：宽高需要交换以适配旋转 */
        RectF bufferRect = new RectF(0, 0, previewHeight, previewWidth); 
        float centerX = viewRect.centerX();
        float centerY = viewRect.centerY();

        /* 将缓冲区矩形的中心点与视图矩形的中心点对齐 */
        bufferRect.offset(centerX - bufferRect.centerX(), centerY - bufferRect.centerY());

        /* 设置缩放比例，确保视频内容适配视图 */
        float scale = Math.max(
                (float) viewHeight / previewHeight,
                (float) viewWidth / previewWidth);
        matrix.setRectToRect(viewRect, bufferRect, Matrix.ScaleToFit.FILL);

        /* 应用缩放 */
        matrix.postScale(scale * scaleFactor, scale * scaleFactor, centerX, centerY);

        /* 计算平移量 */
        float translateX = focalX - centerX;
        float translateY = focalY - centerY;
        /* 应用平移 */
        matrix.postTranslate(translateX, translateY);

        /* 设置旋转角度（顺时针旋转） */
        matrix.postRotate(rotation, centerX, centerY);

        /* 应用变换矩阵到 TextureView */
        textureView.setTransform(matrix);
    }

    /*
     * 计算数字变焦裁剪区域。
     *
     * @param sensorArraySize 传感器有效区域
     * @param scaleFactor     缩放因子
     * @param focalX          焦点 X 坐标
     * @param focalY          焦点 Y 坐标
     * @return 裁剪区域
     */
    public static Rect calculateZoomRect(Rect sensorArraySize, float scaleFactor, float focalX, float focalY)
    {
        /* 获取传感器的宽度和高度 */
        int sensorW = sensorArraySize.width();
        int sensorH = sensorArraySize.height();
        /* 计算变焦后的宽度和高度 */
        int zoomW = (int) (sensorW / scaleFactor);
        int zoomH = (int) (sensorH / scaleFactor);

        /* 计算裁剪区域的左上角坐标 */
        int left = (int) (focalX - zoomW / 2);
        int top = (int) (focalY - zoomH / 2);

        /* 限制裁剪区域在传感器范围内 */
        if (left < sensorArraySize.left)
            left = sensorArraySize.left;
        if (top < sensorArraySize.top)
            top = sensorArraySize.top;
        if (left + zoomW > sensorArraySize.right)
            left = sensorArraySize.right - zoomW;
        if (top + zoomH > sensorArraySize.bottom)
            top = sensorArraySize.bottom - zoomH;

        /* 返回裁剪区域 */
        return new Rect(left, top, left + zoomW, top + zoomH);
    }

    /*
     * 限制缩放因子在最小和最大范围内。
     *
     * @param scaleFactor 当前缩放因子
     * @return 限制后的缩放因子
     */
    public static float clampScaleFactor(float scaleFactor)
    {
        /* 返回限制在最小和最大范围内的缩放因子 */
        return Math.max(MIN_ZOOM, Math.min(scaleFactor, MAX_ZOOM));
    }

    /**
     * 处理滚动事件，根据旋转角度调整焦点位置。
     *
     * @param focalPoint 焦点坐标数组，[0] 为 X 坐标，[1] 为 Y 坐标
     * @param distanceX  滚动的 X 方向距离
     * @param distanceY  滚动的 Y 方向距离
     * @param rotation   旋转角度（顺时针方向，单位：度）
     */
    public static void handleScroll(float[] focalPoint, float distanceX, float distanceY, int rotation)
    {
        /* 根据旋转角度调整焦点位置 */
        switch (rotation)
        {
            case 90:
                /* 旋转 90 度时，x 和 y 轴交换，x 方向反转 */
                focalPoint[0] -= distanceY;
                focalPoint[1] += distanceX;
                break;
            case 180:
                /* 旋转 180 度时，x 和 y 方向都反转 */
                focalPoint[0] += distanceX;
                focalPoint[1] += distanceY;
                break;
            case 270:
                /* 旋转 270 度时，x 和 y 轴交换，y 方向反转 */
                focalPoint[0] += distanceY;
                focalPoint[1] -= distanceX;
                break;
            default:
                /* 默认 0 度，无需调整 */
                focalPoint[0] += distanceX;
                focalPoint[1] += distanceY;
                break;
        }
    }
}