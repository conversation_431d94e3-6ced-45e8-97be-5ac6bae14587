package com.android.rockchip.camera2;

import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraDevice;
import android.widget.Button;
import android.widget.Toast;
import android.content.Intent;
import android.os.Handler;
import android.view.ScaleGestureDetector;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.android.rockchip.camera2.util.TouptekIspParam;
import com.android.rockchip.mediacodecnew.R;
import com.android.rockchip.camera2.video.VideoEncoder;
import com.android.rockchip.camera2.video.CameraManagerHelper;
import com.android.rockchip.camera2.util.FileStorageUtils;
import com.android.rockchip.camera2.util.HdmiService;
import com.android.rockchip.camera2.util.TransformUtils;
import com.android.rockchip.camera2.video.CaptureImageHelper;
import com.android.rockchip.camera2.video.TvPreviewHelper;

public class VideoEncoderActivity extends AppCompatActivity
{
    /* 日志标签 */
    private static final String TAG = "VideoEncoderActivity";

    /* 摄像头权限请求代码 */
    private static final int CAMERA_PERMISSION_REQUEST_CODE = 100;

    /* 视频编码器实例 */
    private VideoEncoder videoEncoder;

    /* 摄像头管理助手实例 */
    private CameraManagerHelper cameraManagerHelper;

    /* 录制按钮 */
    private Button recordButton;

    /* 抓图按钮 */
    private Button captureButton;

    /* 切换模式按钮 */
    private Button switchModeButton;

    /* 是否正在录制 */
    private boolean isRecording = false;
    
    /* 是否处于TV模式 */
    private boolean isTvMode = false;

    /* 预览的 TextureView */
    private TextureView textureView;
    
    /* TV预览助手实例 */
    private TvPreviewHelper tvPreviewHelper;

    /* HDMI 服务实例 */
    private HdmiService hdmiService;

    /* 抓图助手实例 */
    private CaptureImageHelper captureImageHelper;

    /* 缩放手势检测器 */
    private ScaleGestureDetector scaleGestureDetector;

    /* 平移手势检测器 */
    private GestureDetector gestureDetector;

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.encoder); /* 加载布局文件 */

        TouptekIspParam.init(this);
        TouptekIspParam.updateParam(TouptekIspParam.TOUPTEK_PARAM_VERSION,0);

        /* 初始化 HDMI 服务 */
        initHdmiService();

        /* 初始化视图组件 */
        initViews();

        /* 检查摄像头权限 */
        checkCameraPermission();

        /*初始化预览录像等服务 */
        initialize();

        /* 初始化缩放和平移手势检测器 */
        initScaleGestureDetector();
        initPanGestureDetector();

        /* 设置触摸监听器 */
        textureView.setOnTouchListener((v, event) -> {
            boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);
            boolean panHandled = gestureDetector.onTouchEvent(event);
            return scaleHandled || panHandled; // 确保触摸事件被正确处理
        });
    }

    /**
     * 初始化 HDMI 服务
     */
    private void initHdmiService()
    {
        hdmiService = HdmiService.getInstance();
        hdmiService.setHdmiListener(isConnected -> runOnUiThread(() -> handleHdmiStatusChange(isConnected)));
        hdmiService.init();
    }

    /**
     * 初始化视图组件
     */
    private void initViews()
    {
        textureView = findViewById(R.id.texture_view);
        recordButton = findViewById(R.id.btn_record);
        captureButton = findViewById(R.id.btn_capture);

        if (textureView == null || recordButton == null)
        {
            Log.e(TAG, "Missing required views. Check your layout file.");
            return;
        }

        /* 设置录制按钮点击事件 */
        recordButton.setOnClickListener(v -> toggleRecording());

        /* 设置抓图按钮点击事件 */
        if (captureButton != null)
        {
            captureButton.setOnClickListener(v -> captureImage());
        }

        /* 初始化浏览媒体按钮 */
        Button btnBrowseMedia = findViewById(R.id.btn_browse_media);
        if (btnBrowseMedia != null)
        {
            btnBrowseMedia.setOnClickListener(v ->
            {
                Intent intent = new Intent(VideoEncoderActivity.this, MediaBrowserActivity.class);
                startActivity(intent);
            });
        }
        
        /* 初始化切换模式按钮 */
        switchModeButton = findViewById(R.id.btn_switch_mode);
        if (switchModeButton != null)
        {
            switchModeButton.setOnClickListener(v -> togglePreviewMode());
        }
        
        /* 初始化TV预览助手 */
        ViewGroup previewContainer = findViewById(R.id.preview_container);
        if (previewContainer != null) {
            tvPreviewHelper = new TvPreviewHelper(this, previewContainer);
        }
    }

    /**
     * 切换预览模式，在普通模式和TV模式之间切换
     */
    private void togglePreviewMode()
    {
        isTvMode = !isTvMode;
        
        if (isTvMode) {
            // 切换到TV模式
            if (isRecording) {
                stopRecording(); // 如果在录制，先停止录制
            }
            releaseResources(); // 释放普通预览模式的资源
            textureView.setVisibility(View.INVISIBLE); // 隐藏常规预览视图
            tvPreviewHelper.startPreview(); // 启动TV预览
            switchModeButton.setText("切换到camera模式");
            // TV模式下禁用录制和抓图按钮
            recordButton.setEnabled(false);
            captureButton.setEnabled(false);
        } else {
            // 切换回常规模式
            tvPreviewHelper.stopPreview(); // 停止TV预览
            textureView.setVisibility(View.VISIBLE); // 显示常规预览视图
            if (textureView.isAvailable()) {
                SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
                if (surfaceTexture != null) {
                    initVideoEncoder(new Surface(surfaceTexture)); // 重新初始化编码器
                }
            }
            switchModeButton.setText("切换到TV模式");
            // 启用录制和抓图按钮
            recordButton.setEnabled(true);
            captureButton.setEnabled(true);
        }
    }

    /**
     * 检查摄像头权限
     */
    private void checkCameraPermission()
    {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED)
        {
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.CAMERA}, CAMERA_PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults)
    {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == CAMERA_PERMISSION_REQUEST_CODE && grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED)
        {
            initialize();
        }
        else
        {
            Log.e(TAG, "Camera permission denied");
        }
    }

    /**
     * 初始化应用
     */
    private void initialize()
    {
        /* 初始化摄像头管理助手 */
        cameraManagerHelper = new CameraManagerHelper(this);

        /* 初始化抓图助手 */
        captureImageHelper = new CaptureImageHelper(new Size(3840, 2160));
        captureImageHelper.setCaptureCallback(new CaptureImageHelper.CaptureCallback()
        {
            @Override
            public void onImageSaved(String filePath)
            {
//                runOnUiThread(() -> Toast.makeText(VideoEncoderActivity.this, "Image saved: " + filePath, Toast.LENGTH_LONG).show());
            }

            @Override
            public void onError(String errorMessage)
            {
//                runOnUiThread(() -> Toast.makeText(VideoEncoderActivity.this, errorMessage, Toast.LENGTH_LONG).show());
            }
        });

        /* 设置 TextureView 的 SurfaceTextureListener */
        textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener()
        {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height)
            {
                initVideoEncoder(new Surface(surface));
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) { }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface)
            {
                releaseResources();
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) { }
        });
    }

    /**
     * 初始化视频编码器
     *
     * @param textureSurface 预览的 Surface
     */
    private void initVideoEncoder(Surface textureSurface)
    {
        videoEncoder = new VideoEncoder(new VideoEncoder.Callback()
        {
            /* 当界面可用时会触发这个回调 */
            @Override
            public void onSurfaceAvailable(Surface encoderSurface, Surface decoderSurface)
            {
                cameraManagerHelper.openCamera(new CameraDevice.StateCallback()
                {
                    @Override
                    public void onOpened(CameraDevice camera)
                    {
                        /*打开预览，传入编码的suface */
                        cameraManagerHelper.configCameraOutputs(camera, encoderSurface, captureImageHelper.getImageReader().getSurface());
                    }

                    @Override
                    public void onDisconnected(CameraDevice camera)
                    {
                        camera.close();
                    }

                    @Override
                    public void onError(CameraDevice camera, int error)
                    {
                        camera.close();
                    }
                });
            }

            @Override
            public void onStorageFull()
            {
                runOnUiThread(() ->
                {
                    Toast.makeText(VideoEncoderActivity.this, "存储空间不足，录制已停止", Toast.LENGTH_LONG).show();
                    stopRecording();
                });
            }

            @Override
            public void onError(String errorType, Exception e)
            {
                runOnUiThread(() ->
                {
                    Toast.makeText(VideoEncoderActivity.this, "录制错误: " + errorType, Toast.LENGTH_LONG).show();
                    stopRecording();
                });
            }

            @Override
            public void onFileSizeLimitReached()
            {
                runOnUiThread(() ->
                {
                    Toast.makeText(VideoEncoderActivity.this, "文件大小达到限制，录制已停止", Toast.LENGTH_LONG).show();
                    stopRecording();
                });
            }

            @Override
            public void onSaveComplete(String filePath) {
                runOnUiThread(() ->
                {
                    Toast.makeText(VideoEncoderActivity.this, "录制完成: " + filePath, Toast.LENGTH_LONG).show();
                });
            }
        });

        videoEncoder.initialize(new Size(3840, 2160), textureSurface);
    }

    /**
     * 切换录制状态
     */
    private void toggleRecording()
    {
        if (isRecording)
        {
            stopRecording();
        }
        else
        {
            startRecording();
        }
    }

    /**
     * 开始录制
     */
    private void startRecording()
    {
        String newOutputPath = FileStorageUtils.createVideoPath(this);
        videoEncoder.startRecording(newOutputPath);
        recordButton.setText("停止录制");
        isRecording = true;
    }

    /**
     * 停止录制
     */
    private void stopRecording()
    {
        videoEncoder.stopRecording();
        recordButton.setText("开始录制");
        isRecording = false;
    }

    /**
     * 抓图
     */
    private void captureImage()
    {
        Size imageSize = new Size(3840, 2160); // Example size, can be dynamic
        String outputPath = FileStorageUtils.createImagePath(this);
        captureImageHelper.requestCapture(imageSize, outputPath);
    }

    /**
     * 释放资源
     */
    private void releaseResources()
    {
        cameraManagerHelper.releaseCamera();
        if (videoEncoder != null)
        {
            videoEncoder.release();
        }
    }

    /**
     * 处理 HDMI 状态变化
     *
     * @param isConnected HDMI 状态
     */
    private void handleHdmiStatusChange(boolean isConnected)
    {
        if (isConnected)
        {
            Log.d(TAG, "HDMI 插入");

            // 如果当前处于TV模式，先切换回常规模式
            if (isTvMode) {
                isTvMode = false;
                tvPreviewHelper.stopPreview();
                textureView.setVisibility(View.VISIBLE);
                switchModeButton.setText("切换到TV模式");
                recordButton.setEnabled(true);
                captureButton.setEnabled(true);
            }

            releaseResources();

            if (textureView.isAvailable())
            {
                SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
                if (surfaceTexture != null)
                {
                    initVideoEncoder(new Surface(surfaceTexture));
                }
            }
        }
        else
        {
            Log.d(TAG, "HDMI 拔出");
            if (isRecording)
            {
                stopRecording();
            }
            releaseResources();
        }
    }

    /**
     * 初始化缩放手势检测器
     */
    private void initScaleGestureDetector() {
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                // 调用 TransformUtils 的 applyZoom 方法
                TransformUtils.applyZoom(textureView, scaleFactor, focusX, focusY);
                return true;
            }
        });
    }

    /**
     * 初始化平移手势检测器
     */
    private void initPanGestureDetector() {
        gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                // 调用 TransformUtils 的 applyPan 方法
                TransformUtils.applyPan(textureView, -distanceX, -distanceY);
                return true;
            }
        });
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        if (hdmiService != null)
        {
            hdmiService.stop();
        }
        releaseResources();
        
        // 释放TV预览资源
        if (tvPreviewHelper != null) {
            tvPreviewHelper.release();
        }
    }
}