/* ***************************************************************
//  sip_session_context   version:  1.0  date: 23/23/2009
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2009 - All Rights Reserved
// ***************************************************************
// 
// **************************************************************/
#ifndef __sip_session_context_h__
#define __sip_session_context_h__

#include <utiny/utiny_config.h>
#include <utiny/usys_smartptr.h>
#include <utiny/usys_transceiver_tcp.h>
#include <utiny/usys_safemap.h>
#include <utiny/usys_data_block.h>
#include <utiny/usys_reactor.h>
#include <utiny/usys_atomic.h>
#include <utiny/usys_mutex.h>
#include <utiny/sip_header.h>
#include <utiny/usys_data_block.h>

class sip_packet_context;
class sip_response_context;
class sip_request_context;

class UBEDA_API sip_session_context : virtual public usys_smartptr_mtbase
{
protected:
    usys_safemap<int, usys_smartptr_mtbase_ptr> response_ctx_map_;
	bool try_fetch_response_by_method_;
public:
    sip_session_context();
    virtual ~sip_session_context();

	virtual int proc_packet(const usys_smartptr<sip_packet_context>& packetptr, const usys_smartptr<usys_data_block>& dataptr) = 0;

    virtual usys_smartptr<usys_transceiver_tcp> handle_accept(SOCKET fd);
    virtual int handle_connect(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
    virtual int handle_write(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
    virtual void handle_close(const usys_smartptr<usys_transceiver>& transceiver_ptr) { }
	virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& req_ptr, const usys_smartptr_mtbase_ptr& header_ptr, const usys_smartptr<usys_transceiver>& transceiver_ptr);
public:
	int proc_request_exception(const usys_smartptr<sip_response_context>& res_ptr);

	int request(usys_smartptr<usys_data_block>& mb, const usys_smartptr<sip_response_context>& response_ptr, int timeout, bool oneway);
	int response(usys_smartptr<usys_data_block>& mb, const usys_smartptr<sip_request_context>& request_ctx);
public:
	bool try_fetch_response_by_method()const { return try_fetch_response_by_method_; }
	void try_fetch_response_by_method(bool value) { try_fetch_response_by_method_ = value; }
private:
    int fetch_response(int sequence_id, usys_smartptr<sip_response_context>& res_ptr);
	int fetch_response(const std::string& method, usys_smartptr<sip_response_context>& res_ptr);

    int remove_response(int sequence_id);
};

typedef usys_smartptr<sip_session_context> sip_session_context_ptr;

#endif // __sip_session_context_h__
