<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Apr 10 13:26:06 CST 2025 -->
<title>com.android.rockchip.camera2</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-10">
<meta name="description" content="declaration: package: com.android.rockchip.camera2">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.android.rockchip.camera2" class="title">程序包 com.android.rockchip.camera2</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.android.rockchip.camera2</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="util/package-summary.html">com.android.rockchip.camera2.util</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="video/package-summary.html">com.android.rockchip.camera2.video</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ImageViewerActivity.html" title="com.android.rockchip.camera2中的类">ImageViewerActivity</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">ImageViewerActivity 类用于显示选定的图片，并支持缩放和平移。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MediaAdapter.html" title="com.android.rockchip.camera2中的类">MediaAdapter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">MediaAdapter 类用于在 RecyclerView 中显示媒体文件的缩略图和名称。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="MediaAdapter.OnMediaClickListener.html" title="com.android.rockchip.camera2中的接口">MediaAdapter.OnMediaClickListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">定义媒体点击监听器接口。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MediaBrowserActivity.html" title="com.android.rockchip.camera2中的类">MediaBrowserActivity</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">MediaBrowserActivity 类用于显示媒体文件的网格视图，
 并允许用户点击查看视频或图片。</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="VideoDecoderActivity.html" title="com.android.rockchip.camera2中的类">VideoDecoderActivity</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">VideoDecoderActivity 类负责视频解码和播放控制。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="VideoEncoderActivity.html" title="com.android.rockchip.camera2中的类">VideoEncoderActivity</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
