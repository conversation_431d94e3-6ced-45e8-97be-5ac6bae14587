<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu May 15 15:29:48 CST 2025 -->
<title>RTSPStreamer</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-05-15">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.rtsp.service, class: RTSPStreamer">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.rtsp.service</a></div>
<h1 title="类 RTSPStreamer" class="title">类 RTSPStreamer</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.rtsp.service.RTSPStreamer</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="../../video/VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">RTSPStreamer</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>
implements <a href="../../video/VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></span></div>
<div class="block">RTSP推流器
 
 此类仅供RTSPManager内部使用，不应直接被外部访问。</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager.StreamType,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.RTSPManager.StreamStateListener)" class="member-name-link">RTSPStreamer</a><wbr>(android.content.Context&nbsp;context,
 <a href="../config/RTSPConfig.html" title="com.android.rockchip.camera2.rtsp.config中的类">RTSPConfig</a>&nbsp;config,
 <a href="../RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a>&nbsp;streamType,
 <a href="ProjectionData.html" title="com.android.rockchip.camera2.rtsp.service中的类">ProjectionData</a>&nbsp;projectionData,
 <a href="../../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../RTSPManager.StreamStateListener.html" title="com.android.rockchip.camera2.rtsp中的接口">RTSPManager.StreamStateListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color">
<div class="block">创建RTSP推流器</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRtspUrl()" class="member-name-link">getRtspUrl</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取RTSP URL</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStreamType()" class="member-name-link">getStreamType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取推流类型</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isStreaming()" class="member-name-link">isStreaming</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">是否正在推流</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec.BufferInfo)" class="member-name-link">onVideoDataAvailable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;encodedData,
 android.media.MediaCodec.BufferInfo&nbsp;bufferInfo)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">接收编码后的H.264视频数据</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)" class="member-name-link">onVideoFormatChanged</a><wbr>(android.media.MediaFormat&nbsp;format,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;spsBuffer,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;ppsBuffer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">当视频格式改变时接收SPS和PPS</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startStream()" class="member-name-link">startStream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始推流</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopStream()" class="member-name-link">stopStream</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止推流</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#switchToCameraStream(com.android.rockchip.camera2.video.VideoEncoder)" class="member-name-link">switchToCameraStream</a><wbr>(<a href="../../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换到相机推流模式
 如果当前正在推流，会先停止当前推流</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#switchToScreenStream(com.android.rockchip.camera2.rtsp.service.ProjectionData)" class="member-name-link">switchToScreenStream</a><wbr>(<a href="ProjectionData.html" title="com.android.rockchip.camera2.rtsp.service中的类">ProjectionData</a>&nbsp;projectionData)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换到屏幕推流模式
 如果当前正在推流，会先停止当前推流</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager.StreamType,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.RTSPManager.StreamStateListener)">
<h3>RTSPStreamer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">RTSPStreamer</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 <a href="../config/RTSPConfig.html" title="com.android.rockchip.camera2.rtsp.config中的类">RTSPConfig</a>&nbsp;config,
 <a href="../RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a>&nbsp;streamType,
 <a href="ProjectionData.html" title="com.android.rockchip.camera2.rtsp.service中的类">ProjectionData</a>&nbsp;projectionData,
 <a href="../../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder,
 <a href="../RTSPManager.StreamStateListener.html" title="com.android.rockchip.camera2.rtsp中的接口">RTSPManager.StreamStateListener</a>&nbsp;listener)</span></div>
<div class="block">创建RTSP推流器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 上下文</dd>
<dd><code>config</code> - 推流配置</dd>
<dd><code>streamType</code> - 推流类型</dd>
<dd><code>projectionData</code> - 投影数据（屏幕推流时需要）</dd>
<dd><code>videoEncoder</code> - 视频编码器（相机推流时需要）</dd>
<dd><code>listener</code> - 状态监听器</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="startStream()">
<h3>startStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startStream</span>()</div>
<div class="block">开始推流</div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否成功启动推流</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopStream()">
<h3>stopStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopStream</span>()</div>
<div class="block">停止推流</div>
</section>
</li>
<li>
<section class="detail" id="getRtspUrl()">
<h3>getRtspUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getRtspUrl</span>()</div>
<div class="block">获取RTSP URL</div>
<dl class="notes">
<dt>返回:</dt>
<dd>RTSP URL</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isStreaming()">
<h3>isStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isStreaming</span>()</div>
<div class="block">是否正在推流</div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否正在推流</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStreamType()">
<h3>getStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a></span>&nbsp;<span class="element-name">getStreamType</span>()</div>
<div class="block">获取推流类型</div>
<dl class="notes">
<dt>返回:</dt>
<dd>推流类型</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="switchToScreenStream(com.android.rockchip.camera2.rtsp.service.ProjectionData)">
<h3>switchToScreenStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">switchToScreenStream</span><wbr><span class="parameters">(<a href="ProjectionData.html" title="com.android.rockchip.camera2.rtsp.service中的类">ProjectionData</a>&nbsp;projectionData)</span></div>
<div class="block">切换到屏幕推流模式
 如果当前正在推流，会先停止当前推流</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>projectionData</code> - 投影数据</dd>
<dt>返回:</dt>
<dd>是否成功切换</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="switchToCameraStream(com.android.rockchip.camera2.video.VideoEncoder)">
<h3>switchToCameraStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">switchToCameraStream</span><wbr><span class="parameters">(<a href="../../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder)</span></div>
<div class="block">切换到相机推流模式
 如果当前正在推流，会先停止当前推流</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoEncoder</code> - 视频编码器</dd>
<dt>返回:</dt>
<dd>是否成功切换</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)">
<h3>onVideoFormatChanged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onVideoFormatChanged</span><wbr><span class="parameters">(android.media.MediaFormat&nbsp;format,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;spsBuffer,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;ppsBuffer)</span></div>
<div class="block">当视频格式改变时接收SPS和PPS</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../../video/VideoEncoder.VideoDataOutputCallback.html#onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)">onVideoFormatChanged</a></code>&nbsp;在接口中&nbsp;<code><a href="../../video/VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></code></dd>
<dt>参数:</dt>
<dd><code>format</code> - 新的视频格式</dd>
<dd><code>spsBuffer</code> - SPS参数集缓冲区</dd>
<dd><code>ppsBuffer</code> - PPS参数集缓冲区</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec.BufferInfo)">
<h3>onVideoDataAvailable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onVideoDataAvailable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/nio/ByteBuffer.html" title="java.nio中的类或接口" class="external-link">ByteBuffer</a>&nbsp;encodedData,
 android.media.MediaCodec.BufferInfo&nbsp;bufferInfo)</span></div>
<div class="block">接收编码后的H.264视频数据</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../../video/VideoEncoder.VideoDataOutputCallback.html#onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec.BufferInfo)">onVideoDataAvailable</a></code>&nbsp;在接口中&nbsp;<code><a href="../../video/VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></code></dd>
<dt>参数:</dt>
<dd><code>encodedData</code> - 编码后的视频数据</dd>
<dd><code>bufferInfo</code> - 缓冲区信息，包含时间戳、标志等</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
