<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 预览容器，用于放置TextureView和TvView -->
    <FrameLayout
        android:id="@+id/preview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 右侧的 TextureView -->
        <TextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

    <!-- 功能按钮容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="bottom"
        android:padding="20dp">

        <!-- 录制控制按钮 -->
        <Button
            android:id="@+id/btn_record"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录制"
            android:layout_marginEnd="5dp"/>

        <!-- 抓图按钮 -->
        <Button
            android:id="@+id/btn_capture"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="抓图"
            android:layout_marginHorizontal="5dp"/>

        <!-- 浏览媒体按钮 -->
        <Button
            android:id="@+id/btn_browse_media"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="浏览媒体"
            android:layout_marginHorizontal="5dp"/>

        <!-- 模式切换按钮 -->
        <Button
            android:id="@+id/btn_switch_mode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="切换到TV模式"
            android:layout_marginHorizontal="5dp"/>

        <!-- 推流按钮 -->
        <Button
            android:id="@+id/btn_stream"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始推流"
            android:layout_marginHorizontal="5dp"/>

        <!-- 流源切换按钮 - 新增 -->
        <Button
            android:id="@+id/btn_switch_source"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="切换到屏幕流"
            android:layout_marginStart="5dp"/>
    </LinearLayout>

</FrameLayout>