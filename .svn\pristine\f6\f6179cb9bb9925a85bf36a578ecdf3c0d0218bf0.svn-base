[{"directory": "D:/RK3588/APP_TESTa/rkCamer2/app/.cxx/RelWithDebInfo/5o1w6v38/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dcamera2_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++11 -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\camera2.dir\\native-lib.cpp.o -c D:\\RK3588\\APP_TESTa\\rkCamer2\\app\\src\\main\\cpp\\native-lib.cpp", "file": "D:\\RK3588\\APP_TESTa\\rkCamer2\\app\\src\\main\\cpp\\native-lib.cpp"}]