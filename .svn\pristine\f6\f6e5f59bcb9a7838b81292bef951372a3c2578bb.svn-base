package com.android.rockchip.camera2.video;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.ImageView;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicLong;

/**
 * ImageDecoder类用于处理图片的解码和显示。
 * <p>
 * 此类提供了图片解码、缩放和加载到ImageView的功能。
 * 支持同步和异步加载，以及内存优化的采样加载方式。
 * </p>
 */
public class ImageDecoder {
    private static final String TAG = "ImageDecoder";

    /* 用于异步任务的线程池 */
    private static final ExecutorService executor = Executors.newFixedThreadPool(3);

    /* 主线程Handler，用于更新UI */
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    // ========== 请求管理相关变量 ==========

    /* 为每个 ImageView 维护当前请求 */
    private static final ConcurrentHashMap<ImageView, Future<?>> activeRequests = new ConcurrentHashMap<>();

    /* 请求ID生成器，用于调试 */
    private static final AtomicLong requestIdGenerator = new AtomicLong(0);

    // ========== 磁盘缓存相关变量 ==========

    /* 磁盘缓存目录 */
    private static File diskCacheDir;

    /* 文件信息缓存：避免重复的文件系统调用 */
    private static final ConcurrentHashMap<String, FileInfo> fileInfoCache = new ConcurrentHashMap<>();

    /* 缓存初始化标志 */
    private static boolean cacheInitialized = false;

    /* 最大缓存大小：500MB */
    private static final long MAX_CACHE_SIZE = 500 * 1024 * 1024;

    /* 缓存文件扩展名 */
    private static final String CACHE_FILE_EXTENSION = ".cache.jpg";

    /**
     * 文件信息类
     */
    private static class FileInfo {
        final long lastModified;
        final long fileSize;

        FileInfo(long lastModified, long fileSize) {
            this.lastModified = lastModified;
            this.fileSize = fileSize;
        }
    }

    // ========== 缓存管理方法 ==========

    /**
     * 初始化磁盘缓存系统
     */
    private static void initCache(Context context) {
        if (cacheInitialized) return;

        // 创建磁盘缓存目录
        diskCacheDir = new File(context.getCacheDir(), "image_cache");
        if (!diskCacheDir.exists()) {
            boolean created = diskCacheDir.mkdirs();
            Log.d(TAG, "Cache directory created: " + created + " at " + diskCacheDir.getAbsolutePath());
        }

        // 清理过期缓存
        cleanupCache();

        cacheInitialized = true;
        Log.d(TAG, "ImageDecoder disk cache initialized at: " + diskCacheDir.getAbsolutePath());
    }

    /**
     * 生成缓存文件名（使用MD5哈希避免文件名过长和特殊字符问题）
     */
    private static String generateCacheFileName(String imagePath, int width, int height) {
        String key = imagePath + "_" + width + "x" + height;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(key.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString() + CACHE_FILE_EXTENSION;
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5不可用，使用简单的哈希
            return String.valueOf(key.hashCode()) + CACHE_FILE_EXTENSION;
        }
    }

    /**
     * 从磁盘缓存获取图片
     */
    private static Bitmap getBitmapFromCache(Context context, String imagePath, int width, int height) {
        if (!cacheInitialized) initCache(context);

        String cacheFileName = generateCacheFileName(imagePath, width, height);
        File cacheFile = new File(diskCacheDir, cacheFileName);

        if (cacheFile.exists()) {
            // 检查原始文件是否被修改
            if (isFileModified(imagePath, cacheFile)) {
                // 文件已修改，删除缓存
                boolean deleted = cacheFile.delete();
                fileInfoCache.remove(imagePath);
                Log.d(TAG, "File modified, cache deleted: " + deleted + " for " + imagePath);
                return null;
            }

            // 更新缓存文件的访问时间（用于LRU）
            boolean touched = cacheFile.setLastModified(System.currentTimeMillis());

            // 从磁盘读取缓存的图片
            try {
                Bitmap cached = BitmapFactory.decodeFile(cacheFile.getAbsolutePath());
                if (cached != null) {
                    Log.d(TAG, "Disk cache hit: " + cacheFileName);
                    return cached;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error reading cache file: " + cacheFile.getAbsolutePath(), e);
                // 删除损坏的缓存文件
                cacheFile.delete();
            }
        }

        Log.d(TAG, "Disk cache miss: " + cacheFileName);
        return null;
    }

    /**
     * 将图片保存到磁盘缓存（异步）
     */
    private static void putBitmapToCache(Context context, String imagePath, int width, int height, Bitmap bitmap) {
        if (!cacheInitialized) initCache(context);

        if (bitmap == null || bitmap.isRecycled()) return;

        // 异步保存到磁盘缓存
        executor.execute(() -> {
            try {
                String cacheFileName = generateCacheFileName(imagePath, width, height);
                File cacheFile = new File(diskCacheDir, cacheFileName);

                // 保存为JPEG格式以节省空间
                try (FileOutputStream fos = new FileOutputStream(cacheFile)) {
                    boolean compressed = bitmap.compress(Bitmap.CompressFormat.JPEG, 85, fos);
                    if (compressed) {
                        // 缓存文件信息
                        updateFileInfo(imagePath);

                        Log.d(TAG, "Bitmap cached to disk: " + cacheFileName +
                              " (Original: " + (bitmap.getByteCount() / 1024) + "KB, " +
                              "Cached: " + (cacheFile.length() / 1024) + "KB)");

                        // 检查缓存大小并清理
                        cleanupCacheIfNeeded();
                    }
                }
            } catch (IOException e) {
                Log.e(TAG, "Error saving bitmap to cache", e);
            }
        });
    }

    /**
     * 检查原始文件是否比缓存文件更新
     */
    private static boolean isFileModified(String imagePath, File cacheFile) {
        File originalFile = new File(imagePath);
        if (!originalFile.exists()) return true;

        // 比较原始文件和缓存文件的修改时间
        return originalFile.lastModified() > cacheFile.lastModified();
    }

    /**
     * 更新文件信息缓存
     */
    private static void updateFileInfo(String imagePath) {
        File file = new File(imagePath);
        if (file.exists()) {
            fileInfoCache.put(imagePath, new FileInfo(file.lastModified(), file.length()));
        }
    }

    /**
     * 清理缓存（如果超过大小限制）
     */
    private static void cleanupCacheIfNeeded() {
        if (diskCacheDir == null || !diskCacheDir.exists()) return;

        long totalSize = calculateCacheSize();
        if (totalSize > MAX_CACHE_SIZE) {
            Log.d(TAG, "Cache size exceeded: " + (totalSize / 1024 / 1024) + "MB, cleaning up...");
            cleanupCache();
        }
    }

    /**
     * 清理缓存（LRU策略）
     */
    private static void cleanupCache() {
        if (diskCacheDir == null || !diskCacheDir.exists()) return;

        File[] cacheFiles = diskCacheDir.listFiles();
        if (cacheFiles == null || cacheFiles.length == 0) return;

        // 按最后修改时间排序（最久未使用的在前）
        Arrays.sort(cacheFiles, Comparator.comparingLong(File::lastModified));

        long totalSize = calculateCacheSize();
        long targetSize = MAX_CACHE_SIZE * 3 / 4; // 清理到75%

        for (File file : cacheFiles) {
            if (totalSize <= targetSize) break;

            long fileSize = file.length();
            if (file.delete()) {
                totalSize -= fileSize;
                Log.d(TAG, "Deleted cache file: " + file.getName() + " (" + (fileSize / 1024) + "KB)");
            }
        }

        Log.d(TAG, "Cache cleanup completed. Size: " + (totalSize / 1024 / 1024) + "MB");
    }

    /**
     * 计算缓存总大小
     */
    private static long calculateCacheSize() {
        if (diskCacheDir == null || !diskCacheDir.exists()) return 0;

        File[] cacheFiles = diskCacheDir.listFiles();
        if (cacheFiles == null) return 0;

        long totalSize = 0;
        for (File file : cacheFiles) {
            totalSize += file.length();
        }
        return totalSize;
    }

    /**
     * 清除所有缓存
     */
    public static void clearCache() {
        executor.execute(() -> {
            if (diskCacheDir != null && diskCacheDir.exists()) {
                File[] cacheFiles = diskCacheDir.listFiles();
                if (cacheFiles != null) {
                    int deletedCount = 0;
                    long deletedSize = 0;

                    for (File file : cacheFiles) {
                        deletedSize += file.length();
                        if (file.delete()) {
                            deletedCount++;
                        }
                    }

                    Log.d(TAG, "Cleared " + deletedCount + " cache files (" +
                          (deletedSize / 1024 / 1024) + "MB)");
                }
            }

            fileInfoCache.clear();
            Log.d(TAG, "All disk caches cleared");
        });
    }

    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        if (!cacheInitialized) return "Cache not initialized";

        long cacheSize = calculateCacheSize();
        int fileCount = 0;

        if (diskCacheDir != null && diskCacheDir.exists()) {
            File[] cacheFiles = diskCacheDir.listFiles();
            fileCount = cacheFiles != null ? cacheFiles.length : 0;
        }

        return String.format("Disk Cache: %d files, %.1f/%.1f MB, File Info Cache: %d entries",
                fileCount,
                cacheSize / 1024.0 / 1024.0,
                MAX_CACHE_SIZE / 1024.0 / 1024.0,
                fileInfoCache.size());
    }

    /**
     * 加载图片到ImageView。
     * <p>
     * 此方法会将指定路径的图片加载到ImageView中，并自动进行内存优化。
     * 注意：此方法在调用线程中同步执行，不应在主线程中调用。
     * </p>
     *
     * @param imagePath 图片文件路径
     * @param imageView 目标ImageView
     * @return 如果加载成功返回true，否则返回false
     */
    public static boolean loadImage(String imagePath, ImageView imageView) {
        if (imagePath == null || imageView == null) {
            Log.e(TAG, "Invalid parameters: path or imageView is null");
            return false;
        }

        try {
            // 检查文件是否存在
            File imageFile = new File(imagePath);
            if (!imageFile.exists() || !imageFile.isFile()) {
                Log.e(TAG, "Image file not found: " + imagePath);
                return false;
            }

            // 记录图片格式信息
            String fileExtension = getFileExtension(imagePath);
            Log.d(TAG, "Loading image: " + imagePath + ", format: " + fileExtension);

            // 获取ImageView的宽高，用于计算采样率
            int targetWidth = imageView.getWidth();
            int targetHeight = imageView.getHeight();

            // 如果ImageView尚未测量完成，使用屏幕宽度作为默认值
            if (targetWidth <= 0 || targetHeight <= 0) {
                targetWidth = imageView.getResources().getDisplayMetrics().widthPixels;
                targetHeight = imageView.getResources().getDisplayMetrics().heightPixels;
            }

            // 加载适当采样率的Bitmap
            Bitmap bitmap = decodeSampledBitmapFromFile(imagePath, targetWidth, targetHeight);
            if (bitmap != null) {
                // 主线程更新UI
                mainHandler.post(() -> {
                    imageView.setImageBitmap(bitmap);
                });
                Log.d(TAG, "Successfully loaded image: " + imagePath +
                        " (" + bitmap.getWidth() + "x" + bitmap.getHeight() + ")");
                return true;
            } else {
                Log.e(TAG, "Failed to decode bitmap: " + imagePath);
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading image: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步加载图片或视频缩略图到ImageView（带缓存支持）。
     * <p>
     * 此方法会在后台线程加载图片或视频缩略图，完成后在主线程更新ImageView。
     * 自动识别文件类型并使用合适的方法加载。
     * 内置智能缓存系统，提升重复访问性能。
     * </p>
     *
     * @param path 文件路径
     * @param imageView 目标ImageView
     */
    public static void loadImageAsync(String path, ImageView imageView) {
        if (path == null || imageView == null) {
            Log.e(TAG, "Invalid parameters: path or imageView is null");
            return;
        }

        // ========== 请求管理机制（类似 Glide）==========

        // 1. 取消旧请求
        Future<?> oldRequest = activeRequests.get(imageView);
        if (oldRequest != null && !oldRequest.isDone()) {
            boolean cancelled = oldRequest.cancel(true);
            Log.d(TAG, "Cancelled old request for ImageView: " + cancelled);
        }

        // 2. 生成新的请求ID用于调试
        final long requestId = requestIdGenerator.incrementAndGet();
        Log.d(TAG, "Starting new request #" + requestId + " for: " + path);

        // 3. 立即清空 ImageView 并设置标签
        imageView.setTag(path);
        imageView.setImageBitmap(null);

        // 4. 提交新请求并记录
        Future<?> newRequest = executor.submit(() -> {
            try {
                Log.d(TAG, "Executing request #" + requestId + " for: " + path);

                // 检查是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "Request #" + requestId + " was cancelled before file check");
                    return;
                }

                // 检查文件是否存在
                File file = new File(path);
                if (!file.exists()) {
                    Log.e(TAG, "File does not exist: " + path);
                    return;
                }

                // 获取ImageView的尺寸
                int[] dimensions = new int[2];
                mainHandler.post(() -> {
                    dimensions[0] = imageView.getWidth();
                    dimensions[1] = imageView.getHeight();

                    // 确保获取到有效尺寸
                    if (dimensions[0] <= 0 || dimensions[1] <= 0) {
                        dimensions[0] = imageView.getResources().getDisplayMetrics().widthPixels / 2;
                        dimensions[1] = imageView.getResources().getDisplayMetrics().heightPixels / 3;
                    }

                    synchronized (dimensions) {
                        dimensions.notify();
                    }
                });

                // 等待获取尺寸完成
                synchronized (dimensions) {
                    try {
                        dimensions.wait(500); // 最多等待500ms
                    } catch (InterruptedException e) {
                        Log.d(TAG, "Request #" + requestId + " was interrupted during dimension wait");
                        Thread.currentThread().interrupt();
                        return;
                    }
                }

                // 再次检查是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "Request #" + requestId + " was cancelled after dimension wait");
                    return;
                }

                // ========== 先检查磁盘缓存 ==========
                Context context = imageView.getContext();
                Bitmap cachedBitmap = getBitmapFromCache(context, path, dimensions[0], dimensions[1]);
                if (cachedBitmap != null) {
                    // 缓存命中，检查是否被取消
                    if (Thread.currentThread().isInterrupted()) {
                        Log.d(TAG, "Request #" + requestId + " was cancelled after cache hit");
                        return;
                    }

                    Log.d(TAG, "Cache hit for request #" + requestId);
                    mainHandler.post(() -> {
                        // 双重检查：确保 ImageView 标签仍然匹配
                        if (path.equals(imageView.getTag())) {
                            imageView.setImageBitmap(cachedBitmap);
                        } else {
                            Log.d(TAG, "ImageView tag changed, skipping cache display for request #" + requestId);
                        }
                    });
                    return;
                }

                // 确定文件类型
                String fileExtension = getFileExtension(path).toLowerCase();
                boolean isVideo = fileExtension.equals("mp4") || fileExtension.equals("3gp") ||
                                 fileExtension.equals("mkv") || fileExtension.equals("avi");

                // 使用正确的方法加载缩略图
                Bitmap bitmap = null;
                if (isVideo) {
                    bitmap = extractVideoThumbnail(path);
                } else {
                    bitmap = decodeSampledBitmapFromFile(path, dimensions[0], dimensions[1]);
                }

                // 检查是否在解码过程中被取消
                if (Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "Request #" + requestId + " was cancelled after decoding");
                    if (bitmap != null && !bitmap.isRecycled()) {
                        bitmap.recycle(); // 释放已解码的图片内存
                    }
                    return;
                }

                // 在主线程更新UI
                if (bitmap != null) {
                    // ========== 将解码结果保存到磁盘缓存 ==========
                    putBitmapToCache(context, path, dimensions[0], dimensions[1], bitmap);

                    final Bitmap finalBitmap = bitmap;
                    Log.d(TAG, "Request #" + requestId + " decoded successfully, updating UI");

                    mainHandler.post(() -> {
                        // 三重检查：确保请求仍然有效
                        if (path.equals(imageView.getTag())) {
                            imageView.setImageBitmap(finalBitmap);
                            Log.d(TAG, "Request #" + requestId + " displayed successfully");
                        } else {
                            Log.d(TAG, "Request #" + requestId + " obsolete, ImageView tag changed");
                            // 释放无用的图片内存
                            if (!finalBitmap.isRecycled()) {
                                finalBitmap.recycle();
                            }
                        }
                    });
                } else {
                    Log.e(TAG, "Request #" + requestId + " failed to decode: " + path);
                }
            } catch (Exception e) {
                if (e instanceof InterruptedException || Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "Request #" + requestId + " was interrupted: " + e.getMessage());
                } else {
                    Log.e(TAG, "Request #" + requestId + " failed with error: " + e.getMessage(), e);
                }
            } finally {
                // 清理请求记录
                activeRequests.remove(imageView);
                Log.d(TAG, "Request #" + requestId + " completed and cleaned up");
            }
        });

        // 记录新请求
        activeRequests.put(imageView, newRequest);
        Log.d(TAG, "Request #" + requestId + " submitted and recorded");
    }

    /**
     * 解码图片文件，使用内存优化的采样方式。
     * <p>
     * 此方法会根据目标尺寸计算合适的采样率，以减少内存占用。
     * </p>
     *
     * @param imagePath 图片文件路径
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 解码后的Bitmap，如果解码失败返回null
     */
    public static Bitmap decodeSampledBitmapFromFile(String imagePath, int reqWidth, int reqHeight) {
        try {
            // 先获取图片尺寸
            final BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(imagePath, options);
            
            // 检查图片格式
            String fileExtension = getFileExtension(imagePath);
            Log.d(TAG, "Decoding image: " + imagePath + 
                    ", format: " + fileExtension + 
                    ", size: " + options.outWidth + "x" + options.outHeight);

            // 计算合适的采样率
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
            Log.d(TAG, "Using sample size: " + options.inSampleSize);

            // 解码图片
            options.inJustDecodeBounds = false;
            return BitmapFactory.decodeFile(imagePath, options);
        } catch (Exception e) {
            Log.e(TAG, "Error decoding bitmap: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算适当的采样率。
     * <p>
     * 根据原始图片尺寸和目标尺寸计算合适的采样率，以减少内存占用。
     * </p>
     *
     * @param options 包含原始图片尺寸的选项
     * @param reqWidth 请求的宽度
     * @param reqHeight 请求的高度
     * @return 计算得到的采样率
     */
    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // 原始图片的高度和宽度
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            // 计算高度和宽度的比例
            final int heightRatio = Math.round((float) height / (float) reqHeight);
            final int widthRatio = Math.round((float) width / (float) reqWidth);

            // 选择较小的比例作为采样率，保证结果图片不小于请求的尺寸
            inSampleSize = Math.min(heightRatio, widthRatio);
            
            // 采样率必须是2的幂
            int power = 1;
            while (power * 2 <= inSampleSize) {
                power *= 2;
            }
            inSampleSize = power;
        }

        return inSampleSize;
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param filePath 文件路径
     * @return 文件扩展名，如果没有则返回空字符串
     */
    private static String getFileExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        
        int lastDot = filePath.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < filePath.length() - 1) {
            return filePath.substring(lastDot + 1).toLowerCase();
        }
        
        return "";
    }
    
    /**
     * 旋转图片到指定角度。
     *
     * @param bitmap 原始Bitmap
     * @param degrees 旋转角度
     * @return 旋转后的Bitmap
     */
    public static Bitmap rotateBitmap(Bitmap bitmap, float degrees) {
        if (bitmap == null) return null;
        
        Matrix matrix = new Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }
    
    /**
     * 提取视频缩略图
     * 
     * @param videoPath 视频文件路径
     * @return 视频缩略图，如果提取失败则返回null
     */
    private static Bitmap extractVideoThumbnail(String videoPath) {
        android.media.MediaMetadataRetriever retriever = new android.media.MediaMetadataRetriever();
        try {
            retriever.setDataSource(videoPath);
            
            // 尝试获取视频中间帧作为缩略图
            String durationStr = retriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_DURATION);
            long duration = Long.parseLong(durationStr);
            long middleTimeUs = duration * 1000 / 2; // 视频中间位置(微秒)
            
            // 首先尝试获取视频中间帧
            Bitmap bitmap = retriever.getFrameAtTime(middleTimeUs, android.media.MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
            
            // 如果获取中间帧失败，尝试获取第一帧
            if (bitmap == null) {
                bitmap = retriever.getFrameAtTime(0, android.media.MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
            }
            
            // 如果仍然失败，尝试不指定时间获取帧
            if (bitmap == null) {
                bitmap = retriever.getFrameAtTime();
            }
            
            // 记录结果
            if (bitmap != null) {
                Log.d(TAG, "Successfully extracted video thumbnail: " + videoPath + 
                        " (" + bitmap.getWidth() + "x" + bitmap.getHeight() + ")");
            } else {
                Log.e(TAG, "Failed to extract video thumbnail: " + videoPath);
            }
            
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "Error extracting video thumbnail: " + e.getMessage(), e);
            return null;
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e);
            }
        }
    }
}