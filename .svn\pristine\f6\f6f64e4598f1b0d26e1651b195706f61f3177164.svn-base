// ***************************************************************
//  rtsp_header   version:  1.0 - date: 07/13/2007
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  The Institute of System Engineering, Zhejiang University
//  -------------------------------------------------------------
//  Copyright (C) 2002 - All Rights Reserved
// ***************************************************************
// 
// ***************************************************************
#pragma once
#ifndef __rtsp_header__09_4__
#define __rtsp_header__09_4__

#include <vector>
#include <string>
#include <utiny/usys_smartptr.h>
#include <utiny/usys_bytes.h>
#include <utiny/ustdcpp_inc.h>
#include <deque>
#include <utiny/uce_header.h>
#include <stdlib.h>

#define RTSP_VERSION_BIND(x, y) (((x) << 16) | (y))
#define RTSP_VERSION_MAJOR(v)	(((v) >> 16) & 0x0000ffff)
#define RTSP_VERSION_MINOR(v)	((v) & 0x0000ffff)

struct UBEDA_API RTSP_REQUEST
{
    enum RTSP_REQUEST_METHOD_E
    {
        RTSP_REQUEST_METHOD_UNKNOW,
        RTSP_REQUEST_METHOD_OPTIONS,
        RTSP_REQUEST_METHOD_DESCRIBE,
        RTSP_REQUEST_METHOD_ANNOUNCE,
        RTSP_REQUEST_METHOD_SETUP,
        RTSP_REQUEST_METHOD_PLAY,
        RTSP_REQUEST_METHOD_PAUSE,
        RTSP_REQUEST_METHOD_TEARDOWN,
        RTSP_REQUEST_METHOD_GET_PARAMETER,
        RTSP_REQUEST_METHOD_SET_PARAMETER,
        RTSP_REQUEST_METHOD_REDIRECT,
        RTSP_REQUEST_METHOD_RECORD,
		RTSP_REQUEST_METHOD_LIMIT,

		RTSP_REQUEST_RTPOVERRTSP
    };
    RTSP_REQUEST():method_(RTSP_REQUEST_METHOD_UNKNOW), version_(0) {};
    RTSP_REQUEST(const std::string& url, RTSP_REQUEST_METHOD_E method, unsigned version = RTSP_VERSION_BIND(1,0));

    std::string             url_;
    RTSP_REQUEST_METHOD_E   method_;
    unsigned				version_;
	unsigned char			channel_; // used by rtp over rtsp
};

struct UBEDA_API RTSP_RESPONSE
{
    RTSP_RESPONSE(unsigned code = 200, unsigned version = RTSP_VERSION_BIND(1, 0));
    unsigned	result_code_; 
    std::string	result_string_;
    unsigned	version_;
};

class UBEDA_API rtsp_header : public usys_smartptr_mtbase, public TEXT_PROTOCOL
{	
    unsigned	    unpack_offset_;

    int parse_first_line(const char* buf, int size);
    int parse_param(const char* buf, int size);

    int parse_request_uri(const char* buf, int size);
    int parse_status_code(const char* buf, int size);
public:
    RTSP_REQUEST    request_;
    RTSP_RESPONSE   response_;
    
    rtsp_header();
	virtual ~rtsp_header();

    int  unpack(const char* buf, int size);
    int  pack(char* buf, int size)const;

	int msg_length()const { return (header_length_ + body_length()); }
	inline int get_cseq_id()const
	{
		const std::string str = exparam("CSeq");
		if (!str.empty())
			return atoi(str.c_str());
		return 0;
	};
};

typedef usys_smartptr<rtsp_header> rtsp_header_ptr;

#endif
