<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jul 16 10:30:46 CST 2025 -->
<title>VideoEncoder</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-16">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.video, class: VideoEncoder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.video</a></div>
<h1 title="类 VideoEncoder" class="title">类 VideoEncoder</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.video.VideoEncoder</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">VideoEncoder</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">VideoEncoder 类负责视频编码、存储监控以及文件大小限制的处理。
 <p>
 此类提供了初始化编码器、开始录制、停止录制以及释放资源的方法。
 它还支持存储空间监控和 FAT32 文件系统的文件大小限制。
 </p>
 
 <p><b>主要功能：</b></p>
 <ul>
   <li>高性能视频编码 (H.264/AVC)</li>
   <li>支持多种分辨率 (最高支持4K)</li>
   <li>存储空间自动监控</li>
   <li>FAT32文件系统大小限制处理</li>
   <li>编码参数动态调整</li>
   <li>丰富的回调接口</li>
 </ul>
 
 <p><b>使用示例：</b></p>
 <pre>
 // 创建编码器（推荐使用TpVideoConfig）
 VideoEncoder encoder = VideoEncoder.builder()
     .setPreviewSurface(surfaceTexture)
     .setTpVideoConfig(TpVideoConfig.create4K())
     .onSurfaceAvailable(surface -> {
         // 在此处理编码器Surface可用事件
     })
     .onStorageFull(() -> {
         // 存储空间不足时的处理
     })
     .onError((errorType, e) -> {
         // 错误处理
     })
     .build();
 
 // 开始录制
 encoder.startRecording(outputPath);
 
 // 停止录制
 encoder.stopRecording();
 
 // 释放资源
 encoder.release();
 </pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="VideoEncoder.Builder.html" class="type-name-link" title="com.android.rockchip.camera2.video中的类">VideoEncoder.Builder</a></code></div>
<div class="col-last even-row-color">
<div class="block">VideoEncoder的构建器类</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="VideoEncoder.VideoDataOutputCallback.html" class="type-name-link" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a></code></div>
<div class="col-last odd-row-color">
<div class="block">视频数据输出回调接口</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="VideoEncoder.Builder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">创建Builder实例</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>android.media.MediaFormat</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEncoderOutputFormat()" class="member-name-link">getEncoderOutputFormat</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取编码器当前输出格式</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initialize(android.util.Size,android.view.Surface)" class="member-name-link">initialize</a><wbr>(android.util.Size&nbsp;videoSize,
 android.view.Surface&nbsp;decoderSurface)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">初始化编码器和解码器。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">释放编码器和解码器的资源。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#requestKeyFrame()" class="member-name-link">requestKeyFrame</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">请求生成关键帧</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBitrate(int)" class="member-name-link">setBitrate</a><wbr>(int&nbsp;bitrate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">动态设置视频编码比特率</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputCallback(com.android.rockchip.camera2.video.VideoEncoder.VideoDataOutputCallback)" class="member-name-link">setOutputCallback</a><wbr>(<a href="VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a>&nbsp;callback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置视频数据输出回调</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startRecording(java.lang.String)" class="member-name-link">startRecording</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始录制视频。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopRecording()" class="member-name-link">stopRecording</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止录制视频。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="setBitrate(int)">
<h3>setBitrate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setBitrate</span><wbr><span class="parameters">(int&nbsp;bitrate)</span></div>
<div class="block">动态设置视频编码比特率
 <p>
 此方法允许在编码过程中动态调整视频比特率，可用于根据网络状况
 或存储需求实时调整视频质量。设置后会在下一个关键帧生效。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>bitrate</code> - 新的比特率，单位为bps(比特/秒)，推荐值：1080p为5,000,000，4K为10,000,000</dd>
<dt>返回:</dt>
<dd>是否成功设置比特率</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputCallback(com.android.rockchip.camera2.video.VideoEncoder.VideoDataOutputCallback)">
<h3>setOutputCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputCallback</span><wbr><span class="parameters">(<a href="VideoEncoder.VideoDataOutputCallback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.VideoDataOutputCallback</a>&nbsp;callback)</span></div>
<div class="block">设置视频数据输出回调
 <p>设置此回调后，编码的视频数据将通过回调方法提供，而不是写入文件</p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>callback</code> - 回调接口实现</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="builder()">
<h3>builder</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="VideoEncoder.Builder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder.Builder</a></span>&nbsp;<span class="element-name">builder</span>()</div>
<div class="block">创建Builder实例
 <p>VideoEncoder使用Builder模式创建实例，以便灵活配置各种参数和回调</p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>新的Builder实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initialize(android.util.Size,android.view.Surface)">
<h3>initialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initialize</span><wbr><span class="parameters">(android.util.Size&nbsp;videoSize,
 android.view.Surface&nbsp;decoderSurface)</span></div>
<div class="block">初始化编码器和解码器。
 <p>
 此方法会根据指定的视频尺寸和比特率配置编码器和解码器，并启动它们。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoSize</code> - 视频尺寸（宽度和高度）。</dd>
<dd><code>decoderSurface</code> - 解码器输出的 Surface，用于显示解码后的视频。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEncoderOutputFormat()">
<h3>getEncoderOutputFormat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">android.media.MediaFormat</span>&nbsp;<span class="element-name">getEncoderOutputFormat</span>()</div>
<div class="block">获取编码器当前输出格式</div>
<dl class="notes">
<dt>返回:</dt>
<dd>编码器输出格式</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="requestKeyFrame()">
<h3>requestKeyFrame</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">requestKeyFrame</span>()</div>
<div class="block">请求生成关键帧
 <p>
 强制编码器生成一个IDR关键帧。这在动态调整编码参数后或需要
 重新同步视频流时非常有用。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="startRecording(java.lang.String)">
<h3>startRecording</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startRecording</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</span></div>
<div class="block">开始录制视频。
 <p>
 此方法会初始化 MediaMuxer 并启动存储监控。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>outputPath</code> - 输出文件路径，用于保存录制的视频。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopRecording()">
<h3>stopRecording</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopRecording</span>()</div>
<div class="block">停止录制视频。
 <p>
 此方法会停止存储监控并释放 MediaMuxer 资源。
 </p></div>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">释放编码器和解码器的资源。
 <p>
 此方法会停止并释放编码器、解码器和 MediaMuxer 的资源。
 </p></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
