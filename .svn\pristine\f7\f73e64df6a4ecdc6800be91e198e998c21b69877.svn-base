package com.android.rockchip.camera2.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.MalformedURLException;

import jcifs.CIFSContext;
import jcifs.context.SingletonContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileOutputStream;

/**
 * SambaUploader类用于连接Samba服务器并上传图片和视频。
 * <p>
 * 此类提供了以下功能：
 * <ul>
 *   <li>保存和加载Samba连接设置</li>
 *   <li>测试Samba连接</li>
 *   <li>将本地图片上传到Samba服务器</li>
 *   <li>将本地视频上传到Samba服务器</li>
 *   <li>提供上传状态回调</li>
 * </ul>
 * </p>
 * 
 * <h2>项目配置</h2>
 * 
 * <h3>1. build.gradle配置</h3>
 * <p>需要在app/build.gradle文件的dependencies部分添加JCIFS依赖：</p>
 * <pre>{@code
 * dependencies {
 *     // 现有依赖...
 *     
 *     // JCIFS库，用于Samba文件传输
 *     implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'
 * }
 * }</pre>
 * 
 * <h3>2. AndroidManifest.xml配置</h3>
 * <p>需要在AndroidManifest.xml中添加以下网络权限：</p>
 * <pre>{@code
 * <!-- 网络权限，用于Samba文件传输 -->
 * <uses-permission android:name="android.permission.INTERNET" />
 * <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
 * }</pre>
 *
 * <h2>使用方法</h2>
 * 
 * <h3>1. 初始化</h3>
 * <pre>{@code
 * // 创建SambaUploader实例
 * SMBFileUploader sambaUploader = new SMBFileUploader(context);
 * }</pre>
 * 
 * <h3>2. 配置连接参数</h3>
 * <pre>{@code
 * // 设置Samba连接参数
 * sambaUploader.setConnectionParams(
 *     "*************",  // 服务器IP
 *     "username",        // 用户名（留空表示匿名访问）
 *     "password",        // 密码（留空表示匿名访问）
 *     "share",           // 共享名称
 *     "/photos",         // 远程路径
 *     true               // 是否启用上传
 * );
 * 
 * // 或者单独设置是否启用
 * sambaUploader.setEnabled(true);
 * }</pre>
 * 
 * <h3>3. 测试连接</h3>
 * <pre>{@code
 * sambaUploader.testConnection(new SMBFileUploader.UploadCallback() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         // 连接成功
 *         Toast.makeText(context, "连接成功!", Toast.LENGTH_SHORT).show();
 *     }
 *     
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         // 连接失败
 *         Toast.makeText(context, "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
 *     }
 * });
 * }</pre>
 * 
 * <h3>4. 上传图片</h3>
 * <pre>{@code
 * // 手动上传单张图片
 * String localFilePath = "/storage/emulated/0/Pictures/image.jpg";
 * sambaUploader.uploadImage(localFilePath, new SMBFileUploader.UploadCallback() {
 *     @Override
 *     public void onUploadSuccess(String remoteFilePath) {
 *         Log.d("SMBFileUploader", "图片上传成功: " + remoteFilePath);
 *     }
 *     
 *     @Override
 *     public void onUploadFailed(String errorMessage) {
 *         Log.e("SMBFileUploader", "图片上传失败: " + errorMessage);
 *     }
 * });
 * }</pre>
 * 
 * <h3>5. 与CaptureImageHelper集成</h3>
 * <pre>{@code
 * // 创建CaptureImageHelper并设置SambaUploader
 * CaptureImageHelper captureImageHelper = CaptureImageHelper.builder(new Size(3840, 2160))
 *     .onImageSaved(filePath -> {
 *         // 图片已保存到本地，如果SambaUploader启用，会自动上传
 *     })
 *     .onError(errorMessage -> {
 *         // 处理错误
 *     })
 *     .setSambaUploader(sambaUploader) // 设置SambaUploader
 *     .build();
 * 
 * // 当使用captureImageHelper拍照时，如果Samba上传已启用，将自动上传图片到Samba服务器
 * }</pre>
 * 
 * <h3>6. 在Activity中完整实现</h3>
 * <pre>{@code
 * public class MainActivity extends AppCompatActivity {
 *     private SMBFileUploader sambaUploader;
 *     private CaptureImageHelper captureImageHelper;
 *     
 *     @Override
 *     protected void onCreate(Bundle savedInstanceState) {
 *         super.onCreate(savedInstanceState);
 *         
 *         // 初始化SambaUploader
 *         sambaUploader = new SMBFileUploader(this);
 *         
 *         // 初始化CaptureImageHelper并集成SambaUploader
 *         captureImageHelper = CaptureImageHelper.builder(new Size(3840, 2160))
 *             .onImageSaved(filePath -> {
 *                 runOnUiThread(() -> Toast.makeText(this, "图片已保存", Toast.LENGTH_SHORT).show());
 *             })
 *             .onError(errorMessage -> {
 *                 runOnUiThread(() -> Toast.makeText(this, "错误: " + errorMessage, Toast.LENGTH_SHORT).show());
 *             })
 *             .setSambaUploader(sambaUploader)
 *             .build();
 *             
 *         // 设置Samba设置按钮点击事件
 *         findViewById(R.id.btnSambaSettings).setOnClickListener(v -> {
 *             showSambaSettingsDialog();
 *         });
 *         
 *         // 设置拍照按钮点击事件
 *         findViewById(R.id.btnCapture).setOnClickListener(v -> {
 *             captureImage();
 *         });
 *     }
 *     
 *     private void showSambaSettingsDialog() {
 *         SMBSettingsDialog dialog = new SMBSettingsDialog(this, sambaUploader);
 *         dialog.show();
 *     }
 *     
 *     private void captureImage() {
 *         // 调用拍照方法
 *         captureImageHelper.captureImage();
 *         // 如果Samba上传启用，图片将在保存后自动上传
 *     }
 * }
 * }</pre>
 */
public class SMBFileUploader {
    private static final String TAG = "SMBFileUploader";
    private static final String PREFS_NAME = "SambaUploaderPrefs";
    
    /**
     * 上传结果回调接口
     */
    public interface UploadCallback {
        void onUploadSuccess(String remoteFilePath);
        void onUploadFailed(String errorMessage);
    }
    
    // Samba连接参数
    private String serverIp;
    private String username;
    private String password;
    private String shareName;
    private String remotePath;
    private boolean enabled = false;
    private boolean videoUploadEnabled = false; // 是否启用视频上传
    
    private Context context;
    
    /**
     * 构造函数
     * @param context 应用上下文
     */
    public SMBFileUploader(Context context) {
        this.context = context;
        loadSettings();
    }
    
    /**
     * 从SharedPreferences加载设置
     */
    private void loadSettings() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        serverIp = prefs.getString("serverIp", "");
        username = prefs.getString("username", "");
        password = prefs.getString("password", "");
        shareName = prefs.getString("shareName", "");
        remotePath = prefs.getString("remotePath", "/");
        enabled = prefs.getBoolean("enabled", false);
        videoUploadEnabled = prefs.getBoolean("videoUploadEnabled", false);
        
        Log.d(TAG, "加载Samba设置: 启用状态=" + enabled + ", 服务器=" + serverIp);
    }
    
    /**
     * 保存设置到SharedPreferences
     */
    private void saveSettings() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("serverIp", serverIp);
        editor.putString("username", username);
        editor.putString("password", password);
        editor.putString("shareName", shareName);
        editor.putString("remotePath", remotePath);
        editor.putBoolean("enabled", enabled);
        editor.putBoolean("videoUploadEnabled", videoUploadEnabled);
        editor.apply();
        
        Log.d(TAG, "保存Samba设置: 启用状态=" + enabled + ", 服务器=" + serverIp);
    }
    
    /**
     * 设置Samba连接参数
     * <p>
     * 配置Samba服务器连接所需的所有参数。这些参数将被保存到本地SharedPreferences中，
     * 应用重启后仍然有效。
     * </p>
     * <p>
     * 参数说明：
     * <ul>
     *   <li><strong>serverIp</strong> - Samba服务器的IP地址，必填</li>
     *   <li><strong>username</strong> - 登录用户名，留空表示匿名访问</li>
     *   <li><strong>password</strong> - 登录密码，留空表示匿名访问</li>
     *   <li><strong>shareName</strong> - Samba共享名称，必填</li>
     *   <li><strong>remotePath</strong> - 服务器上的路径，可以是根路径"/"或子路径"/photos"等</li>
     *   <li><strong>enabled</strong> - 是否启用Samba上传功能</li>
     * </ul>
     * </p>
     * <p>
     * 示例用法：
     * <pre>{@code
     * // 配置Samba连接参数（匿名访问）
     * sambaUploader.setConnectionParams(
     *     "*************",    // 服务器IP
     *     "",                 // 用户名（留空表示匿名访问）
     *     "",                 // 密码（留空表示匿名访问）
     *     "share",            // 共享名称
     *     "/photos",          // 远程路径 
     *     true                // 启用上传
     * );
     * 
     * // 配置Samba连接参数（需要认证）
     * sambaUploader.setConnectionParams(
     *     "*************",    // 服务器IP
     *     "username",         // 用户名
     *     "password",         // 密码
     *     "share",            // 共享名称
     *     "/photos",          // 远程路径
     *     true                // 启用上传
     * );
     * }</pre>
     * </p>
     * 
     * @param serverIp Samba服务器的IP地址
     * @param username 登录用户名，留空表示匿名访问
     * @param password 登录密码，留空表示匿名访问
     * @param shareName Samba共享名称
     * @param remotePath 服务器上的路径
     * @param enabled 是否启用Samba上传功能
     */
    public void setConnectionParams(String serverIp, String username, String password, 
                                   String shareName, String remotePath, boolean enabled) {
        this.serverIp = serverIp;
        this.username = username;
        this.password = password;
        this.shareName = shareName;
        this.remotePath = remotePath;
        this.enabled = enabled;
        saveSettings();
    }
    
    /**
     * 获取服务器IP
     */
    public String getServerIp() {
        return serverIp;
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 获取密码
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * 获取共享名称
     */
    public String getShareName() {
        return shareName;
    }
    
    /**
     * 获取远程路径
     */
    public String getRemotePath() {
        return remotePath;
    }
    
    /**
     * 检查是否启用了Samba上传
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 设置是否启用Samba上传
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        saveSettings();
    }
    
    /**
     * 检查是否启用了视频上传
     * <p>
     * 注意：即使此方法返回true，如果{@link #isEnabled()}返回false，
     * 上传功能仍然不会生效。
     * </p>
     * 
     * @return 如果启用了视频上传则返回true，否则返回false
     */
    public boolean isVideoUploadEnabled() {
        return videoUploadEnabled;
    }
    
    /**
     * 设置是否启用视频上传
     * <p>
     * 设置是否在录制完成后自动上传视频文件到Samba服务器。
     * 注意：要使此功能生效，{@link #setEnabled(boolean)}也必须设置为true。
     * </p>
     * 
     * @param enabled 是否启用视频上传
     */
    public void setVideoUploadEnabled(boolean enabled) {
        this.videoUploadEnabled = enabled;
        saveSettings();
    }
    
    /**
     * 测试Samba服务器连接
     * <p>
     * 尝试连接到配置的Samba服务器，验证连接参数的正确性。测试过程在后台线程中异步执行，
     * 不会阻塞UI线程，测试结果通过UploadCallback回调返回。
     * </p>
     * <p>
     * 测试内容包括：
     * <ul>
     *   <li>验证服务器IP和共享名称是否有效</li>
     *   <li>验证用户名和密码是否正确（如果提供）</li>
     *   <li>验证指定的远程路径是否可访问</li>
     * </ul>
     * </p>
     * <p>
     * 如果Samba上传功能未启用（通过setEnabled(false)禁用），测试将失败并返回相应的错误消息。
     * </p>
     * <p>
     * 示例用法：
     * <pre>{@code
     * // 测试Samba连接并处理结果
     * sambaUploader.testConnection(new SMBFileUploader.UploadCallback() {
     *     @Override
     *     public void onUploadSuccess(String message) {
     *         // 连接测试成功
     *         Toast.makeText(context, "连接成功!", Toast.LENGTH_SHORT).show();
     *     }
     *     
     *     @Override
     *     public void onUploadFailed(String errorMessage) {
     *         // 连接测试失败
     *         Toast.makeText(context, "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
     *     }
     * });
     * }</pre>
     * </p>
     * 
     * @param callback 测试结果回调，可以为null（如果不关心测试结果）
     */
    public void testConnection(UploadCallback callback) {
        if (!enabled) {
            if (callback != null) {
                callback.onUploadFailed("Samba上传功能未启用");
            }
            return;
        }
        
        new TestConnectionTask(callback).execute();
    }
    
    /**
     * 上传图片到Samba服务器
     * <p>
     * 将本地保存的图片异步上传到配置的Samba服务器。上传过程在后台线程中执行，
     * 不会阻塞UI线程。上传结果通过UploadCallback回调返回。
     * </p>
     * <p>
     * 图片将被上传到配置的Samba路径中，保持原文件名不变。如果目标服务器上已存在
     * 同名文件，将会被覆盖。
     * </p>
     * <p>
     * 如果Samba上传功能未启用（通过setEnabled(false)禁用），此方法将不会执行任何
     * 上传操作，也不会触发回调。
     * </p>
     * <p>
     * 示例用法：
     * <pre>{@code
     * // 上传图片并处理结果
     * sambaUploader.uploadImage("/storage/emulated/0/Pictures/image.jpg", new SMBFileUploader.UploadCallback() {
     *     @Override
     *     public void onUploadSuccess(String remoteFilePath) {
     *         // 上传成功处理
     *         Log.d("TAG", "图片上传成功: " + remoteFilePath);
     *     }
     *     
     *     @Override
     *     public void onUploadFailed(String errorMessage) {
     *         // 上传失败处理
     *         Log.e("TAG", "图片上传失败: " + errorMessage);
     *     }
     * });
     * }</pre>
     * </p>
     * 
     * @param localFilePath 本地图片的完整文件路径，必须是一个有效的文件
     * @param callback 上传结果回调，可以为null（如果不关心上传结果）
     */
    public void uploadImage(String localFilePath, UploadCallback callback) {
        if (!enabled) {
            Log.d(TAG, "Samba上传功能未启用，跳过上传");
            return;
        }
        
        new UploadTask(localFilePath, callback).execute();
    }
    
    /**
     * 上传视频到Samba服务器
     * <p>
     * 将本地保存的视频异步上传到配置的Samba服务器。上传过程在后台线程中执行，
     * 不会阻塞UI线程。上传结果通过UploadCallback回调返回。
     * </p>
     * <p>
     * 视频将被上传到配置的Samba路径中，保持原文件名不变。如果目标服务器上已存在
     * 同名文件，将会被覆盖。
     * </p>
     * <p>
     * 如果Samba上传功能未启用（通过setEnabled(false)禁用），或者视频上传功能
     * 未启用（通过setVideoUploadEnabled(false)禁用），此方法将不会执行任何
     * 上传操作，也不会触发回调。
     * </p>
     * <p>
     * 示例用法：
     * <pre>{@code
     * // 上传视频并处理结果
     * sambaUploader.uploadVideo("/storage/emulated/0/Movies/video.mp4", new SMBFileUploader.UploadCallback() {
     *     @Override
     *     public void onUploadSuccess(String remoteFilePath) {
     *         // 上传成功处理
     *         Log.d("TAG", "视频上传成功: " + remoteFilePath);
     *     }
     *     
     *     @Override
     *     public void onUploadFailed(String errorMessage) {
     *         // 上传失败处理
     *         Log.e("TAG", "视频上传失败: " + errorMessage);
     *     }
     * });
     * }</pre>
     * </p>
     * 
     * @param localFilePath 本地视频的完整文件路径，必须是一个有效的文件
     * @param callback 上传结果回调，可以为null（如果不关心上传结果）
     */
    public void uploadVideo(String localFilePath, UploadCallback callback) {
        if (!enabled || !videoUploadEnabled) {
            Log.d(TAG, "Samba视频上传功能未启用，跳过上传");
            return;
        }
        
        Log.d(TAG, "开始上传视频到Samba服务器: " + localFilePath);
        // 复用相同的上传任务，上传逻辑是一样的
        new UploadTask(localFilePath, callback).execute();
    }
    
    /**
     * 测试连接的异步任务
     */
    private class TestConnectionTask extends AsyncTask<Void, Void, Boolean> {
        private UploadCallback callback;
        private String errorMessage = "";
        
        public TestConnectionTask(UploadCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                // 检查参数
                if (serverIp.isEmpty()) {
                    errorMessage = "服务器IP不能为空";
                    return false;
                }
                
                if (shareName.isEmpty()) {
                    errorMessage = "共享名称不能为空";
                    return false;
                }
                
                // 创建CIFS上下文并设置认证信息
                CIFSContext auth;
                if (username.isEmpty() && password.isEmpty()) {
                    // 匿名访问
                    auth = SingletonContext.getInstance().withGuestCrendentials();
                } else {
                    auth = SingletonContext.getInstance().withCredentials(
                            new NtlmPasswordAuthenticator(username, password));
                }
                
                // 构建SMB URL
                String smbUrl = "smb://" + serverIp + "/" + shareName + remotePath;
                if (!remotePath.endsWith("/")) {
                    smbUrl += "/";
                }
                
                // 尝试连接
                SmbFile smbFile = new SmbFile(smbUrl, auth);
                smbFile.exists(); // 如果连接失败会抛出异常
                
                Log.d(TAG, "Samba连接测试成功: " + smbUrl);
                return true;
            } catch (MalformedURLException e) {
                errorMessage = "无效的URL: " + e.getMessage();
                Log.e(TAG, "SMB URL格式错误", e);
            } catch (SmbException e) {
                errorMessage = "SMB错误: " + e.getMessage();
                Log.e(TAG, "SMB连接错误", e);
            } catch (Exception e) {
                errorMessage = "未预期的错误: " + e.getMessage();
                Log.e(TAG, "未知错误", e);
            }
            
            return false;
        }
        
        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                if (success) {
                    callback.onUploadSuccess("连接测试成功");
                } else {
                    callback.onUploadFailed(errorMessage);
                }
            }
        }
    }
    
    /**
     * 上传图片的异步任务
     */
    private class UploadTask extends AsyncTask<Void, Void, Boolean> {
        private String localFilePath;
        private UploadCallback callback;
        private String errorMessage = "";
        private String remoteFilePath = "";
        
        public UploadTask(String localFilePath, UploadCallback callback) {
            this.localFilePath = localFilePath;
            this.callback = callback;
        }
        
        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                // 检查本地文件是否存在
                File localFile = new File(localFilePath);
                if (!localFile.exists()) {
                    errorMessage = "本地文件不存在: " + localFilePath;
                    return false;
                }
                
                // 创建CIFS上下文并设置认证信息
                CIFSContext auth;
                if (username.isEmpty() && password.isEmpty()) {
                    // 匿名访问
                    auth = SingletonContext.getInstance().withGuestCrendentials();
                } else {
                    auth = SingletonContext.getInstance().withCredentials(
                            new NtlmPasswordAuthenticator(username, password));
                }
                
                // 构建SMB URL
                String fileName = localFile.getName();
                String smbUrl = "smb://" + serverIp + "/" + shareName + remotePath;
                if (!remotePath.endsWith("/")) {
                    smbUrl += "/";
                }
                
                remoteFilePath = smbUrl + fileName;
                Log.d(TAG, "准备上传文件到: " + remoteFilePath);
                
                // 创建SMB文件并写入内容
                SmbFile smbFile = new SmbFile(remoteFilePath, auth);
                SmbFileOutputStream outputStream = new SmbFileOutputStream(smbFile);
                
                // 读取本地文件并写入SMB文件
                FileInputStream inputStream = new FileInputStream(localFile);
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                inputStream.close();
                outputStream.close();
                
                Log.d(TAG, "文件上传成功: " + remoteFilePath + ", 大小: " + totalBytes + " bytes");
                return true;
            } catch (MalformedURLException e) {
                errorMessage = "无效的URL: " + e.getMessage();
                Log.e(TAG, "SMB URL格式错误", e);
            } catch (IOException e) {
                errorMessage = "I/O错误: " + e.getMessage();
                Log.e(TAG, "SMB文件写入错误", e);
            } catch (Exception e) {
                errorMessage = "未预期的错误: " + e.getMessage();
                Log.e(TAG, "未知错误", e);
            }
            
            return false;
        }
        
        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                if (success) {
                    callback.onUploadSuccess(remoteFilePath);
                } else {
                    callback.onUploadFailed(errorMessage);
                }
            }
        }
    }
} 