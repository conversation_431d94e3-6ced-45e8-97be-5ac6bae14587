<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed May 07 13:06:38 CST 2025 -->
<title>O - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-05-07">
<meta name="description" content="index: O">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">F</a>&nbsp;<a href="index-4.html">G</a>&nbsp;<a href="index-5.html">H</a>&nbsp;<a href="index-6.html">I</a>&nbsp;<a href="index-7.html">O</a>&nbsp;<a href="index-8.html">R</a>&nbsp;<a href="index-9.html">S</a>&nbsp;<a href="index-10.html">T</a>&nbsp;<a href="index-11.html">U</a>&nbsp;<a href="index-12.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html#onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">onDataChanged(TouptekIspParam, int)</a> - 接口中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></dt>
<dd>
<div class="block">当 int 类型数据发生变化时调用。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.DeviceStateCallback.html#onDeviceStateChanged(boolean)" class="member-name-link">onDeviceStateChanged(boolean)</a> - 接口中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.DeviceStateCallback.html" title="com.android.rockchip.camera2.util中的接口">touptek_serial_rk.DeviceStateCallback</a></dt>
<dd>
<div class="block">当设备状态发生变化时调用。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#onDeviceStateChanged(boolean)" class="member-name-link">onDeviceStateChanged(boolean)</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">设备插入/拔出回调方法。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html#onError(java.lang.String)" class="member-name-link">onError(String)</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a></dt>
<dd>
<div class="block">当抓图失败时调用</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html#onError(java.lang.String,java.lang.Exception)" class="member-name-link">onError(String, Exception)</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></dt>
<dd>
<div class="block">发生错误时触发</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html#onFileSizeLimitReached()" class="member-name-link">onFileSizeLimitReached()</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></dt>
<dd>
<div class="block">文件大小达到限制，主要是FAT32文件系统，单个视频满3.9GB时触发</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/HdmiService.HdmiListener.html#onHdmiStatusChanged(boolean)" class="member-name-link">onHdmiStatusChanged(boolean)</a> - 接口中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/HdmiService.HdmiListener.html" title="com.android.rockchip.camera2.util中的接口">HdmiService.HdmiListener</a></dt>
<dd>
<div class="block">当 HDMI 状态发生变化时调用。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html#onImageSaved(java.lang.String)" class="member-name-link">onImageSaved(String)</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CaptureImageHelper.CaptureCallback.html" title="com.android.rockchip.camera2.video中的接口">CaptureImageHelper.CaptureCallback</a></dt>
<dd>
<div class="block">当图像保存成功时调用</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html#onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)" class="member-name-link">onLongDataChanged(TouptekIspParam, long)</a> - 接口中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></dt>
<dd>
<div class="block">当 long 类型数据发生变化时调用。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoDecoder.PlaybackListener.html#onPlaybackCompleted()" class="member-name-link">onPlaybackCompleted()</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoDecoder.PlaybackListener.html" title="com.android.rockchip.camera2.video中的接口">VideoDecoder.PlaybackListener</a></dt>
<dd>
<div class="block">播放完成时调用</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html#onSaveComplete(java.lang.String)" class="member-name-link">onSaveComplete(String)</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></dt>
<dd>
<div class="block">MP4文件保存完成时触发</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html#onSerialDataReceived(int%5B%5D)" class="member-name-link">onSerialDataReceived(int[])</a> - 类中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/touptek_serial_rk.html" title="com.android.rockchip.camera2.util中的类">touptek_serial_rk</a></dt>
<dd>
<div class="block">串口数据接收回调。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnSerialStateChangedListener.html#onSerialStateChanged(boolean)" class="member-name-link">onSerialStateChanged(boolean)</a> - 接口中的方法 com.android.rockchip.camera2.util.<a href="../com/android/rockchip/camera2/util/TouptekIspParam.OnSerialStateChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnSerialStateChangedListener</a></dt>
<dd>
<div class="block">当串口状态发生变化时调用。</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html#onStorageFull()" class="member-name-link">onStorageFull()</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></dt>
<dd>
<div class="block">存储空间不足时50MB触发</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html#onSurfaceAvailable(android.view.Surface,android.view.Surface)" class="member-name-link">onSurfaceAvailable(Surface, Surface)</a> - 接口中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/VideoEncoder.Callback.html" title="com.android.rockchip.camera2.video中的接口">VideoEncoder.Callback</a></dt>
<dd>
<div class="block">编码器和解码器的 Surface 可用时触发</div>
</dd>
<dt><a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html#openCamera(android.hardware.camera2.CameraDevice.StateCallback)" class="member-name-link">openCamera(CameraDevice.StateCallback)</a> - 类中的方法 com.android.rockchip.camera2.video.<a href="../com/android/rockchip/camera2/video/CameraManagerHelper.html" title="com.android.rockchip.camera2.video中的类">CameraManagerHelper</a></dt>
<dd>
<div class="block">打开摄像头。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">C</a>&nbsp;<a href="index-3.html">F</a>&nbsp;<a href="index-4.html">G</a>&nbsp;<a href="index-5.html">H</a>&nbsp;<a href="index-6.html">I</a>&nbsp;<a href="index-7.html">O</a>&nbsp;<a href="index-8.html">R</a>&nbsp;<a href="index-9.html">S</a>&nbsp;<a href="index-10.html">T</a>&nbsp;<a href="index-11.html">U</a>&nbsp;<a href="index-12.html">V</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
