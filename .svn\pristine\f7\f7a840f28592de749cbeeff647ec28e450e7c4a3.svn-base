package com.android.rockchip.camera2.video;

import android.graphics.*;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

public class CaptureImageHelper
{
    private static final String TAG = "CaptureImageHelper";

    /* ImageReader 用于接收摄像头输出的图像数据 */
    private final ImageReader imageReader;

    /* 后台线程的 Handler，用于处理耗时操作 */
    private final Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private final HandlerThread backgroundThread;

    /* 回调接口，用于通知抓图结果 */
    private CaptureCallback captureCallback;

    /* 标志位，表示是否有抓图请求 */
    private volatile boolean isCaptureRequested = false;

    /* 请求抓图的目标尺寸 */
    private Size requestedSize;

    /* 抓图保存的输出路径 */
    private String outputPath;

    /**
     * 回调接口，用于通知抓图结果
     */
    public interface CaptureCallback
    {
        /* 当图像保存成功时调用 */
        void onImageSaved(String filePath);

        /* 当抓图失败时调用 */
        void onError(String errorMessage);
    }

    /**
     * 构造函数，初始化 ImageReader 和后台线程
     *
     * @param imageSize       ImageReader 的图像尺寸
     * @param backgroundHandler 外部传入的后台线程 Handler
     */
    public CaptureImageHelper(Size imageSize, Handler backgroundHandler)
    {
        /* 启动后台线程 */
        backgroundThread = new HandlerThread("CaptureImageBackground");
        backgroundThread.start();
        this.backgroundHandler = new Handler(backgroundThread.getLooper());

        /* 初始化 ImageReader，用于接收摄像头输出的图像 */
        this.imageReader = ImageReader.newInstance(imageSize.getWidth(), imageSize.getHeight(), ImageFormat.YUV_420_888, 50);
        this.imageReader.setOnImageAvailableListener(this::onImageAvailable, this.backgroundHandler);
    }

    /**
     * 获取 ImageReader 实例
     *
     * @return ImageReader 实例
     */
    public ImageReader getImageReader()
    {
        return imageReader;
    }

    /**
     * 设置抓图回调
     *
     * @param callback 回调接口
     */
    public void setCaptureCallback(CaptureCallback callback)
    {
        this.captureCallback = callback;
    }

    /**
     * 请求抓图
     *
     * @param size       抓图的目标尺寸
     * @param outputPath 抓图保存的输出路径
     */
    public void requestCapture(Size size, String outputPath)
    {
        this.isCaptureRequested = true;
        this.requestedSize = size;
        this.outputPath = outputPath;
    }

    /**
     * 当 ImageReader 有新的图像数据时调用
     *
     * @param reader ImageReader 实例
     */
    private void onImageAvailable(ImageReader reader)
    {
        /* 获取最新的图像 */
        Image image = reader.acquireLatestImage();
        if (image == null) return;

        if (isCaptureRequested)
        {
            isCaptureRequested = false; // 重置抓图请求标志

            /* 在主线程复制图像数据并立即释放图像 */
            Size cameraSize = new Size(image.getWidth(), image.getHeight());
            ByteBuffer yuvData = copyImageToBuffer(image);
            image.close(); // 立即释放图像

            /* 在后台线程处理图像数据 */
            backgroundHandler.post(() ->
            {
                try
                {
                    /* 将 YUV 数据转换为 JPEG 格式 */
                    byte[] jpegData = convertYUVToJPEG(yuvData, cameraSize, requestedSize);

                    /* 保存图像到指定路径 */
                    saveImage(jpegData, outputPath);

                    /* 通知回调图像保存成功 */
                    if (captureCallback != null)
                    {
                        captureCallback.onImageSaved(outputPath);
                    }
                }
                catch (Exception e)
                {
                    Log.e(TAG, "Error processing image", e);

                    /* 通知回调图像保存失败 */
                    if (captureCallback != null)
                    {
                        captureCallback.onError("Failed to process image");
                    }
                }
            });
        }
        else
        {
            /* 如果没有抓图请求，立即释放图像 */
            image.close();
        }
    }

    /**
     * 将 YUV 数据转换为 JPEG 格式
     *
     * @param yuvData    包含 YUV 数据的 ByteBuffer
     * @param cameraSize 摄像头的原始图像尺寸
     * @param targetSize 目标图像尺寸
     * @return JPEG 格式的字节数组
     */
    private byte[] convertYUVToJPEG(ByteBuffer yuvData, Size cameraSize, Size targetSize)
    {
        /* 将 YUV 数据转换为 NV21 格式 */
        byte[] nv21 = convertYUV420ToNV21(yuvData, cameraSize.getWidth(), cameraSize.getHeight());

        /* 使用 YuvImage 将 NV21 数据转换为 JPEG */
        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, cameraSize.getWidth(), cameraSize.getHeight(), null);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, cameraSize.getWidth(), cameraSize.getHeight()), 100, out);

        /* 如果目标尺寸与原始尺寸相同，直接返回 JPEG 数据 */
        if (targetSize.equals(cameraSize))
        {
            return out.toByteArray();
        }
        else
        {
            /* 将 JPEG 数据解码为 Bitmap */
            Bitmap originalBitmap = BitmapFactory.decodeByteArray(out.toByteArray(), 0, out.size());

            /* 缩放 Bitmap 到目标尺寸 */
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, targetSize.getWidth(), targetSize.getHeight(), true);

            /* 将缩放后的 Bitmap 转换回 JPEG 数据 */
            ByteArrayOutputStream scaledOut = new ByteArrayOutputStream();
            scaledBitmap.compress(Bitmap.CompressFormat.JPEG, 100, scaledOut);
            return scaledOut.toByteArray();
        }
    }

    /**
     * 将 Image 数据复制到缓冲区
     *
     * @param image YUV_420_888 格式的 Image
     * @return 包含 YUV 数据的 ByteBuffer
     */
    private ByteBuffer copyImageToBuffer(Image image)
    {
        int bufferSize = 0;
        for (Image.Plane plane : image.getPlanes())
        {
            bufferSize += plane.getBuffer().remaining();
        }

        ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);
        for (Image.Plane plane : image.getPlanes())
        {
            ByteBuffer planeBuffer = plane.getBuffer();
            byte[] planeData = new byte[planeBuffer.remaining()];
            planeBuffer.get(planeData);
            buffer.put(planeData);
        }
        buffer.rewind();
        return buffer;
    }

    /**
     * 将 YUV_420_888 格式转换为 NV21 格式
     *
     * @param yuvData 包含 YUV 数据的 ByteBuffer
     * @param width   图像宽度
     * @param height  图像高度
     * @return NV21 格式的字节数组
     */
    private byte[] convertYUV420ToNV21(ByteBuffer yuvData, int width, int height)
    {
        int frameSize = width * height;
        int chromaSize = frameSize / 4;
        byte[] nv21 = new byte[frameSize + frameSize / 2];

        byte[] yPlane = new byte[frameSize];
        byte[] uvPlane = new byte[2 * chromaSize];

        yuvData.position(0);
        yuvData.get(yPlane, 0, frameSize);
        yuvData.get(uvPlane, 0, 2 * chromaSize);

        System.arraycopy(yPlane, 0, nv21, 0, frameSize);
        for (int i = 0; i < chromaSize; i++)
        {
            nv21[frameSize + i * 2] = uvPlane[i * 2 + 1];
            nv21[frameSize + i * 2 + 1] = uvPlane[i * 2];
        }

        return nv21;
    }

    /**
     * 保存图像到文件
     *
     * @param jpegData   JPEG 格式的字节数组
     * @param outputPath 输出文件路径
     * @throws IOException 如果保存失败
     */
    private void saveImage(byte[] jpegData, String outputPath) throws IOException
    {
        try (FileOutputStream output = new FileOutputStream(outputPath))
        {
            output.write(jpegData);
        }
    }

    /**
     * 释放资源，停止后台线程
     */
    public void release()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }
}
