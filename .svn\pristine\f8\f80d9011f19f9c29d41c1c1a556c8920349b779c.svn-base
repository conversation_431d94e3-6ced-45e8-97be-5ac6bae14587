<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Jun 05 09:11:19 CST 2025 -->
<title>RTSPManager</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-05">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.rtsp, class: RTSPManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.rtsp</a></div>
<h1 title="类 RTSPManager" class="title">类 RTSPManager</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.rtsp.RTSPManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">RTSPManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">RTSP推流统一管理器
 
 这是RTSP推流功能的唯一访问点，负责协调所有推流相关操作。
 应用程序应该只与此类交互，而不直接使用底层实现类。</div>
<dl class="notes">
<dt>从以下版本开始:</dt>
<dd>1.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="RTSPManager.StreamStateListener.html" class="type-name-link" title="com.android.rockchip.camera2.rtsp中的接口">RTSPManager.StreamStateListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">推流状态监听接口
 仅供RTSPManager内部使用</div>
</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="RTSPManager.StreamType.html" class="type-name-link" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a></code></div>
<div class="col-last odd-row-color">
<div class="block">推流类型枚举</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentStreamType()" class="member-name-link">getCurrentStreamType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前推流类型</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInstance()" class="member-name-link">getInstance</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取RTSPManager单例实例</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStreamUrl()" class="member-name-link">getStreamUrl</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前推流URL</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initialize(androidx.appcompat.app.AppCompatActivity)" class="member-name-link">initialize</a><wbr>(androidx.appcompat.app.AppCompatActivity&nbsp;activity)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">初始化RTSPManager
 必须在使用其他方法前调用此方法</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isStreaming()" class="member-name-link">isStreaming</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">判断是否正在推流</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onPermissionGranted(java.util.function.Consumer)" class="member-name-link">onPermissionGranted</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置权限获取成功的回调处理器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStreamError(java.util.function.Consumer)" class="member-name-link">onStreamError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置流错误的回调处理器</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStreamStarted(java.util.function.Consumer)" class="member-name-link">onStreamStarted</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置流开始的回调处理器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStreamStopped(java.lang.Runnable)" class="member-name-link">onStreamStopped</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Runnable.html" title="java.lang中的类或接口" class="external-link">Runnable</a>&nbsp;handler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置流停止的回调处理器</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">释放所有资源
 在不再需要推流功能时调用</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig)" class="member-name-link">setConfig</a><wbr>(<a href="config/RTSPConfig.html" title="com.android.rockchip.camera2.rtsp.config中的类">RTSPConfig</a>&nbsp;config)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置RTSP配置</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStreamType(com.android.rockchip.camera2.rtsp.RTSPManager.StreamType)" class="member-name-link">setStreamType</a><wbr>(<a href="RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a>&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置推流类型
 只切换类型，不自动请求权限或开始推流</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder)" class="member-name-link">setVideoEncoder</a><wbr>(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置视频编码器
 当推流类型为CAMERA时必须设置</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startStreaming()" class="member-name-link">startStreaming</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始推流
 如果是屏幕推流但没有权限，会先请求权限</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopStreaming()" class="member-name-link">stopStreaming</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止推流</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="getInstance()">
<h3>getInstance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">getInstance</span>()</div>
<div class="block">获取RTSPManager单例实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>RTSPManager单例实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initialize(androidx.appcompat.app.AppCompatActivity)">
<h3>initialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">initialize</span><wbr><span class="parameters">(androidx.appcompat.app.AppCompatActivity&nbsp;activity)</span></div>
<div class="block">初始化RTSPManager
 必须在使用其他方法前调用此方法</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>activity</code> - 关联的Activity实例，用于权限请求</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig)">
<h3>setConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">setConfig</span><wbr><span class="parameters">(<a href="config/RTSPConfig.html" title="com.android.rockchip.camera2.rtsp.config中的类">RTSPConfig</a>&nbsp;config)</span></div>
<div class="block">设置RTSP配置</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>config</code> - RTSP配置对象</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder)">
<h3>setVideoEncoder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">setVideoEncoder</span><wbr><span class="parameters">(<a href="../video/VideoEncoder.html" title="com.android.rockchip.camera2.video中的类">VideoEncoder</a>&nbsp;videoEncoder)</span></div>
<div class="block">设置视频编码器
 当推流类型为CAMERA时必须设置</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoEncoder</code> - 视频编码器实例</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onStreamStarted(java.util.function.Consumer)">
<h3>onStreamStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">onStreamStarted</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</span></div>
<div class="block">设置流开始的回调处理器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>handler</code> - 处理流URL的回调，参数为完整RTSP URL</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onStreamStopped(java.lang.Runnable)">
<h3>onStreamStopped</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">onStreamStopped</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Runnable.html" title="java.lang中的类或接口" class="external-link">Runnable</a>&nbsp;handler)</span></div>
<div class="block">设置流停止的回调处理器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>handler</code> - 处理流停止的回调</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onStreamError(java.util.function.Consumer)">
<h3>onStreamError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">onStreamError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</span></div>
<div class="block">设置流错误的回调处理器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>handler</code> - 处理错误消息的回调，参数为错误信息</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onPermissionGranted(java.util.function.Consumer)">
<h3>onPermissionGranted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">onPermissionGranted</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Consumer.html" title="java.util.function中的类或接口" class="external-link">Consumer</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;handler)</span></div>
<div class="block">设置权限获取成功的回调处理器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>handler</code> - 处理权限消息的回调，参数为成功消息</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStreamType(com.android.rockchip.camera2.rtsp.RTSPManager.StreamType)">
<h3>setStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.html" title="com.android.rockchip.camera2.rtsp中的类">RTSPManager</a></span>&nbsp;<span class="element-name">setStreamType</span><wbr><span class="parameters">(<a href="RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a>&nbsp;type)</span></div>
<div class="block">设置推流类型
 只切换类型，不自动请求权限或开始推流</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>type</code> - 推流类型，SCREEN或CAMERA</dd>
<dt>返回:</dt>
<dd>RTSPManager实例，用于链式调用</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startStreaming()">
<h3>startStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startStreaming</span>()</div>
<div class="block">开始推流
 如果是屏幕推流但没有权限，会先请求权限</div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否成功开始推流或请求权限</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopStreaming()">
<h3>stopStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopStreaming</span>()</div>
<div class="block">停止推流</div>
</section>
</li>
<li>
<section class="detail" id="getStreamUrl()">
<h3>getStreamUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getStreamUrl</span>()</div>
<div class="block">获取当前推流URL</div>
<dl class="notes">
<dt>返回:</dt>
<dd>推流URL，如果未推流则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentStreamType()">
<h3>getCurrentStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RTSPManager.StreamType.html" title="enum class in com.android.rockchip.camera2.rtsp">RTSPManager.StreamType</a></span>&nbsp;<span class="element-name">getCurrentStreamType</span>()</div>
<div class="block">获取当前推流类型</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前推流类型</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isStreaming()">
<h3>isStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isStreaming</span>()</div>
<div class="block">判断是否正在推流</div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否正在推流</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">释放所有资源
 在不再需要推流功能时调用</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
