{"buildFiles": ["D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\.cxx\\Debug\\k726jz3a\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\.cxx\\Debug\\k726jz3a\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"camera2::@6890427a1f51a3e7e1df": {"artifactName": "camera2", "abi": "x86", "output": "D:\\RK3588\\APP\\APP_cangshiyizhirkcame2\\rkCamer2\\app\\build\\intermediates\\cxx\\Debug\\k726jz3a\\obj\\x86\\libcamera2.so", "runtimeFiles": []}}}