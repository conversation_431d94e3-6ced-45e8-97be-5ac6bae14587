package com.touptek.xcamview.util

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.touptek.xcamview.R

object FontUtils {
    const val FONT_SYSTEM = 0
    const val FONT_SONG = 1
    const val FONT_KAI = 2

    private var currentFont = FONT_SYSTEM
    private var appTypeface: Typeface? = null
    private val activities = mutableListOf<AppCompatActivity>()

    fun setAppFont(context: Context, fontType: Int) {
        currentFont = fontType
        appTypeface = when (fontType) {
            FONT_SONG -> getSongTypeface(context)
            FONT_KAI -> getKaiTypeface(context)
            else -> Typeface.DEFAULT
        }
        updateAllActivities()
    }

    fun registerActivity(activity: AppCompatActivity) {
        if (!activities.contains(activity)) {
            activities.add(activity)
            applyFontToActivity(activity)
        }
    }

    fun unregisterActivity(activity: AppCompatActivity) {
        activities.remove(activity)
    }

    private fun updateAllActivities() {
        activities.forEach { activity ->
            activity.runOnUiThread {
                applyFontToActivity(activity)
            }
        }
    }

    fun applyFontToActivity(activity: AppCompatActivity) {
        val rootView = activity.window.decorView.findViewById<View>(android.R.id.content)
        applyFontToViewGroup(rootView)
    }

    private fun applyFontToViewGroup(view: View) {
        when (view) {
            is TextView -> {
                view.postDelayed({
                    if (view.text.isNotEmpty() && view.typeface != appTypeface) {
                        Log.d("FontUtils", "延迟设置字体到TextView: ${view.text} (原字体: ${view.typeface})")
                        view.typeface = appTypeface ?: Typeface.DEFAULT
                    }
                }, 2000) // 延迟300ms确保内容已加载
            }
            is ViewGroup -> {
                for (i in 0 until view.childCount) {
                    applyFontToViewGroup(view.getChildAt(i))
                }
            }
        }
    }

    fun getCurrentFontType(): Int = currentFont

    private fun getSongTypeface(context: Context): Typeface {
        return try {
            ResourcesCompat.getFont(context, R.font.song) ?: Typeface.DEFAULT
        } catch (e: Exception) {
            Typeface.DEFAULT
        }
    }

    private fun getKaiTypeface(context: Context): Typeface {
        return try {
            val typeface = ResourcesCompat.getFont(context, R.font.kai)
            Log.d("FontUtils", "楷体字体加载: ${if (typeface != null) "成功" else "失败"}")
            typeface ?: run {
                Log.e("FontUtils", "楷体字体加载返回null")
                Typeface.DEFAULT
            }
        } catch (e: Exception) {
            Log.e("FontUtils", "楷体字体加载异常: ${e.message}")
            Typeface.DEFAULT
        }
    }
}

abstract class BaseActivity : AppCompatActivity() {
    override fun onResume() {
        super.onResume()
        FontUtils.registerActivity(this)
        FontUtils.applyFontToActivity(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        FontUtils.unregisterActivity(this)
    }
}