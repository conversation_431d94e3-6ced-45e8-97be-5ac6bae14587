import os

target_platform = ARGUMENTS.get('tp', '')

inc_path = ['#', '#include']

env = Environment(CPPPATH=inc_path, ENV = {'PATH' : os.environ['PATH']})

if env["PLATFORM"]=="win32" and target_platform == '':
    cc_flags = """/O2 /Oi /GL 
        /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_USRDLL" /D "_WIN32_WINNT=0x0501" 
        /D "_CRT_SECURE_NO_WARNINGS" /D "UTINY_STATIC_LIB" /D "ZDP" /D "_BIND_TO_CURRENT_VCLIBS_VERSION=1" /D "_MBCS" /D "BZ_NO_STDIO"
        /FD /EHsc /MT /Gy /W3 /nologo /c /Zi /errorReport:prompt"""
    ar_flags = '/LTCG'
else:
    cxx_flags = "-std=gnu++0x"
    cc_flags = '-Os -DHAVE_MEMMOVE -DUSYS_HASNOT_LOG -DBZ_NO_STDIO -DNDEBUG -DOBJECT_MONITOR -DUCE_HASNOT_COMPRESS -DUCE_HASNOT_EPOLL -D_NOIFADDRS -DWITH_NOIO '
    ar_flags = ""
    if (target_platform == 'davinci'):
        tc='arm_v5t_le-'       
    elif (target_platform == 'hisi'):
        tc='arm-hismall-linux-'
    elif (target_platform == 'arm'):
        tc='arm-linux-'
        cc_flags += " -DHASNOT_UUID"
    elif (target_platform == 'hisiv100'):
        tc='arm-hisiv100-linux-'
    elif (target_platform == 'hisiv300'):
        tc='arm-hisiv300-linux-'
        cc_flags += " -DHASNOT_UUID"
    elif (target_platform == 'android'):
        tc='arm-linux-androideabi-'
    else:
        cc_flags = '-O2 -DZDP -g -DHAVE_MEMMOVE -DUSYS_HASNOT_LOG -DBZ_NO_STDIO -DNDEBUG -DOBJECT_MONITOR'
        tc=''

env['CC']=tc + 'gcc'
env['CXX']=tc + 'g++'
env['AR']=tc + 'ar'
env['STRIP']=tc + 'strip'
env.Append(ARFLAGS = ar_flags)
env.Append(CCFLAGS = cc_flags)
env.Append(CXXFLAGS = cxx_flags)
print env['CCFLAGS']

Export('env')

objs = env.Object(Glob('bzip2/*.c') + Glob('core/*.cpp') 
    + Glob('gep/*.cpp') + Glob('oep/*.cpp') + Glob('sip/*.cpp') 
    + Glob('xml/*.cpp') + Glob('soap/*.cpp') + Glob('expat/*.c')
    + Glob('http/*.cpp') + Glob('rtsp/*.cpp')
    + Glob('vendorsrc/*.cpp')) + Glob('snappy/*.cc')
libutiny = env.StaticLibrary('libutiny', objs)
env.Install('#../../bin/release', libutiny)
env.Alias('install', '#../../bin/release')
