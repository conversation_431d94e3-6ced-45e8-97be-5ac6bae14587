<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Fri Apr 11 14:44:32 CST 2025 -->
<title>TouptekIspParam</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-04-11">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, enum: TouptekIspParam">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#enum-constant-summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li><a href="#enum-constant-detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="Enum Class TouptekIspParam" class="title">Enum Class TouptekIspParam</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">java.lang.Enum</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;
<div class="inheritance">com.android.rockchip.camera2.util.TouptekIspParam</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public enum </span><span class="element-name type-name-label">TouptekIspParam</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a>&lt;<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&gt;</span></div>
<div class="block">枚举类 TouptekIspParam 定义了摄像头 ISP 参数的键值对。
 <p>
 每个枚举项对应一个 ISP 参数及其唯一的整数值。</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TouptekIspParam.OnDataChangedListener.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">定义数据变化监听器接口。</div>
</div>
</div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-java.lang.Enum">从类继承的嵌套类/接口&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a></h2>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">Enum.EnumDesc</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">E</a> extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.EnumDesc.html" title="java.lang中的类或接口" class="external-link">E</a>&gt;&gt;</code></div>
</section>
</li>
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<li>
<section class="constants-summary" id="enum-constant-summary">
<h2>枚举常量概要</h2>
<div class="caption"><span>枚举常量</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">枚举常量</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_BANDWIDTH" class="member-name-link">TOUPTEK_PARAM_BANDWIDTH</a></code></div>
<div class="col-last even-row-color">
<div class="block">带宽控制（影响图像处理的速度和质量）</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_BRIGHTNESS" class="member-name-link">TOUPTEK_PARAM_BRIGHTNESS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">亮度</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_COLORORGRAY" class="member-name-link">TOUPTEK_PARAM_COLORORGRAY</a></code></div>
<div class="col-last even-row-color">
<div class="block">彩色/灰度模式选择</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_COLORTONE" class="member-name-link">TOUPTEK_PARAM_COLORTONE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">色彩色调</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_CONTRAST" class="member-name-link">TOUPTEK_PARAM_CONTRAST</a></code></div>
<div class="col-last even-row-color">
<div class="block">对比度</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_CTBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_CTBLUEGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">彩色温度蓝色通道增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_CTGREENGAIN" class="member-name-link">TOUPTEK_PARAM_CTGREENGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">彩色温度绿色通道增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_CTREDGAIN" class="member-name-link">TOUPTEK_PARAM_CTREDGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">彩色温度红色通道增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_DARKENHANCE" class="member-name-link">TOUPTEK_PARAM_DARKENHANCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">暗部增强</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_DENOISE" class="member-name-link">TOUPTEK_PARAM_DENOISE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">降噪参数</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURECHOICE" class="member-name-link">TOUPTEK_PARAM_EXPOSURECHOICE</a></code></div>
<div class="col-last even-row-color">
<div class="block">曝光模式选择</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURECOMPENSATION" class="member-name-link">TOUPTEK_PARAM_EXPOSURECOMPENSATION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">曝光补偿</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSUREGAIN" class="member-name-link">TOUPTEK_PARAM_EXPOSUREGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">曝光增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_EXPOSURETIME" class="member-name-link">TOUPTEK_PARAM_EXPOSURETIME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">曝光时间</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_FLIP" class="member-name-link">TOUPTEK_PARAM_FLIP</a></code></div>
<div class="col-last even-row-color">
<div class="block">翻转效果（水平/垂直翻转）</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_GAMMA" class="member-name-link">TOUPTEK_PARAM_GAMMA</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Gamma 校正</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_HUE" class="member-name-link">TOUPTEK_PARAM_HUE</a></code></div>
<div class="col-last even-row-color">
<div class="block">色调</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_HZ" class="member-name-link">TOUPTEK_PARAM_HZ</a></code></div>
<div class="col-last odd-row-color">
<div class="block">水平分辨率/频率（具体含义取决于硬件）</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_LDCRATIO" class="member-name-link">TOUPTEK_PARAM_LDCRATIO</a></code></div>
<div class="col-last even-row-color">
<div class="block">低动态范围对比度比率</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_MIRROR" class="member-name-link">TOUPTEK_PARAM_MIRROR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">镜像效果（水平/垂直镜像）</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_SATURATION" class="member-name-link">TOUPTEK_PARAM_SATURATION</a></code></div>
<div class="col-last even-row-color">
<div class="block">饱和度</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_SHARPNESS" class="member-name-link">TOUPTEK_PARAM_SHARPNESS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">锐化参数</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_VERSION" class="member-name-link">TOUPTEK_PARAM_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">版本号</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_WBBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_WBBLUEGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">蓝色通道的白平衡增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_WBCHOICE" class="member-name-link">TOUPTEK_PARAM_WBCHOICE</a></code></div>
<div class="col-last even-row-color">
<div class="block">白平衡模式选择</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_WBGREENGAIN" class="member-name-link">TOUPTEK_PARAM_WBGREENGAIN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">绿色通道的白平衡增益</div>
</div>
<div class="col-first even-row-color"><code><a href="#TOUPTEK_PARAM_WBREDGAIN" class="member-name-link">TOUPTEK_PARAM_WBREDGAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">红色通道的白平衡增益</div>
</div>
<div class="col-first odd-row-color"><code><a href="#TOUPTEK_PARAM_WDREXPRATIO" class="member-name-link">TOUPTEK_PARAM_WDREXPRATIO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">宽动态范围曝光比率</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromInt(int)" class="member-name-link">fromInt</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr>?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAllData()" class="member-name-link">getAllData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取所有存储的数据。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getData(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取存储的数据。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getLongData(com.android.rockchip.camera2.util.TouptekIspParam)" class="member-name-link">getLongData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取存储的 long 类型数据。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getParamByIndex(int)" class="member-name-link">getParamByIndex</a><wbr>(int&nbsp;index)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">根据索引获取对应的枚举项。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#GetState()" class="member-name-link">GetState</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取当前状态标志。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getValue()" class="member-name-link">getValue</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取枚举值的整数值。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#init(android.content.Context)" class="member-name-link">init</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">初始化 SharedPreferences。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#saveData(com.android.rockchip.camera2.util.TouptekIspParam,int)" class="member-name-link">saveData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">保存 int 类型数据。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#saveLongData(com.android.rockchip.camera2.util.TouptekIspParam,long)" class="member-name-link">saveLongData</a><wbr>(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">保存 long 类型数据。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)" class="member-name-link">setOnDataChangedListener</a><wbr>(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置数据变化监听器。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#SetState(boolean)" class="member-name-link">SetState</a><wbr>(boolean&nbsp;NewFlag)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">设置状态标志。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#valueOf(java.lang.String)" class="member-name-link">valueOf</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the enum constant of this class with the specified name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#values()" class="member-name-link">values</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Enum">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" title="java.lang中的类或接口" class="external-link">Enum</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#compareTo(E)" title="java.lang中的类或接口" class="external-link">compareTo</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#describeConstable()" title="java.lang中的类或接口" class="external-link">describeConstable</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#getDeclaringClass()" title="java.lang中的类或接口" class="external-link">getDeclaringClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#name()" title="java.lang中的类或接口" class="external-link">name</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#ordinal()" title="java.lang中的类或接口" class="external-link">ordinal</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html#valueOf(java.lang.Class,java.lang.String)" title="java.lang中的类或接口" class="external-link">valueOf</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<li>
<section class="constant-details" id="enum-constant-detail">
<h2>枚举常量详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="TOUPTEK_PARAM_VERSION">
<h3>TOUPTEK_PARAM_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_VERSION</span></div>
<div class="block">版本号</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURECHOICE">
<h3>TOUPTEK_PARAM_EXPOSURECHOICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURECHOICE</span></div>
<div class="block">曝光模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURECOMPENSATION">
<h3>TOUPTEK_PARAM_EXPOSURECOMPENSATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURECOMPENSATION</span></div>
<div class="block">曝光补偿</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSURETIME">
<h3>TOUPTEK_PARAM_EXPOSURETIME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSURETIME</span></div>
<div class="block">曝光时间</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_EXPOSUREGAIN">
<h3>TOUPTEK_PARAM_EXPOSUREGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_EXPOSUREGAIN</span></div>
<div class="block">曝光增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBCHOICE">
<h3>TOUPTEK_PARAM_WBCHOICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBCHOICE</span></div>
<div class="block">白平衡模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBREDGAIN">
<h3>TOUPTEK_PARAM_WBREDGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBREDGAIN</span></div>
<div class="block">红色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBGREENGAIN">
<h3>TOUPTEK_PARAM_WBGREENGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBGREENGAIN</span></div>
<div class="block">绿色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WBBLUEGAIN">
<h3>TOUPTEK_PARAM_WBBLUEGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WBBLUEGAIN</span></div>
<div class="block">蓝色通道的白平衡增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_SHARPNESS">
<h3>TOUPTEK_PARAM_SHARPNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_SHARPNESS</span></div>
<div class="block">锐化参数</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_DENOISE">
<h3>TOUPTEK_PARAM_DENOISE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_DENOISE</span></div>
<div class="block">降噪参数</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_MIRROR">
<h3>TOUPTEK_PARAM_MIRROR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_MIRROR</span></div>
<div class="block">镜像效果（水平/垂直镜像）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_FLIP">
<h3>TOUPTEK_PARAM_FLIP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_FLIP</span></div>
<div class="block">翻转效果（水平/垂直翻转）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_SATURATION">
<h3>TOUPTEK_PARAM_SATURATION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_SATURATION</span></div>
<div class="block">饱和度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_GAMMA">
<h3>TOUPTEK_PARAM_GAMMA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_GAMMA</span></div>
<div class="block">Gamma 校正</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CONTRAST">
<h3>TOUPTEK_PARAM_CONTRAST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CONTRAST</span></div>
<div class="block">对比度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_HZ">
<h3>TOUPTEK_PARAM_HZ</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_HZ</span></div>
<div class="block">水平分辨率/频率（具体含义取决于硬件）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_BRIGHTNESS">
<h3>TOUPTEK_PARAM_BRIGHTNESS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_BRIGHTNESS</span></div>
<div class="block">亮度</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_HUE">
<h3>TOUPTEK_PARAM_HUE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_HUE</span></div>
<div class="block">色调</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_COLORORGRAY">
<h3>TOUPTEK_PARAM_COLORORGRAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_COLORORGRAY</span></div>
<div class="block">彩色/灰度模式选择</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_BANDWIDTH">
<h3>TOUPTEK_PARAM_BANDWIDTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_BANDWIDTH</span></div>
<div class="block">带宽控制（影响图像处理的速度和质量）</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_COLORTONE">
<h3>TOUPTEK_PARAM_COLORTONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_COLORTONE</span></div>
<div class="block">色彩色调</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTREDGAIN">
<h3>TOUPTEK_PARAM_CTREDGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTREDGAIN</span></div>
<div class="block">彩色温度红色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTGREENGAIN">
<h3>TOUPTEK_PARAM_CTGREENGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTGREENGAIN</span></div>
<div class="block">彩色温度绿色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_CTBLUEGAIN">
<h3>TOUPTEK_PARAM_CTBLUEGAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_CTBLUEGAIN</span></div>
<div class="block">彩色温度蓝色通道增益</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_DARKENHANCE">
<h3>TOUPTEK_PARAM_DARKENHANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_DARKENHANCE</span></div>
<div class="block">暗部增强</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_WDREXPRATIO">
<h3>TOUPTEK_PARAM_WDREXPRATIO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_WDREXPRATIO</span></div>
<div class="block">宽动态范围曝光比率</div>
</section>
</li>
<li>
<section class="detail" id="TOUPTEK_PARAM_LDCRATIO">
<h3>TOUPTEK_PARAM_LDCRATIO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">TOUPTEK_PARAM_LDCRATIO</span></div>
<div class="block">低动态范围对比度比率</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="values()">
<h3>values</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>[]</span>&nbsp;<span class="element-name">values</span>()</div>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
<dl class="notes">
<dt>返回:</dt>
<dd>an array containing the constants of this enum class, in the order they are declared</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="valueOf(java.lang.String)">
<h3>valueOf</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">valueOf</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns the enum constant of this class with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this class.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>name</code> - 要返回的枚举常量的名称。</dd>
<dt>返回:</dt>
<dd>返回带有指定名称的枚举常量</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></code> - if this enum class has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/NullPointerException.html" title="java.lang中的类或接口" class="external-link">NullPointerException</a></code> - 如果参数为空值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getValue()">
<h3>getValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getValue</span>()</div>
<div class="block">获取枚举值的整数值。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前枚举项对应的整数值。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fromInt(int)">
<h3>fromInt</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">fromInt</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
</section>
</li>
<li>
<section class="detail" id="init(android.content.Context)">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">初始化 SharedPreferences。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于获取 SharedPreferences 实例。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam.OnDataChangedListener)">
<h3>setOnDataChangedListener</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOnDataChangedListener</span><wbr><span class="parameters">(<a href="TouptekIspParam.OnDataChangedListener.html" title="com.android.rockchip.camera2.util中的接口">TouptekIspParam.OnDataChangedListener</a>&nbsp;listener)</span></div>
<div class="block">设置数据变化监听器。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 数据变化监听器实例。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveData(com.android.rockchip.camera2.util.TouptekIspParam,int)">
<h3>saveData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 int&nbsp;data)</span></div>
<div class="block">保存 int 类型数据。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要保存的参数。</dd>
<dd><code>data</code> - 要保存的 int 类型数据。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveLongData(com.android.rockchip.camera2.util.TouptekIspParam,long)">
<h3>saveLongData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveLongData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param,
 long&nbsp;data)</span></div>
<div class="block">保存 long 类型数据。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要保存的参数。</dd>
<dd><code>data</code> - 要保存的 long 类型数据。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getData(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取存储的数据。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要获取的参数。</dd>
<dt>返回:</dt>
<dd>存储的 int 类型数据，如果未找到则返回 -1。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLongData(com.android.rockchip.camera2.util.TouptekIspParam)">
<h3>getLongData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongData</span><wbr><span class="parameters">(<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a>&nbsp;param)</span></div>
<div class="block">获取存储的 long 类型数据。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>param</code> - 枚举类型 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>，表示需要获取的参数。</dd>
<dt>返回:</dt>
<dd>存储的 long 类型数据，如果未找到则返回 -1。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllData()">
<h3>getAllData</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr>?&gt;</span>&nbsp;<span class="element-name">getAllData</span>()</div>
<div class="block">获取所有存储的数据。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>包含所有存储键值对的 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link"><code>Map</code></a>，键为参数名称，值为对应的存储数据。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParamByIndex(int)">
<h3>getParamByIndex</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util">TouptekIspParam</a></span>&nbsp;<span class="element-name">getParamByIndex</span><wbr><span class="parameters">(int&nbsp;index)</span></div>
<div class="block">根据索引获取对应的枚举项。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>index</code> - 索引值，从 0 开始。</dd>
<dt>返回:</dt>
<dd>对应的 <a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a> 枚举项，如果索引无效则返回 null。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SetState(boolean)">
<h3>SetState</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">SetState</span><wbr><span class="parameters">(boolean&nbsp;NewFlag)</span></div>
<div class="block">设置状态标志。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>NewFlag</code> - 新的状态标志值，true 表示状态有效，false 表示状态无效。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GetState()">
<h3>GetState</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">GetState</span>()</div>
<div class="block">获取当前状态标志。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前状态标志值，true 表示状态有效，false 表示状态无效。</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
