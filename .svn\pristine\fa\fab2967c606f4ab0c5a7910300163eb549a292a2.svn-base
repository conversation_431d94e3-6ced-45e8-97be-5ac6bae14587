// ***************************************************************
//  usys_reactor_epoll   version:  1.0   -  date: 3/1/2012
//  -------------------------------------------------------------
//  Yong<PERSON>(<EMAIL>)
//  -------------------------------------------------------------
//  Copyright (C) 2010 - All Rights Reserved
// ***************************************************************
// 
// ***************************************************************
#ifndef usys_reactor_epoll_h__
#define usys_reactor_epoll_h__

#if defined(__linux__) && !defined(UCE_HASNOT_EPOLL)
#include <set>
#include "utiny/usys_reactor.h"
#include "utiny/usys_handler.h"
#include "usys_reactor_inc.h"

class usys_reactor_epoll : public usys_reactor
{
	friend class reactor_epoll_unregister_handler_task;

	int epfd_;
	usys_mutex handler_set_lock_;
    std::set<usys_smartptr<usys_handler> > handler_set_;
    std::vector<usys_smartptr<usys_handler> > remove_vec_;

private:
	void do_unregister_handler(const usys_smartptr<usys_handler>& handler_ptr);

public:
    usys_reactor_epoll();
    virtual ~usys_reactor_epoll();

protected:
    virtual int work(timeval* p_tv_wati);

public:
    virtual int register_handler(const usys_smartptr<usys_handler>& handler_ptr);
    virtual int unregister_handler(const usys_smartptr<usys_handler>& handler_ptr);
};

#endif

#endif // usys_reactor_epoll_h__
