[{"directory": "D:/RK3588/touptek_serial_rk/app/.cxx/Debug/6o6h6d6d/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dlibtouptek_serial_rk_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++11 -fno-limit-debug-info  -fPIC -o CMakeFiles\\libtouptek_serial_rk.dir\\native-lib.cpp.o -c D:\\RK3588\\touptek_serial_rk\\app\\src\\main\\cpp\\native-lib.cpp", "file": "D:\\RK3588\\touptek_serial_rk\\app\\src\\main\\cpp\\native-lib.cpp"}]