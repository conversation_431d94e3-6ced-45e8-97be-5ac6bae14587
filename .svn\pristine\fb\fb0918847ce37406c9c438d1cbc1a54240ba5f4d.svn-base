<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!-- Foreground is the transparent color. Without the foreground, UIs above SurfaceView
     sometimes show some artifacts. -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:foreground="@android:color/transparent"
    android:keepScreenOn="true">


    <!-- 在 activity_main.xml 中添加一个常亮按钮 -->
    <Button
    android:id="@+id/btn_test1"
    android:layout_width="200dp"
    android:layout_height="200dp"
    android:text="常亮按钮"

    android:onClick="onTestButtonClick" 
    android:layout_marginTop="100dp"
    android:layout_marginLeft="50dp"
    />

    <!--
        android:layout_centerInParent="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true" -->

</RelativeLayout>
