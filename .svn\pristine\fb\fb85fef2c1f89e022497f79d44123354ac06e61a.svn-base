package com.android.rockchip.camera2.rtsp.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.media.projection.MediaProjection;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.android.rockchip.camera2.rtsp.config.RTSPConfig;
import com.android.rockchip.camera2.rtsp.encoder.AudioEncoder;
import com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder;
import com.pedro.common.ConnectChecker;
import com.pedro.rtspserver.RtspServer;

/**
 * RTSP推流服务
 * 负责在后台运行推流功能
 */
public class RTSPService extends Service implements ConnectChecker {
    private static final String TAG = "RTSPService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "rtsp_streaming_channel";
    
    /**
     * RTSP服务器
     */
    private RtspServer rtspServer;
    
    /**
     * 视频编码器
     */
    private ScreenVideoEncoder videoEncoder;
    
    /**
     * 音频编码器
     */
    private AudioEncoder audioEncoder;
    
    /**
     * 推流配置
     */
    private RTSPConfig config;
    
    /**
     * RTSP地址
     */
    private String rtspUrl;
    
    /**
     * Binder实例，用于与客户端通信
     */
    private final RTSPBinder binder = new RTSPBinder();
    
    /**
     * 服务绑定器，用于客户端与服务通信
     */
    public class RTSPBinder extends Binder {
        /**
         * 获取服务实例
         */
        public RTSPService getService() {
            return RTSPService.this;
        }
        
        /**
         * 获取RTSP URL
         */
        public String getRtspUrl() {
            return rtspUrl;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "RTSPService created");
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "RTSPService bound");
        return binder;
    }
    
    @Override
    public boolean onUnbind(Intent intent) {
        Log.d(TAG, "RTSPService unbound");
        stopStreaming();
        return super.onUnbind(intent);
    }
    
    @Override
    public void onDestroy() {
        Log.d(TAG, "RTSPService destroyed");
        stopStreaming();
        super.onDestroy();
    }
    
    /**
     * 初始化并开始推流
     * @param config 推流配置
     * @param projectionData 屏幕捕获权限结果
     * @return RTSP URL
     */
    public String startStreaming(RTSPConfig config, ProjectionData projectionData) {
        this.config = config;
        
        // 创建前台服务通知
        startForeground();
        
        // 初始化RTSP服务器
        rtspServer = new RtspServer(this, config.getPort());
        rtspServer.setLogs(false);
        if (rtspServer != null) {
            // 设置音频参数
            rtspServer.setAudioInfo(AudioEncoder.SAMPLE_RATE, false);
            
            // 设置无认证
            rtspServer.setAuth("", "");
            
            // 启动RTSP服务器
            rtspServer.startServer();
            
            // 生成RTSP URL
            rtspUrl = "rtsp://" + rtspServer.getServerIp() + ":" + rtspServer.getPort();
            
            // 创建视频编码器
            MediaProjection mediaProjection = projectionData.getMediaProjection(this);
            
            if (mediaProjection != null) {
                // 使用建造者模式创建视频编码器
                videoEncoder = ScreenVideoEncoder.builder(mediaProjection)
                    .setResolution(config.getWidth(), config.getHeight())
                    .setFrameRate(config.getFrameRate())
                    .setBitrate(config.getVideoBitrate())
                    .setKeyFrameInterval(config.getKeyFrameInterval())
                    .build();
                
                // 使用建造者模式创建音频编码器
                audioEncoder = AudioEncoder.builder(this, mediaProjection)
                    .recordMicrophone(config.isRecordMic())
                    .build();
                
                // 开始编码和推流
                videoEncoder.startEncoding(rtspServer, audioEncoder);
                
                return rtspUrl;
            } else {
                Log.e(TAG, "Failed to get MediaProjection");
                stopSelf();
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 停止推流
     */
    public void stopStreaming() {
        Log.d(TAG, "Stopping streaming");
        
        // 停止编码器
        if (videoEncoder != null) {
            videoEncoder.stopEncoding();
            videoEncoder = null;
        }
        
        if (audioEncoder != null) {
            audioEncoder.release();
            audioEncoder = null;
        }
        
        // 停止RTSP服务器
        if (rtspServer != null) {
            rtspServer.stopServer();
            rtspServer = null;
        }
        
        rtspUrl = null;
        
        // 停止前台服务
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE);
        } else {
            stopForeground(true);
        }
        
        // 停止服务
        stopSelf();
    }
    
    /**
     * 启动前台服务
     */
    private void startForeground() {
        // 创建通知渠道（Android 8.0+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "RTSP推流服务",
                NotificationManager.IMPORTANCE_LOW
            );
            
            channel.setDescription("用于后台RTSP推流的通知");
            channel.setSound(null, null);
            channel.enableLights(false);
            channel.enableVibration(false);
            
            NotificationManager notificationManager = 
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
        
        // 创建打开应用的PendingIntent
        Intent intent = getPackageManager().getLaunchIntentForPackage(getPackageName());
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
        );
        
        // 创建通知
        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("RTSP推流")
            .setContentText(rtspUrl != null ? rtspUrl : "准备中...")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentIntent(pendingIntent)
            .setOngoing(true);
        
        Notification notification = notificationBuilder.build();
        
        // 启动前台服务
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION);
        } else {
            startForeground(NOTIFICATION_ID, notification);
        }
    }
    
    // 实现ConnectChecker接口的方法
    
    @Override
    public void onAuthError() {
        Log.e(TAG, "RTSP authentication error");
    }
    
    @Override
    public void onAuthSuccess() {
        Log.d(TAG, "RTSP authentication success");
    }
    
    @Override
    public void onConnectionFailed(String reason) {
        Log.e(TAG, "RTSP connection failed: " + reason);
    }
    
    @Override
    public void onConnectionStarted(String url) {
        Log.d(TAG, "RTSP connection started: " + url);
    }
    
    @Override
    public void onConnectionSuccess() {
        Log.d(TAG, "RTSP connection success");
    }
    
    @Override
    public void onDisconnect() {
        Log.d(TAG, "RTSP disconnected");
    }
    
    @Override
    public void onNewBitrate(long bitrate) {
        Log.d(TAG, "RTSP new bitrate: " + bitrate);
    }
}
