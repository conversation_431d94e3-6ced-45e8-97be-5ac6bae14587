// Generated by view binder compiler. Do not edit!
package com.android.rockchip.camera2.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.android.rockchip.camera2.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SettingImageFormatLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioButton radioFusionMode;

  @NonNull
  public final RadioGroup radioGroupImageFormat;

  @NonNull
  public final RadioGroup radioGroupSaveMode;

  @NonNull
  public final RadioButton radioJpeg;

  @NonNull
  public final RadioButton radioLayerMode;

  @NonNull
  public final RadioButton radioTiff;

  private SettingImageFormatLayoutBinding(@NonNull LinearLayout rootView,
      @NonNull RadioButton radioFusionMode, @NonNull RadioGroup radioGroupImageFormat,
      @NonNull RadioGroup radioGroupSaveMode, @NonNull RadioButton radioJpeg,
      @NonNull RadioButton radioLayerMode, @NonNull RadioButton radioTiff) {
    this.rootView = rootView;
    this.radioFusionMode = radioFusionMode;
    this.radioGroupImageFormat = radioGroupImageFormat;
    this.radioGroupSaveMode = radioGroupSaveMode;
    this.radioJpeg = radioJpeg;
    this.radioLayerMode = radioLayerMode;
    this.radioTiff = radioTiff;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SettingImageFormatLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SettingImageFormatLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.setting_image_format_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SettingImageFormatLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.radio_fusion_mode;
      RadioButton radioFusionMode = ViewBindings.findChildViewById(rootView, id);
      if (radioFusionMode == null) {
        break missingId;
      }

      id = R.id.radio_group_image_format;
      RadioGroup radioGroupImageFormat = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupImageFormat == null) {
        break missingId;
      }

      id = R.id.radio_group_save_mode;
      RadioGroup radioGroupSaveMode = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupSaveMode == null) {
        break missingId;
      }

      id = R.id.radio_jpeg;
      RadioButton radioJpeg = ViewBindings.findChildViewById(rootView, id);
      if (radioJpeg == null) {
        break missingId;
      }

      id = R.id.radio_layer_mode;
      RadioButton radioLayerMode = ViewBindings.findChildViewById(rootView, id);
      if (radioLayerMode == null) {
        break missingId;
      }

      id = R.id.radio_tiff;
      RadioButton radioTiff = ViewBindings.findChildViewById(rootView, id);
      if (radioTiff == null) {
        break missingId;
      }

      return new SettingImageFormatLayoutBinding((LinearLayout) rootView, radioFusionMode,
          radioGroupImageFormat, radioGroupSaveMode, radioJpeg, radioLayerMode, radioTiff);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
