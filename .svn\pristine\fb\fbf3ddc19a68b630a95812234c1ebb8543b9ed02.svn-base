package com.android.rockchip.camera2.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.app.DialogFragment;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.android.rockchip.camera2.R;
import com.android.rockchip.camera2.util.TouptekIspParam;
import com.android.rockchip.camera2.util.touptek_serial_rk;


public class TouptekDialogFragment extends DialogFragment  implements touptek_serial_rk.DeviceStateCallback{
    private static final String PREFS_NAME = "TouptekDialogPrefs";
    private touptek_serial_rk touptek_serial;
    private TextView textVersionInfo;
    private RadioGroup radioGroupWhiteBalance;
    private RadioButton radioAuto;
    private RadioButton radioManual;
    private RadioButton radioRoi;
    private final Handler handler = new Handler(Looper.getMainLooper());


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.activity_touptek, container, false);
        touptek_serial = new touptek_serial_rk();
//        touptek_serial.initializeSerial(921600);
        touptek_serial.startMonitor();
        touptek_serial.setDeviceStateCallback(this);

        TouptekIspParam.init(getContext());


//        long data = 1120250213;
//        TouptekIspParam.saveLongData(TouptekIspParam.TOUPTEK_PARAM_VERSION,data);
        // 获取存储的参数数据（调试用）
//        int exposureTime = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_EXPOSURETIME);
//        int saturation = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_SATURATION);
//
//        System.out.println("Exposure Time: " + exposureTime); // 输出: Exposure Time: 100
//        System.out.println("Saturation: " + saturation); // 输出: Saturation: 50

        // 获取所有存储的数据（调试用）
//        Map<String, ?> allData = TouptekIspParam.getAllData();
//        for (Map.Entry<String, ?> entry : allData.entrySet()) {
//            System.out.println(entry.getKey() + " : " + entry.getValue());
//        }



        sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_VERSION,0x00,0);
        System.out.println("TOUPTEK_PARAM_VERSION send successfully");
        sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_VERSION,0x00,0);
        System.out.println("TOUPTEK_PARAM_VERSION send successfully");
        // 设置曝光补偿的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_exposure_compensation, R.id.text_exposure_compensation, TouptekIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);

        // 设置曝光时间的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_exposure_time, R.id.text_exposure_time, TouptekIspParam.TOUPTEK_PARAM_EXPOSURETIME);

        // 设置曝光增益的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_gain, R.id.text_gain, TouptekIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);

        // 设置红色通道白平衡增益的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_red, R.id.text_red, TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN);

        // 设置绿色通道白平衡增益的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_green, R.id.text_green, TouptekIspParam.TOUPTEK_PARAM_WBGREENGAIN);

        // 设置蓝色通道白平衡增益的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_blue, R.id.text_blue, TouptekIspParam.TOUPTEK_PARAM_WBBLUEGAIN);

        // 设置锐化参数的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_sharpness, R.id.text_sharpness, TouptekIspParam.TOUPTEK_PARAM_SHARPNESS);

        // 设置饱和度的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_saturation, R.id.text_saturation, TouptekIspParam.TOUPTEK_PARAM_SATURATION);

        // 设置色调的SeekBar，并显示当前值
        setupSeekBar(view, R.id.seekbar_hue, R.id.text_hue, TouptekIspParam.TOUPTEK_PARAM_HUE);

        // 捕获按钮点击事件处理
        Button buttonCapture = view.findViewById(R.id.button_capture);
        buttonCapture.setOnClickListener(v -> handleCaptureButtonClick());

        @SuppressLint("UseSwitchCompatOrMaterialCode")
        Switch switchAutoExposure = view.findViewById(R.id.switch_auto_exposure);
        switchAutoExposure.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                // 自动曝光开启
                sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 0x01,1);
                TouptekIspParam.saveData(TouptekIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 1);

                // 禁用曝光补偿、曝光时间、增益的滑块
                enableSeekBar(view, R.id.seekbar_exposure_compensation);
                disableSeekBar(view, R.id.seekbar_exposure_time);
                disableSeekBar(view, R.id.seekbar_gain);
            } else {
                // 自动曝光关闭
                sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 0x01,0);
                TouptekIspParam.saveData(TouptekIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, 0);

                // 启用曝光补偿、曝光时间、增益的滑块
                disableSeekBar(view, R.id.seekbar_exposure_compensation);
                enableSeekBar(view, R.id.seekbar_exposure_time);
                enableSeekBar(view, R.id.seekbar_gain);
            }
        });

        //白平衡按键
        radioGroupWhiteBalance = view.findViewById(R.id.radio_group_white_balance);
        radioAuto = view.findViewById(R.id.radio_auto);
        radioManual = view.findViewById(R.id.radio_manual);
        radioRoi = view.findViewById(R.id.radio_roi);
        radioGroupWhiteBalance.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radio_auto) {
                // 选择自动白平衡
                // 在这里执行相关操作，比如发送命令启用自动白平衡
                sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 0x01,0);
                TouptekIspParam.saveData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 0);
                disableSeekBar(view, R.id.seekbar_blue);
                disableSeekBar(view, R.id.seekbar_green);
                disableSeekBar(view, R.id.seekbar_red);

            } else if (checkedId == R.id.radio_manual) {
                // 选择手动白平衡
                // 在这里执行相关操作，可能需要显示手动设置的控件
                sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 0x01,1);
                TouptekIspParam.saveData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 1);
                enableSeekBar(view, R.id.seekbar_blue);
                enableSeekBar(view, R.id.seekbar_green);
                enableSeekBar(view, R.id.seekbar_red);

            } else if (checkedId == R.id.radio_roi) {
                // 选择ROI白平衡
                // 在这里执行相关操作，启用ROI模式
                sendProgressToSerial(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 0x01,2);
                TouptekIspParam.saveData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE, 2);
                disableSeekBar(view, R.id.seekbar_blue);
                disableSeekBar(view, R.id.seekbar_green);
                disableSeekBar(view, R.id.seekbar_red);

            }
        });

        // 录像按钮点击事件处理
        Button buttonRecord = view.findViewById(R.id.button_record);
        buttonRecord.setOnClickListener(v -> handleRecordButtonClick());

        textVersionInfo = view.findViewById(R.id.text_version_info);


        // 场景下拉框处理
        Spinner spinnerScene = view.findViewById(R.id.spinner_scene);
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(getActivity(),
                R.array.scene_options, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerScene.setAdapter(adapter);
        spinnerScene.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selectedScene = (String) parent.getItemAtPosition(position);
                handleSceneSelection(selectedScene);
                savePreference("scene", selectedScene);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 处理没有选择任何选项的情况
            }
        });

        // 恢复存储的设置
        loadPreferences(view);
        TouptekIspParam.setOnDataChangedListener(new TouptekIspParam.OnDataChangedListener() {
            @Override
            public void onDataChanged(TouptekIspParam param, int newValue) {
                handler.post(() -> {
                    updateSeekBar(param, newValue); // 调用更新方法
                });
            }

            @Override
            public void onLongDataChanged(TouptekIspParam param, long newValue) {
                System.out.println("参数 " + param.name() + " 更新为: " + newValue);
            }
        });


        return view;
    }

    private void setupSeekBar(View view, int seekBarId, int textViewId, TouptekIspParam param) {
        SeekBar seekBar = view.findViewById(seekBarId);
        TextView textView = view.findViewById(textViewId);

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // 只处理用户滑动触发的变化
                if (fromUser) {
                    // 更新 TextView
                    textView.setText(String.valueOf(progress));
                    // 保存当前滑块值
                    TouptekIspParam.saveData(TouptekIspParam.valueOf(param.name()), progress);
                    // 发送数据到串口
                    sendProgressToSerial(param, 0x01, progress);
                    System.out.println("set!");
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // 可在此处添加触摸开始时的逻辑
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // 可在此处添加触摸结束时的逻辑
            }
        });
    }



    private void sendProgressToSerial(TouptekIspParam param, int ctrl,int progress) {
        //

        int[] data = new int[4];

        // 将进度值拆解为4个字节
        data[3] = (byte) (progress & 0xFF);               // 获取最低字节
        data[2] = (byte) ((progress >> 8) & 0xFF);         // 获取第二个字节
        data[1] = (byte) ((progress >> 16) & 0xFF);        // 获取第三个字节
        data[0] = (byte) ((progress >> 24) & 0xFF);        // 获取最高字节

        //         发送拆解后的数据到串口
        touptek_serial.sendCommandToSerial(ctrl, param.getValue(), data);
    }

    @Override
    public void onStart() {
        super.onStart();

        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                // 关闭屏幕界面焦点显示
                window.setDimAmount(0f);
                WindowManager.LayoutParams params = window.getAttributes();
                // 获取屏幕宽度
                WindowManager wm = getActivity().getWindowManager();
                Display display = wm.getDefaultDisplay();
                Point size = new Point();
                display.getSize(size);
                int screenWidth = size.x;
                int screenHeight = size.y;

                // 计算对话框的宽度为屏幕宽度的一半
                params.width = screenWidth / 3;
                // 这里将对话框的高度设置为屏幕高度，你可以根据实际需求调整
                params.height = screenHeight - 20;
                // 将对话框定位在屏幕的左侧
                params.gravity = Gravity.LEFT;

                window.setAttributes(params);
            }
        }
        updateSerialStatus(); // 确保对话框启动后调用
    }

    // 处理捕获按钮点击事件
    private void handleCaptureButtonClick() {
        // 在这里添加捕获按钮点击时的操作，例如开始捕获图像
        Toast.makeText(getActivity(), "开始捕获图像", Toast.LENGTH_SHORT).show();
    }

    // 处理录像按钮点击事件
    private void handleRecordButtonClick() {
        // 在这里添加录像按钮点击时的操作，例如开始录像
        Toast.makeText(getActivity(), "开始录像", Toast.LENGTH_SHORT).show();
    }

    // 处理场景选择事件
    private void handleSceneSelection(String selectedScene) {
        // 在这里添加场景选择时的操作，例如根据选择的场景进行不同的配置
        Toast.makeText(getActivity(), "选择的场景：" + selectedScene, Toast.LENGTH_SHORT).show();
    }

    private void savePreference(String key, int value) {
        SharedPreferences.Editor editor = getActivity().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE).edit();
        editor.putInt(key, value);
        editor.apply();
    }

    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = getActivity().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE).edit();
        editor.putString(key, value);
        editor.apply();
    }

    private void loadPreferences(View view) {
        // 直接使用 TouptekIspParam.getData 来设置 SeekBar 的进度
        setSeekBarProgress(view, R.id.seekbar_exposure_compensation, R.id.text_exposure_compensation, TouptekIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
        setSeekBarProgress(view, R.id.seekbar_exposure_time, R.id.text_exposure_time, TouptekIspParam.TOUPTEK_PARAM_EXPOSURETIME);
        setSeekBarProgress(view, R.id.seekbar_gain, R.id.text_gain, TouptekIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
        setSeekBarProgress(view, R.id.seekbar_red, R.id.text_red, TouptekIspParam.TOUPTEK_PARAM_WBREDGAIN);
        setSeekBarProgress(view, R.id.seekbar_green, R.id.text_green, TouptekIspParam.TOUPTEK_PARAM_WBGREENGAIN);
        setSeekBarProgress(view, R.id.seekbar_blue, R.id.text_blue, TouptekIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
        setSeekBarProgress(view, R.id.seekbar_sharpness, R.id.text_sharpness, TouptekIspParam.TOUPTEK_PARAM_SHARPNESS);
        setSeekBarProgress(view, R.id.seekbar_saturation, R.id.text_saturation, TouptekIspParam.TOUPTEK_PARAM_SATURATION);
        setSeekBarProgress(view, R.id.seekbar_hue, R.id.text_hue, TouptekIspParam.TOUPTEK_PARAM_HUE);

        // 更新自动曝光 Switch
        @SuppressLint("UseSwitchCompatOrMaterialCode") Switch switchAutoExposure = view.findViewById(R.id.switch_auto_exposure);
        int autoExposureValue = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_EXPOSURECHOICE);
        switchAutoExposure.setChecked(autoExposureValue == 1); // 1 表示开启，0 表示关闭


        // 假设 TouptekIspParam 提供 getVersion() 方法获取版本号
        long version_time = TouptekIspParam.getLongData(TouptekIspParam.TOUPTEK_PARAM_VERSION);
        long temp = version_time >> 32;
        int version_1 = (int)temp / 10;
        long version_2 = (int)temp % 10;
        long time = (int)version_time;
        textVersionInfo.setText("版本信息: V" + version_1 +"."+version_2+"_" + time);

        int wbChoice = TouptekIspParam.getData(TouptekIspParam.TOUPTEK_PARAM_WBCHOICE);
        // 根据读取的值设置默认白平衡模式
        switch (wbChoice) {
            case 0: // 自动白平衡
                radioAuto.setChecked(true);
                disableSeekBar(view, R.id.seekbar_red);
                disableSeekBar(view, R.id.seekbar_green);
                disableSeekBar(view, R.id.seekbar_blue);
                break;
            case 1: // 手动白平衡
                radioManual.setChecked(true);
                enableSeekBar(view, R.id.seekbar_red);
                enableSeekBar(view, R.id.seekbar_green);
                enableSeekBar(view, R.id.seekbar_blue);
                break;
            case 2: // ROI白平衡
                radioRoi.setChecked(true);
                disableSeekBar(view, R.id.seekbar_red);
                disableSeekBar(view, R.id.seekbar_green);
                disableSeekBar(view, R.id.seekbar_blue);
                break;
            default:
                // 默认选择自动白平衡
                radioAuto.setChecked(true);
                disableSeekBar(view, R.id.seekbar_red);
                disableSeekBar(view, R.id.seekbar_green);
                disableSeekBar(view, R.id.seekbar_blue);
                break;
        }
    }


    private void setSeekBarProgress(View view, int seekBarId, int textViewId, TouptekIspParam param) {
        SeekBar seekBar = view.findViewById(seekBarId);
        TextView textView = view.findViewById(textViewId);

        // 直接从 TouptekIspParam 获取数据，而不是从 SharedPreferences
        int value = TouptekIspParam.getData(param); // 获取保存的进度值

        seekBar.setProgress(value);  // 设置 SeekBar 的进度
        textView.setText(String.valueOf(value)); // 设置 TextView 显示当前值
    }
    private void disableSeekBar(View view, int seekBarId) {
        SeekBar seekBar = view.findViewById(seekBarId);
        seekBar.setEnabled(false);
    }

    private void enableSeekBar(View view, int seekBarId) {
        SeekBar seekBar = view.findViewById(seekBarId);
        seekBar.setEnabled(true);
    }

    private void updateSeekBar(TouptekIspParam param, int newValue) {
        if (getView() == null) return; // 避免空指针

        int seekBarId = -1;
        int textViewId = -1;

        switch (param) {
            case TOUPTEK_PARAM_EXPOSURECOMPENSATION:
                seekBarId = R.id.seekbar_exposure_compensation;
                textViewId = R.id.text_exposure_compensation;
                break;
            case TOUPTEK_PARAM_EXPOSURETIME:
                seekBarId = R.id.seekbar_exposure_time;
                textViewId = R.id.text_exposure_time;
                break;
            case TOUPTEK_PARAM_EXPOSUREGAIN:
                seekBarId = R.id.seekbar_gain;
                textViewId = R.id.text_gain;
                break;
            case TOUPTEK_PARAM_WBREDGAIN:
                seekBarId = R.id.seekbar_red;
                textViewId = R.id.text_red;
                break;
            case TOUPTEK_PARAM_WBGREENGAIN:
                seekBarId = R.id.seekbar_green;
                textViewId = R.id.text_green;
                break;
            case TOUPTEK_PARAM_WBBLUEGAIN:
                seekBarId = R.id.seekbar_blue;
                textViewId = R.id.text_blue;
                break;
            case TOUPTEK_PARAM_SHARPNESS:
                seekBarId = R.id.seekbar_sharpness;
                textViewId = R.id.text_sharpness;
                break;
            case TOUPTEK_PARAM_SATURATION:
                seekBarId = R.id.seekbar_saturation;
                textViewId = R.id.text_saturation;
                break;
            case TOUPTEK_PARAM_HUE:
                seekBarId = R.id.seekbar_hue;
                textViewId = R.id.text_hue;
                break;
            default:
                return;
        }

        SeekBar seekBar = getView().findViewById(seekBarId);
        TextView textView = getView().findViewById(textViewId);

        if (seekBar != null && textView != null) {
            seekBar.setProgress(newValue);
            textView.setText(String.valueOf(newValue));
        }
    }

    @Override
    public void onDeviceStateChanged(boolean connected) {
        String message = connected ? "设备已连接" : "设备已断开";
        Log.d("SerialMonitor", message);

        // 切换到主线程更新 UI
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                TextView textSerialStatus = getView().findViewById(R.id.text_serial_status);
                if (textSerialStatus != null) {
                    textSerialStatus.setText("串口状态：" + (connected ? "已连接" : "已断开"));
                }
                // 也可以弹出 Toast 提示
                Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
            });
        }
    }

    private void updateSerialStatus() {
        boolean isConnected = touptek_serial.isSerialConnected();

        // 确保 Fragment 仍然附着到 Activity
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (getView() != null) {
                    TextView textSerialStatus = getView().findViewById(R.id.text_serial_status);
                    if (textSerialStatus != null) {
                        textSerialStatus.setText("串口状态：" + (isConnected ? "已连接" : "已断开"));
                    }
                }
            });
        }
    }

}
