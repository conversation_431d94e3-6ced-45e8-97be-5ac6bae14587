<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>TpNetworkMonitor.NetworkStateListener</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.utils, class: TpNetworkMonitor, interface: NetworkStateListener">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.touptek.utils</a></div>
<h1 title="接口 TpNetworkMonitor.NetworkStateListener" class="title">接口 TpNetworkMonitor.NetworkStateListener</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>封闭类:</dt>
<dd><a href="TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static interface </span><span class="element-name type-name-label">TpNetworkMonitor.NetworkStateListener</span></div>
<div class="block">网络状态监听器接口
 <p>
 用于接收WiFi、以太网和热点状态变化的回调通知。
 实现此接口可以在UI上实时显示网络状态变化。
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">抽象方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#onEthernetStateChanged(boolean)" class="member-name-link">onEthernetStateChanged</a><wbr>(boolean&nbsp;isConnected)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">当以太网状态发生变化时调用</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#onHotspotStateChanged(boolean,java.lang.String)" class="member-name-link">onHotspotStateChanged</a><wbr>(boolean&nbsp;isEnabled,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;hotspotInfo)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">当热点状态发生变化时调用</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#onWifiStateChanged(boolean,java.lang.String)" class="member-name-link">onWifiStateChanged</a><wbr>(boolean&nbsp;isConnected,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;ssid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">当WiFi状态发生变化时调用</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onWifiStateChanged(boolean,java.lang.String)">
<h3>onWifiStateChanged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">onWifiStateChanged</span><wbr><span class="parameters">(boolean&nbsp;isConnected,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;ssid)</span></div>
<div class="block">当WiFi状态发生变化时调用</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>isConnected</code> - 是否已连接到WiFi网络</dd>
<dd><code>ssid</code> - 当前连接的WiFi网络名称，如果未连接则为空字符串</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onEthernetStateChanged(boolean)">
<h3>onEthernetStateChanged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">onEthernetStateChanged</span><wbr><span class="parameters">(boolean&nbsp;isConnected)</span></div>
<div class="block">当以太网状态发生变化时调用</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>isConnected</code> - 是否已连接到以太网</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onHotspotStateChanged(boolean,java.lang.String)">
<h3>onHotspotStateChanged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">onHotspotStateChanged</span><wbr><span class="parameters">(boolean&nbsp;isEnabled,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;hotspotInfo)</span></div>
<div class="block">当热点状态发生变化时调用</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>isEnabled</code> - 热点是否已启用</dd>
<dd><code>hotspotInfo</code> - 热点信息，包含SSID和密码等</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
