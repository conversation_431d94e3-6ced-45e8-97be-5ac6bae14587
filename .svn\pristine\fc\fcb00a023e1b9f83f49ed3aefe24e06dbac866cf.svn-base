package com.android.rockchip.camera2.activity

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.graphics.SurfaceTexture
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.widget.Button
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.util.TransformUtils
import com.android.rockchip.camera2.video.VideoDecoder

class TpVideoDecoderActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "VideoDecoder"
    }

    private lateinit var textureView: TextureView
    private lateinit var seekBar: SeekBar
    private lateinit var playPauseButton: Button
    private lateinit var frameByFrameButton: Button
    private lateinit var fastForwardButton: Button
    private lateinit var fastBackwardButton: Button
    private lateinit var tvDuration: TextView
    private lateinit var tvCurrentPosition: TextView

    private var videoDecoder: VideoDecoder? = null
    private val handler = Handler()
    private val updateSeekBarRunnable = object : Runnable {
        override fun run() {
            videoDecoder?.let {
                if (it.isDecoding) {
                    val currentPosition = it.currentPosition
                    val duration = it.videoDuration
                    seekBar.progress = (currentPosition * 100 / duration).toInt()
                    tvCurrentPosition.text = "当前时长: ${formatTime(currentPosition)}"
                    handler.postDelayed(this, 1000)
                }
            }
        }
    }

    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var gestureDetector: GestureDetector? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.video_decode)

        textureView = findViewById(R.id.surface_view)
        seekBar = findViewById(R.id.seek_bar)
        playPauseButton = findViewById(R.id.btn_play_pause)
        frameByFrameButton = findViewById(R.id.btn_step_decode)
        fastForwardButton = findViewById(R.id.btn_fast_forward)
        fastBackwardButton = findViewById(R.id.btn_fast_backward)
        tvDuration = findViewById(R.id.tv_duration)
        tvCurrentPosition = findViewById(R.id.tv_current_position)

        requestStoragePermission()
        val context = this

        textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                Log.d(TAG, "SurfaceTexture Available")
                var videoPath = intent.getStringExtra("videoPath") ?: FileStorageUtils.createVideoPath(context)
                videoDecoder = VideoDecoder(videoPath, Surface(surface)).apply {
                    startDecoding()
                }
                handler.post(updateSeekBarRunnable)
                updatePlayButton(true)
                tvDuration.text = "总时长: ${formatTime(videoDecoder?.videoDuration ?: 0)}"
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                videoDecoder?.stopDecoding()
                handler.removeCallbacks(updateSeekBarRunnable)
                return true
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    videoDecoder?.let {
                        val videoDuration = it.videoDuration
                        it.seekTo(videoDuration * progress / 100)
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {}
            override fun onStopTrackingTouch(seekBar: SeekBar) {}
        })

        playPauseButton.setOnClickListener {
            videoDecoder?.let {
                it.togglePlayPause()
                updatePlayButton(!it.isPaused)
                if (!it.isPaused) {
                    handler.post(updateSeekBarRunnable)
                }
            }
        }

        frameByFrameButton.setOnClickListener {
            videoDecoder?.let {
                it.stepFrame()
                updatePlayButton(false)
            }
        }

        fastForwardButton.setOnClickListener {
            videoDecoder?.seekRelative(5000)
        }

        fastBackwardButton.setOnClickListener {
            videoDecoder?.seekRelative(-5000)
        }

        initScaleGestureDetector()
        initPanGestureDetector()

        textureView.setOnTouchListener { _, event ->
            val scaleHandled = scaleGestureDetector?.onTouchEvent(event) ?: false
            val panHandled = gestureDetector?.onTouchEvent(event) ?: false
            scaleHandled || panHandled
        }
    }

    private fun initScaleGestureDetector() {
        scaleGestureDetector = ScaleGestureDetector(this, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                val focusX = detector.focusX
                val focusY = detector.focusY
                TransformUtils.applyZoom(textureView, scaleFactor, focusX, focusY)
                return true
            }
        })
    }

    private fun initPanGestureDetector() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
//            fun onScroll(e1: MotionEvent?, e2: MotionEvent?, distanceX: Float, distanceY: Float): Boolean {
//                TransformUtils.applyPan(textureView, -distanceX, -distanceY)
//                return true
//            }
        })
    }

    private fun requestStoragePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE), 1)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 1 && grantResults.isNotEmpty() && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Storage permission denied")
            finish()
        }
    }

    private fun updatePlayButton(playing: Boolean) {
        playPauseButton.text = if (playing) "Pause" else "Play"
    }

    override fun onDestroy() {
        super.onDestroy()
        videoDecoder?.stopDecoding()
        handler.removeCallbacks(updateSeekBarRunnable)
    }

    private fun formatTime(timeUs: Long): String {
        val totalSeconds = timeUs / 1_000_000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
}
