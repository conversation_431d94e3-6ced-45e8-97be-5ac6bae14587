#ifndef __rtsp_session_context_h__
#define __rtsp_session_context_h__

#include <utiny/utiny_config.h>
#include <utiny/usys_smartptr.h>
#include <utiny/usys_transceiver_tcp.h>
#include <utiny/usys_safemap.h>
#include <utiny/usys_data_block.h>
#include <utiny/usys_reactor.h>
#include <utiny/usys_atomic.h>
#include <utiny/usys_mutex.h>
#include <utiny/rtsp_header.h>
#include <utiny/usys_data_block.h>

class rtsp_packet_context;
class rtsp_response_context;
class rtsp_request_context;

class UBEDA_API rtsp_session_context : virtual public usys_smartptr_mtbase
{
protected:
    usys_safemap<int, usys_smartptr_mtbase_ptr> response_ctx_map_;
public:
    rtsp_session_context();
    virtual ~rtsp_session_context();

	virtual int proc_packet(const usys_smartptr<rtsp_packet_context>& packetptr, const usys_smartptr<usys_data_block>& dataptr) = 0;

    virtual usys_smartptr<usys_transceiver_tcp> handle_accept(SOCKET fd);
    virtual int handle_connect(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
    virtual int handle_write(const usys_smartptr<usys_transceiver>& transceiver_ptr) { return 0; };
    virtual void handle_close(const usys_smartptr<usys_transceiver>& transceiver_ptr) { }
	virtual int handle_read_datablock(const usys_smartptr<usys_data_block>& req_ptr, const usys_smartptr_mtbase_ptr& header_ptr, const usys_smartptr<usys_transceiver>& transceiver_ptr);
public:
	int proc_request_exception(const usys_smartptr<rtsp_response_context>& res_ptr);

	int request(usys_smartptr<usys_data_block>& mb, const usys_smartptr<rtsp_response_context>& response_ptr, int timeout, bool oneway);
	int response(usys_smartptr<usys_data_block>& mb, const usys_smartptr<rtsp_request_context>& request_ctx);
private:
    int fetch_response(int sequence_id, usys_smartptr<rtsp_response_context>& res_ptr);
    int remove_response(int sequence_id);
};

typedef usys_smartptr<rtsp_session_context> rtsp_session_context_ptr;

#endif // __rtsp_session_context_h__
