package com.android.rockchip.camera2.activity.ispdialogfragment.wbroimanagement

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View

class TpRectangleOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val rectPaint = Paint().apply {
        color = Color.RED
        style = Paint.Style.STROKE
        strokeWidth = 5f
    }

    private val cornerPaint = Paint().apply {
        color = Color.GREEN
        style = Paint.Style.FILL
    }

    private val cornerRadius = 10f
    private var rect = RectF(0f, 0f, 200f, 120f)
    private var isDragging = false
    private var lastX = 0f
    private var lastY = 0f
    private var activeCorner: Corner? = null

    private enum class Corner {
        TOP_LEFT, TOP_RIGHT, BOTTOM_RIGHT, BOTTOM_LEFT
    }

    var onPositionChanged: ((RectF) -> Unit)? = null

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 初始化矩形位置，使其居中
        val rectWidth = 200f
        val rectHeight = 120f
        val left = (w - rectWidth) / 2
        val top = (h - rectHeight) / 2
        rect.set(left, top, left + rectWidth, top + rectHeight)
    }

    fun setRectanglePosition(left: Float, top: Float, right: Float, bottom: Float) {
        rect.set(left, top, right, bottom)
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制矩形
        canvas.drawRect(rect, rectPaint)

        // 绘制四个角的圆点
        canvas.drawCircle(rect.left, rect.top, cornerRadius, cornerPaint)
        canvas.drawCircle(rect.right, rect.top, cornerRadius, cornerPaint)
        canvas.drawCircle(rect.right, rect.bottom, cornerRadius, cornerPaint)
        canvas.drawCircle(rect.left, rect.bottom, cornerRadius, cornerPaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        var handled = false

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 检查是否点击了某个角或边框
                activeCorner = getCornerAtPoint(x, y)
                isDragging = activeCorner == null && isPointOnBorder(x, y)

                if (activeCorner != null || isDragging) {
                    lastX = x
                    lastY = y
                    handled = true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging || activeCorner != null) {
                    val dx = x - lastX
                    val dy = y - lastY

                    when (activeCorner) {
                        Corner.TOP_LEFT -> {
                            rect.left += dx
                            rect.top += dy
                        }
                        Corner.TOP_RIGHT -> {
                            rect.right += dx
                            rect.top += dy
                        }
                        Corner.BOTTOM_RIGHT -> {
                            rect.right += dx
                            rect.bottom += dy
                        }
                        Corner.BOTTOM_LEFT -> {
                            rect.left += dx
                            rect.bottom += dy
                        }
                        else -> {
                            // 拖动整个矩形
                            rect.offset(dx, dy)
                        }
                    }

                    // 确保矩形不会超出视图边界
                    constrainRectangleToBounds()

                    lastX = x
                    lastY = y
                    invalidate()
                    onPositionChanged?.invoke(rect)
                    handled = true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging || activeCorner != null) {
                    isDragging = false
                    activeCorner = null
                    handled = true
                }
            }
        }

        // 如果处理了事件，返回true；否则返回false让父视图处理
        return handled || super.onTouchEvent(event)
    }

    private fun getCornerAtPoint(x: Float, y: Float): Corner? {
        val corners = listOf(
            Pair(PointF(rect.left, rect.top), Corner.TOP_LEFT),
            Pair(PointF(rect.right, rect.top), Corner.TOP_RIGHT),
            Pair(PointF(rect.right, rect.bottom), Corner.BOTTOM_RIGHT),
            Pair(PointF(rect.left, rect.bottom), Corner.BOTTOM_LEFT)
        )

        for ((point, corner) in corners) {
            if (point.distanceTo(PointF(x, y)) <= cornerRadius * 2) {
                return corner
            }
        }
        return null
    }

    private fun isPointOnBorder(x: Float, y: Float): Boolean {
        // 检查是否在矩形边框上（简化版，实际可能需要更精确的检测）
        return (x >= rect.left - 10 && x <= rect.right + 10 &&
                (y >= rect.top - 10 && y <= rect.top + 10 || y >= rect.bottom - 10 && y <= rect.bottom + 10)) ||
                (y >= rect.top - 10 && y <= rect.bottom + 10 &&
                        (x >= rect.left - 10 && x <= rect.left + 10 || x >= rect.right - 10 && x <= rect.right + 10))
    }

    private fun PointF.distanceTo(other: PointF): Float {
        return Math.sqrt(Math.pow((x - other.x).toDouble(), 2.0) + Math.pow((y - other.y).toDouble(), 2.0)).toFloat()
    }

    private fun constrainRectangleToBounds() {
        val minSize = cornerRadius * 4 // 最小尺寸

        // 确保宽度和高度不小于最小值
        //
        if (rect.width() < minSize) {
            when (activeCorner) {
                Corner.TOP_LEFT, Corner.BOTTOM_LEFT -> rect.left = rect.right - minSize
                else -> rect.right = rect.left + minSize
            }
        }

        if (rect.height() < minSize) {
            when (activeCorner) {
                Corner.TOP_LEFT, Corner.TOP_RIGHT -> rect.top = rect.bottom - minSize
                else -> rect.bottom = rect.top + minSize
            }
        }

        // 确保不超出视图边界
        val padding = cornerRadius
        rect.left = rect.left.coerceAtLeast(padding)
        rect.top = rect.top.coerceAtLeast(padding)
        rect.right = rect.right.coerceAtMost(width - padding)
        rect.bottom = rect.bottom.coerceAtMost(height - padding)
    }
}