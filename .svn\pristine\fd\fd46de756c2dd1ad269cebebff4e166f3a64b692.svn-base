package com.android.rockchip.camera2.integrated;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;

import com.touptek.test.isp.TpTestDialog;
import com.touptek.ui.TpTextureView;
import com.touptek.ui.TpRoiView;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

// StreamingService现在通过TpVideoSystem管理，无需直接导入
import com.android.rockchip.camera2.integrated.settings.TpSettingsDialog;
import com.touptek.utils.TpHdmiMonitor;
import com.touptek.utils.TpSambaClient;
import com.touptek.utils.TpFileManager;
import com.touptek.video.TpIspParam;
import com.touptek.video.TpVideoSystem;
import com.touptek.video.TpVideoConfig;

import com.android.rockchip.mediacodecnew.R;

import java.util.Objects;

/**
 * MainActivity - 使用TpVideoSystem统一入口的示例
 * <p>
 * 此Activity演示如何使用TpVideoSystem类来简化相机和视频操作，
 * 功能包括视频预览、录制和图像捕获。基于VideoEncoderActivity的成功实现，确保1080P 60fps性能。
 * 支持TpVideoConfig配置系统，提供灵活的视频参数配置。
 * </p>
 *
 * <p><b>ISP场景管理测试功能：</b></p>
 * <ul>
 *   <li><b>长按设置按钮</b> - 触发ISP场景管理功能测试</li>
 *   <li>自动保存当前ISP参数为新场景</li>
 *   <li>显示所有已保存的场景列表</li>
 *   <li>测试场景应用功能</li>
 * </ul>
 *
 * <p><b>使用方法：</b></p>
 * <pre>
 * 1. 启动应用，等待相机预览正常显示
 * 2. 调整ISP参数（亮度、对比度等）到满意的效果
 * 3. 长按设置按钮，触发场景管理测试
 * 4. 观察日志输出和Toast提示，验证功能正常
 * </pre>
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    // UI组件
    private TpTextureView mTextureView;
    private TpRoiView mRoiView;
    private Button mCaptureButton;
    private Button mRecordButton;
    private Button mSettingsButton;
    private Button mResolutionTestButton;
    private Button mBrowserButton;


    private ViewGroup mPreviewContainer;

    // 测试功能组件
    private Button mTestButton;

    // 视频系统
    private TpVideoSystem videoSystem;

    // HDMI服务
    private TpHdmiMonitor tpHdmiMonitor;

    // SMB文件上传器
    private TpSambaClient tpSambaClient;

    // 延迟处理器
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    // 状态管理
    private boolean isHdmiConnected = false;

    // WiFi设置启动器
    private ActivityResultLauncher<Intent> wifiPanelLauncher;

    // 流媒体状态现在通过TpVideoSystem管理，无需单独维护
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
        WindowManager.LayoutParams.FLAG_FULLSCREEN);
        
        // 初始化TouptekIspParam，确保SharedPreferences被正确初始化
        TpIspParam.init(this);

//        TpIspParam.requestAllParamRanges();
//        TpIspParam.saveAllDefaultValuesToLocal(true);
//        TpIspParam.syncAllCurrentValuesToDevice();
//        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_ISP_DEFAULT_TYPE,0);
        TpIspParam.setOnSerialStateChangedListener(
            connected -> {
                Log.d(TAG, "串口连接状态变化: " + (connected ? "已连接" : "已断开"));
                if (connected) {
                    // 延迟3秒后请求所有参数范围
                    mHandler.postDelayed(() -> {
                        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE,1);
                        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN, 4095);
                    }, 500);
                }
            }
        );






        // 初始化WiFi设置启动器
        initWifiPanelLauncher();

        // 初始化UI组件
        mPreviewContainer = findViewById(R.id.preview_container);
        mTextureView = findViewById(R.id.textureView);
        mRoiView = findViewById(R.id.roiView);
        mCaptureButton = findViewById(R.id.captureButton);
        mRecordButton = findViewById(R.id.recordButton);
        mSettingsButton = findViewById(R.id.settingsButton);
        mResolutionTestButton = findViewById(R.id.resolutionTestButton);
        mBrowserButton = findViewById(R.id.browserButton);


        // 初始化测试功能按钮
        mTestButton = findViewById(R.id.test_button);

        // 设置按钮点击事件
        setupButtonListeners();

        // 初始化HDMI服务
        initHdmiService();

        // 初始化SMB文件上传器
        tpSambaClient = new TpSambaClient(this);

        // 创建TpVideoSystem
        TpVideoConfig config = TpVideoConfig.createDefault4K();
        videoSystem = new TpVideoSystem(this, config);

        // 为TpVideoSystem设置TV模式支持
        setupTpVideoSystemTvMode();

        // 配置TpZoomableTextureView
        setupZoomableTextureView();

        // 初始化ROI视图
        initRoiView();

        // 当TextureView准备好后，初始化TpVideoSystem
        mTextureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surfaceTexture, int width, int height) {
                // 创建用于相机预览的Surface
                Surface previewSurface = new Surface(surfaceTexture);

                // 完成TpVideoSystem初始化（TpVideoSystem已在onCreate中创建）
                completeVideoSystemInit(previewSurface);

                // 检查相机权限
                if (checkCameraPermission()) {
                    // 权限已有，直接初始化
                    videoSystem.initialize(previewSurface);
                } else {
                    requestCameraPermission();
                }
            }

            @Override
            public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surfaceTexture, int width, int height) {
                // 处理Surface尺寸变化
            }

            @Override
            public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surfaceTexture) {
                // 释放资源
                if (videoSystem != null) {
                    videoSystem.release();
                }
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surfaceTexture) {
                // 当纹理更新时
            }
        });
    }

    /**
     * 初始化WiFi设置启动器
     */
    private void initWifiPanelLauncher() {
        wifiPanelLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                Log.d(TAG, "WiFi设置返回，结果码: " + result.getResultCode());
                // WiFi设置返回后的处理逻辑可以在这里添加
            }
        );
    }

    /**
     * 配置TpZoomableTextureView
     */
    private void setupZoomableTextureView() {
        if (mTextureView != null) {
            // 启用缩放和平移功能
            mTextureView.setZoomEnabled(true);
            mTextureView.setPanEnabled(true);
            mTextureView.setDoubleTapEnabled(true);

            // 设置最大缩放倍数
            mTextureView.setMaxScale(5.0f);

            // 设置缩放变化监听器（可选）
            mTextureView.setOnZoomChangeListener(new TpTextureView.OnZoomChangeListener() {
                @Override
                public void onZoomChanged(float scale, float focusX, float focusY) {
                    Log.d(TAG, "TextureView缩放变化: " + scale + ", 焦点: (" + focusX + ", " + focusY + ")");
                    // ROI框会自动跟随缩放变换（由TpRoiView内部管理）
                }
            });

            Log.d(TAG, "TpZoomableTextureView配置完成");
        } else {
            Log.e(TAG, "TextureView未找到，无法配置缩放功能");
        }
    }

    /**
     * 初始化ROI视图
     */
    private void initRoiView() {
        if (mRoiView != null && mTextureView != null) {
            // 设置ROI视图与TextureView的关联，确保变换同步
            mTextureView.setRoiView(mRoiView);

            // ROI视图现在内部智能处理变换，无需额外配置
            Log.d(TAG, "ROI视图初始化完成，已与TpTextureView建立变换同步");
        } else {
            Log.e(TAG, "ROI视图或TextureView未找到，无法初始化ROI功能");
        }
    }

    /**
     * 为TpVideoSystem设置TV模式支持
     */
    private void setupTpVideoSystemTvMode() {
        if (mPreviewContainer != null && videoSystem != null) {
            // 设置TV容器
            videoSystem.setTvContainer(mPreviewContainer);
            Log.d(TAG, "TpVideoSystem TV容器已设置");

            // 设置视频系统监听器
            videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "系统错误: " + errorMessage);
                    runOnUiThread(() -> {
                        Toast.makeText(MainActivity.this, errorMessage, Toast.LENGTH_SHORT).show();
                    });
                }
            });
        } else {
            Log.e(TAG, "预览容器或视频系统未找到，无法设置TV模式");
        }
    }
    
    /**
     * 初始化HDMI服务
     */
    private void initHdmiService() {
        tpHdmiMonitor = TpHdmiMonitor.getInstance();
        tpHdmiMonitor.setHdmiListener(this::handleHdmiStatusChanged);
        tpHdmiMonitor.init();
        Log.d(TAG, "HDMI服务已初始化");
    }
    
    /**
     * 处理HDMI状态变化
     * 
     * @param isConnected HDMI连接状态
     */
    private void handleHdmiStatusChanged(boolean isConnected) {
        Log.d(TAG, "HDMI状态变化: " + (isConnected ? "已连接" : "已断开"));
        
        // 如果状态没有变化，不处理
        if (isHdmiConnected == isConnected) {
            return;
        }
        
        isHdmiConnected = isConnected;
        
        // 使用延迟处理，确保状态稳定
        mHandler.postDelayed(() -> {
            if (isConnected) {
                handleHdmiConnected();
            } else {
                handleHdmiDisconnected();
            }
        }, 500); // 延迟500毫秒处理
    }
    
    /**
     * 处理HDMI连接事件
     */
    private void handleHdmiConnected() {
        Log.d(TAG, "处理HDMI连接");
        runOnUiThread(() -> {
            Toast.makeText(this, "HDMI已连接", Toast.LENGTH_SHORT).show();

            if (videoSystem != null) {
                if (videoSystem.isTvMode()) {
                    // TV模式：重新启动TV预览（通过TpVideoSystem的TV预览助手）
                    try {
                        // 先切换到Camera模式再切换回TV模式以重新初始化
                        videoSystem.switchToCameraMode();
                        mHandler.postDelayed(() -> {
                            try {
                                videoSystem.switchToTvMode();
                            } catch (IllegalStateException e) {
                                Log.e(TAG, "HDMI连接后重新启动TV模式失败", e);
                            }
                        }, 100);
                    } catch (IllegalStateException e) {
                        Log.e(TAG, "HDMI连接处理失败", e);
                    }
                } else {
                    // Camera模式：重新初始化相机预览
                    if (mTextureView.isAvailable() && !videoSystem.isCameraStarted()) {
                        android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                        if (surfaceTexture != null) {
                            Surface previewSurface = new Surface(surfaceTexture);
                            videoSystem.initialize(previewSurface);
                        }
                    }
                }
            }
        });
    }
    
    /**
     * 处理HDMI断开事件
     */
    private void handleHdmiDisconnected() {
        Log.d(TAG, "处理HDMI断开");
        runOnUiThread(() -> {
            Toast.makeText(this, "HDMI已断开", Toast.LENGTH_SHORT).show();

            if (videoSystem != null) {
                // 如果正在录制，停止录制
                if (videoSystem.isRecording()) {
                    videoSystem.stopRecording();
                    updateRecordButtonState(false);
                }

                // 如果当前是TV模式，切换到Camera模式
                if (videoSystem.isTvMode()) {
                    try {
                        videoSystem.switchToCameraMode();
                    } catch (IllegalStateException e) {
                        Log.e(TAG, "HDMI断开后切换到Camera模式失败", e);
                    }
                }

                // HDMI断开时，推流会自动停止，无需手动处理

                // 释放资源
                videoSystem.release();
            }
        });
    }
    
    /**
     * 第二步：完成TpVideoSystem初始化
     * 在TextureView准备好后调用，完成视频系统的完整初始化
     *
     * @param previewSurface 用于预览的Surface
     */
    private void completeVideoSystemInit(Surface previewSurface) {
        if (videoSystem == null) {
            Log.e(TAG, "TpVideoSystem未在onCreate中创建，无法完成初始化");
            Toast.makeText(this, "视频系统未正确初始化", Toast.LENGTH_LONG).show();
            return;
        }

        // 使用适配器模式，只重写需要的方法
        videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
            @Override
            public void onCameraStarted() {
                Log.d(TAG, "相机启动完成，初始化ROI视图参数");
                runOnUiThread(() -> {
                    // 相机启动后，设置ROI视图的相机分辨率
                    if (mRoiView != null && videoSystem != null) {
                        android.util.Size currentSize = videoSystem.getVideoConfig().getSize();
                        mRoiView.setCameraResolution(currentSize.getWidth(), currentSize.getHeight());
                        Log.d(TAG, "ROI视图相机分辨率已设置: " + currentSize.getWidth() + "x" + currentSize.getHeight());
                    }
                });
            }

            @Override
            public void onRecordingStarted(String outputPath) {
                Log.d(TAG, "录制已开始: " + outputPath);
                runOnUiThread(() -> {
                    updateRecordButtonState(true);
                    Toast.makeText(MainActivity.this, "开始录制", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onRecordingStopped(String outputPath) {
                Log.d(TAG, "录制已停止: " + outputPath);
                runOnUiThread(() -> {
                    updateRecordButtonState(false);
                    Toast.makeText(MainActivity.this,
                            "录制完成: " + outputPath, Toast.LENGTH_SHORT).show();
                });

                // SMB视频自动上传（使用新的统一上传方法）
                if (tpSambaClient != null && tpSambaClient.isEnabled()) {
                    Log.d(TAG, "开始上传视频到SMB服务器: " + outputPath);
                    tpSambaClient.uploadFile(outputPath, new TpSambaClient.UploadListener() {
                        @Override
                        public void onUploadSuccess(String remoteFilePath) {
                            Log.d(TAG, "视频上传成功: " + remoteFilePath);
                            runOnUiThread(() -> Toast.makeText(MainActivity.this,
                                "视频上传成功", Toast.LENGTH_SHORT).show());
                        }

                        @Override
                        public void onUploadFailed(String errorMessage) {
                            Log.e(TAG, "视频上传失败: " + errorMessage);
                            runOnUiThread(() -> Toast.makeText(MainActivity.this,
                                "视频上传失败: " + errorMessage, Toast.LENGTH_SHORT).show());
                        }
                    });
                }
            }

            @Override
            public void onImageCaptured(String imagePath) {
                Log.d(TAG, "图片已捕获: " + imagePath);
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this,
                            "拍照完成: " + imagePath, Toast.LENGTH_SHORT).show();
                });

                // SMB图片自动上传（参考VideoEncoderActivity实现）
                if (tpSambaClient != null && tpSambaClient.isEnabled()) {
                    Log.d(TAG, "开始上传图片到SMB服务器: " + imagePath);
                    tpSambaClient.uploadFile(imagePath, new TpSambaClient.UploadListener() {
                        @Override
                        public void onUploadSuccess(String remoteFilePath) {
                            Log.d(TAG, "图片上传成功: " + remoteFilePath);
                            runOnUiThread(() -> Toast.makeText(MainActivity.this,
                                "图片上传成功", Toast.LENGTH_SHORT).show());
                        }

                        @Override
                        public void onUploadFailed(String errorMessage) {
                            Log.e(TAG, "图片上传失败: " + errorMessage);
                            runOnUiThread(() -> Toast.makeText(MainActivity.this,
                                "图片上传失败: " + errorMessage, Toast.LENGTH_SHORT).show());
                        }
                    });
                }
            }


            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "错误: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this,
                            "错误: " + errorMessage, Toast.LENGTH_LONG).show();
                    // 如果是录制错误，更新按钮状态
                    if (videoSystem.isRecording()) {
                        updateRecordButtonState(false);
                    }
                    // 错误处理，推流相关错误现在由设置界面处理
                });
            }
        });
    }

    /**
     * 设置按钮点击事件
     */
    private void setupButtonListeners() {

        // 拍照按钮
        mCaptureButton.setOnClickListener(v -> {
            if (videoSystem != null && videoSystem.isCameraStarted()) {
                videoSystem.captureImage(TpFileManager.createImagePath(this,"image",true,"jpeg")); // 使用默认路径
//                Toast.makeText(this, "拍照中...", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "相机未启动", Toast.LENGTH_SHORT).show();
            }
        });

        // 录像按钮
        mRecordButton.setOnClickListener(v -> {
            if (videoSystem == null || !videoSystem.isCameraStarted()) {
                Toast.makeText(this, "相机未启动", Toast.LENGTH_SHORT).show();
                return;
            }

            // 检查SimpleVideoSystem中的状态
            boolean isRecording = videoSystem.isRecording();

            if (isRecording) {
                // 停止录制
                videoSystem.stopRecording();
            } else {
                // 开始录制（使用默认路径）
                videoSystem.startRecording(null);
            }
        });

        // 设置按钮
        mSettingsButton.setOnClickListener(v -> {
            openSettings();
        });

        // 分辨率测试按钮
        mResolutionTestButton.setOnClickListener(v -> {
            Size temp = videoSystem.getVideoConfig().getSize();
            if(Objects.equals(temp, new Size(1920, 1080))) {
                Toast.makeText(this, "当前分辨率已是1080P", Toast.LENGTH_SHORT).show();
                videoSystem.updateResolution(3840, 2160); // 切换到4K分辨率
            }
            else
            {
                videoSystem.updateResolution(1920, 1080); // 切换到1080P分辨率
            }
        });

        // 浏览器按钮
        mBrowserButton.setOnClickListener(v -> {
            openMediaBrowser();
        });



        // 测试功能按钮
        if (mTestButton != null) {
            mTestButton.setOnClickListener(v -> {
                openNewTestDialog();
            });
        }

        // 初始化测试模式
        initTestMode();
    }
    
    /**
     * 打开设置对话框
     */
    private void openSettings() {
        if (videoSystem == null) {
            Toast.makeText(this, "视频系统未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建设置对话框（不再需要StreamingService参数）
        TpSettingsDialog settingsDialog = TpSettingsDialog.newInstance(
            videoSystem,
            wifiPanelLauncher,
                tpSambaClient // 传递SMB文件上传器
        );

        settingsDialog.show(getSupportFragmentManager(), "TpSettingsDialog");
        Log.d(TAG, "设置对话框已显示");
    }

    /**
     * 打开媒体浏览器
     */
    private void openMediaBrowser() {
        Intent intent = new Intent(this, com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity.class);
        startActivity(intent);
        Log.d(TAG, "媒体浏览器已启动");
    }



    /**
     * 更新录制按钮状态
     */
    private void updateRecordButtonState(boolean isRecording) {
        mRecordButton.setText(isRecording ? "停止录制" : "开始录制");
    }


    // ===== 公开方法，供Fragment调用 =====

    
    /**
     * 检查相机权限
     */
    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.CAMERA}, 
                REQUEST_CAMERA_PERMISSION);
    }
    
    /**
     * 权限请求结果处理
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限获取成功，初始化视频系统
                if (mTextureView.isAvailable() && videoSystem != null) {
                    android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                    if (surfaceTexture != null) {
                        Surface previewSurface = new Surface(surfaceTexture);
                        videoSystem.initialize(previewSurface);
                    }
                }
            } else {
                Toast.makeText(this, "需要相机权限才能使用此功能", Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();

        // 根据当前模式恢复预览
        if (videoSystem != null) {
            if (videoSystem.isTvMode()) {
                // TV模式：通过TpVideoSystem恢复TV预览
                try {
                    // 重新启动TV模式以恢复预览
                    videoSystem.switchToCameraMode();
                    mHandler.postDelayed(() -> {
                        try {
                            videoSystem.switchToTvMode();
                        } catch (IllegalStateException e) {
                            Log.e(TAG, "恢复TV模式失败", e);
                        }
                    }, 100);
                } catch (IllegalStateException e) {
                    Log.e(TAG, "onResume处理TV模式失败", e);
                }
            } else {
                // Camera模式：恢复相机预览
                if (mTextureView.isAvailable() && !videoSystem.isCameraStarted()) {
                    android.graphics.SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                    if (surfaceTexture != null) {
                        Surface previewSurface = new Surface(surfaceTexture);
                        videoSystem.initialize(previewSurface);
                    }
                }
            }
        }

        // 重新启动HDMI服务
        if (tpHdmiMonitor != null) {
            tpHdmiMonitor.init();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 停止录制
        if (videoSystem != null && videoSystem.isRecording()) {
            videoSystem.stopRecording();
            updateRecordButtonState(false);
        }

        // ROI状态现在由TpRoiView自动管理

        // TV预览的暂停现在由TpVideoSystem内部管理

        // 推流状态现在由设置界面管理，主界面不直接控制
    }



    // ===== 新测试功能集成 =====

    /**
     * 初始化测试模式
     */
    private void initTestMode() {
        // 测试ISP按钮现在已集成到底部控制面板，无需单独设置可见性
        Log.d(TAG, "ISP测试功能已就绪 - 测试ISP按钮已集成到底部控制面板");
    }

    /**
     * 打开新的测试对话框
     */
    private void openNewTestDialog() {
        try {
            Log.d(TAG, "打开新测试功能对话框");

            // 直接显示测试对话框
            TpTestDialog.show(this);

        } catch (Exception e) {
            Log.e(TAG, "打开测试对话框失败", e);
            Toast.makeText(this, "打开测试功能失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // ROI状态现在由TpRoiView自动管理，无需手动清理

        // 释放资源（包括TV预览助手）
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
        }

        // 停止HDMI服务
        if (tpHdmiMonitor != null) {
            tpHdmiMonitor.stop();
        }

        // StreamingService资源现在通过TpVideoSystem自动管理
    }
}
