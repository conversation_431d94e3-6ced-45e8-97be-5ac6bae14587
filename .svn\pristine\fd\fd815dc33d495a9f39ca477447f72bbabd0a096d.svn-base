package com.android.rockchip.camera2.activity.settings
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.fragment.app.Fragment
import com.android.rockchip.camera2.R

class TpMiscSettingsFragment : Fragment() {

    interface OnModeChangeListener {
        fun onSwitchToTVMode()
    }
    // 声明模式选择的RadioGroup
    private lateinit var modeRadioGroup: RadioGroup
    var modeChangeListener: OnModeChangeListener? = null



    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.settings_misc, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化RadioGroup
        modeRadioGroup = view.findViewById(R.id.mode_radio_group)
        // 设置默认选项（可选）
        modeRadioGroup.check(R.id.radio_B) // 默认选中低延时模式

//        createAndApplyBlueSelector(view)

        // 设置单选按钮组监听器
        modeRadioGroup.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.radio_A -> {
                    // 低延时模式被选中
                    handleLowLatencyMode()
                }
                R.id.radio_B -> {
                    // 高帧率模式被选中
                    handleHighFrameRateMode()
                }
            }
        }


    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        modeChangeListener = parentFragment as? OnModeChangeListener
    }

    private fun createAndApplyBlueSelector(view: View) {
        // 创建颜色状态数组：选中=蓝色，未选中=默认灰色
        val states = arrayOf(
            intArrayOf(android.R.attr.state_checked),  // 选中状态
            intArrayOf(-android.R.attr.state_checked)  // 未选中状态
        )

        val colors = intArrayOf(
            Color.BLUE,               // 选中时的颜色
            Color.LTGRAY               // 未选中时的颜色（浅灰）
        )

        // 创建ColorStateList
        val colorStateList = ColorStateList(states, colors)

        // 应用到所有RadioButton
        val radioIds = listOf(R.id.radio_A, R.id.radio_B, R.id.radio_udisk, R.id.radio_sdcard)
        radioIds.forEach { id ->
            view.findViewById<RadioButton>(id)?.buttonTintList = colorStateList
        }
    }

    private fun handleLowLatencyMode() {
        // 这里实现低延时模式的业务逻辑
        // 例如：调整相机参数、更新UI或保存设置
        modeChangeListener?.onSwitchToTVMode()
    }

    private fun handleHighFrameRateMode() {
        // 这里实现高帧率模式的业务逻辑

        modeChangeListener?.onSwitchToTVMode()
    }
}
