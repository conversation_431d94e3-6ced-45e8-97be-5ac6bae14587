package com.android.rockchip.camera2.activity
import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.util.HdmiService
import com.android.rockchip.camera2.video.CameraManagerHelper
import com.android.rockchip.camera2.video.VideoEncoder


class TpVideoDialogFragment : DialogFragment(), HdmiService.HdmiListener{
    private lateinit var cameraManagerHelper: CameraManagerHelper
    private lateinit var videoEncoder: VideoEncoder
    private var textureView: TextureView? = null
    private var isRecording = false
    private val TAG = "MyDialogFragment"


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.video_layout, container, false)

        val context: Context = requireContext()

        textureView = view.findViewById<TextureView>(R.id.texture_view)
        cameraManagerHelper = CameraManagerHelper(context, textureView)
        videoEncoder = VideoEncoder(context, cameraManagerHelper)


        /* 初始化 HDMI 服务并设置监听器 */
        HdmiService.getInstance().init()
        HdmiService.getInstance().setHdmiListener(this)

        textureView?.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surface: android.graphics.SurfaceTexture,
                width: Int,
                height: Int
            ) {
                Log.d(TAG, "TextureView 已可用")
            }

            override fun onSurfaceTextureSizeChanged(
                surface: android.graphics.SurfaceTexture,
                width: Int,
                height: Int
            ) {}

            override fun onSurfaceTextureDestroyed(surface: android.graphics.SurfaceTexture): Boolean {
                return true
            }

            override fun onSurfaceTextureUpdated(surface: android.graphics.SurfaceTexture) {}
        }

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 绑定返回按钮的点击事件
        view.findViewById<Button>(R.id.btn_record).setOnClickListener {
            if (!isRecording){
                Toast.makeText(context, "开始录像", Toast.LENGTH_SHORT).show()
                videoEncoder.setOutputPath(FileStorageUtils.createOutputPath(context))
//                videoEncoder.setVideoSize(Size(3840, 2160))
                videoEncoder.startRecording()
            }else{
                videoEncoder.stopRecording()
                Toast.makeText(context, "停止录像", Toast.LENGTH_SHORT).show()
            }
            isRecording = !isRecording
        }

        view.findViewById<Button>(R.id.btn_return).setOnClickListener {
            dismiss() // 关闭对话框
        }

        view.findViewById<Button>(R.id.btn_switch).setOnClickListener {
            dismiss() // 关闭对话框
            showVideoDecodeDialog()
        }

    }


    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            // 全屏设置
            setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT
            )
            setBackgroundDrawableResource(android.R.color.transparent)
            setBackgroundDrawable(ColorDrawable(Color.WHITE))
            setDimAmount(0f) // 可选：去除背景遮罩

            // 强制全屏布局参数
            val params = attributes
            params.gravity = Gravity.FILL // 填充整个屏幕
            attributes = params
        }
    }

    override fun onResume() {
        super.onResume()
//        Log.d(TAG, "进入 onResume 方法")
//
//        // 启动后台线程并检查相机权限
//        Log.d(TAG, "开始启动后台线程")
//        cameraManagerHelper.startBackgroundThread()
//        Log.d(TAG, "后台线程已启动")
//
//        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
//            Log.d(TAG, "未获得相机权限，开始请求权限")
//            requestPermissions(arrayOf(Manifest.permission.CAMERA), REQUEST_CAMERA_PERMISSION)
//            Log.d(TAG, "已发送相机权限请求")
//        } else {
//            Log.d(TAG, "已获得相机权限")
//            if (textureView?.isAvailable == true) {
//                Log.d(TAG, "TextureView 可用，开始打开相机")
//                cameraManagerHelper.openCamera()
//                Log.d(TAG, "相机已打开")
//            } else {
//                Log.d(TAG, "TextureView 不可用，不打开相机")
//                cameraManagerHelper.openCamera()
//            }
//        }
    }


    private fun showVideoDecodeDialog() {
        // 显示新的 TpVideoDecodeDialogFragment
        TpVideoDecodeDialogFragment().show(
            requireActivity().supportFragmentManager,
            "TpVideoDecodeDialogFragment"
        )
    }

    override fun onHdmiStatusChanged(status: String) {
        /* 处理 HDMI 状态变化 */
//        cameraManagerHelper.openCamera()
//        cameraManagerHelper.startPreview()

        if (status.contains("plugout")) {
            println("检测到设备拔出，开始处理相关逻辑")
            if (isRecording) {
                videoEncoder.stopRecording()
                isRecording = false
                println("停止录制，录制状态已更新")
            }
            cameraManagerHelper.closeCamera()
            println("关闭相机")
            cameraManagerHelper.stopBackgroundThread()
            println("停止后台线程")
        } else if (status.contains("plugin")) {
            println("检测到设备插入，开始处理相关逻辑")
            cameraManagerHelper.startBackgroundThread()
            println("启动后台线程")
            if (ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.CAMERA
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                println("已授予权限")
                if (textureView?.isAvailable == true)
                {
                    cameraManagerHelper.openCamera()
                    println("打开相机")
                    cameraManagerHelper.startPreview()
                    println("开始预览")
                }
            }
        }
        if (status == "disconnected") {  // Kotlin 直接使用 == 比较字符串
            if (isRecording) {
                try {
                    videoEncoder.stopRecording()
                } catch (e: Exception) {
                }
                isRecording = false
            }
        }

    }
}

//import android.Manifest
//import android.content.pm.PackageManager
//import android.os.Bundle
//import android.util.Log
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.Button
//import android.view.TextureView
//import androidx.core.content.ContextCompat
//import androidx.fragment.app.DialogFragment
//import com.android.rockchip.camera2.R
//import com.android.rockchip.camera2.video.CameraManagerHelper
//import com.android.rockchip.camera2.util.HdmiService
//import com.android.rockchip.camera2.video.VideoEncoder
//
//class TpVideoDialogFragment : DialogFragment(), HdmiService.HdmiListener {
//
//    private lateinit var textureView: TextureView
//    private lateinit var btnRecord: Button
//    private lateinit var btnSwitch: Button
//    private var isRecording = false
//    private lateinit var cameraManagerHelper: CameraManagerHelper
//    private lateinit var videoEncoder: VideoEncoder
//
//    companion object {
//        private const val TAG = "MediaCodecDemo"
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle?
//    ): View? {
//        return inflater.inflate(R.layout.video_layout, container, false)
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//
//        // 初始化视图组件
//        textureView = view.findViewById(R.id.texture_view)
//        btnRecord = view.findViewById(R.id.btn_record)
//        btnSwitch = view.findViewById(R.id.btn_switch)
//
//        // 初始化摄像头管理工具
//        cameraManagerHelper = CameraManagerHelper(requireActivity(), textureView)
//        videoEncoder = VideoEncoder(requireActivity(), cameraManagerHelper)
//
//        // 初始化HDMI服务
//        HdmiService.getInstance().init()
//        setupButtonListeners()
//    }
//
//    override fun onResume() {
//        super.onResume()
//        HdmiService.getInstance().setHdmiListener(this)
//    }
//
//    override fun onPause() {
//        super.onPause()
//        HdmiService.getInstance().setHdmiListener(null)
//    }
//
//    override fun onDestroyView() {
//        super.onDestroyView()
//        // 释放资源
//        if (isRecording) {
//            videoEncoder.stopRecording()
//        }
//        cameraManagerHelper.closeCamera()
//        cameraManagerHelper.stopBackgroundThread()
//    }
//
//    override fun onHdmiStatusChanged(status: String) {
//        when {
//            status.contains("plugout") -> handleHdmiDisconnect()
//            status.contains("plugin") -> handleHdmiConnect()
//            status == "disconnected" -> handleHdmiDisconnect()
//        }
//    }
//
//    private fun setupButtonListeners() {
//        btnRecord.setOnClickListener { toggleRecording() }
//        btnSwitch.setOnClickListener { /* 切换摄像头逻辑（根据原项目实现补充） */ }
//    }
//
//    private fun toggleRecording() {
//        isRecording = if (!isRecording) {
//            videoEncoder.startRecording()
//            btnRecord.text = "停止录制"
//            true
//        } else {
//            videoEncoder.stopRecording()
//            btnRecord.text = "开始录制"
//            false
//        }
//    }
//
//    private fun handleHdmiDisconnect() {
//        if (isRecording) {
//            videoEncoder.stopRecording()
//            isRecording = false
//            btnRecord.text = "开始录制"
//        }
//        cameraManagerHelper.closeCamera()
//        cameraManagerHelper.stopBackgroundThread()
//        Log.e(TAG, "onHdmiStatusChanged: 断开")
//    }
//
//    private fun handleHdmiConnect() {
//        cameraManagerHelper.startBackgroundThread()
//        if (hasCameraPermission()) {
//            if (textureView.isAvailable) {
//                cameraManagerHelper.openCamera()
//                cameraManagerHelper.startPreview()
//            }
//        } else {
//            requestPermissions(arrayOf(Manifest.permission.CAMERA), 0)
//        }
//    }
//
//    private fun hasCameraPermission(): Boolean {
//        return ContextCompat.checkSelfPermission(
//            requireContext(),
//            Manifest.permission.CAMERA
//        ) == PackageManager.PERMISSION_GRANTED
//    }
//
//    override fun onRequestPermissionsResult(
//        requestCode: Int,
//        permissions: Array<out String>,
//        grantResults: IntArray
//    ) {
//        if (requestCode == 0 && grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//            if (textureView.isAvailable) {
//                cameraManagerHelper.openCamera()
//                cameraManagerHelper.startPreview()
//            }
//        }
//    }
//}