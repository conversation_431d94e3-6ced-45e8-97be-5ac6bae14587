#ifndef __libdsp_h__
#define __libdsp_h__

#include <cstddef>

#ifdef __cplusplus
extern "C"{
#endif

#define H26X_NAL_MAX	6
typedef struct{
	unsigned char*	pData0;
	size_t			nLen0;
	unsigned char*	pData1;
	size_t			nLen1;
}h26xnal_t;

#if 0
typedef struct{
	char cBuffSSID[8][128];
	unsigned char ucWifiNum;

}wifilist_st;
#endif
extern int libdsp_init(int iCodec, int iSize);
extern void libdsp_fini();
extern int libdsp_pull_frame(h26xnal_t nal[H26X_NAL_MAX], unsigned* pTimeStamp, void** pCtx);
extern void libdsp_free_frame(h26xnal_t nal[H26X_NAL_MAX], void* pCtx);
//extern void libdsp_free_frame(void* pCtx);

extern int put_wifi(const char* ssid, const char* password);
extern int get_wifi(char ssid[], char password[]);

#ifdef CAPTURE_SUPPORT
extern int libdsp_pull_image(bool thumbnail, unsigned* pnWidth, unsigned* pnHeight, unsigned* pnFourcc, h26xnal_t* pPicture, void** pCtx);
extern void libdsp_free_image(void* pCtx);
extern int save_to_disk(const char* filename, unsigned nWidth, unsigned nHeight,const h26xnal_t* pPicture,int iLen);
#endif

#ifdef SD_SUPPORT
extern int touptek_wifi_mp4_h264_start(const char* pcFilePath);
extern void touptek_wifi_mp4_h264_end();
extern int touptek_is_sdcardfull();
extern int touptek_is_fileoverbig();



#endif

#ifdef __cplusplus
}
#endif
#endif
