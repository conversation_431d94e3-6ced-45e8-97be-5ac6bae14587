package com.android.rockchip.camera2.integrated.browser;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.touptek.video.TpImageLoader;
import com.android.rockchip.mediacodecnew.R;

import java.io.File;
import java.util.List;

/**
 * MediaAdapter - integrated版本的媒体适配器
 * <p>
 * 此类用于在 RecyclerView 中显示媒体文件的缩略图和名称。
 * 使用TpImageLoader加载缩略图，支持图片和视频文件。
 * </p>
 */
public class MediaAdapter extends RecyclerView.Adapter<MediaAdapter.MediaViewHolder> {
    private static final String TAG = "MediaAdapter";
    
    /* 媒体文件列表 */
    private final List<File> mediaFiles;

    /* 媒体点击监听器 */
    private final OnMediaClickListener listener;

    /**
     * 定义媒体点击监听器接口。
     */
    public interface OnMediaClickListener {
        /**
         * 当媒体文件被点击时调用。
         *
         * @param file 被点击的媒体文件
         */
        void onMediaClick(File file);
    }

    /**
     * 构造函数。
     *
     * @param mediaFiles 媒体文件列表
     * @param listener   媒体点击监听器
     */
    public MediaAdapter(List<File> mediaFiles, OnMediaClickListener listener) {
        this.mediaFiles = mediaFiles;
        this.listener = listener;
    }

    /**
     * 创建 ViewHolder。
     *
     * @param parent   父视图组
     * @param viewType 视图类型
     * @return 创建的 MediaViewHolder
     */
    @NonNull
    @Override
    public MediaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.media_item, parent, false);
        return new MediaViewHolder(view);
    }

    /**
     * 绑定 ViewHolder。
     *
     * @param holder   MediaViewHolder 实例
     * @param position 当前项的位置
     */
    @Override
    public void onBindViewHolder(@NonNull MediaViewHolder holder, int position) {
        File file = mediaFiles.get(position);

        /* 使用 TpImageLoader 加载缩略图（支持图片和视频） */
        TpImageLoader.loadThumbnail(file.getAbsolutePath(), holder.imageView);

        /* 设置文件名称 */
        holder.textView.setText(file.getName());

        /* 设置点击监听器 */
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMediaClick(file);
            }
        });
    }

    /**
     * 获取媒体文件的数量。
     *
     * @return 媒体文件数量
     */
    @Override
    public int getItemCount() {
        return mediaFiles.size();
    }

    /**
     * 更新媒体文件列表
     *
     * @param newMediaFiles 新的媒体文件列表
     */
    public void updateMediaFiles(List<File> newMediaFiles) {
        this.mediaFiles.clear();
        this.mediaFiles.addAll(newMediaFiles);
        notifyDataSetChanged();
    }

    /**
     * MediaViewHolder 类用于绑定媒体文件的视图。
     */
    static class MediaViewHolder extends RecyclerView.ViewHolder {
        /* 显示媒体缩略图的 ImageView */
        ImageView imageView;

        /* 显示媒体文件名称的 TextView */
        TextView textView;

        /**
         * 构造函数。
         *
         * @param itemView 项视图
         */
        public MediaViewHolder(@NonNull View itemView) {
            super(itemView);
            imageView = itemView.findViewById(R.id.media_thumbnail);
            textView = itemView.findViewById(R.id.media_name);
        }
    }
}
