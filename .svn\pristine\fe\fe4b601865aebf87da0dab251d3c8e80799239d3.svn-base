<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Thu Jun 05 09:11:19 CST 2025 -->
<title>touptek_serial_rk</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-06-05">
<meta name="description" content="declaration: package: com.android.rockchip.camera2.util, class: touptek_serial_rk">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.android.rockchip.camera2.util</a></div>
<h1 title="类 touptek_serial_rk" class="title">类 touptek_serial_rk</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.android.rockchip.camera2.util.touptek_serial_rk</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">touptek_serial_rk</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">touptek_serial_rk 类用于管理串口通信。
 <p>
 此类提供了串口的更加底层的初始化、命令发送、状态监听等功能。
 主要职责包括：
 1. 提供串口设备的初始化和关闭
 2. 提供串口命令的发送
 3. 管理设备插拔状态监听
 4. 处理串口数据接收</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="touptek_serial_rk.DeviceStateCallback.html" class="type-name-link" title="com.android.rockchip.camera2.util中的接口">touptek_serial_rk.DeviceStateCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">定义设备状态回调接口。</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">touptek_serial_rk</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">关闭串口。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initializeSerial(int)" class="member-name-link">initializeSerial</a><wbr>(int&nbsp;baudRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">初始化串口。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSerialConnected()" class="member-name-link">isSerialConnected</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">判断当前串口的连接状态。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onDeviceStateChanged(boolean)" class="member-name-link">onDeviceStateChanged</a><wbr>(boolean&nbsp;connected)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设备插入/拔出回调方法。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onSerialDataReceived(int%5B%5D)" class="member-name-link">onSerialDataReceived</a><wbr>(int[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">串口数据接收回调。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#sendCommandToSerial(int,int,int)" class="member-name-link">sendCommandToSerial</a><wbr>(int&nbsp;ctrl,
 int&nbsp;command,
 int&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">发送串口命令。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk.DeviceStateCallback)" class="member-name-link">setDeviceStateCallback</a><wbr>(<a href="touptek_serial_rk.DeviceStateCallback.html" title="com.android.rockchip.camera2.util中的接口">touptek_serial_rk.DeviceStateCallback</a>&nbsp;callback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置设备状态回调。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startMonitor()" class="member-name-link">startMonitor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始串口设备检测。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopMonitor()" class="member-name-link">stopMonitor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止串口设备检测。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>touptek_serial_rk</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">touptek_serial_rk</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="startMonitor()">
<h3>startMonitor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">startMonitor</span>()</div>
<div class="block">开始串口设备检测。
 <p>
 如果检测到串口设备插入，将自动初始化串口。
 此方法会启动一个监控线程来持续检测设备状态。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果检测启动成功返回 1，否则返回 0。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopMonitor()">
<h3>stopMonitor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopMonitor</span>()</div>
<div class="block">停止串口设备检测。
 <p>
 停止所有与串口设备检测相关的操作。
 会中止监控线程并释放相关资源。</div>
</section>
</li>
<li>
<section class="detail" id="isSerialConnected()">
<h3>isSerialConnected</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSerialConnected</span>()</div>
<div class="block">判断当前串口的连接状态。</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果串口已连接返回 true，否则返回 false。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sendCommandToSerial(int,int,int)">
<h3>sendCommandToSerial</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendCommandToSerial</span><wbr><span class="parameters">(int&nbsp;ctrl,
 int&nbsp;command,
 int&nbsp;data)</span></div>
<div class="block">发送串口命令。
 <p>
 将整数数据拆分为4个字节，并通过JNI发送到设备。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>ctrl</code> - 控制字节，0x00表示读操作，0x01表示写操作。</dd>
<dd><code>command</code> - 命令字节，对应<a href="TouptekIspParam.html" title="enum class in com.android.rockchip.camera2.util"><code>TouptekIspParam</code></a>中的枚举值。</dd>
<dd><code>data</code> - 整型数据，会被拆分为4个字节发送。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initializeSerial(int)">
<h3>initializeSerial</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">initializeSerial</span><wbr><span class="parameters">(int&nbsp;baudRate)</span></div>
<div class="block">初始化串口。
 <p>
 设置串口通信参数并打开串口。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>baudRate</code> - 波特率，常用值有9600、115200等。</dd>
<dt>返回:</dt>
<dd>如果初始化成功返回 true，否则返回 false。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onDeviceStateChanged(boolean)">
<h3>onDeviceStateChanged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onDeviceStateChanged</span><wbr><span class="parameters">(boolean&nbsp;connected)</span></div>
<div class="block">设备插入/拔出回调方法。
 当设备状态发生变化时由JNI层调用。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>connected</code> - 设备是否已连接，true表示已连接，false表示已断开。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk.DeviceStateCallback)">
<h3>setDeviceStateCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDeviceStateCallback</span><wbr><span class="parameters">(<a href="touptek_serial_rk.DeviceStateCallback.html" title="com.android.rockchip.camera2.util中的接口">touptek_serial_rk.DeviceStateCallback</a>&nbsp;callback)</span></div>
<div class="block">设置设备状态回调。
 <p>
 当设备的连接状态发生变化时，将触发回调方法 <a href="touptek_serial_rk.DeviceStateCallback.html#onDeviceStateChanged(boolean)"><code>touptek_serial_rk.DeviceStateCallback.onDeviceStateChanged(boolean)</code></a>。
 应用层可以通过此回调获知设备的插拔状态。</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>callback</code> - 回调接口实例，用于监听设备状态的变化。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onSerialDataReceived(int[])">
<h3>onSerialDataReceived</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onSerialDataReceived</span><wbr><span class="parameters">(int[]&nbsp;data)</span></div>
<div class="block">串口数据接收回调。
 <p>
 当串口接收到数据时自动调用，然后将接收到的数据存入TouptekIspParam中。
 用户需要使用变量可以在TouptekIspParam中获取。
 此方法由JNI层在接收到数据时调用。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>data</code> - 接收到的数据数组，包含命令字节和参数值。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()</div>
<div class="block">关闭串口。
 <p>
 释放串口资源，应在不再需要串口通信时调用。</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
