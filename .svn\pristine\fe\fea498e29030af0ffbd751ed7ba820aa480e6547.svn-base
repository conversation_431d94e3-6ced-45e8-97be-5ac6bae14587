package com.android.rockchip.mediacodecnew;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.android.rockchip.mediacodecnew.video.VideoDecoder;
import com.android.rockchip.mediacodecnew.util.FileStorageUtils;

/**
 * VideoDecoderActivity 类负责视频解码和播放控制。
 */
public class VideoDecoderActivity extends AppCompatActivity
{
    /* 日志标签 */
    private static final String TAG = "VideoDecoder";

    /* 界面控件 */
    private SurfaceView surfaceView;
    private SeekBar seekBar;
    private Button playPauseButton;
    private Button frameByFrameButton;
    private Button fastForwardButton;
    private Button fastBackwardButton;
    private TextView tvDuration;
    private TextView tvCurrentPosition;

    /* 视频解码器实例 */
    private VideoDecoder videoDecoder;

    /* 用于更新进度条的 Handler 和 Runnable */
    private final Handler handler = new Handler();
    private final Runnable updateSeekBarRunnable = new Runnable()
    {
        @Override
        public void run()
        {
            /* 若解码器存在且正在解码 */
            if (videoDecoder != null && videoDecoder.isDecoding())
            {
                /* 获取当前播放位置和视频总时长 */
                long currentPosition = videoDecoder.getCurrentPosition();
                long duration = videoDecoder.getVideoDuration();

                /* 更新进度条和当前播放位置文本 */
                seekBar.setProgress((int) (currentPosition * 100 / duration));
                tvCurrentPosition.setText("当前时长: " + formatTime(currentPosition));

                /* 每秒更新一次进度条 */
                handler.postDelayed(this, 1000);
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.decoder);

        /* 初始化界面控件 */
        surfaceView = findViewById(R.id.surface_view);
        seekBar = findViewById(R.id.seek_bar);
        playPauseButton = findViewById(R.id.btn_play_pause);
        frameByFrameButton = findViewById(R.id.btn_step_decode);
        fastForwardButton = findViewById(R.id.btn_fast_forward);
        fastBackwardButton = findViewById(R.id.btn_fast_backward);
        tvDuration = findViewById(R.id.tv_duration);
        tvCurrentPosition = findViewById(R.id.tv_current_position);

        /* 请求存储权限 */
        requestStoragePermission();
        final Context context = this;

        /* 设置 SurfaceView 回调 */
        surfaceView.getHolder().addCallback(new SurfaceHolder.Callback()
        {
            @Override
            public void surfaceCreated(SurfaceHolder holder)
            {
                Log.d(TAG, "Surface Created");
                /* 获取视频路径 */
                String videoPath = getIntent().getStringExtra("videoPath"); // 获取传递的视频路径
                if (videoPath == null) {
                    videoPath = FileStorageUtils.createOutputPath(context); // 默认路径
                }
                /*初始化视频解码器，将解码输出绑定到 surface */
                videoDecoder = new VideoDecoder(videoPath, holder.getSurface());
                videoDecoder.startDecoding();

                /* 开始更新进度条 */
                handler.post(updateSeekBarRunnable);
                /* 更新播放按钮文本 */
                updatePlayButton(true);
                /* 获取视频总时长并显示 */
                long duration = videoDecoder.getVideoDuration();
                tvDuration.setText("总时长: " + formatTime(duration));
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height)
            {
                // 空实现，可根据需求添加逻辑
            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder)
            {
                /* 停止解码 */
                if (videoDecoder != null)
                {
                    videoDecoder.stopDecoding();
                }
                /* 停止更新进度条 */
                handler.removeCallbacks(updateSeekBarRunnable);
            }
        });

        /* 设置进度条监听器 */
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener()
        {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser)
            {
                /* 若用户手动拖动进度条 */
                if (fromUser && videoDecoder != null)
                {
                    /* 获取视频总时长 */
                    long videoDuration = videoDecoder.getVideoDuration();
                    /* 跳转到指定位置 */
                    videoDecoder.seekTo(videoDuration * progress / 100);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar)
            {
                // 空实现，可根据需求添加逻辑
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar)
            {
                // 空实现，可根据需求添加逻辑
            }
        });

        /* 设置播放/暂停按钮点击事件 */
        playPauseButton.setOnClickListener(v ->
        {
            if (videoDecoder != null)
            {
                /* 切换播放/暂停状态 */
                videoDecoder.togglePlayPause();
                /* 更新播放按钮文本 */
                updatePlayButton(!videoDecoder.isPaused());
                if (!videoDecoder.isPaused())
                {
                    /* 继续更新进度条 */
                    handler.post(updateSeekBarRunnable);
                }
            }
        });

        /* 设置逐帧播放按钮点击事件 */
        frameByFrameButton.setOnClickListener(v ->
        {
            if (videoDecoder != null)
            {
                if (!videoDecoder.isPaused())
                {
                    /* 暂停播放 */
                    videoDecoder.togglePlayPause();
                    /* 更新播放按钮文本 */
                    updatePlayButton(false);

                    /* 添加一个短暂的延迟，确保解码器切换到暂停状态 */
                    handler.postDelayed(() ->
                    {
                        /* 逐帧播放 */
                        videoDecoder.stepFrame();
                    }, 100); /* 延迟 100 毫秒 */
                }
                else
                {
                    /* 逐帧播放 */
                    videoDecoder.stepFrame();
                }
            }
        });

        /* 设置快进按钮点击事件 */
        fastForwardButton.setOnClickListener(v ->
        {
            if (videoDecoder != null)
            {
                /* 快进5秒 */
                videoDecoder.seekRelative(5000);
            }
        });

        /* 设置快退按钮点击事件 */
        fastBackwardButton.setOnClickListener(v ->
        {
            if (videoDecoder != null)
            {
                /* 快退5秒 */
                videoDecoder.seekRelative(-5000);
            }
        });
    }

    /**
     * 请求存储权限
     */
    private void requestStoragePermission()
    {
        /* 检查是否有存储权限 */
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)
        {
            /* 请求存储权限 */
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE}, 1);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults)
    {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        /* 检查权限请求结果 */
        if (requestCode == 1 && grantResults.length > 0 && grantResults[0] != PackageManager.PERMISSION_GRANTED)
        {
            Log.e(TAG, "Storage permission denied");
            /* 权限被拒绝，关闭活动 */
            finish();
        }
    }

    /**
     * 更新播放按钮文本
     * @param playing 是否正在播放
     */
    private void updatePlayButton(boolean playing)
    {
        /* 根据播放状态更新按钮文本 */
        playPauseButton.setText(playing ? "Pause" : "Play");
    }

    @Override
    protected void onDestroy()
    {
        super.onDestroy();
        /* 停止解码 */
        if (videoDecoder != null)
        {
            videoDecoder.stopDecoding();
        }
        /* 停止更新进度条 */
        handler.removeCallbacks(updateSeekBarRunnable);
    }

    /**
     * 格式化时间显示
     * @param timeUs 时间（微秒）
     * @return 格式化后的时间字符串
     */
    private String formatTime(long timeUs)
    {
        /* 转换为秒 */
        long totalSeconds = timeUs / 1000000;
        /* 计算小时 */
        long hours = totalSeconds / 3600;
        /* 计算分钟 */
        long minutes = (totalSeconds % 3600) / 60;
        /* 计算秒 */
        long seconds = totalSeconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
}