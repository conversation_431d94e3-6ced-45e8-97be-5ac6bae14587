<?xml version="1.0" encoding="utf-8"?>
<!-- 播放速度选择对话框布局 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/tp_speed_menu_background"
    android:padding="16dp"
    android:minWidth="200dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="播放速度"
        android:textColor="#FFFFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="12dp"
        android:fontFamily="sans-serif-medium" />

    <!-- 速度选项列表 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 0.5x -->
        <TextView
            android:id="@+id/speed_0_5"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="0.5x"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

        <!-- 0.75x -->
        <TextView
            android:id="@+id/speed_0_75"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="0.75x"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

        <!-- 1.0x -->
        <TextView
            android:id="@+id/speed_1_0"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="1.0x ✓"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

        <!-- 1.25x -->
        <TextView
            android:id="@+id/speed_1_25"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="1.25x"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

        <!-- 1.5x -->
        <TextView
            android:id="@+id/speed_1_5"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="1.5x"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

        <!-- 2.0x -->
        <TextView
            android:id="@+id/speed_2_0"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="2.0x"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:background="@drawable/tp_speed_item_background"
            android:fontFamily="sans-serif-medium"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</LinearLayout>
