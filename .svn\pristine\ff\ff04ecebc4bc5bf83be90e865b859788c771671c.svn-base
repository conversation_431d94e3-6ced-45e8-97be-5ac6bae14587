package com.android.rockchip.camera2.activity

import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.util.Size
import android.view.*
import android.widget.ImageButton
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.android.rockchip.camera2.R
import com.android.rockchip.camera2.activity.ispdialogfragment.*
import com.android.rockchip.camera2.util.FileStorageUtils
import com.android.rockchip.camera2.video.CameraManagerHelper
import com.android.rockchip.camera2.video.CaptureImageHelper
import android.graphics.drawable.StateListDrawable
import androidx.core.content.ContextCompat
import com.android.rockchip.camera2.activity.measurement.TpMeasurementDialogFragment
import com.android.rockchip.camera2.activity.settings.TpSettingsDialogFragment
import com.android.rockchip.camera2.databinding.BrowseLayoutBinding
import com.android.rockchip.camera2.util.TouptekIspParam
import com.android.rockchip.camera2.util.getStorageDCIMPath
import com.android.rockchip.camera2.util.getStoragePicturePath
import com.android.rockchip.camera2.util.getStorageVideoPath
import com.android.rockchip.camera2.util.touptek_serial_rk
import java.io.File

class MainMenu : DialogFragment() {
    private val TAG = "MainMenu"
    private var isRecording = false
    private var captureImageHelper: CaptureImageHelper? = null
    private var cameraManagerHelper: CameraManagerHelper? = null

    //roi
    private var isRectangleVisible = false
    // 添加回调接口
    interface OnRectangleVisibilityListener {
        fun onShowRectangle()
        fun onHideRectangle()
    }
    private var rectangleListener: OnRectangleVisibilityListener? = null

    private val buttonActions = listOf(
        ButtonAction(R.id.btn_take_photo, MenuAction.TAKE_PHOTO),
        ButtonAction(R.id.btn_record_video, MenuAction.RECORD_VIDEO),
        ButtonAction(R.id.btn_pause, MenuAction.PAUSE),
        ButtonAction(R.id.btn_folder, MenuAction.BROWSE),
        ButtonAction(R.id.btn_zoom_in, MenuAction.ZOOM_IN),
        ButtonAction(R.id.btn_zoom_out, MenuAction.ZOOM_OUT),
        ButtonAction(R.id.btn_settings, MenuAction.SETTINGS),
        ButtonAction(R.id.btn_about, MenuAction.ABOUT),
        ButtonAction(R.id.btn_draw, MenuAction.DRAW),
        ButtonAction(R.id.btn_menu, MenuAction.MENU)
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.activity_touptek_btn, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupButtonClickListeners(view)
        initCapture()
        setupButtonStates(view)

        // 初始化按钮状态
        isRectangleVisible = (activity as? MainActivity)?.isRectangleVisible() ?: false
        view.findViewById<ImageButton>(R.id.btn_zoom_in)?.isSelected = isRectangleVisible
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setDimAmount(0f)
            setBackgroundDrawableResource(R.color.Light_black)    //设置自定义背景色
//            addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)

            attributes = attributes.apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                gravity = Gravity.START or Gravity.TOP
            }
        }
    }

    private fun setupButtonClickListeners(view: View) {
        buttonActions.forEach { (id, action) ->
            view.findViewById<ImageButton>(id)?.setOnClickListener {
                handleMenuAction(action)
            }
        }
    }

    private fun handleMenuAction(action: MenuAction) {
        when (action) {
            MenuAction.TAKE_PHOTO -> captureImage()
            MenuAction.RECORD_VIDEO -> toggleRecording()
            MenuAction.BROWSE -> openBrowse()
            MenuAction.ZOOM_IN -> ZoomIn()
            MenuAction.ZOOM_OUT -> ZoomOut()
            MenuAction.SETTINGS->openTestSettings()
            MenuAction.DRAW -> showMeasurementPanel()
            MenuAction.MENU -> showSubMenu()
            MenuAction.ABOUT->createStorageDefaultPath()

            else -> showToast(action.label)
        }
    }

    private fun captureImage() {
        (activity as? MainActivity)?.apply {
            ActivityCaptureImage()
        }
    }

    private fun toggleRecording() {
        (activity as? MainActivity)?.let { mainActivity ->
            if (mainActivity.isRecording()) {
                mainActivity.stopRecord()
            } else {
                mainActivity.startRecord()
            }
        }

    }

    private fun openBrowse() {
        val path = FileStorageUtils.getExternalStoragePath(context)
        if(!path.isNullOrEmpty()) {
            (activity as? MainActivity)?.showBrowseActivity()
        }
        else
        {
            showToast("存储设备未插入")
        }
    }

    private fun ZoomIn() {
        (activity as? MainActivity)?.SetZoomIn()
    }

    private fun ZoomOut() {
        (activity as? MainActivity)?.SetZoomOut()
    }

    private fun openTestSettings() {
        if (!isAdded || isRemoving) return // 确保 Fragment 处于活跃状态
        showToast("打开测试窗口")
        activity?.supportFragmentManager?.let { fm ->
            if (!fm.isStateSaved && !fm.isDestroyed) {
                TpSettingsDialogFragment().apply {
                    show(fm, "SettingsDialog")
                }
            }
        } ?: showToast("无法打开设置")
    }

    private fun showMeasurementPanel() {
        if (!isAdded || context == null) return

        view?.post {
            val drawButton = view?.findViewById<ImageButton>(R.id.btn_draw)
            drawButton?.let { button ->
                val location = IntArray(2).apply { button.getLocationOnScreen(this) }

                val args = Bundle().apply {
                    putInt("anchor_x", location[0])
                    putInt("anchor_y", location[1])
                    putInt("anchor_width", button.width)
                }

                val fm = parentFragmentManager.takeIf { isAdded }
                    ?: activity?.supportFragmentManager
                    ?: return@post

                try {
                    TpMeasurementDialogFragment().apply {
                        arguments = args
                        show(fm, "MeasurementDialog")
                    }
                } catch (e: IllegalStateException) {
                    Log.e("Dialog", "Show measurement failed: ${e.message}")
                }
            }
        }
    }

    private fun showSubMenu() {
        if (!isAdded || context == null) {
            Log.e("Dialog", "Fragment not attached!")
            return
        }

        view?.post { // 等待视图布局完成
            val menuButton = view?.findViewById<ImageButton>(R.id.btn_menu)
            menuButton?.let { button ->
                // 获取屏幕坐标
                val location = IntArray(2).apply { button.getLocationOnScreen(this) }

                // 创建参数
                val args = Bundle().apply {
                    putInt("anchor_x", location[0])
                    putInt("anchor_y", location[1])
                    putInt("anchor_width", button.width)
                }

                // 正确获取FragmentManager
                val fm = parentFragmentManager.takeIf { isAdded }
                    ?: activity?.supportFragmentManager
                    ?: return@post

                try {
                    MenuPopupDialogFragment().apply {
                        arguments = args
                        show(fm, "MenuPopupDialogFragment") // 使用安全的FragmentManager
                    }
                } catch (e: IllegalStateException) {
                    Log.e("Dialog", "Show failed: ${e.message}")
                }
            }
        }

    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initCapture() {
        cameraManagerHelper = CameraManagerHelper(context)
        captureImageHelper = CaptureImageHelper(Size(3840, 2160)).apply {
            setCaptureCallback(object : CaptureImageHelper.CaptureCallback {
                override fun onImageSaved(filePath: String) {}
                override fun onError(errorMessage: String) {}
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraManagerHelper?.releaseCamera()
        captureImageHelper = null
    }

    class MenuPopupDialogFragment : DialogFragment() {
        private fun showToast(message: String) {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }

        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle? // 关键修正点：Bundle 改为 Bundle?
        ): View? {
            val view = inflater.inflate(R.layout.popup_menu_layout, container, false)
            val popupButtonScene = view.findViewById<ImageButton>(R.id.btn_scene)
            popupButtonScene.setOnClickListener {
                showToast("切换场景")
            }


            val popupButtonAE = view.findViewById<ImageButton>(R.id.btn_exposure)
            popupButtonAE.setOnClickListener {
                val AEDialog = TpAEDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 30)
                        putInt("width", popupButtonAE.width)
                    }
                }
                AEDialog.show(parentFragmentManager, "AEDialog")
            }

            val popupButtonWB = view.findViewById<ImageButton>(R.id.btn_white_balance)
            popupButtonWB.setOnClickListener {
                val WBDialog = TpWBDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 40)
                        putInt("width", popupButtonWB.width)
                    }
                }
                WBDialog.show(parentFragmentManager, "WBDialog")
            }


            val popupButtonImageProcess = view.findViewById<ImageButton>(R.id.btn_color_adjustment)
            popupButtonImageProcess.setOnClickListener {
                // 处理点击事件（示例）

                val ImageProcessDialog = TpImageProcessDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                ImageProcessDialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonImageProcess2 = view.findViewById<ImageButton>(R.id.btn_image_processing)
            popupButtonImageProcess2.setOnClickListener {
                // 处理点击事件（示例）

                val ImageProcess2Dialog = TpImageProcess2DialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                ImageProcess2Dialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonFlip = view.findViewById<ImageButton>(R.id.btn_flip)
            popupButtonFlip.setOnClickListener {
                // 处理点击事件（示例）

                val FlipDialog = TpFlipDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                FlipDialog.show(parentFragmentManager, "WBDialog")
            }

            val popupButtonHz = view.findViewById<ImageButton>(R.id.btn_power_frequency)
            popupButtonHz.setOnClickListener {
                // 处理点击事件（示例）

                val HzDialog = TpHzDialogFragment().apply {
                    arguments = Bundle().apply {
                        // 获取当前按钮的位置参数（需要先计算）
                        val buttonLocation = IntArray(2)
                        popupButtonAE.getLocationOnScreen(buttonLocation)
                        putInt("x", buttonLocation[0] - 90)
                        putInt("y", buttonLocation[1] - 70)
                        putInt("width", popupButtonWB.width)
                    }
                }
                HzDialog.show(parentFragmentManager, "WBDialog")
            }


            setupPopMenuButtonStates(view)
            return view
        }

        override fun onStart() {
            super.onStart()
            dialog?.window?.apply {
                setDimAmount(0f)
                setBackgroundDrawableResource(R.color.Light_black)
//                addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)



                val params = attributes.apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                }

                arguments?.let { args ->
//                    val marginPx = 20f.dpToPx(requireContext())
                    val marginPx = 20
                    val anchorX = args.getInt("anchor_x")
                    val anchorY = args.getInt("anchor_y")
                    val anchorWidth = args.getInt("anchor_width")
                    val targetX = anchorX + anchorWidth + marginPx
                    val targetY = anchorY  // 保持相同Y坐标实现水平对齐
                    params.gravity = Gravity.TOP or Gravity.START
                    params.x = targetX
                    params.y = targetY
                }

                attributes = params
            }

        }


        private fun setupPopMenuButtonStates(view: View) {
            val buttonIds = listOf(
                R.id.btn_exposure, R.id.btn_white_balance,
                R.id.btn_color_adjustment, R.id.btn_image_processing,
                R.id.btn_flip, R.id.btn_power_frequency,
            )
            buttonIds.forEach { id ->
                view.findViewById<ImageButton>(id)?.apply {
                    val resName = resources.getResourceEntryName(id)
                    val normalRes = resources.getIdentifier("${resName}_n", "drawable", context.packageName)
                    val pressedRes = resources.getIdentifier("${resName}_pressed", "drawable", context.packageName)

                    background = StateListDrawable().apply {
                        addState(intArrayOf(android.R.attr.state_pressed), ContextCompat.getDrawable(context, pressedRes))
                        addState(intArrayOf(), ContextCompat.getDrawable(context, normalRes))
                    }
                }
            }
        }
    }

    private data class ButtonAction(val id: Int, val action: MenuAction)

    private enum class MenuAction(val label: String) {
        TAKE_PHOTO("Take Photo"),
        RECORD_VIDEO("Record Video"),
        PAUSE("Pause"),
        BROWSE("Folder"),
        ZOOM_IN("Zoom In"),
        ZOOM_OUT("Zoom Out"),
        SETTINGS("Settings"),
        ABOUT("About"),
        DRAW("Draw"),
        MENU("Menu")
    }


    private fun setupButtonStates(view: View) {
        val buttonIds = listOf(
            R.id.btn_take_photo, R.id.btn_record_video,
            R.id.btn_pause, R.id.btn_folder,
            R.id.btn_zoom_in, R.id.btn_zoom_out,
            R.id.btn_settings, R.id.btn_about,
            R.id.btn_draw, R.id.btn_menu
        )
        buttonIds.forEach { id ->
            view.findViewById<ImageButton>(id)?.apply {
                val resName = resources.getResourceEntryName(id)
                val normalRes = resources.getIdentifier("${resName}_n", "drawable", context.packageName)
                val pressedRes = resources.getIdentifier("${resName}_pressed", "drawable", context.packageName)

                background = StateListDrawable().apply {
                    addState(intArrayOf(android.R.attr.state_pressed), ContextCompat.getDrawable(context, pressedRes))
                    addState(intArrayOf(), ContextCompat.getDrawable(context, normalRes))
                }
            }
        }

        view?.findViewById<ImageButton>(R.id.btn_zoom_in)?.visibility = View.GONE
        view?.findViewById<ImageButton>(R.id.btn_zoom_out)?.visibility = View.GONE
        view?.findViewById<ImageButton>(R.id.btn_about)?.visibility = View.GONE

        if(TouptekIspParam.isSerialConnected()){
            enableMenuButton()
        }else{
            disableMenuButton()
        }





    }

    fun createStorageDefaultPath()
    {
        val storagePath = FileStorageUtils.getExternalStoragePath(context)

        val dcimPath = File(storagePath, getStorageDCIMPath()).path
        val videosPath = File(storagePath, getStorageVideoPath()).path
        val picturesPath = File(storagePath, getStoragePicturePath()).path

        // 创建 DCIM 目录（如果不存在）
        val dcimDir = File(dcimPath)
        if (!dcimDir.exists()) {
            dcimDir.mkdirs()
        }

        // 创建 Videos 目录（如果不存在）
        val videosDir = File(videosPath)
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }

        // 创建 Pictures 目录（如果不存在）
        val picturesDir = File(picturesPath)
        if (!picturesDir.exists()) {
            picturesDir.mkdirs()
        }
    }

    private fun PointF.distanceTo(other: PointF): Float {
        return Math.sqrt(Math.pow((x - other.x).toDouble(), 2.0) + Math.pow((y - other.y).toDouble(), 2.0)).toFloat()
    }

    // 设置监听器的方法
    fun setRectangleVisibilityListener(listener: OnRectangleVisibilityListener) {
        this.rectangleListener = listener
    }

    fun displayRectangle() {
        isRectangleVisible = !isRectangleVisible  // 切换状态

        (activity as? MainActivity)?.let { activity ->
            if (isRectangleVisible) {
                rectangleListener?.onShowRectangle()
                view?.findViewById<ImageButton>(R.id.btn_zoom_in)?.isSelected = true
            } else{

            }
        }
    }

    fun hideRectangle() {
        isRectangleVisible = false
        (activity as? MainActivity)?.let { activity ->
                rectangleListener?.onHideRectangle()
                view?.findViewById<ImageButton>(R.id.btn_zoom_in)?.isSelected = false
        }
    }


    //恢复或禁用ISP功能，关联串口连接状态
    fun disableMenuButton() {
        view?.findViewById<ImageButton>(R.id.btn_menu)?.let { menuButton ->
            // Close any open submenu first
            (parentFragmentManager.findFragmentByTag("MenuPopupDialogFragment") as? MenuPopupDialogFragment)?.dismiss()

            // 仅设置透明度来实现视觉上置灰效果
            menuButton.alpha = 0.5f
            // Disable the button
            menuButton.isEnabled = false

            closeAllChildDialogs()
        }
    }

    // Function to re-enable the menu button
    fun enableMenuButton() {

        view?.findViewById<ImageButton>(R.id.btn_menu)?.let { menuButton ->
            // Re-enable the button
            menuButton.alpha = 1.0f
            menuButton.isEnabled = true
            // Restore normal appearance
        }
    }

    //关闭所有子ISP功能窗口
    fun closeAllChildDialogs() {
        // 1. 关闭 MenuPopupDialogFragment（主菜单的子窗口）
        (parentFragmentManager.findFragmentByTag("MenuPopupDialogFragment") as? DialogFragment)?.dismiss()

        // 2. 关闭其他可能的子窗口（如 AE、WB、ImageProcess 等对话框）
        listOf(
            "AEDialog",          // 曝光设置
            "WBDialog",          // 白平衡
            "ImageProcessDialog",// 图像处理
            "FlipDialog",        // 翻转
            "HzDialog",         // 电源频率
            "SettingsDialog",    // 设置
            "MeasurementDialog"  // 测量
        ).forEach { tag ->
            (parentFragmentManager.findFragmentByTag(tag) as? DialogFragment)?.dismiss()
        }
    }

    fun disableAllMenuButtons() {
        view?.post {
            // 关闭所有子对话框
            closeAllChildDialogs()

            // 遍历所有按钮进行禁用操作
            buttonActions.forEach { (id, _) ->
                view?.findViewById<ImageButton>(id)?.let { button ->
                    button.isEnabled = false
                    button.alpha = 0.5f  // 视觉置灰效果
                }
            }
        }
    }

    // 启用所有菜单按钮并恢复外观
    fun enableAllMenuButtons() {
        view?.post {
            buttonActions.forEach { (id, _) ->
                view?.findViewById<ImageButton>(id)?.let { button ->
                    button.isEnabled = true
                    button.alpha = 1.0f  // 恢复正常透明度

                    // 特别处理需要默认隐藏的按钮
                    when (id) {
                        R.id.btn_zoom_in,
                        R.id.btn_zoom_out,
                        R.id.btn_about -> button.visibility = View.GONE
                    }
                }
            }
        }
    }
}