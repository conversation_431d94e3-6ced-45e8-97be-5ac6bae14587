# Project Structure

## Repository Organization

The repository contains multiple Android projects and supporting materials organized as follows:

### Core Projects

#### VideoTest/ - Primary SDK Development
```
VideoTest/
├── app/                          # Test application and reference implementation
│   └── src/main/java/com/android/rockchip/camera2/
│       ├── integrated/           # Main activities and UI components
│       │   ├── MainActivity.java # Primary test interface using TpVideoSystem
│       │   └── settings/         # Configuration dialogs
│       └── dialogs/              # UI dialog components
├── CodecUtils/                   # Core SDK module (compiles to AAR)
│   └── src/main/java/com/android/rockchip/camera2/
│       ├── video/                # Video processing core classes
│       │   ├── TpVideoSystem.java    # Main SDK entry point
│       │   ├── VideoEncoder.java     # Video encoding and recording
│       │   ├── CameraManagerHelper.java # Camera control
│       │   └── CaptureImageHelper.java  # Image capture
│       ├── util/                 # Utility classes
│       │   ├── SMBFileUploader.java  # Network file upload
│       │   ├── TouptekIspParam.java  # ISP parameter control
│       │   └── HdmiService.java      # HDMI interface management
│       ├── rtsp/                 # RTSP streaming functionality
│       ├── service/              # Background services
│       └── view/                 # Custom UI components
│           ├── TpTextureView.java    # Enhanced texture view
│           └── TpRoiView.java        # ROI interaction component
└── docs/                         # Technical documentation and reports
```

#### XCamView/ - Customer Application Example
```
XCamView/
├── app/                          # Customer-facing application
└── build.gradle.kts              # Kotlin DSL build configuration
```

#### touptek_serial_rk/ - Hardware Communication
```
touptek_serial_rk/
├── app/                          # JNI wrapper and Java interfaces
└── native/                       # C++ serial communication implementation
```

### Supporting Materials

#### TP2HD-VisionSDK/ - SDK Distribution
```
TP2HD-VisionSDK/
├── TP2HD-VisionSDK.aar          # Compiled SDK library
└── TP2HD-VisionSDK Developer Guide/ # API documentation
```

#### Documentation & Analysis
```
docs/                             # Technical analysis documents
AndroidStudio/                    # Comprehensive project guide
重要：/                           # Development milestone archives
```

## Package Structure (SDK Core)

### Primary Package: `com.android.rockchip.camera2`

#### video/ - Core Video Processing
- **TpVideoSystem** - Main SDK entry point with dual-layer API design
- **VideoEncoder** - H.264 encoding and MP4 recording
- **CameraManagerHelper** - Camera2 API wrapper with Builder pattern
- **CaptureImageHelper** - High-resolution image capture
- **TpVideoConfig** - Centralized configuration management
- **EncoderConfig** - Video encoding parameter configuration

#### util/ - Utility Classes
- **SMBFileUploader** - Samba/CIFS file sharing (replaces deprecated SambaUploader)
- **FileStorageUtils** - File path and storage management
- **TouptekIspParam** - ISP parameter definitions and control
- **HdmiService** - HDMI connection state management
- **TransformUtils** - Image transformation utilities

#### view/ - Custom UI Components
- **TpTextureView** - Enhanced TextureView with gesture support
- **TpRoiView** - ROI selection and visualization
- **TpImageView** - Image display with zoom/pan capabilities

#### rtsp/ - Streaming Services
- **RTSPManager** - RTSP server management
- **service/RTSPService** - Background streaming service

#### service/ - Background Services
- **TpctrlService** - Hardware communication service
- **TpctrlSocketService** - Socket-based parameter control

## Naming Conventions

### Class Naming
- **Tp prefix** - All SDK public classes use "Tp" prefix (e.g., TpVideoSystem, TpTextureView)
- **Helper suffix** - Utility classes often use "Helper" suffix
- **Manager suffix** - Service management classes use "Manager" suffix
- **Service suffix** - Background services use "Service" suffix

### Package Organization
- **Functional grouping** - Classes grouped by primary function (video/, util/, view/)
- **Subdirectory preference** - New code organized into subdirectories rather than root packages
- **Consistent hierarchy** - Maintains clear separation between core functionality and utilities

## Architecture Patterns

### Builder Pattern
Used extensively for complex object configuration:
- `TpVideoSystem.builder()`
- `VideoEncoder.builder()`
- `CameraManagerHelper.builder()`

### Singleton Pattern
Applied to system-wide services:
- `RTSPManager.getInstance()`
- `HdmiService.getInstance()`

### Callback/Listener Pattern
Consistent callback naming with "Tp" prefix and "Listener" suffix:
- `TpVideoSystemListener`
- `TpCaptureListener`

### Dual-Layer API Design
- **High-level API** (80% of users) - Simple, encapsulated functionality
- **Low-level API** (20% of users) - Advanced customization through delegation

## File Organization Best Practices

1. **Modular separation** - Core SDK (CodecUtils) separate from test app
2. **Clear boundaries** - Distinct packages for different functional areas
3. **Documentation co-location** - Technical docs alongside relevant code
4. **Version control** - Milestone archives preserve development history
5. **Build separation** - Independent build configurations for different targets