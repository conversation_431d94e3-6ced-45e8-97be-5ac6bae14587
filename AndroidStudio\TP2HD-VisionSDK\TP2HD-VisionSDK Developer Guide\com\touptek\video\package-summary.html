<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>com.touptek.video</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.video">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.touptek.video" class="title">程序包 com.touptek.video</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.touptek.video</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">TpIspParam 枚举类用于定义和管理摄像头ISP参数。</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TpIspParam.OnDataChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">数据变化监听器接口</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="TpIspParam.OnSerialStateChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnSerialStateChangedListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">串口状态变化监听器接口</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpIspParam.ParamData.html" title="com.touptek.video中的类">TpIspParam.ParamData</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">参数数据结构</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">场景信息数据类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">TpVideoConfig - ToupTek视频配置类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TpVideoConfig.BitrateMode.html" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">比特率模式</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TpVideoConfig.VideoCodec.html" title="enum class in com.touptek.video">TpVideoConfig.VideoCodec</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">视频编码格式</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">TpVideoSystem - ToupTek视频系统类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">流类型枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TpVideoSystem.TpVideoSystemAdapter.html" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">视频系统监听器适配器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">视频系统监听器接口</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
