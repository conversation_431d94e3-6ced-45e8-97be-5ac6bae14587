<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>T - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: T">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="../com/touptek/utils/TpSambaClient.html#testConnection(com.touptek.utils.TpSambaClient.UploadListener)" class="member-name-link">testConnection(TpSambaClient.UploadListener)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">测试Samba服务器连接</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#timestamp" class="member-name-link">timestamp</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#togglePreviewPause()" class="member-name-link">togglePreviewPause()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">切换预览暂停状态</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#toggleWifi(boolean)" class="member-name-link">toggleWifi(boolean)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">切换WiFi开关状态</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html#toString()" class="member-name-link">toString()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.ParamData.html#toString()" class="member-name-link">toString()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.ParamData.html" title="com.touptek.video中的类">TpIspParam.ParamData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#toString()" class="member-name-link">toString()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#toString()" class="member-name-link">toString()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_BANDWIDTH" class="member-name-link">TOUPTEK_PARAM_BANDWIDTH</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">带宽控制，影响图像处理的速度和质量</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_BRIGHTNESS" class="member-name-link">TOUPTEK_PARAM_BRIGHTNESS</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">亮度，调整图像整体明亮度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_COLORORGRAY" class="member-name-link">TOUPTEK_PARAM_COLORORGRAY</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">彩色/灰度模式选择，0:彩色，1:灰度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_COLORTONE" class="member-name-link">TOUPTEK_PARAM_COLORTONE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">色彩色调，预设的色彩风格</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_CONTRAST" class="member-name-link">TOUPTEK_PARAM_CONTRAST</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">对比度，控制图像明暗层次</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_CTBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_CTBLUEGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">彩色温度蓝色通道增益，用于手动调整色温</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_CTGREENGAIN" class="member-name-link">TOUPTEK_PARAM_CTGREENGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">彩色温度绿色通道增益，用于手动调整色温</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_CTREDGAIN" class="member-name-link">TOUPTEK_PARAM_CTREDGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">彩色温度红色通道增益，用于手动调整色温</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_DARKENHANCE" class="member-name-link">TOUPTEK_PARAM_DARKENHANCE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">暗部增强，提升阴影区域细节</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_DENOISE" class="member-name-link">TOUPTEK_PARAM_DENOISE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">降噪参数，减少图像噪点</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_EXPOSURECHOICE" class="member-name-link">TOUPTEK_PARAM_EXPOSURECHOICE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">曝光模式选择，0:手动曝光，1:自动曝光</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_EXPOSURECOMPENSATION" class="member-name-link">TOUPTEK_PARAM_EXPOSURECOMPENSATION</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">曝光补偿，调整自动曝光的目标亮度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_EXPOSUREGAIN" class="member-name-link">TOUPTEK_PARAM_EXPOSUREGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">曝光增益，放大传感器信号，仅在手动曝光模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_EXPOSURETIME" class="member-name-link">TOUPTEK_PARAM_EXPOSURETIME</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">曝光时间，单位为毫秒，仅在手动曝光模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_FLIP" class="member-name-link">TOUPTEK_PARAM_FLIP</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">翻转效果，0:关闭，1:开启</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_GAMMA" class="member-name-link">TOUPTEK_PARAM_GAMMA</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">Gamma校正，调整图像亮度非线性分布</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_HUE" class="member-name-link">TOUPTEK_PARAM_HUE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">色调，调整图像色彩倾向</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_HZ" class="member-name-link">TOUPTEK_PARAM_HZ</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">频率，用于抑制电源频率引起的闪烁，0:50Hz，1:60Hz，2:禁用</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_ISP_DEFAULT_TYPE" class="member-name-link">TOUPTEK_PARAM_ISP_DEFAULT_TYPE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">场景选择参数，预设的参数组合</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_LDCRATIO" class="member-name-link">TOUPTEK_PARAM_LDCRATIO</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">低动态范围对比度比率，调整低光照条件下的对比度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_MIRROR" class="member-name-link">TOUPTEK_PARAM_MIRROR</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">镜像效果，0:关闭，1:开启</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_ROI_HEIGHT" class="member-name-link">TOUPTEK_PARAM_ROI_HEIGHT</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">ROI区域高度，仅在ROI白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_ROI_LEFT" class="member-name-link">TOUPTEK_PARAM_ROI_LEFT</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">ROI区域左边界坐标，仅在ROI白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_ROI_TOP" class="member-name-link">TOUPTEK_PARAM_ROI_TOP</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">ROI区域上边界坐标，仅在ROI白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_ROI_WIDTH" class="member-name-link">TOUPTEK_PARAM_ROI_WIDTH</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">ROI区域宽度，仅在ROI白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_SATURATION" class="member-name-link">TOUPTEK_PARAM_SATURATION</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">饱和度，控制色彩鲜艳程度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_SHARPNESS" class="member-name-link">TOUPTEK_PARAM_SHARPNESS</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">锐化参数，控制图像边缘清晰度</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_VERSION" class="member-name-link">TOUPTEK_PARAM_VERSION</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">版本号，只读参数</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_WBBLUEGAIN" class="member-name-link">TOUPTEK_PARAM_WBBLUEGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">蓝色通道的白平衡增益，仅在手动白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_WBCHOICE" class="member-name-link">TOUPTEK_PARAM_WBCHOICE</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">白平衡模式选择，0:手动，1:自动，2:ROI</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_WBGREENGAIN" class="member-name-link">TOUPTEK_PARAM_WBGREENGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">绿色通道的白平衡增益，仅在手动白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_WBREDGAIN" class="member-name-link">TOUPTEK_PARAM_WBREDGAIN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">红色通道的白平衡增益，仅在手动白平衡模式下有效</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#TOUPTEK_PARAM_WDREXPRATIO" class="member-name-link">TOUPTEK_PARAM_WDREXPRATIO</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">宽动态范围曝光比率，增强高对比度场景的细节</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html" class="type-name-link" title="com.touptek.utils中的类">TpFileManager</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">TpFileManager 类提供文件存储相关的工具方法。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#%3Cinit%3E()" class="member-name-link">TpFileManager()</a> - 类的构造器 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpFileManager.StorageListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的接口</dt>
<dd>
<div class="block">存储设备变化监听接口。</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html" class="type-name-link" title="com.touptek.utils中的类">TpHdmiMonitor</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">TpHdmiMonitor 类用于检测和管理HDMI输入状态。</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#%3Cinit%3E()" class="member-name-link">TpHdmiMonitor()</a> - 类的构造器 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.HdmiListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpHdmiMonitor.HdmiListener</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的接口</dt>
<dd>
<div class="block">定义 HDMI 状态变化监听接口。</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html" class="type-name-link" title="com.touptek.ui中的类">TpImageView</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的类</dt>
<dd>
<div class="block">TpImageView - 模仿Android系统相册的可缩放ImageView</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#%3Cinit%3E(android.content.Context)" class="member-name-link">TpImageView(Context)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet)" class="member-name-link">TpImageView(Context, AttributeSet)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)" class="member-name-link">TpImageView(Context, AttributeSet, int)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpImageView.OnZoomChangeListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpImageView.OnZoomChangeListener</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的接口</dt>
<dd>
<div class="block">缩放变化监听器</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html" class="type-name-link" title="enum class in com.touptek.video">TpIspParam</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的Enum Class</dt>
<dd>
<div class="block">TpIspParam 枚举类用于定义和管理摄像头ISP参数。</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.OnDataChangedListener.html" class="type-name-link" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的接口</dt>
<dd>
<div class="block">数据变化监听器接口</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.OnSerialStateChangedListener.html" class="type-name-link" title="com.touptek.video中的接口">TpIspParam.OnSerialStateChangedListener</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的接口</dt>
<dd>
<div class="block">串口状态变化监听器接口</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.ParamData.html" class="type-name-link" title="com.touptek.video中的类">TpIspParam.ParamData</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>
<div class="block">参数数据结构</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html" class="type-name-link" title="com.touptek.video中的类">TpIspParam.SceneInfo</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>
<div class="block">场景信息数据类</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">TpNetworkMonitor 类用于管理WiFi、以太网和热点相关操作。</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#%3Cinit%3E(android.content.Context,com.touptek.utils.TpNetworkMonitor.NetworkStateListener,androidx.activity.result.ActivityResultLauncher)" class="member-name-link">TpNetworkMonitor(Context, TpNetworkMonitor.NetworkStateListener, ActivityResultLauncher&lt;Intent&gt;)</a> - 类的构造器 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">创建一个新的网络管理器实例</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.HotspotInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">热点信息类</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">网络接口信息类</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的接口</dt>
<dd>
<div class="block">网络状态监听器接口</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">WiFi连接信息类</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html" class="type-name-link" title="com.touptek.ui中的类">TpRoiView</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的类</dt>
<dd>
<div class="block">TpRoiView - 可拖动的感兴趣区域(ROI)选择框</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#%3Cinit%3E(android.content.Context)" class="member-name-link">TpRoiView(Context)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet)" class="member-name-link">TpRoiView(Context, AttributeSet)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)" class="member-name-link">TpRoiView(Context, AttributeSet, int)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html" class="type-name-link" title="com.touptek.utils中的类">TpSambaClient</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">SambaUploader类用于连接Samba服务器并上传图片和视频。</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#%3Cinit%3E(android.content.Context)" class="member-name-link">TpSambaClient(Context)</a> - 类的构造器 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.DirectoryListListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的接口</dt>
<dd>
<div class="block">目录列表回调接口</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" class="type-name-link" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的类</dt>
<dd>
<div class="block">SMB连接配置类</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.UploadListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a> - <a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a>中的接口</dt>
<dd>
<div class="block">上传结果回调接口</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html" class="type-name-link" title="com.touptek.ui中的类">TpTextureView</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的类</dt>
<dd>
<div class="block">TpTextureView - 支持缩放和平移的TextureView</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#%3Cinit%3E(android.content.Context)" class="member-name-link">TpTextureView(Context)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet)" class="member-name-link">TpTextureView(Context, AttributeSet)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)" class="member-name-link">TpTextureView(Context, AttributeSet, int)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpTextureView.OnZoomChangeListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpTextureView.OnZoomChangeListener</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的接口</dt>
<dd>
<div class="block">缩放变化监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html" class="type-name-link" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的接口</dt>
<dd>
<div class="block">触摸事件处理策略接口</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html" class="type-name-link" title="com.touptek.video中的类">TpVideoConfig</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>
<div class="block">TpVideoConfig - ToupTek视频配置类</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.BitrateMode.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的Enum Class</dt>
<dd>
<div class="block">比特率模式</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.Builder.html" class="type-name-link" title="com.touptek.video中的类">TpVideoConfig.Builder</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.VideoCodec.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoConfig.VideoCodec</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的Enum Class</dt>
<dd>
<div class="block">视频编码格式</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html" class="type-name-link" title="com.touptek.ui中的类">TpVideoPlayerView</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的类</dt>
<dd>
<div class="block">TpVideoPlayerView - 高层封装的视频播放组件</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#%3Cinit%3E(android.content.Context)" class="member-name-link">TpVideoPlayerView(Context)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet)" class="member-name-link">TpVideoPlayerView(Context, AttributeSet)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)" class="member-name-link">TpVideoPlayerView(Context, AttributeSet, int)</a> - 类的构造器 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpVideoPlayerView.VideoPlayerListener</a> - <a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a>中的接口</dt>
<dd>
<div class="block">视频播放器监听器接口</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html" class="type-name-link" title="com.touptek.video中的类">TpVideoSystem</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>
<div class="block">TpVideoSystem - ToupTek视频系统类</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#%3Cinit%3E(androidx.appcompat.app.AppCompatActivity)" class="member-name-link">TpVideoSystem(AppCompatActivity)</a> - 类的构造器 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">构造函数 - 使用默认1080P配置</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#%3Cinit%3E(androidx.appcompat.app.AppCompatActivity,com.touptek.video.TpVideoConfig)" class="member-name-link">TpVideoSystem(AppCompatActivity, TpVideoConfig)</a> - 类的构造器 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">构造函数 - 使用自定义配置</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.StreamType.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的Enum Class</dt>
<dd>
<div class="block">流类型枚举</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html" class="type-name-link" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的类</dt>
<dd>
<div class="block">视频系统监听器适配器</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" class="type-name-link" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a> - <a href="../com/touptek/video/package-summary.html">com.touptek.video</a>中的接口</dt>
<dd>
<div class="block">视频系统监听器接口</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html#%3Cinit%3E()" class="member-name-link">TpVideoSystemAdapter()</a> - 类的构造器 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
