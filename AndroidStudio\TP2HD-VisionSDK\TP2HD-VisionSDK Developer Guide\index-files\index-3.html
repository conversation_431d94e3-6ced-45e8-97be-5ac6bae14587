<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>C - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: C">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="../com/touptek/video/TpVideoSystem.StreamType.html#CAMERA" class="member-name-link">CAMERA</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></dt>
<dd>
<div class="block">摄像头流</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#captureImage(java.lang.String)" class="member-name-link">captureImage(String)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">捕获图像</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.BitrateMode.html#CBR" class="member-name-link">CBR</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.BitrateMode.html" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/package-summary.html">com.touptek.ui</a> - 程序包 com.touptek.ui</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/package-summary.html">com.touptek.utils</a> - 程序包 com.touptek.utils</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/package-summary.html">com.touptek.video</a> - 程序包 com.touptek.video</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.BitrateMode.html#CQ" class="member-name-link">CQ</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.BitrateMode.html" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#createDefault1080P()" class="member-name-link">createDefault1080P()</a> - 类中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>
<div class="block">创建默认1080P配置</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#createDefault4K()" class="member-name-link">createDefault4K()</a> - 类中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>
<div class="block">创建默认4K配置</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#createImagePath(android.content.Context)" class="member-name-link">createImagePath(Context)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">创建带日期后缀的图像文件路径。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String)" class="member-name-link">createImagePath(Context, String, boolean, String)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">创建自定义图像文件路径。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#createVideoPath(android.content.Context)" class="member-name-link">createVideoPath(Context)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">创建带日期后缀的视频文件路径。</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.ParamData.html#current" class="member-name-link">current</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.ParamData.html" title="com.touptek.video中的类">TpIspParam.ParamData</a></dt>
<dd>
<div class="block">参数的当前值</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
