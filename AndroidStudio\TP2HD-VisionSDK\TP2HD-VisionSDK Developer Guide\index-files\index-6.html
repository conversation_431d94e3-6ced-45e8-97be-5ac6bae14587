<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>G - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: G">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="../com/touptek/utils/TpFileManager.html#generateUniqueFileName(java.lang.String)" class="member-name-link">generateUniqueFileName(String)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">生成唯一的文件名，避免文件覆盖。</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getAllCurrentValues()" class="member-name-link">getAllCurrentValues()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取所有参数的当前值</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getAllParamInfo()" class="member-name-link">getAllParamInfo()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取所有参数的完整信息</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getAllSceneNames()" class="member-name-link">getAllSceneNames()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取所有可用场景名称（系统+用户）</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#getAvailableNetworkInterfaces()" class="member-name-link">getAvailableNetworkInterfaces()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">获取所有可用的网络接口信息</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#getAvailableStorageSpace(java.lang.String)" class="member-name-link">getAvailableStorageSpace(String)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">获取指定路径的可用存储空间。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getBitRate()" class="member-name-link">getBitRate()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getBitrateMode()" class="member-name-link">getBitrateMode()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getCameraManager()" class="member-name-link">getCameraManager()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取相机管理器（高级用户）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getCodec()" class="member-name-link">getCodec()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#getConnectionParams()" class="member-name-link">getConnectionParams()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">获取当前连接配置</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#getCurrentHotspotState()" class="member-name-link">getCurrentHotspotState()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">获取当前热点状态</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getCurrentPosition()" class="member-name-link">getCurrentPosition()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取当前播放位置</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#getCurrentScale()" class="member-name-link">getCurrentScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取当前缩放比例</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#getCurrentScale()" class="member-name-link">getCurrentScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取当前缩放比例</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getCurrentStreamType()" class="member-name-link">getCurrentStreamType()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前流类型</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getCurrentValue(com.touptek.video.TpIspParam)" class="member-name-link">getCurrentValue(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的当前值</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getCurrentVideoPath()" class="member-name-link">getCurrentVideoPath()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前播放的视频路径（简化版）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getCurrentVideoPlaybackSpeed()" class="member-name-link">getCurrentVideoPlaybackSpeed()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前视频的播放速度（简化版）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getCurrentVideoPosition()" class="member-name-link">getCurrentVideoPosition()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前播放位置（简化版）</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#getCurrentWifiState()" class="member-name-link">getCurrentWifiState()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">获取当前WiFi连接状态</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getDefaultValue(com.touptek.video.TpIspParam)" class="member-name-link">getDefaultValue(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的默认值</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html#getDescription()" class="member-name-link">getDescription()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></dt>
<dd>
<div class="block">获取网络接口的描述信息</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getDuration()" class="member-name-link">getDuration()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取视频总时长</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#getExternalStoragePath(android.content.Context)" class="member-name-link">getExternalStoragePath(Context)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">获取外部存储设备根目录。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#getFileSystemType(java.lang.String)" class="member-name-link">getFileSystemType(String)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">判断文件系统的格式。</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#getFitScreenScale()" class="member-name-link">getFitScreenScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取适应屏幕的缩放比例</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#getFrameRate()" class="member-name-link">getFrameRate()</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">获取当前HDMI输入的帧率</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getFrameRate()" class="member-name-link">getFrameRate()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#getHdmiResolution()" class="member-name-link">getHdmiResolution()</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">获取当前HDMI输入的分辨率</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getHeight()" class="member-name-link">getHeight()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getImageCapture()" class="member-name-link">getImageCapture()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取图像捕获器（高级用户）</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.HotspotInfo.html#getInfo()" class="member-name-link">getInfo()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.HotspotInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a></dt>
<dd>
<div class="block">获取热点配置信息</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#getInstance()" class="member-name-link">getInstance()</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">获取 TpHdmiMonitor 的单例实例。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#getInternalStoragePath(android.content.Context)" class="member-name-link">getInternalStoragePath(Context)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">获取内部存储设备根目录。</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html#getIpAddress()" class="member-name-link">getIpAddress()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></dt>
<dd>
<div class="block">获取网络接口的IP地址</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getIsDisableValue(com.touptek.video.TpIspParam)" class="member-name-link">getIsDisableValue(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的禁用状态</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getKeyFrameInterval()" class="member-name-link">getKeyFrameInterval()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#getMaxScale()" class="member-name-link">getMaxScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取当前设置的最大缩放倍数</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#getMaxScale()" class="member-name-link">getMaxScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取当前设置的最大缩放倍数</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getMaxValue(com.touptek.video.TpIspParam)" class="member-name-link">getMaxValue(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的最大值。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.VideoCodec.html#getMimeType()" class="member-name-link">getMimeType()</a> - enum class中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.VideoCodec.html" title="enum class in com.touptek.video">TpVideoConfig.VideoCodec</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#getMinScale()" class="member-name-link">getMinScale()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">获取当前设置的最小缩放倍数</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getMinValue(com.touptek.video.TpIspParam)" class="member-name-link">getMinValue(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的最小值。</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html#getName()" class="member-name-link">getName()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></dt>
<dd>
<div class="block">获取网络接口名称</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getParamByIndex(int)" class="member-name-link">getParamByIndex(int)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">根据索引获取对应的枚举项。</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getParamId()" class="member-name-link">getParamId()</a> - enum class中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的协议ID</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getParamInfo(com.touptek.video.TpIspParam)" class="member-name-link">getParamInfo(TpIspParam)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取参数的完整信息</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getPlaybackSpeed()" class="member-name-link">getPlaybackSpeed()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取当前播放速度</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#getRemoteDirectories(com.touptek.utils.TpSambaClient.DirectoryListListener)" class="member-name-link">getRemoteDirectories(TpSambaClient.DirectoryListListener)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">获取远程目录列表</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#getRemotePath()" class="member-name-link">getRemotePath()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#getSceneInfo(java.lang.String)" class="member-name-link">getSceneInfo(String)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">获取场景详细信息</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#getServerIp()" class="member-name-link">getServerIp()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#getShareName()" class="member-name-link">getShareName()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getSize()" class="member-name-link">getSize()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html#getSsid()" class="member-name-link">getSsid()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a></dt>
<dd>
<div class="block">获取当前连接的WiFi网络名称</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getStreamingService()" class="member-name-link">getStreamingService()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取推流服务（高级用户）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getStreamUrl()" class="member-name-link">getStreamUrl()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取推流URL</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getTextureView()" class="member-name-link">getTextureView()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取内部的TpTextureView实例</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#getTouchEventHandler()" class="member-name-link">getTouchEventHandler()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">获取当前的触摸事件处理器</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getTvPreviewHelper()" class="member-name-link">getTvPreviewHelper()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取TV预览助手实例（高级功能）</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#getUsername()" class="member-name-link">getUsername()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.BitrateMode.html#getValue()" class="member-name-link">getValue()</a> - enum class中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.BitrateMode.html" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getVideoConfig()" class="member-name-link">getVideoConfig()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取当前视频配置</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getVideoDecoder()" class="member-name-link">getVideoDecoder()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取视频解码器（高级用户）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getVideoDuration()" class="member-name-link">getVideoDuration()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取视频总时长（简化版）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#getVideoEncoder()" class="member-name-link">getVideoEncoder()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">获取视频编码器（高级用户）</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getVideoPlayerListener()" class="member-name-link">getVideoPlayerListener()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取视频播放器监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#getVideoSystem()" class="member-name-link">getVideoSystem()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">获取内部的TpVideoSystem实例（供高级用户使用）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.html#getWidth()" class="member-name-link">getWidth()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>
