package com.android.rockchip.camera2.integrated.browser;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.GestureDetector;
import android.graphics.Matrix;
import android.animation.ValueAnimator;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.touptek.ui.TpImageView;
import com.touptek.video.TpImageLoader;
import com.touptek.video.TpVideoSystem;
import com.touptek.video.TpVideoConfig;
import com.android.rockchip.mediacodecnew.R;

// Matrix和RectF不再需要，使用View缩放方案

import java.io.File;

/**
 * ImageVideoCompareActivity - 图片视频对比界面
 *
 * 提供左右分屏对比功能：
 * - 左侧：显示用户选择的图片
 * - 右侧：显示实时相机预览
 */
public class ImageVideoCompareActivity extends AppCompatActivity {
    private static final String TAG = "ImageVideoCompare";
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    // UI组件
    private TpImageView imageView;
    private TextureView textureView;
    private TextView tvImageInfo;
    private TextView tvPreviewInfo;
    private Button btnBack;

    // 视频系统（简化模式）
    private TpVideoSystem videoSystem;

    // 图片路径
    private String imagePath;

    // Camera实际输出尺寸缓存
    private android.util.Size cameraActualOutputSize;

    // 手势检测器
    private ScaleGestureDetector scaleGestureDetector;
    private GestureDetector gestureDetector;

    // Matrix变换（与TpImageView一致）
    private final Matrix matrix = new Matrix();
    private final Matrix savedMatrix = new Matrix();
    private final Matrix baseMatrix = new Matrix(); // 存储基础变换矩阵

    // 缩放相关（与TpImageView一致）
    private float currentScale = 1.0f;
    private float baseScale = 1.0f;
    private float dynamicMinScale = 0.3f;
    private static final float MAX_SCALE = 5.0f;
    private static final float SCALE_SENSITIVITY = 3.0f; // 缩放敏感度增强

    // 动画相关
    private ValueAnimator scaleAnimator;
    private ValueAnimator flingAnimator;

    // 状态标志
    private boolean isInitialized = false;
    private boolean isScaling = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_video_compare);

        // 获取传入的图片路径
        imagePath = getIntent().getStringExtra("image_path");
        if (imagePath == null) {
            Log.e(TAG, "未接收到图片路径");
            Toast.makeText(this, "图片路径错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化UI组件
        initViews();

        // 加载图片
        loadImage();

        // 检查相机权限并启动预览
        checkCameraPermissionAndStart();
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        imageView = findViewById(R.id.image_view);
        textureView = findViewById(R.id.texture_view);
        tvImageInfo = findViewById(R.id.tv_image_info);
        tvPreviewInfo = findViewById(R.id.tv_preview_info);
        btnBack = findViewById(R.id.btn_back);

        // 设置返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 初始化手势检测器
        setupGestureDetectors();

        Log.d(TAG, "UI组件初始化完成");
    }

    /**
     * 初始化手势检测器（与TpImageView一致）
     */
    private void setupGestureDetectors() {
        // 缩放手势检测器（与TpImageView一致的实现）
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale begin - span: " + detector.getCurrentSpan());
                isScaling = true;
                return true;
            }

            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!isInitialized) {
                    Log.w(TAG, "⚠️ Scale attempted but not initialized");
                    return false;
                }

                float scaleFactor = detector.getScaleFactor();
                Log.d(TAG, "🔍 Raw scale factor: " + scaleFactor);

                // 增强缩放敏感度 - 让小幅度手势也能产生明显效果（与TpImageView一致）
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float newScale = Math.max(dynamicMinScale, Math.min(currentScale * enhancedFactor, MAX_SCALE));

                if (newScale != currentScale) {
                    float actualFactor = newScale / currentScale;
                    currentScale = newScale;

                    // 使用Matrix进行焦点缩放（与TpImageView一致）
                    matrix.postScale(actualFactor, actualFactor, detector.getFocusX(), detector.getFocusY());
                    updateTextureMatrix(matrix);
                    Log.d(TAG, "✅ Scale applied: " + currentScale + " (factor: " + actualFactor + ")");
                }
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale end");
                isScaling = false;
                syncCurrentScale(); // 同步状态（与TpImageView一致）
            }
        });

        // 平移手势检测器（与TpImageView一致）
        gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                if (!isInitialized || isScaling) return false;

                // 检查是否在最小缩放状态（与TpImageView一致）
                if (isAtMinimumScale()) {
                    Log.d(TAG, "🚫 Dragging disabled at minimum scale");
                    return false;
                }

                // 使用Matrix进行平移（与TpImageView一致）
                matrix.postTranslate(-distanceX, -distanceY);
                updateTextureMatrix(matrix);
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                if (!isInitialized || isScaling || isAtMinimumScale()) return false;
                startFlingAnimation(velocityX, velocityY);
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!isInitialized) return false;

                // 获取真实的缩放状态（与TpImageView一致）
                float realScale = getCurrentScaleFactor();
                syncCurrentScale(); // 同步状态

                float targetScale = realScale > baseScale * 1.5f ?
                    baseScale : // 缩小到适配大小
                    Math.min(MAX_SCALE, baseScale * 3f); // 放大到3倍

                instantScaleTo(targetScale, e.getX(), e.getY());
                return true;
            }
        });

        // 设置TextureView的触摸监听器（与TpImageView一致）
        textureView.setOnTouchListener((v, event) -> {
            // 优先处理缩放手势
            boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);

            // 只有在非缩放状态下才处理平移
            boolean gestureHandled = false;
            if (!scaleGestureDetector.isInProgress()) {
                gestureHandled = gestureDetector.onTouchEvent(event);
            }

            return scaleHandled || gestureHandled;
        });
    }





    /**
     * 加载选中的图片
     */
    private void loadImage() {
        try {
            // 使用TpImageLoader加载图片
            TpImageLoader.loadFullImage(imagePath, imageView);

            // 显示图片信息
            File imageFile = new File(imagePath);
            String fileName = imageFile.getName();
            tvImageInfo.setText(fileName);

            Log.d(TAG, "图片加载完成: " + fileName);

        } catch (Exception e) {
            Log.e(TAG, "加载图片失败", e);
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 检查相机权限并启动预览
     */
    private void checkCameraPermissionAndStart() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        } else {
            requestCameraPermission();
        }
    }

    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                REQUEST_CAMERA_PERMISSION);
    }

    /**
     * 启动相机预览（使用TpVideoSystem）
     */
    private void startCameraPreview() {
        try {
            // 创建简化的视频配置
            TpVideoConfig config = TpVideoConfig.createDefault4K();
            videoSystem = new TpVideoSystem(this, config);

            // 设置监听器
            videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
                @Override
                public void onCameraStarted() {
                    Log.d(TAG, "对比模式相机启动完成");
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("实时预览");
                        // 应用CENTER_CROP Matrix变换
                        configureTransform();

                        // 延迟验证和重新应用变换（确保Camera完全稳定）
                        textureView.postDelayed(() -> {
                            Log.d(TAG, "执行延迟变换验证");
                            configureTransform();
                        }, 1000);
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "对比模式相机错误: " + errorMessage);
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("预览错误");
                        Toast.makeText(ImageVideoCompareActivity.this,
                            "预览错误: " + errorMessage, Toast.LENGTH_SHORT).show();
                    });
                }
            });

            // 等待TextureView准备好后初始化
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            } else {
                textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture可用，TextureView尺寸: " + width + "x" + height);
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture尺寸改变: " + width + "x" + height);
                        // 重新设置buffer size和变换
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surface) {
                        return true;
                    }

                    @Override
                    public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surface) {}
                });
            }

            Log.d(TAG, "TpVideoSystem预览已启动");

        } catch (Exception e) {
            Log.e(TAG, "启动TpVideoSystem预览失败", e);
            Toast.makeText(this, "启动预览失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置SurfaceTexture并初始化视频系统
     * 核心解决方案：设置SurfaceTexture buffer size为Camera输出尺寸，避免变形
     */
    private void setupSurfaceTextureAndInit() {
        if (textureView == null || textureView.getSurfaceTexture() == null) {
            Log.w(TAG, "TextureView或SurfaceTexture未准备好");
            return;
        }

        try {
            // 1. 获取Camera实际输出尺寸
            android.util.Size cameraOutputSize = getCameraActualOutputSize();
            if (cameraOutputSize == null) {
                Log.w(TAG, "无法获取Camera输出尺寸，使用默认初始化");
                initVideoSystem();
                return;
            }

            // 2. 设置SurfaceTexture buffer size为Camera输出尺寸（关键步骤）
            android.graphics.SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
            surfaceTexture.setDefaultBufferSize(cameraOutputSize.getWidth(), cameraOutputSize.getHeight());

            Log.d(TAG, "✅ SurfaceTexture buffer size已设置为: " + cameraOutputSize.getWidth() + "x" + cameraOutputSize.getHeight());
            Log.d(TAG, "📐 这将避免Camera输出在SurfaceTexture层面的变形");

            // 3. 初始化视频系统
            initVideoSystem();

        } catch (Exception e) {
            Log.e(TAG, "设置SurfaceTexture失败，使用默认初始化", e);
            initVideoSystem();
        }
    }

    /**
     * 初始化视频系统
     */
    private void initVideoSystem() {
        if (videoSystem != null && textureView.getSurfaceTexture() != null) {
            android.view.Surface surface = new android.view.Surface(textureView.getSurfaceTexture());
            videoSystem.initialize(surface);
        }
    }

    /**
     * 获取Camera实际输出尺寸
     * 这是解决变形问题的关键：获取真实的Camera stream输出尺寸
     */
    private android.util.Size getCameraActualOutputSize() {
        if (cameraActualOutputSize != null) {
            return cameraActualOutputSize;
        }

        try {
            if (videoSystem != null && videoSystem.getVideoConfig() != null) {
                // 获取配置的视频尺寸作为基础
                android.util.Size configSize = videoSystem.getVideoConfig().getSize();

                // 缓存结果
                cameraActualOutputSize = configSize;

                Log.d(TAG, "Camera实际输出尺寸: " + configSize.getWidth() + "x" + configSize.getHeight());
                return configSize;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取Camera输出尺寸失败", e);
        }

        // 如果无法获取，返回4K默认尺寸
        cameraActualOutputSize = new android.util.Size(3840, 2160);
        Log.w(TAG, "使用默认Camera输出尺寸: 3840x2160");
        return cameraActualOutputSize;
    }

    /**
     * 配置TextureView的缩放，实现FIT_CENTER效果
     * 与左侧TpImageView保持一致：完整显示，不裁剪，可能有黑边
     * 使用View缩放属性，避开复杂的Matrix变换
     */
    private void configureTransform() {
        if (textureView == null) {
            return;
        }

        textureView.post(() -> {
            try {
                // 获取TextureView的尺寸
                int viewWidth = textureView.getWidth();
                int viewHeight = textureView.getHeight();

                if (viewWidth <= 0 || viewHeight <= 0) {
                    Log.w(TAG, "TextureView尺寸无效: " + viewWidth + "x" + viewHeight);
                    return;
                }

                // 获取Camera实际输出尺寸
                android.util.Size cameraSize = getCameraActualOutputSize();
                int cameraWidth = cameraSize.getWidth();
                int cameraHeight = cameraSize.getHeight();

                Log.d(TAG, "配置View缩放 CENTER_CROP:");
                Log.d(TAG, "  TextureView尺寸: " + viewWidth + "x" + viewHeight);
                Log.d(TAG, "  Camera输出尺寸: " + cameraWidth + "x" + cameraHeight);

                // 计算宽高比
                float cameraRatio = (float) cameraWidth / cameraHeight;   // 16:9 ≈ 1.78
                float viewRatio = (float) viewWidth / viewHeight;         // 接近 1:1

                Log.d(TAG, "  Camera宽高比: " + String.format("%.3f", cameraRatio));
                Log.d(TAG, "  TextureView宽高比: " + String.format("%.3f", viewRatio));

                // 设置缩放中心为TextureView的中心
                textureView.setPivotX(viewWidth / 2f);
                textureView.setPivotY(viewHeight / 2f);

                // 🔧 修复方案：正确处理SurfaceTexture buffer size和View缩放的关系
                //
                // 问题分析：
                // 1. SurfaceTexture buffer size = 3840x2160 (16:9)
                // 2. TextureView size = 959x976 (接近1:1)
                // 3. TextureView会将16:9的内容拉伸到1:1的容器中，导致变形
                // 4. 我们需要通过View缩放来修正这个变形

                // 计算修正缩放：让16:9的内容在1:1容器中正确显示
                float textureViewRatio = (float) viewWidth / viewHeight;  // 容器比例 ≈ 1.0
                float videoRatio = (float) cameraWidth / cameraHeight;    // 视频比例 ≈ 1.78

                Log.d(TAG, "  🔧 修正变形计算:");
                Log.d(TAG, "    TextureView比例: " + String.format("%.3f", textureViewRatio));
                Log.d(TAG, "    视频比例: " + String.format("%.3f", videoRatio));

                if (videoRatio > textureViewRatio) {
                    // 视频更宽，需要在Y轴方向缩小来保持比例
                    baseScaleX = 1.0f;  // X轴保持满宽度
                    baseScaleY = textureViewRatio / videoRatio;  // Y轴缩小到正确比例
                    Log.d(TAG, "    视频更宽，X轴=1.0，Y轴=" + String.format("%.3f", baseScaleY));
                } else {
                    // 视频更高，需要在X轴方向缩小来保持比例
                    baseScaleX = videoRatio / textureViewRatio;  // X轴缩小到正确比例
                    baseScaleY = 1.0f;  // Y轴保持满高度
                    Log.d(TAG, "    视频更高，X轴=" + String.format("%.3f", baseScaleX) + "，Y轴=1.0");
                }

                // 不再应用额外的整体缩放，直接使用比例修正值
                Log.d(TAG, "    效果: 视频将以正确的16:9比例填满容器的一个方向");

                Log.d(TAG, "  最终缩放: scaleX=" + baseScaleX + ", scaleY=" + baseScaleY);
                Log.d(TAG, "  预期比例: " + String.format("%.2f", baseScaleX/baseScaleY) + " (应该≈" + String.format("%.2f", videoRatio) + ")");

                // 计算实际显示尺寸验证
                float actualDisplayWidth = viewWidth * baseScaleX;
                float actualDisplayHeight = viewHeight * baseScaleY;
                float actualRatio = actualDisplayWidth / actualDisplayHeight;

                Log.d(TAG, "  📐 最终显示验证:");
                Log.d(TAG, "    实际显示尺寸: " + String.format("%.1f", actualDisplayWidth) + "x" + String.format("%.1f", actualDisplayHeight));
                Log.d(TAG, "    实际显示比例: " + String.format("%.2f", actualRatio) + ":1");
                Log.d(TAG, "    目标比例: " + String.format("%.2f", videoRatio) + ":1 (16:9)");

                if (Math.abs(actualRatio - videoRatio) < 0.1f) {
                    Log.d(TAG, "    ✅ 比例正确！");
                } else {
                    Log.w(TAG, "    ⚠️ 比例可能有偏差");
                }

                // 初始化Matrix变换（与TpImageView一致）
                baseScale = Math.min(baseScaleX, baseScaleY); // 使用较小的缩放值作为基础缩放
                dynamicMinScale = baseScale;
                currentScale = baseScale;

                // 设置基础Matrix
                baseMatrix.reset();
                baseMatrix.postScale(baseScaleX, baseScaleY);
                baseMatrix.postTranslate(
                    (viewWidth - viewWidth * baseScaleX) / 2f,
                    (viewHeight - viewHeight * baseScaleY) / 2f
                );

                // 初始化当前Matrix
                matrix.set(baseMatrix);
                updateTextureMatrix(matrix);

                // 标记为已初始化
                isInitialized = true;

                Log.d(TAG, "✅ Matrix变换初始化完成:");
                Log.d(TAG, "  baseScale: " + baseScale);
                Log.d(TAG, "  baseScaleX: " + baseScaleX + ", baseScaleY: " + baseScaleY);
                Log.d(TAG, "  缩放中心: (" + (viewWidth/2f) + ", " + (viewHeight/2f) + ")");
                Log.d(TAG, "📐 效果: 保持" + String.format("%.2f", videoRatio) + ":1宽高比，Matrix变换，与TpImageView一致");
                Log.d(TAG, "🎯 初始化状态: isInitialized=" + isInitialized);

            } catch (Exception e) {
                Log.e(TAG, "配置TextureView缩放失败", e);
            }
        });
    }
    /**
     * 应用所有变换（基础缩放 + 手势缩放 + 平移）
     */
    private void applyTransformations() {
        if (textureView == null) return;

        // 计算最终的缩放值
        float finalScaleX = baseScaleX * currentScale;
        float finalScaleY = baseScaleY * currentScale;

        // 智能边界检查：根据缩放程度调整平移范围
        float viewWidth = textureView.getWidth();
        float viewHeight = textureView.getHeight();

        if (viewWidth > 0 && viewHeight > 0) {
            // 计算合理的平移范围（基于视图尺寸和缩放比例）
            float maxTranslateX = viewWidth * 0.5f * Math.max(0, finalScaleX - 1.0f);
            float maxTranslateY = viewHeight * 0.5f * Math.max(0, finalScaleY - 1.0f);

            // 限制平移范围
            translateX = Math.max(-maxTranslateX, Math.min(maxTranslateX, translateX));
            translateY = Math.max(-maxTranslateY, Math.min(maxTranslateY, translateY));
        }

        // 应用变换
        textureView.setScaleX(finalScaleX);
        textureView.setScaleY(finalScaleY);
        textureView.setTranslationX(translateX);
        textureView.setTranslationY(translateY);

        // 暂时移除旋转，先解决缩放问题
        textureView.setRotation(0f); // 不旋转

        Log.d(TAG, "🔧 最终应用的变换:");
        Log.d(TAG, "  finalScaleX: " + finalScaleX);
        Log.d(TAG, "  finalScaleY: " + finalScaleY);
        Log.d(TAG, "  translateX: " + translateX);
        Log.d(TAG, "  translateY: " + translateY);
        Log.d(TAG, "  rotation: 0度");

        // 检查TextureView的实际尺寸
        Log.d(TAG, "🔍 TextureView状态检查:");
        Log.d(TAG, "  TextureView尺寸: " + textureView.getWidth() + "x" + textureView.getHeight());
        Log.d(TAG, "  预期显示尺寸: " + String.format("%.1f", textureView.getWidth() * finalScaleX) + "x" + String.format("%.1f", textureView.getHeight() * finalScaleY));
    }

    /**
     * 重置所有变换到初始状态（Matrix版本）
     */
    private void resetTransformations() {
        if (!isInitialized) return;

        cancelAnimations();
        currentScale = baseScale;

        // 重置Matrix到基础状态
        matrix.set(baseMatrix);
        updateTextureMatrix(matrix);

        Log.d(TAG, "变换已重置到初始状态");
    }

    /**
     * 更新TextureView的Matrix变换（与TpImageView一致）
     */
    private void updateTextureMatrix(Matrix newMatrix) {
        if (textureView == null) return;

        textureView.setTransform(newMatrix);
        textureView.invalidate(); // 强制刷新

        Log.d(TAG, "🔧 Matrix变换已应用到TextureView");
    }

    /**
     * 同步当前缩放状态（与TpImageView一致）
     */
    private void syncCurrentScale() {
        float realScale = getCurrentScaleFactor();
        if (Math.abs(realScale - currentScale) > 0.01f) {
            Log.d(TAG, "🔄 Sync scale: " + currentScale + " -> " + realScale);
            currentScale = realScale;
        }
    }

    /**
     * 获取当前实际的缩放因子
     */
    private float getCurrentScaleFactor() {
        if (!isInitialized) return baseScale;

        float[] values = new float[9];
        matrix.getValues(values);
        return values[Matrix.MSCALE_X]; // 获取X轴缩放值
    }

    /**
     * 检查是否处于最小缩放状态
     */
    private boolean isAtMinimumScale() {
        return Math.abs(currentScale - dynamicMinScale) < 0.01f;
    }

    /**
     * 惯性滑动动画（与TpImageView一致）
     */
    private void startFlingAnimation(float velocityX, float velocityY) {
        cancelAnimations();

        float startVelX = velocityX * 0.3f;
        float startVelY = velocityY * 0.3f;

        flingAnimator = ValueAnimator.ofFloat(0f, 1f);
        flingAnimator.setDuration(800L);
        flingAnimator.setInterpolator(new DecelerateInterpolator());

        flingAnimator.addUpdateListener(animator -> {
            float progress = (Float) animator.getAnimatedValue();
            float currentVelX = startVelX * (1f - progress);
            float currentVelY = startVelY * (1f - progress);

            matrix.postTranslate(currentVelX * 0.016f, currentVelY * 0.016f);
            updateTextureMatrix(matrix);
        });

        flingAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                syncCurrentScale();
            }
        });

        flingAnimator.start();
    }

    /**
     * 瞬间缩放到指定比例（与TpImageView一致）
     */
    private void instantScaleTo(float targetScale, float focusX, float focusY) {
        if (!isInitialized) return;

        cancelAnimations();
        float scaleFactor = targetScale / currentScale;
        currentScale = targetScale;

        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
        updateTextureMatrix(matrix);

        // 缩放完成后验证状态一致性
        syncCurrentScale();
    }

    /**
     * 取消所有动画
     */
    private void cancelAnimations() {
        if (scaleAnimator != null) {
            scaleAnimator.cancel();
        }
        if (flingAnimator != null) {
            flingAnimator.cancel();
        }
    }






    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
            Log.d(TAG, "TpVideoSystem预览已停止");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startCameraPreview();
            } else {
                Toast.makeText(this, "需要相机权限才能显示实时预览", Toast.LENGTH_LONG).show();
                tvPreviewInfo.setText("无相机权限");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 如果视频系统已经初始化，重新启动
        if (videoSystem != null && ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            }
        } else if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopCameraPreview();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cancelAnimations(); // 取消所有动画
        stopCameraPreview();
        Log.d(TAG, "Activity已销毁，资源已清理");
    }
}
