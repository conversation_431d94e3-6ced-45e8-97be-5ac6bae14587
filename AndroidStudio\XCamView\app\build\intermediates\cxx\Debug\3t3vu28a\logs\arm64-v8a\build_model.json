{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a", "soFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\3t3vu28a\\obj\\arm64-v8a", "soRepublishFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 27, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": ["-std=c++11"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx", "intermediatesBaseFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates", "intermediatesFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app", "moduleBuildFile": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build.gradle.kts", "makeFile": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkVersion": "16.1.4479499", "ndkSupportedAbiList": ["<PERSON><PERSON><PERSON>", "armeabi-v7a", "arm64-v8a", "mips", "mips64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "GNUSTL_STATIC", "ndkMetaAbiList": [{"name": "<PERSON><PERSON><PERSON>", "bitness": 32, "isDefault": false, "isDeprecated": true, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "unknown-llvm-triple"}, {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "mips", "bitness": 32, "isDefault": false, "isDeprecated": true, "architecture": "mips", "triple": "mipsel-linux-android", "llvmTriple": "unknown-llvm-triple"}, {"name": "mips64", "bitness": 64, "isDefault": false, "isDeprecated": true, "architecture": "mips64", "triple": "mips64el-linux-android", "llvmTriple": "unknown-llvm-triple"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"GNUSTL_SHARED": {"armeabi": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\armeabi\\libgnustl_shared.so", "armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\armeabi-v7a\\libgnustl_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\arm64-v8a\\libgnustl_shared.so", "mips": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\mips\\libgnustl_shared.so", "mips64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\mips64\\libgnustl_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\x86\\libgnustl_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\gnu-libstdc++\\4.9\\libs\\x86_64\\libgnustl_shared.so"}, "GNUSTL_STATIC": {}, "LIBCXX_SHARED": {"armeabi": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\armeabi\\libc++_shared.so", "armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\armeabi-v7a\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\arm64-v8a\\libc++_shared.so", "mips": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\mips\\libc++_shared.so", "mips64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\mips64\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\x86\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\llvm-libc++\\libs\\x86_64\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "STLPORT_SHARED": {"armeabi": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\armeabi\\libstlport_shared.so", "armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\armeabi-v7a\\libstlport_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\arm64-v8a\\libstlport_shared.so", "mips": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\mips\\libstlport_shared.so", "mips64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\mips64\\libstlport_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\x86\\libstlport_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\sources\\cxx-stl\\stlport\\libs\\x86_64\\libstlport_shared.so"}, "STLPORT_STATIC": {}, "SYSTEM": {}, "UNKNOWN": {}}, "project": {"rootBuildGradleFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a,armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "gnustl_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "3t3vu28ah6c2c4u2n3o1q2u41566w36l1l513px733uu1v2l6i3p1m5032", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.0.\n#   - $NDK is the path to NDK 16.1.4479499.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=27\n-DANDROID_PLATFORM=android-27\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DC<PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-std=c++11\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HC:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=27", "-DANDROID_PLATFORM=android-27", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-std=c++11", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\3t3vu28a\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\3t3vu28a\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\3t3vu28a"}