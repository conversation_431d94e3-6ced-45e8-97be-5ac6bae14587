package com.touptek.xcamview.activity;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u0004>?@AB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0015\u001a\u00020\u0014H\u0002J\b\u0010\u0016\u001a\u00020\u0014H\u0002J\u0006\u0010\u0017\u001a\u00020\u0014J\u0006\u0010\u0018\u001a\u00020\u0014J\u0006\u0010\u0019\u001a\u00020\u0014J\u0006\u0010\u001a\u001a\u00020\u0014J\u0006\u0010\u001b\u001a\u00020\u0014J\u0006\u0010\u001c\u001a\u00020\u0014J\u0006\u0010\u001d\u001a\u00020\u0014J\u0010\u0010\u001e\u001a\u00020\u00142\u0006\u0010\u001f\u001a\u00020 H\u0002J\u0006\u0010!\u001a\u00020\u0014J\b\u0010\"\u001a\u00020\u0014H\u0002J&\u0010#\u001a\u0004\u0018\u00010$2\u0006\u0010%\u001a\u00020&2\b\u0010\'\u001a\u0004\u0018\u00010(2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u0010+\u001a\u00020\u0014H\u0016J\b\u0010,\u001a\u00020\u0014H\u0016J\u001a\u0010-\u001a\u00020\u00142\u0006\u0010.\u001a\u00020$2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u0010/\u001a\u00020\u0014H\u0002J\b\u00100\u001a\u00020\u0014H\u0002J\u000e\u00101\u001a\u00020\u00142\u0006\u00102\u001a\u00020\fJ\u0010\u00103\u001a\u00020\u00142\u0006\u0010.\u001a\u00020$H\u0002J\u0010\u00104\u001a\u00020\u00142\u0006\u0010.\u001a\u00020$H\u0002J\b\u00105\u001a\u00020\u0014H\u0002J\b\u00106\u001a\u00020\u0014H\u0002J\u0010\u00107\u001a\u00020\u00142\u0006\u00108\u001a\u00020\u0004H\u0002J\b\u00109\u001a\u00020\u0014H\u0002J\u0014\u0010:\u001a\u00020;*\u00020<2\u0006\u0010=\u001a\u00020<H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lcom/touptek/xcamview/activity/MainMenu;", "Lcom/touptek/xcamview/util/BaseDialogFragment;", "()V", "TAG", "", "buttonActions", "", "Lcom/touptek/xcamview/activity/MainMenu$ButtonAction;", "isRecording", "", "isRectangleVisible", "rectangleListener", "Lcom/touptek/xcamview/activity/MainMenu$OnRectangleVisibilityListener;", "sceneType", "", "tpCameraManager", "Lcom/touptek/video/internal/TpCameraManager;", "tpCaptureImage", "Lcom/touptek/video/internal/TpCaptureImage;", "ZoomIn", "", "ZoomOut", "captureImage", "closeAllChildDialogs", "createStorageDefaultPath", "disableAllMenuButtons", "disableMenuButton", "displayRectangle", "enableAllMenuButtons", "enableMenuButton", "handleMenuAction", "action", "Lcom/touptek/xcamview/activity/MainMenu$MenuAction;", "hideRectangle", "initCapture", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onStart", "onViewCreated", "view", "openBrowse", "openTestSettings", "setRectangleVisibilityListener", "listener", "setupButtonClickListeners", "setupButtonStates", "showMeasurementPanel", "showSubMenu", "showToast", "message", "toggleRecording", "distanceTo", "", "Landroid/graphics/PointF;", "other", "ButtonAction", "MenuAction", "MenuPopupDialogFragment", "OnRectangleVisibilityListener", "app_debug"})
public final class MainMenu extends com.touptek.xcamview.util.BaseDialogFragment {
    private final java.lang.String TAG = "MainMenu";
    private boolean isRecording = false;
    private com.touptek.video.internal.TpCaptureImage tpCaptureImage;
    private com.touptek.video.internal.TpCameraManager tpCameraManager;
    private int sceneType = 0;
    private boolean isRectangleVisible = false;
    private com.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener rectangleListener;
    private final java.util.List<com.touptek.xcamview.activity.MainMenu.ButtonAction> buttonActions = null;
    
    public MainMenu() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void setupButtonClickListeners(android.view.View view) {
    }
    
    private final void handleMenuAction(com.touptek.xcamview.activity.MainMenu.MenuAction action) {
    }
    
    private final void captureImage() {
    }
    
    private final void toggleRecording() {
    }
    
    private final void openBrowse() {
    }
    
    private final void ZoomIn() {
    }
    
    private final void ZoomOut() {
    }
    
    private final void openTestSettings() {
    }
    
    private final void showMeasurementPanel() {
    }
    
    private final void showSubMenu() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void initCapture() {
    }
    
    @java.lang.Override
    public void onDestroy() {
    }
    
    private final void setupButtonStates(android.view.View view) {
    }
    
    public final void createStorageDefaultPath() {
    }
    
    private final float distanceTo(android.graphics.PointF $this$distanceTo, android.graphics.PointF other) {
        return 0.0F;
    }
    
    public final void setRectangleVisibilityListener(@org.jetbrains.annotations.NotNull
    com.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener listener) {
    }
    
    public final void displayRectangle() {
    }
    
    public final void hideRectangle() {
    }
    
    public final void disableMenuButton() {
    }
    
    public final void enableMenuButton() {
    }
    
    public final void closeAllChildDialogs() {
    }
    
    public final void disableAllMenuButtons() {
    }
    
    public final void enableAllMenuButtons() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\b\u0010\u0004\u001a\u00020\u0003H&\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/MainMenu$OnRectangleVisibilityListener;", "", "onHideRectangle", "", "onShowRectangle", "app_debug"})
    public static abstract interface OnRectangleVisibilityListener {
        
        public abstract void onShowRectangle();
        
        public abstract void onHideRectangle();
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J&\u0010\t\u001a\u0004\u0018\u00010\b2\u0006\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0006H\u0016J\b\u0010\u000f\u001a\u00020\u0010H\u0016J\u0010\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0014H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/touptek/xcamview/activity/MainMenu$MenuPopupDialogFragment;", "Lcom/touptek/xcamview/util/BaseDialogFragment;", "()V", "sceneType", "", "createISPDialogArguments", "Landroid/os/Bundle;", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "onStart", "", "setupPopMenuButtonStates", "showToast", "message", "", "app_debug"})
    public static final class MenuPopupDialogFragment extends com.touptek.xcamview.util.BaseDialogFragment {
        private int sceneType = 0;
        
        public MenuPopupDialogFragment() {
            super();
        }
        
        private final void showToast(java.lang.String message) {
        }
        
        @org.jetbrains.annotations.Nullable
        @java.lang.Override
        public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
        android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
        android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
        android.os.Bundle savedInstanceState) {
            return null;
        }
        
        @java.lang.Override
        public void onStart() {
        }
        
        private final void setupPopMenuButtonStates(android.view.View view) {
        }
        
        /**
         * 创建ISP对话框的参数Bundle，计算4个按钮组（第2-5个按钮）的位置和宽度
         */
        private final android.os.Bundle createISPDialogArguments(android.view.View view) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0082\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/touptek/xcamview/activity/MainMenu$ButtonAction;", "", "id", "", "action", "Lcom/touptek/xcamview/activity/MainMenu$MenuAction;", "(ILcom/touptek/xcamview/activity/MainMenu$MenuAction;)V", "getAction", "()Lcom/touptek/xcamview/activity/MainMenu$MenuAction;", "getId", "()I", "component1", "component2", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    static final class ButtonAction {
        private final int id = 0;
        @org.jetbrains.annotations.NotNull
        private final com.touptek.xcamview.activity.MainMenu.MenuAction action = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.MainMenu.ButtonAction copy(int id, @org.jetbrains.annotations.NotNull
        com.touptek.xcamview.activity.MainMenu.MenuAction action) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public ButtonAction(int id, @org.jetbrains.annotations.NotNull
        com.touptek.xcamview.activity.MainMenu.MenuAction action) {
            super();
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int getId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.MainMenu.MenuAction component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.MainMenu.MenuAction getAction() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0082\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/touptek/xcamview/activity/MainMenu$MenuAction;", "", "label", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getLabel", "()Ljava/lang/String;", "TAKE_PHOTO", "RECORD_VIDEO", "PAUSE", "BROWSE", "ZOOM_IN", "ZOOM_OUT", "SETTINGS", "ABOUT", "DRAW", "MENU", "app_debug"})
    static enum MenuAction {
        /*public static final*/ TAKE_PHOTO /* = new TAKE_PHOTO(null) */,
        /*public static final*/ RECORD_VIDEO /* = new RECORD_VIDEO(null) */,
        /*public static final*/ PAUSE /* = new PAUSE(null) */,
        /*public static final*/ BROWSE /* = new BROWSE(null) */,
        /*public static final*/ ZOOM_IN /* = new ZOOM_IN(null) */,
        /*public static final*/ ZOOM_OUT /* = new ZOOM_OUT(null) */,
        /*public static final*/ SETTINGS /* = new SETTINGS(null) */,
        /*public static final*/ ABOUT /* = new ABOUT(null) */,
        /*public static final*/ DRAW /* = new DRAW(null) */,
        /*public static final*/ MENU /* = new MENU(null) */;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String label = null;
        
        MenuAction(java.lang.String label) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getLabel() {
            return null;
        }
    }
}