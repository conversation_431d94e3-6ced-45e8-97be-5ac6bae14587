apply plugin: 'com.android.application'
android {
    signingConfigs {
    }
    compileSdkVersion 30
    buildToolsVersion '30.0.3'
    ndkVersion '23.1.7779620'
    aaptOptions.cruncherEnabled = false
    aaptOptions.useNewCruncher = false

    defaultConfig {
        applicationId "com.droid.usbcam"
        minSdkVersion 21
        targetSdkVersion 30
        ndk {
            moduleName "jnicam"
            ldLibs "log","jnigraphics"
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
        buildConfigField 'String', 'BUILD_TIME', 'new java.text.SimpleDateFormat("yyyyMMdd").format(new java.util.Date(' + System.currentTimeMillis() +'L))'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }
    }
    sourceSets { main { jni.srcDirs = ['src/main/jni', 'src/main/jni/'] } }
    externalNativeBuild {
        ndkBuild {
            path file("src/main/jni/Android.mk")
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    productFlavors {
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.jakewharton:disklrucache:2.0.2'
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.2.0"
}

