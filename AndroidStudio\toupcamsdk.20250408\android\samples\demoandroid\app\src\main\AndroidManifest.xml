<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.droid.usbcam"
    android:versionCode="1"
    android:versionName="1.0">

    <application
        android:allowBackup="false"
        android:icon="@drawable/ic_launcher"
        android:label="@string/str_app_name"
        android:theme="@style/AppTheme">
        <activity
            android:name=".CamListActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|keyboard|keyboardHidden|navigation"
            android:screenOrientation="fullSensor">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|keyboard|keyboardHidden|navigation"
            android:screenOrientation="fullSensor"
            android:theme="@style/Theme.Fullscreen" />
    </application>

</manifest>