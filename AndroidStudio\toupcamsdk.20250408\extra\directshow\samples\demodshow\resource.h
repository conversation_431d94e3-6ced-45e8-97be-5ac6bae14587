//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by demodshow.rc
//
#define IDR_MAINFRAME                   128
#define IDD_SIZEDLG                     310
#define IDD_ROIDLG                      311
#define IDC_EDIT1                       1000
#define IDC_EDIT2                       1001
#define IDC_EDIT3                       1002
#define IDC_EDIT4                       1003
#define ID_EXAMPLE_FLIPVERTICAL         32772
#define ID_EXAMPLE_FLIPHORIZONTAL       32773
#define IDS_FRAMERATE                   32774
#define ID_SETUP_SETUP0                 32791
#define ID_SETUP_SETUP1                 32792
#define ID_SETUP_SETUP2                 32793
#define ID_SETUP_SETUP3                 32794
#define ID_SETUP_SETUP4                 32795
#define ID_EXAMPLE_SIZE                 32796
#define ID_EXAMPLE_PREVIEW_SNAPSHOT     32798
#define ID_EXAMPLE_STILLIMAGE_SNAPSHOT  32799
#define ID_EXAMPLE_SN                   32802
#define ID_EXAMPLE_CAPTURE              32803
#define ID_EXAMPLE_STOPCAPTURE          32806
#define ID_EXAMPLE_ROI                  32807
#define ID_CAMERA00                     33000
#define ID_CAMERA01                     33001
#define ID_CAMERA02                     33002
#define ID_CAMERA03                     33003
#define ID_CAMERA04                     33004
#define ID_CAMERA05                     33005
#define ID_CAMERA06                     33006
#define ID_CAMERA07                     33007
#define ID_CAMERA08                     33008
#define ID_CAMERA09                     33009
#define ID_CAMERA10                     33010
#define ID_CAMERA11                     33011
#define ID_CAMERA12                     33012
#define ID_CAMERA13                     33013
#define ID_CAMERA14                     33014
#define ID_CAMERA15                     33015
#define ID_CAMERA16                     33016
#define ID_CAMERA17                     33017
#define ID_CAMERA18                     33018
#define ID_CAMERA19                     33019
#define ID_CAMERA20                     33020
#define ID_CAMERA21                     33021
#define ID_CAMERA22                     33022
#define ID_CAMERA23                     33023
#define ID_CAMERA24                     33024
#define ID_CAMERA25                     33025
#define ID_CAMERA26                     33026
#define ID_CAMERA27                     33027
#define ID_CAMERA28                     33028
#define ID_CAMERA29                     33029
#define ID_CAMERA30                     33030
#define ID_CAMERA31                     33031
#define ID_CAMERA32                     33032
#define ID_CAMERA33                     33033
#define ID_CAMERA34                     33034
#define ID_CAMERA35                     33035
#define ID_CAMERA36                     33036
#define ID_CAMERA37                     33037
#define ID_CAMERA38                     33038
#define ID_CAMERA39                     33039

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        311
#define _APS_NEXT_COMMAND_VALUE         32808
#define _APS_NEXT_CONTROL_VALUE         1001
#define _APS_NEXT_SYMED_VALUE           312
#endif
#endif
